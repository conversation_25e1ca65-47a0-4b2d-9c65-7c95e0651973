package common

import (
	"strings"

	"tms/model"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

// Route 结构体用于存储路由信息
type Route struct {
	ID               string            `json:"id"`       // 路由唯一ID
	ParentID         string            `json:"parentId"` // 父分组ID
	Method           string            `json:"method"`
	Path             string            `json:"path"`
	Name             string            `json:"name"`
	Handler          gin.HandlerFunc   `json:"-"`
	RouteMiddlewares []gin.HandlerFunc `json:"-"`        // 路由特定的中间件
	GroupMiddlewares []gin.HandlerFunc `json:"-"`        // 组中间件
	AuthType         string            `json:"authType"` // 表示认证类型 guest不认证user用户teacher教师admin管理员all所有用户
}

// routes 存储所有需要注册的路由
var Routes = make(map[string]Route)

var APIRootGroup *RouteGroup // 全局 API 根路由组

func init() {
	APIRootGroup = &RouteGroup{
		Prefix:      "api",
		ID:          model.GeneratePermissionID("GROUP", "/api"), // 为根分组生成ID
		Description: "API Root",
		Routes:      make([]Route, 0),
		Children:    make([]*RouteGroup, 0),
	}
}

// RouteGroup 表示一个路由分组，支持嵌套
type RouteGroup struct {
	ID            string            // 分组的唯一ID，用于权限同步
	Parent        *RouteGroup       // 父分组，用于构建完整路径
	Prefix        string            // 当前分组路径前缀
	Description   string            // 分组描述
	GroupHandlers []gin.HandlerFunc // 组中间件列表
	Routes        []Route           `json:"routes"`
	Children      []*RouteGroup     // 子分组列表
}

// NewGroup 创建根分组
func NewGroup(prefix, description string) *RouteGroup {
	trimmedPrefix := strings.Trim(prefix, "/")

	// 检查 APIRootGroup 的子分组中是否已存在该前缀的分组
	for _, child := range APIRootGroup.Children {
		if child.Prefix == trimmedPrefix {
			return child // 如果存在，则返回已存在的子分组
		}
	}

	// 如果不存在，则创建新的分组
	child := &RouteGroup{
		Parent:      APIRootGroup,                                                                   // 将其父分组设置为 APIRootGroup
		ID:          model.GeneratePermissionID("GROUP", APIRootGroup.buildFullPath(trimmedPrefix)), // 为子分组生成ID
		Prefix:      trimmedPrefix,
		Description: description,
		Routes:      make([]Route, 0),
		Children:    make([]*RouteGroup, 0),
	}
	// 将新创建的分组添加到 APIRootGroup 的子分组列表中
	APIRootGroup.Children = append(APIRootGroup.Children, child)
	return child
}

// Group 创建当前分组下的子分组
func (rg *RouteGroup) Group(prefix, description string) *RouteGroup {
	trimmedPrefix := strings.Trim(prefix, "/")

	// 检查当前分组的子分组中是否已存在该前缀的分组
	for _, child := range rg.Children {
		if child.Prefix == trimmedPrefix {
			return child // 如果存在，则返回已存在的子分组
		}
	}

	// 如果不存在，则创建新的子分组
	child := &RouteGroup{
		Parent:      rg,
		ID:          model.GeneratePermissionID("GROUP", rg.buildFullPath(trimmedPrefix)), // 为子分组生成ID
		Prefix:      trimmedPrefix,
		Description: description,
		Routes:      make([]Route, 0),
		Children:    make([]*RouteGroup, 0),
	}
	rg.Children = append(rg.Children, child)
	return child
}

// Use 添加中间件到当前路由组
func (rg *RouteGroup) Use(middlewares ...gin.HandlerFunc) {
	rg.GroupHandlers = append(rg.GroupHandlers, middlewares...)
}

// Register 向该分组中注册一个路由
// Register 向该分组中注册一个路由
func (rg *RouteGroup) Register(method, path string, handler gin.HandlerFunc, authType string, name string, routeMiddlewares ...gin.HandlerFunc) {
	fullPath := rg.buildFullPath(path)

	id := GeneratePermissionID(method, fullPath) // 使用已有方法生成 ID
	parentID := rg.ID                            // 当前路由属于当前分组

	route := Route{
		Method:           method,
		Path:             fullPath,
		Name:             name,
		Handler:          handler,
		RouteMiddlewares: routeMiddlewares,
		GroupMiddlewares: rg.GroupHandlers,
		AuthType:         authType,
		ID:               id,       // 设置 ID
		ParentID:         parentID, // 设置 ParentID
	}
	RegisterRouter(id, parentID, method, fullPath, handler, authType, name, route.GroupMiddlewares, route.RouteMiddlewares)
	rg.Routes = append(rg.Routes, route)
}

// 构建完整路径
func (rg *RouteGroup) buildFullPath(path string) string {
	var parentPath string
	if rg.Parent != nil {
		parentPath = rg.Parent.buildFullPath("")
	}

	currentPath := "/" + rg.Prefix
	if path == "/" || path == "" {
		return parentPath + currentPath
	}

	return parentPath + currentPath + "/" + strings.Trim(path, "/")
}

// GetGroupRoutes 获取当前分组下所有路由（不包括子分组）
func (rg *RouteGroup) GetGroupRoutes() []Route {
	return rg.Routes
}

// GetAllRoutes 递归获取当前分组及其所有子分组下的路由
func GetAllRoutes(group *RouteGroup) []Route {
	var allRoutes []Route
	allRoutes = append(allRoutes, group.Routes...)

	for _, child := range group.Children {
		allRoutes = append(allRoutes, GetAllRoutes(child)...)
	}

	return allRoutes
}

// RegisterRouter 注册控制器中的路由
func RegisterRouter(id, pid, method string, path string, handler gin.HandlerFunc, authType string, name string, groupMiddlewares []gin.HandlerFunc, routeMiddlewares []gin.HandlerFunc) {
	path = strings.TrimPrefix(path, "/")
	path = strings.TrimSuffix(path, "/")

	// 拼接路由地址
	if path == "index" {
		path = "/"
	} else {
		path = "/" + strings.ToLower(path)
	}

	// 构造键值
	//key := method + ":" + path

	// 添加路由
	Routes[id] = Route{
		ID:               id,
		ParentID:         pid,
		Path:             path,
		Method:           method,
		Handler:          handler,
		AuthType:         authType,
		Name:             name,
		GroupMiddlewares: groupMiddlewares, // 存储组中间件
		RouteMiddlewares: routeMiddlewares, // 存储路由特定中间件
	}

	//slog.Info("Register route", "method", method, "path", path)
}

// BindRouter 绑定所有注册的路由到 Gin 引擎
func BindRouter(e *gin.Engine) {
	for _, route := range Routes {
		var allHandlers []gin.HandlerFunc
		allHandlers = append(allHandlers, route.GroupMiddlewares...)
		allHandlers = append(allHandlers, route.RouteMiddlewares...)
		allHandlers = append(allHandlers, route.Handler)

		switch route.Method {
		case "GET":
			e.GET(route.Path, allHandlers...)
		case "POST":
			e.POST(route.Path, allHandlers...)
		case "DELETE":
			e.DELETE(route.Path, allHandlers...)
		case "PUT":
			e.PUT(route.Path, allHandlers...)
		}
	}
}
func GetTreeRoutes() *RouteGroup {
	return APIRootGroup
}

// // GetSortedRoutes 返回所有已注册的路由，按层级结构排序
// func GetSortedRoutes() []Route {
// 	var routes []Route
// 	if APIRootGroup == nil {
// 		return routes
// 	}

// 	var dfs func(group *RouteGroup)
// 	dfs = func(group *RouteGroup) {
// 		// 先添加本组路由
// 		routes = append(routes, group.Routes...)
// 		// 再递归子分组
// 		for _, child := range group.Children {
// 			dfs(child)
// 		}
// 	}

//		dfs(APIRootGroup)
//		return routes
//	}
func GeneratePermissionID(method, path string) string {
	hash := 0
	for _, char := range method + path {
		hash = (hash*31 + int(char)) % 1000000007 // Using a large prime to reduce collisions
	}
	return cast.ToString(hash)
}

// GetPathsByID 通过权限ID获取路径
func GetPathsByCode(code string) []Route {
	var paths []Route

	for _, route := range Routes {
		if route.AuthType == code {
			paths = append(paths, route)
		}
	}

	return paths
}

// PrintRouteTreeDFS 深度优先遍历并打印路由树
// func PrintRouteTreeDFS(group *RouteGroup, indent int) {
// 	if group == nil {
// 		return
// 	}

// 	// 打印当前分组信息
// 	log.Printf("%sGroup: %s (%s)", strings.Repeat("  ", indent), group.Prefix, group.Description)

// 	// 打印当前分组下的路由
// 	for _, route := range group.Routes {
// 		log.Printf("%s  Route: %s %s (%s)", strings.Repeat("  ", indent), route.Method, route.Path, route.Name)
// 	}

// 	// 递归遍历子分组
// 	for _, child := range group.Children {
// 		PrintRouteTreeDFS(child, indent+1)
// 	}
// }

type TreeNode struct {
	ID       string     `json:"id"`
	Label    string     `json:"label"`    // 节点显示名称
	Method   string     `json:"method"`   // HTTP 方法（仅路由节点有）
	Path     string     `json:"path"`     // 路径（仅路由节点有）
	AuthType string     `json:"authType"` // 权限类型（仅路由节点有）
	Children []TreeNode `json:"children"` // 子节点
}

func (rg *RouteGroup) ToTreeNodes(sysCode string) []TreeNode {
	var nodes []TreeNode

	// 添加当前分组下的路由
	for _, route := range rg.Routes {
		if route.AuthType == model.AuthNone {
			continue
		}
		if route.AuthType == sysCode {
			nodes = append(nodes, TreeNode{
				ID:       route.ID,
				Label:    route.Name,
				Method:   route.Method,
				Path:     route.Path,
				AuthType: route.AuthType,
			})
		}

	}

	// 添加子分组
	for _, child := range rg.Children {
		childNodes := child.ToTreeNodes(sysCode)
		if len(childNodes) > 0 {
			childNode := TreeNode{
				ID:       child.ID,
				Label:    child.Description, // 使用分组描述作为节点名称
				Children: childNodes,
			}
			nodes = append(nodes, childNode)
		}

	}

	return nodes
}
func (rg *RouteGroup) GetApiList(sysCode string) []TreeNode {
	nodes := rg.ToTreeNodes(sysCode)

	// 检查根节点是否存在且有子节点
	if len(nodes) > 0 && len(nodes[0].Children) > 0 {
		return nodes[0].Children
	}

	// 如果没有子节点，返回空列表
	return []TreeNode{}
}
