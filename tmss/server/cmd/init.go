package cmd

import (
	"log"
	"tms/common"
	"tms/model"
	"tms/pkg/cache"
	"tms/pkg/db"
	"tms/services/user"
	"tms/services/workflow"
	"tms/utils"

	gormadapter "github.com/casbin/gorm-adapter/v3"
	"gorm.io/gorm"
)

func autoMigrate(db *gorm.DB) error {
	models := []interface{}{
		&model.Users{},
		&model.Roles{},
		&model.Menus{},
		&model.Permissions{},
		&model.RolePermissions{},
		&model.RoleMenu{},
		&model.RoleDataMap{},
		&model.RoleDataConfig{},
		&model.UserRoles{},
		&model.UserCodes{},
		&model.StudentMap{},
		&gormadapter.CasbinRule{},
		&model.SyncRecords{},
		&model.Class{},
		&model.Classroom{},
		&model.ClassMajor{},
		&model.ClassSchedule{},
		&model.RequestLog{},
		&model.Majors{},
		&model.Syllabus{},
		//&model.SyllabusRevision{},
		&model.SyllabusExt{},
		&model.Textbook{},
		&model.TeachingPlan{},
		&model.TeachingPlanExt{},
		&model.TeachingDocument{},
		&model.Certificates{},
		&model.Courses{},
		&model.CourseExt{},
		&model.Chapter{},
		&model.Courseware{},
		&model.CoursewareExt{},
		&model.Questions{},
		&model.QuestionsExt{},
		&model.QuestionCourseware{},
		&model.KnowledgePoints{},
		&model.Papers{},
		&model.PaperQuestions{},
		&model.Resources{},
		&model.ResourceCategory{},
		&model.ResourceDownloadLog{},
		&model.Workflow{},
		&model.WorkflowNodeUsers{},
		&model.WorkflowMsg{},
		&model.WorkflowApprove{},
		&model.WorkflowRemark{},
		&model.Exams{},
		&model.ExamsExt{},
		&model.ExamProgress{},
		&model.ExamAnswer{},
		&model.ExamReview{},
		&model.ExamScore{},
		&model.QuestionProgress{},
		&model.QuestionAnswer{},
		&model.Assignment{},
		&model.AssignmentActions{},
		&model.AssignmentExt{},
		&model.LearningProgress{},
		&model.LearningSummary{},
		&model.LearningProgressSummary{},
		&model.RevisionRecord{},
		&model.ScormSessions{},
		&model.VirtualCoursewareSubmitHistory{},
	}
	err := db.AutoMigrate(models...)
	if err != nil {
		//return fmt.Errorf("AutoMigrate for User failed: %v", err)
		//log.Printf("AutoMigrate for User failed: %v", err)
	}

	return nil
}

// 初始化 角色 和 超级管理员账户
func initInitialData(tx *gorm.DB) error {
	var rolesCount int64
	// 获取超级管理员角色总数
	if err := tx.Model(&model.Roles{}).Where("role_code = ?", model.AuthAdmin).Count(&rolesCount).Error; err != nil {
		return err
	}

	if rolesCount == 0 {
		// 添加超级管理员角色, 无法指定ID会造成自动生成id冲突
		err := AddRoles(tx, model.AuthAdmin, "管理员", "系统内置角色，拥有后台管理所有权限")
		if err != nil {
			return err
		}
		err = AddRoles(tx, model.AuthTeacher, "教员", "系统内置角色，拥有教员所有权限")
		if err != nil {
			return err
		}
		err = AddRoles(tx, model.AuthUser, "学员", "系统内置角色，拥有学员所有权限")
		if err != nil {
			return err
		}
		// 添加超级管理员账户
		err = AddUser(tx, model.AuthAdmin, "123456")
		if err != nil {
			return err
		}
		err = AddUser(tx, model.AuthTeacher, "123456")
		if err != nil {
			return err
		}
		err = AddUser(tx, model.AuthUser, "123456")
		if err != nil {
			return err
		}
		// 添加测试账号，正是环境需要去除
		err = AddTestUser(tx, "taoge")
		if err != nil {
			return err
		}
		err = AddTestUser(tx, "tian")
		if err != nil {
			return err
		}
		err = AddTestUser(tx, "wuao")
		if err != nil {
			return err
		}
		err = model.InitMenuData(tx)
		if err != nil {
			return err
		}
		err = model.InitMajorData(tx)
		if err != nil {
			return err
		}
		err = model.InitWorkflowData(tx)
		if err != nil {
			return err
		}
		err = model.InitWorkflowNodeUsersData(tx)
		if err != nil {
			return err
		}
	}

	return nil
}
func AddRoles(tx *gorm.DB, code, name, desc string) error {
	adminRole := model.Roles{RoleName: name, RoleCode: code, SysCode: code, Description: desc, IsSystem: true}
	if err := tx.Create(&adminRole).Error; err != nil {
		return err
	}
	paths := common.GetPathsByCode(code)
	// 添加角色权限
	if len(paths) == 0 {
		return nil
	}
	var rolePermissions []model.RolePermissions
	for _, p := range paths {
		rolePermissions = append(rolePermissions, model.RolePermissions{RoleCode: code, Path: p.Path, Method: p.Method, ID: p.ID, Label: p.Name})
	}
	if err := tx.Create(&rolePermissions).Error; err != nil {
		return err
	}
	return nil
}
func AddUser(tx *gorm.DB, code, pwd string) error {
	salt, _ := utils.GenerateSalt(6)
	password := utils.HashPassword(pwd, salt)
	adminUser := model.Users{
		Account:  code,
		Username: code,
		Password: password,
		Status:   "1",
		Salt:     salt,
	}
	if err := tx.Create(&adminUser).Error; err != nil {
		return err
	}
	// 添加超级管理员账户关联
	if err := AddUserRole(tx, adminUser.ID, code); err != nil {
		return err
	}
	return nil
}
func AddUserRole(tx *gorm.DB, userId int64, code string) error {
	// 添加超级管理员账户关联
	if err := tx.Create(&model.UserRoles{UserID: userId, RoleCode: code}).Error; err != nil {
		return err
	}
	if err := tx.Create(&model.UserCodes{UserID: userId, SysCode: code}).Error; err != nil {
		return err
	}
	return nil
}
func AddTestUser(tx *gorm.DB, username string) error {
	salt, _ := utils.GenerateSalt(6)
	password := utils.HashPassword("111111", salt)
	adminUser := model.Users{
		Account:  username,
		Username: username,
		Password: password,
		Status:   "1",
		Salt:     salt,
	}
	if err := tx.Create(&adminUser).Error; err != nil {
		return err
	}
	// 添加超级管理员账户关联
	if err := AddUserRole(tx, adminUser.ID, "admin"); err != nil {
		return err
	}
	if err := AddUserRole(tx, adminUser.ID, "teacher"); err != nil {
		return err
	}
	if err := AddUserRole(tx, adminUser.ID, "user"); err != nil {
		return err
	}
	return nil
}
func initSystemData() {
	tx := db.DB.Begin()
	err := autoMigrate(tx)
	if err != nil {
		tx.Rollback()
		log.Println("Failed to auto migrate:", err)
		return
	}
	cache.New()
	err = initInitialData(tx)
	if err != nil {
		tx.Rollback()
		log.Println("Failed to init initial data:", err)
		return
	}
	if err := workflow.InitWorkflowModuleConfigService(); err != nil {
		tx.Rollback()
		log.Printf("Failed to initialize workflow config: " + err.Error())
	}
	if err := user.InitDataConfigCache(tx); err != nil {
		tx.Rollback()
		log.Printf("Failed to initialize data config cache: " + err.Error())
	}
	tx.Commit()

}
