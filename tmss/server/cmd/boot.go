package cmd

import (
	"context"
	"log/slog"
	"net/http"
	"os"
	"os/signal"
	"strconv"
	"syscall"
	"time"
	_ "tms/app"
	"tms/common"
	"tms/middleware"
	"tms/pkg/config"
	"tms/pkg/db"
	"tms/pkg/dict"
	"tms/pkg/logger"

	"github.com/gin-gonic/gin"
)

var Server *http.Server

// Start 启动 TMS 服务器
//
// 该函数将执行以下操作:
//
// 1. 初始化数据库
// 2. 加载系统配置
// 3. 初始化系统数据
// 4. 初始化日志
// 5. 设置 Gin 模式
// 6. 创建 Gin 路由
// 7. 添加中间件
// 8. 绑定路由
// 9. 同步权限到数据库
// 10. 添加一个根路径处理程序
// 11. 启动服务
// 12. 监听信号来重启服务
func Start() {
	// 初始化数据库
	err := db.InitDatabase()
	if err != nil {
		slog.Error("Failed to initialize database:", "error", err)
		os.Exit(1)
		return
	}

	// 加载系统配置
	config.LoadSystemConfig()
	// 初始化字典
	dict.InitDictModule()
	// 初始化系统数据
	initSystemData()
	// 注册路由
	//app.RegisterRouters()
	// 初始化日志
	handler, err := logger.InitLogger()
	if err != nil {
		slog.Error("Failed to initialize logger:", "error", err)
	}

	// 设置 Gin 模式
	if config.Config.System.Debug {
		gin.SetMode(gin.DebugMode)
	} else {
		gin.SetMode(gin.ReleaseMode)
	}

	// 创建 Gin 路由
	r := gin.Default()

	// 处理异常
	r.Use(middleware.Recover)

	// 使用中间件
	r.Use(middleware.Cors())

	// store := middleware.GetSessionStore()
	// r.Use(sessions.Sessions("godoSession", store))

	r.Use(middleware.JwtVerify())
	err = middleware.InitCasbin()
	if err != nil {
		slog.Error("Failed to initialize casbin:", "error", err)
		os.Exit(1)
		return
	}
	r.Use(middleware.CasbinMiddleware())
	r.Use(middleware.LoggerMiddleware(handler))
	common.BindRouter(r)
	// 添加一个根路径处理程序，避免在没有其他路由匹配时触发 404
	r.GET("/", func(c *gin.Context) {
		c.String(http.StatusOK, "Welcome to TMS Server!")
	})

	r.Static("/uploads", "uploads")
	r.StaticFS("/courseware-assets", http.Dir("data/scorm"))
	r.StaticFS("/resource-assets", http.Dir("data/resources"))

	// 将端口号转换为字符串
	portStr := strconv.Itoa(config.Config.System.Port)

	// 创建并返回 http.Server 实例
	Server = &http.Server{
		Addr:    ":" + portStr,
		Handler: r,
	}
	go func() {
		if err := Server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			slog.Error("Failed to start server:", "error", err)
			os.Exit(1)
		}
	}()

	// 监听信号来重启服务
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, os.Interrupt, syscall.SIGTERM, syscall.SIGHUP)

	go func() {
		for sig := range sigChan {
			switch sig {
			case os.Interrupt, syscall.SIGTERM:
				slog.Info("Received SIGTERM or SIGINT, shutting down...")
				Shutdown()
				os.Exit(0)
			case syscall.SIGHUP:
				slog.Info("Received SIGHUP, restarting...")
				Restart()
			default:
				slog.Info("Received unknown signal:", "signal", sig)
			}
		}
	}()

	// 主循环
	select {}
}
func Restart() {
	if Server == nil {
		slog.Error("Server is not running.")
		return
	}
	// 关闭当前服务
	Shutdown()

	// 重新启动服务
	Start()
}

// 关闭 HTTP 服务器
func Shutdown() {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// 关闭服务器
	if err := Server.Shutdown(ctx); err != nil {
		slog.Error("Failed to shutdown server:", "error", err)
	} else {
		slog.Info("Server gracefully shutdown")
	}
}
