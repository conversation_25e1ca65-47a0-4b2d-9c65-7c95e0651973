package config

import (
	"crypto/rand"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"

	"os"

	"github.com/emmansun/gmsm/sm2"
)

// Config 是全局的系统配置实例。
var Config SystemEnv

// SystemEnv 结构体定义了系统、日志和缓存的配置信息。
type SystemEnv struct {
	System System  `json:"system"`
	Log    Log     `json:"log"`
	Cache  Cache   `json:"cache"`
	Course Course  `json:"course"` // 课件路径配置
	Upload Upload  `json:"upload"` // 资料上传配置
	Backup Backup  `json:"backup"` // 备份策略配置
	Secret Secrets `json:"secret"` // 密钥配置
}

// System 结构体定义了系统相关的配置。
type System struct {
	Debug         bool     `json:"debug"`
	Port          int      `json:"port"`
	Host          string   `json:"host"`
	Scheme        string   `json:"scheme"`
	SessionType   string   `json:"sessionType"`
	SessionSecret string   `json:"sessionSecret"`
	IpAccess      []string `json:"ipAccess"`
}
type Secrets struct {
	CertificatesKey string `json:"certificates_key"`
	ResourcesKey    string `json:"resources_key"`
	CoursewareKey   string `json:"courseware_key"`
}

// Cache 结构体定义了缓存相关的配置。
type Cache struct {
	Type    string `json:"type"`    // 缓存类型: memory/file
	FileDir string `json:"fileDir"` // 文件缓存目录(仅文件缓存需要)
}

// Log 结构体定义了日志相关的配置。
type Log struct {
	WriteFile  bool   `json:"writeFile"`
	Path       string `json:"path"`
	Filename   string `json:"filename"`
	MaxSize    int    `json:"maxSize"`
	MaxBackups int    `json:"maxBackups"`
	MaxAge     int    `json:"maxAge"`
}

// Course 结构体定义了课件路径配置
type Course struct {
	Path string `json:"path"`
}

// Upload 结构体定义了资料上传下载设置
type Upload struct {
	Path       string   `json:"path"`
	Formats    []string `json:"formats"`    // 允许的格式
	MaxSize    int      `json:"maxSize"`    // 文件最大大小（MB）
	Permission []string `json:"permission"` // 下载权限
}

// Backup 结构体定义了备份策略
type Backup struct {
	Frequency string `json:"frequency"` // 备份频率 daily/weekly/monthly
	Time      string `json:"time"`      // 备份时间（HH:mm 格式）
	Path      string `json:"path"`      // 备份路径
	Retention int    `json:"retention"` // 保留天数
}

// LoadSystemConfig 加载系统和登录配置
func LoadSystemConfig() error {
	data, err := os.ReadFile("config/system.json")
	if err != nil {
		return err
	}
	err = json.Unmarshal(data, &Config)
	if err != nil {
		return err
	}
	// 新增：检查并生成缺失的密钥
	if err := ensureSecretKeys(); err != nil {
		return fmt.Errorf("密钥初始化失败: %w", err)
	}
	return nil
}

// SaveSystemConfig 将当前配置写入 system.json 文件
func SaveSystemConfig() error {
	data, err := json.MarshalIndent(Config, "", "  ")
	if err != nil {
		return err
	}
	err = os.WriteFile("config/system.json", data, 0644)
	if err != nil {
		return err
	}
	return nil
}

// ensureSecretKeys 确保所有必需的密钥已配置
func ensureSecretKeys() error {
	secrets := &Config.Secret

	// 检查并生成证书签名私钥
	if secrets.CertificatesKey == "" {
		key, err := generateSM2PrivateKey()
		if err != nil {
			return fmt.Errorf("生成证书私钥失败: %w", err)
		}
		secrets.CertificatesKey = key
	}

	// 检查并生成资源密钥
	if secrets.ResourcesKey == "" {
		key, err := generateSM2PrivateKey()
		if err != nil {
			return fmt.Errorf("生成资源私钥失败: %w", err)
		}
		secrets.ResourcesKey = key
	}

	// 检查并生成课件密钥
	if secrets.CoursewareKey == "" {
		key, err := generateSM2PrivateKey()
		if err != nil {
			return fmt.Errorf("生成课件私钥失败: %w", err)
		}
		secrets.CoursewareKey = key
	}

	// 保存更新后的配置
	return SaveSystemConfig()
}

// generateSM2PrivateKey 生成SM2私钥(PEM格式)
func LoadPrivateKey(hexKey string) (*sm2.PrivateKey, error) {
	if hexKey == "" {
		return nil, errors.New("hexKey cannot be empty")
	}
	keyBytes, err := hex.DecodeString(hexKey)
	if err != nil {
		return nil, err
	}
	return sm2.NewPrivateKey(keyBytes)
}

// 示例用法
// priv, err := loadPrivateKey(Config.Secret.CertificatesKey)
// generateSM2PrivateKey 生成SM2私钥，返回原始私钥字节（D值）
func generateSM2PrivateKey() (string, error) {
	privateKey, err := sm2.GenerateKey(rand.Reader)
	if err != nil {
		return "", err
	}

	// 直接获取私钥的D值（*big.Int）
	rawKeyBytes := privateKey.D.Bytes()

	// 返回16进制字符串表示（或Base64，根据需求选择）
	return fmt.Sprintf("%x", rawKeyBytes), nil
}
