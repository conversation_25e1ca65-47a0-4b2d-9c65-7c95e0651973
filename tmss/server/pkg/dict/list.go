package dict

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"sync"
	"tms/libs"
)

var DictEntryMap = sync.Map{} // 存储字典条目：map[string]DictEntry

type DictEntry struct {
	Name       string `json:"name"`        // 键名
	CategoryID string `json:"category_id"` // 所属分类 ID
	Value      any    `json:"value"`       // 值
}

// 获取字典数据文件路径
func getDictDataFilePath() (string, error) {
	// baseDir, err := common.GetAppDir()
	// if err != nil {
	// 	return "", err
	// }
	// dictFilePath := filepath.Join(baseDir, "dict_list.json")
	// if !libs.PathExists(dictFilePath) {
	// 	os.WriteFile(dictFilePath, []byte{}, 0644)
	// }
	// return dictFilePath, nil
	return filepath.Join("config", "dict_list.json"), nil
}
func InitDictModule() error {
	if err := loadDictCategories(); err != nil {
		return err
	}
	if err := loadDictEntries(); err != nil {
		return err
	}
	return nil
}

// 加载字典条目到内存
func loadDictEntries() error {
	filePath, err := getDictDataFilePath()
	if err != nil {
		return fmt.Errorf("获取字典文件失败: %w", err)
	}

	if !libs.PathExists(filePath) {
		os.WriteFile(filePath, []byte{}, 0644)
		return nil
	}

	content, err := os.ReadFile(filePath)
	if err != nil {
		return err
	}

	var entries []DictEntry
	if len(content) > 0 {
		err = json.Unmarshal(content, &entries)
		if err != nil {
			return err
		}
		for _, entry := range entries {
			DictEntryMap.Store(entry.Name, entry)
		}
	}
	return nil
}

// 持久化所有字典条目到文件
func saveDictEntries() error {
	filePath, err := getDictDataFilePath()
	if err != nil {
		return err
	}

	seenNames := make(map[string]bool)
	var entries []DictEntry
	DictEntryMap.Range(func(key, value interface{}) bool {
		entry := value.(DictEntry)
		if _, exists := seenNames[entry.Name]; !exists {
			seenNames[entry.Name] = true
			entries = append(entries, entry)
		}
		return true
	})

	data, err := json.MarshalIndent(entries, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化字典条目失败: %w", err)
	}

	if err := os.WriteFile(filePath, data, 0644); err != nil {
		return fmt.Errorf("写入字典文件失败: %w", err)
	}
	return nil
}

// 根据键名获取字典条目
func GetDictEntry(name string) (DictEntry, bool) {
	value, ok := DictEntryMap.Load(name)
	if !ok {
		return DictEntry{}, false
	}
	return value.(DictEntry), true
}

// 获取某个分类下的所有字典条目
func GetDictEntriesByCategory(categoryID string) []DictEntry {
	var result []DictEntry
	DictEntryMap.Range(func(key, value interface{}) bool {
		entry := value.(DictEntry)
		if entry.CategoryID == categoryID {
			result = append(result, entry)
		}
		return true
	})
	return result
}

// 添加或更新字典条目
func SaveOrUpdateDictEntry(entry DictEntry) error {
	DictEntryMap.Store(entry.Name, entry)
	return saveDictEntries()
}

// 批量添加或更新字典条目
func SaveOrUpdateDictEntries(entries []DictEntry) error {
	for _, entry := range entries {
		DictEntryMap.Store(entry.Name, entry)
	}
	return saveDictEntries()
}

// 删除字典条目
func DeleteDictEntry(name string) error {
	DictEntryMap.Delete(name)
	return saveDictEntries()
}
