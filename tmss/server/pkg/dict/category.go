package dict

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"sync"
	"tms/libs"
)

type DictCategory struct {
	ID   string `json:"id"`   // 分类唯一标识
	Name string `json:"name"` // 分类名称
}

var DictCategoryMap = sync.Map{} // map[string]DictCategory

// 获取分类配置文件路径
func getCategoryFilePath() (string, error) {
	// baseDir, err := common.GetAppDir()
	// if err != nil {
	// 	return "", err
	// }
	return filepath.Join("config", "dict_categories.json"), nil
}

// 加载所有分类
func loadDictCategories() error {
	filePath, err := getCategoryFilePath()
	if err != nil {
		return err
	}

	if !libs.PathExists(filePath) {
		os.WriteFile(filePath, []byte{}, 0644)
	}

	content, err := os.ReadFile(filePath)
	if err != nil {
		return err
	}

	var categories []DictCategory
	if len(content) > 0 {
		err = json.Unmarshal(content, &categories)
		if err != nil {
			return err
		}
		for _, cat := range categories {
			DictCategoryMap.Store(cat.ID, cat)
		}
	}
	return nil
}

// 保存所有分类到文件
func saveDictCategories() error {
	filePath, err := getCategoryFilePath()
	if err != nil {
		return err
	}

	var categories []DictCategory
	DictCategoryMap.Range(func(key, value interface{}) bool {
		categories = append(categories, value.(DictCategory))
		return true
	})

	data, err := json.MarshalIndent(categories, "", "  ")
	if err != nil {
		return err
	}

	return os.WriteFile(filePath, data, 0644)
}

// 添加分类
func AddDictCategory(id, name string) error {
	if _, loaded := DictCategoryMap.Load(id); loaded {
		return fmt.Errorf("分类 %s 已存在", id)
	}
	DictCategoryMap.Store(id, DictCategory{ID: id, Name: name})
	return saveDictCategories()
}

// 更新分类
func UpdateDictCategory(id, name string) error {
	if _, loaded := DictCategoryMap.Load(id); !loaded {
		return fmt.Errorf("分类 %s 不存在", id)
	}
	DictCategoryMap.Store(id, DictCategory{ID: id, Name: name})
	return saveDictCategories()
}

// 删除分类及其所有条目
func DeleteDictCategory(id string) error {
	DictCategoryMap.Delete(id)

	// 删除该分类下的所有字典条目
	DictEntryMap.Range(func(key, value interface{}) bool {
		entry := value.(DictEntry)
		if entry.CategoryID == id {
			DictEntryMap.Delete(entry.Name)
		}
		return true
	})

	return saveDictCategories()
}

// 获取所有分类
func GetAllDictCategories() []DictCategory {
	var categories []DictCategory
	DictCategoryMap.Range(func(key, value interface{}) bool {
		categories = append(categories, value.(DictCategory))
		return true
	})
	return categories
}
