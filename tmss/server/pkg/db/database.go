package db

import (
	"encoding/json"
	"fmt"
	"log"
	"os"
	"time"

	"gorm.io/driver/mysql"
	"gorm.io/driver/postgres"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	"gorm.io/gorm/schema"
)

type MySQL struct {
	Enable   bool   `json:"enable"`
	Host     string `json:"host"`
	Port     string `json:"port"`
	User     string `json:"user"`
	Password string `json:"password"`
	DBName   string `json:"dbname"`
}

type MongosDB struct {
	Enable   bool   `json:"enable"`
	URI      string `json:"uri"`
	Database string `json:"database"`
}

type SQLite struct {
	Enable bool   `json:"enable"`
	Path   string `json:"path"` // 数据库文件路径
}

type PostgreSQL struct {
	Enable   bool   `json:"enable"`
	Host     string `json:"host"`
	Port     string `json:"port"`
	User     string `json:"user"`
	Password string `json:"password"`
	DBName   string `json:"dbname"`
	SSLMode  string `json:"sslmode"` // disable/require
}
type DatabaseConfig struct {
	DBType          string     `json:"dbType"`          // mysql/mongodb/sqlite/postgresql
	Debug           bool       `json:"debug"`           // 是否开启调试模式
	MaxIdleConns    int        `json:"maxIdleConns"`    // 空闲连接数
	MaxOpenConns    int        `json:"maxOpenConns"`    // 最大连接数
	ConnMaxLifetime int        `json:"connMaxLifetime"` // 连接最大生命周期（秒）
	MySQL           MySQL      `json:"mysql"`
	MongoDB         MongosDB   `json:"mongodb"`
	SQLite          SQLite     `json:"sqlite"`
	PostgreSQL      PostgreSQL `json:"postgresql"`
}

var (
	MySQLDB      *gorm.DB
	PostgreSQLDB *gorm.DB
	SQLiteDB     *gorm.DB
	MongoDB      *gorm.DB
	AppConfig    DatabaseConfig
)
var DB *gorm.DB
var DBS = make(map[string]*gorm.DB)

func InitDatabase() error {
	if err := LoadConfig(); err != nil {
		log.Fatal("Failed to load config:", err)
		return err
	}
	gormConfig := GormConfig()

	switch AppConfig.DBType {
	case "postgresql":
		if err := InitPostgreSQL(gormConfig); err != nil {
			log.Fatal("Failed to connect to PostgreSQL:", err)
			return err
		}
		DB = PostgreSQLDB
	case "sqlite":
		if err := InitSQLite(gormConfig); err != nil {
			log.Fatal("Failed to connect to SQLite:", err)
			return err
		}
		DB = SQLiteDB

	// case "mongodb":
	// 	if err := InitMongoDB(gormConfig); err != nil {
	// 		log.Fatal("Failed to connect to MongoDB:", err)
	// 		return err
	// 	}
	// 	DB = MongoDB
	default:
		if err := InitMySQL(gormConfig); err != nil {
			log.Fatal("Failed to connect to MySQL:", err)
			return err
		}
		DB = MySQLDB
	}

	// 支持多数据库共存
	if AppConfig.MySQL.Enable && AppConfig.DBType != "mysql" {
		InitMySQL(gormConfig)
		DBS["mysql"] = MySQLDB
	}
	// if AppConfig.MongoDB.Enable && AppConfig.DBType != "mongodb" {
	// 	InitMongoDB(gormConfig)
	// 	DBS["mongodb"] = MongoDB
	// }
	if AppConfig.SQLite.Enable && AppConfig.DBType != "sqlite" {
		InitSQLite(gormConfig)
		DBS["sqlite"] = SQLiteDB
	}
	if AppConfig.PostgreSQL.Enable && AppConfig.DBType != "postgresql" {
		InitPostgreSQL(gormConfig)
		DBS["postgresql"] = PostgreSQLDB
	}
	return nil
}
func LoadConfig() error {
	data, err := os.ReadFile("config/database.json")
	if err != nil {
		return err
	}
	if err := json.Unmarshal(data, &AppConfig); err != nil {
		return err
	}

	// 设置连接池默认值
	if AppConfig.MaxIdleConns == 0 {
		AppConfig.MaxIdleConns = 50 // 调整为50，以支持更多空闲连接
	}
	if AppConfig.MaxOpenConns == 0 {
		AppConfig.MaxOpenConns = 200 // 调整为200，以支持500并发用户
	}
	if AppConfig.ConnMaxLifetime == 0 {
		AppConfig.ConnMaxLifetime = 600 // 10分钟
	}

	return nil
}
func SaveConfig() error {
	data, err := json.MarshalIndent(AppConfig, "", "  ")
	if err != nil {
		return err
	}
	err = os.WriteFile("config/database.json", data, 0644)
	if err != nil {
		return err
	}
	return nil
}
func GormConfig() gorm.Config {
	var loggerConfig logger.Interface
	if AppConfig.Debug {
		loggerConfig = logger.Default.LogMode(logger.Info)
	} else {
		loggerConfig = logger.Default.LogMode(logger.Silent)
	}
	return gorm.Config{
		NamingStrategy: schema.NamingStrategy{
			SingularTable: true,
		},
		Logger:                                   loggerConfig,
		DisableForeignKeyConstraintWhenMigrating: true,
	}
}
func InitMySQL(gormConfig gorm.Config) error {
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=utf8mb4&parseTime=True&loc=Local",
		AppConfig.MySQL.User,
		AppConfig.MySQL.Password,
		AppConfig.MySQL.Host,
		AppConfig.MySQL.Port,
		AppConfig.MySQL.DBName)

	db, err := gorm.Open(mysql.Open(dsn), &gormConfig)
	if err != nil {
		return err
	}

	sqlDB, err := db.DB()
	if err != nil {
		return err
	}

	// SetMaxIdleConns 设置空闲连接池中连接的最大数量
	sqlDB.SetMaxIdleConns(AppConfig.MaxIdleConns)
	// SetMaxOpenConns 设置打开数据库连接的最大数量
	sqlDB.SetMaxOpenConns(AppConfig.MaxOpenConns)
	// SetConnMaxLifetime 设置了连接可复用的最长时间
	sqlDB.SetConnMaxLifetime(time.Second * time.Duration(AppConfig.ConnMaxLifetime))

	MySQLDB = db
	log.Println("MySQL connected successfully")
	return nil
}

func InitPostgreSQL(gormConfig gorm.Config) error {
	dsn := fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=%s",
		AppConfig.PostgreSQL.Host,
		AppConfig.PostgreSQL.Port,
		AppConfig.PostgreSQL.User,
		AppConfig.PostgreSQL.Password,
		AppConfig.PostgreSQL.DBName,
		AppConfig.PostgreSQL.SSLMode)

	db, err := gorm.Open(postgres.Open(dsn), &gormConfig)
	if err != nil {
		return err
	}

	sqlDB, err := db.DB()
	if err != nil {
		return err
	}

	// SetMaxIdleConns 设置空闲连接池中连接的最大数量
	sqlDB.SetMaxIdleConns(AppConfig.MaxIdleConns)
	// SetMaxOpenConns 设置打开数据库连接的最大数量
	sqlDB.SetMaxOpenConns(AppConfig.MaxOpenConns)
	// SetConnMaxLifetime 设置了连接可复用的最长时间
	sqlDB.SetConnMaxLifetime(time.Second * time.Duration(AppConfig.ConnMaxLifetime))

	PostgreSQLDB = db
	log.Println("PostgreSQL connected successfully")
	return nil
}

func InitSQLite(gormConfig gorm.Config) error {
	db, err := gorm.Open(sqlite.Open(AppConfig.SQLite.Path), &gormConfig)
	if err != nil {
		return err
	}

	sqlDB, err := db.DB()
	if err != nil {
		return err
	}

	// SetMaxIdleConns 设置空闲连接池中连接的最大数量
	sqlDB.SetMaxIdleConns(AppConfig.MaxIdleConns)
	// SetMaxOpenConns 设置打开数据库连接的最大数量
	sqlDB.SetMaxOpenConns(AppConfig.MaxOpenConns)
	// SetConnMaxLifetime 设置了连接可复用的最长时间
	sqlDB.SetConnMaxLifetime(time.Second * time.Duration(AppConfig.ConnMaxLifetime))

	SQLiteDB = db
	log.Println("SQLite connected successfully")
	return nil
}

// func InitMongoDB(gormConfig gorm.Config) error {
// 	dialector := &mongodm.MongoDBDialector{
// 		URI:      AppConfig.MongoDB.URI, // MongoDB 连接 URI
// 		Database: AppConfig.MongoDB.Database,
// 	}
// 	var err error
// 	MongoDB, err = gorm.Open(dialector, &gormConfig)
// 	if err != nil {
// 		return err
// 	}
// 	log.Println("MongoDB connected successfully")
// 	return nil
// }
