package db

import (
	"fmt"

	"gorm.io/gorm"
)

// Transactional 是一个通用的 GORM 事务处理函数。
// 它接受一个数据库连接实例和一个回调函数。
// 回调函数将在事务中执行，如果回调函数返回错误，事务将回滚。
//
// 相比于 GORM 原生的 db.Transaction，此函数增加了以下特性：
// 1. 自动处理 panic：如果在事务回调函数中发生 panic，事务将自动回滚，并捕获 panic 信息作为错误返回。
// 2. 统一的错误处理：无论回调函数返回错误、开启事务失败、提交事务失败，都会通过统一的 err 返回。
// 3. 简化调用：用户无需手动调用 Commit 或 Rollback，这些操作由 Transactional 函数自动管理。
//
// 示例:
//
//	err := Transactional(db, func(tx *gorm.DB) error {
//	    // 在这里执行数据库操作，使用 tx 实例
//	    if err := tx.Create(&User{Name: "Test"}).Error; err != nil {
//	        return err
//	    }
//	    // 模拟一个 panic
//	    // panic("something went wrong")
//	    return nil
//	})
//	if err != nil {
//	    fmt.Printf("事务失败: %v\n", err)
//	} else {
//	    fmt.Println("事务成功")
//	}
func Transactional(db *gorm.DB, fn func(tx *gorm.DB) error) (err error) {
	// 开始事务
	tx := db.Begin()
	if tx.Error != nil {
		return fmt.Errorf("开启事务失败: %w", tx.Error)
	}

	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			err = fmt.Errorf("事务执行过程中发生 panic: %v", r)
			// 可以添加日志记录 panic 信息
			// log.Printf("事务 panic: %v", r)
		} else if err != nil {
			// 如果回调函数返回错误，则回滚事务
			tx.Rollback()
			// 可以添加日志记录回滚信息
			// log.Printf("事务回滚: %v", err)
		} else {
			// 如果没有错误，则提交事务
			if commitErr := tx.Commit().Error; commitErr != nil {
				err = fmt.Errorf("提交事务失败: %w", commitErr)
				// 可以添加日志记录提交失败信息
				// log.Printf("事务提交失败: %v", commitErr)
			}
		}
	}()

	// 执行回调函数
	err = fn(tx)
	return err
}
