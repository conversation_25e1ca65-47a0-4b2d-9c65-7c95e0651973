package auth_test

import (
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-contrib/sessions"
	"github.com/gin-contrib/sessions/cookie"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"

	"tms/model"
	gdb "tms/pkg/db"
	"tms/services/auth"
	"tms/test"
	"tms/utils"
)

// SetupTestEnvironment 设置测试环境，包括数据库和 Gin 上下文
func SetupTestEnvironment(t *testing.T) (*gorm.DB, *gin.Context, *auth.AuthService) {
	db := test.SetupDB()

	// 创建 Gin 上下文
	gin.SetMode(gin.TestMode)
	w := httptest.NewRecorder()
	// 创建 Gin 上下文和路由器
	c, r := gin.CreateTestContext(w)
	c.Request = httptest.NewRequest(http.MethodGet, "/test", nil)

	// 设置 session 中间件到路由器
	store := cookie.NewStore([]byte("godocms!2981$"))
	r.Use(sessions.Sessions("godoSession", store))

	// 模拟一个请求，让 session 中间件有机会设置 session
	r.GET("/test", func(ctx *gin.Context) {
		// 将 session 实例从中间件设置的上下文复制到我们用于测试的上下文
		session := sessions.Default(ctx)
		c.Set(sessions.DefaultKey, session)
		ctx.Status(http.StatusOK)
	})
	r.ServeHTTP(w, c.Request) // Trigger session middleware

	// 创建 AuthService 实例
	authService := &auth.AuthService{}

	return db, c, authService
}

func TestAuthService_Login(t *testing.T) {
	db, c, authService := SetupTestEnvironment(t)
	defer func() {
		// Clean up data after each test run
		db.Exec("DELETE FROM users")
		db.Exec("DELETE FROM roles")
		db.Exec("DELETE FROM user_roles")
	}()

	gdb.DB = db
	// 准备测试数据
	salt, err := utils.GenerateSalt(16) // Corrected: GenerateSalt takes length and returns error
	assert.NoError(t, err)
	hashedPassword := utils.HashPassword("password123", salt)
	user := model.Users{
		Account:  "testuser",
		Password: hashedPassword,
		Salt:     salt,
		Username: "Test User", // Corrected: Nickname -> Username
		Email:    "<EMAIL>",
		Phone:    "**********",
		Status:   "1", // Corrected: int -> string
	}
	user.CreatedAt = time.Now().Unix()
	user.UpdatedAt = time.Now().Unix()
	assert.NoError(t, db.Create(&user).Error)
	t.Logf("Created user with ID: %d", user.ID)

	role := model.Roles{
		RoleName:    "admin",
		RoleCode:    "admin",
		Description: "Admin Role", // Corrected: Remark -> Description
		CreatedAt:   time.Now().Unix(),
	}
	assert.NoError(t, db.Create(&role).Error)
	t.Logf("Created role with ID: %d", role.ID)

	userRole := model.UserRoles{
		UserID: user.ID,
		RoleID: role.ID,
	}
	assert.NoError(t, db.Create(&userRole).Error)
	t.Logf("Created userRole: UserID=%d, RoleID=%d", userRole.UserID, userRole.RoleID)

	// 验证 user_roles 表中的数据
	var count int64
	db.Model(&model.UserRoles{}).Where("user_id = ?", user.ID).Count(&count)
	t.Logf("UserRoles count for user %d: %d", user.ID, count)

	var userRoleIDS []int64
	db.Model(&model.UserRoles{}).Where("user_id = ?", user.ID).Pluck("role_id", &userRoleIDS)
	t.Logf("UserRoleIDs for user %d: %v", user.ID, userRoleIDS)

	var roles []model.Roles
	db.Model(&model.Roles{}).Where("id in (?)", userRoleIDS).Find(&roles)
	t.Logf("Roles found for user %d: %v", user.ID, roles)

	// 测试成功登录
	t.Run("Successful Login", func(t *testing.T) {
		loginResult, err := authService.Login("testuser", "password123", c)
		assert.NoError(t, err)
		assert.NotNil(t, loginResult)
		assert.Equal(t, user.Account, loginResult.User.Account)
		assert.Contains(t, loginResult.Roles, "admin")
		assert.NotEmpty(t, loginResult.Token)
	})

	// 测试用户不存在
	t.Run("User Not Found", func(t *testing.T) {
		loginResult, err := authService.Login("nonexistent", "password123", c)
		assert.Error(t, err)
		assert.Nil(t, loginResult)
		assert.EqualError(t, err, "用户不存在")
	})

	// 测试密码错误
	t.Run("Incorrect Password", func(t *testing.T) {
		loginResult, err := authService.Login("testuser", "wrongpassword", c)
		assert.Error(t, err)
		assert.Nil(t, loginResult)
		assert.EqualError(t, err, "密码错误")
	})
}
