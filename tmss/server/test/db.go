package test

import (
	"log"

	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"

	"tms/model"
)

// SetupDB initializes a new in-memory SQLite database for testing.
func SetupDB() *gorm.DB {
	db, err := gorm.Open(sqlite.Open("file::memory:?cache=shared&_auto_increment=true"), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Silent), // Silent logger for tests
	})
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}

	// AutoMigrate all models
	err = db.AutoMigrate(
		&model.Users{},
		&model.Roles{},
		&model.Menus{},
		&model.Permissions{},
		&model.RolePermissions{},
		&model.RoleMenu{},
		&model.UserRoles{},
	)
	if err != nil {
		log.Fatalf("Failed to auto migrate models: %v", err)
	}

	return db
}
