package question

import (
	"testing"
	"tms/model"
	"tms/services/tech"
)

func TestGenerateQuestions(t *testing.T) {
	qs := tech.NewQuestionService()
	for i := 0; i < 200; i++ {
		qs.CreateQuestionDraft(model.ReqQuestionUpdate{
			Questions: model.Questions{
				Title:        "测试题目",
				QuestionType: model.QuestionTypeJudge,
				Content:      "测试题目内容",
				Answer:       "测试答案",
				Score:        3,
				Minutes:      3,
				Options:      "",
			},
			CoursewareIDs: []int64{1},
			ChapterIDs:    []int64{1},
		}, 2)
	}
}
