package question

import (
	"testing"
	"tms/model"
	"tms/pkg/db"
	"tms/services/tech"
	"tms/test"
)

func TestGenerateQuestions(t *testing.T) {
	pgdb, err := test.InitPostgreSQL()
	if err != nil {
		t.Fatalf("Failed to connect to database: %v", err)
	}
	db.DB = pgdb

	qs := tech.NewQuestionService()

	// 生成100道单选题
	singleChoiceQuestions := []struct {
		title   string
		content string
		answer  string
	}{
		{"计算机基础知识1", "计算机的中央处理器CPU主要由什么组成？", "运算器和控制器"},
		{"计算机基础知识2", "下列哪个不是操作系统的主要功能？", "编译程序"},
		{"计算机基础知识3", "在计算机中，1KB等于多少字节？", "1024"},
		{"计算机基础知识4", "TCP/IP协议属于哪一层？", "传输层"},
		{"计算机基础知识5", "下列哪个是面向对象编程语言？", "Java"},
		{"数据结构1", "栈的特点是什么？", "后进先出"},
		{"数据结构2", "队列的特点是什么？", "先进先出"},
		{"数据结构3", "二叉树的遍历方式有几种？", "三种"},
		{"数据结构4", "哈希表的平均查找时间复杂度是？", "O(1)"},
		{"数据结构5", "链表相比数组的优势是什么？", "插入删除效率高"},
		{"算法基础1", "冒泡排序的时间复杂度是？", "O(n²)"},
		{"算法基础2", "快速排序的平均时间复杂度是？", "O(nlogn)"},
		{"算法基础3", "二分查找的前提条件是？", "数组有序"},
		{"算法基础4", "递归算法必须具备什么条件？", "终止条件"},
		{"算法基础5", "动态规划的核心思想是？", "最优子结构"},
		{"数据库1", "SQL中用于查询的关键字是？", "SELECT"},
		{"数据库2", "主键的作用是什么？", "唯一标识记录"},
		{"数据库3", "外键用于建立什么关系？", "表间关联"},
		{"数据库4", "事务的ACID特性中A代表什么？", "原子性"},
		{"数据库5", "索引的主要作用是？", "提高查询速度"},
		{"网络基础1", "HTTP协议默认端口号是？", "80"},
		{"网络基础2", "HTTPS协议默认端口号是？", "443"},
		{"网络基础3", "DNS的作用是什么？", "域名解析"},
		{"网络基础4", "IP地址分为几类？", "五类"},
		{"网络基础5", "子网掩码的作用是？", "划分网络"},
		{"编程语言1", "Java是什么类型的语言？", "面向对象"},
		{"编程语言2", "Python的特点是什么？", "简洁易读"},
		{"编程语言3", "C语言属于什么类型？", "过程式语言"},
		{"编程语言4", "JavaScript主要用于？", "网页开发"},
		{"编程语言5", "SQL是什么语言？", "结构化查询语言"},
		{"软件工程1", "软件生命周期包括哪些阶段？", "需求分析、设计、编码、测试、维护"},
		{"软件工程2", "瀑布模型的特点是？", "线性顺序"},
		{"软件工程3", "敏捷开发的核心理念是？", "快速迭代"},
		{"软件工程4", "单元测试的目的是？", "测试最小功能单元"},
		{"软件工程5", "版本控制的作用是？", "管理代码变更"},
		{"操作系统1", "进程和线程的区别是？", "进程是资源分配单位，线程是调度单位"},
		{"操作系统2", "死锁产生的必要条件有几个？", "四个"},
		{"操作系统3", "虚拟内存的作用是？", "扩大内存空间"},
		{"操作系统4", "文件系统的作用是？", "管理存储设备"},
		{"操作系统5", "中断的作用是？", "处理异步事件"},
		{"计算机组成1", "冯·诺依曼结构的特点是？", "程序和数据存储在同一存储器"},
		{"计算机组成2", "Cache的作用是？", "提高CPU访问速度"},
		{"计算机组成3", "总线的作用是？", "连接各部件"},
		{"计算机组成4", "指令周期包括哪些阶段？", "取指、译码、执行"},
		{"计算机组成5", "存储器的层次结构是？", "寄存器、Cache、内存、外存"},
		{"信息安全1", "对称加密的特点是？", "加密解密使用同一密钥"},
		{"信息安全2", "非对称加密的优势是？", "密钥分发安全"},
		{"信息安全3", "数字签名的作用是？", "身份认证和完整性验证"},
		{"信息安全4", "防火墙的作用是？", "网络访问控制"},
		{"信息安全5", "病毒的特征是？", "传染性、破坏性、隐蔽性"},
		{"人工智能1", "机器学习的三种类型是？", "监督学习、无监督学习、强化学习"},
		{"人工智能2", "神经网络的基本单元是？", "神经元"},
		{"人工智能3", "深度学习的特点是？", "多层神经网络"},
		{"人工智能4", "决策树的优势是？", "可解释性强"},
		{"人工智能5", "聚类算法属于哪种学习？", "无监督学习"},
		{"Web开发1", "HTML的作用是？", "网页结构"},
		{"Web开发2", "CSS的作用是？", "网页样式"},
		{"Web开发3", "JavaScript的作用是？", "网页交互"},
		{"Web开发4", "HTTP请求方法有哪些？", "GET、POST、PUT、DELETE等"},
		{"Web开发5", "RESTful API的特点是？", "无状态、统一接口"},
		{"移动开发1", "Android开发主要使用什么语言？", "Java/Kotlin"},
		{"移动开发2", "iOS开发主要使用什么语言？", "Swift/Objective-C"},
		{"移动开发3", "跨平台开发的优势是？", "一次开发多平台运行"},
		{"移动开发4", "响应式设计的目的是？", "适配不同屏幕尺寸"},
		{"移动开发5", "移动应用的生命周期包括？", "创建、启动、暂停、销毁"},
		{"云计算1", "云计算的服务模式有哪些？", "IaaS、PaaS、SaaS"},
		{"云计算2", "虚拟化技术的优势是？", "资源利用率高"},
		{"云计算3", "容器技术的特点是？", "轻量级、可移植"},
		{"云计算4", "微服务架构的优势是？", "松耦合、独立部署"},
		{"云计算5", "负载均衡的作用是？", "分散请求压力"},
		{"大数据1", "大数据的特征是？", "4V：Volume、Velocity、Variety、Value"},
		{"大数据2", "Hadoop的核心组件是？", "HDFS和MapReduce"},
		{"大数据3", "NoSQL数据库的特点是？", "非关系型、高扩展性"},
		{"大数据4", "数据挖掘的目的是？", "发现数据中的模式"},
		{"大数据5", "流处理的特点是？", "实时处理数据流"},
		{"区块链1", "区块链的特点是？", "去中心化、不可篡改"},
		{"区块链2", "比特币使用的共识机制是？", "工作量证明"},
		{"区块链3", "智能合约的作用是？", "自动执行合约条款"},
		{"区块链4", "哈希函数的特性是？", "单向性、雪崩效应"},
		{"区块链5", "默克尔树的作用是？", "验证数据完整性"},
		{"项目管理1", "项目管理的三要素是？", "时间、成本、质量"},
		{"项目管理2", "敏捷开发的迭代周期通常是？", "1-4周"},
		{"项目管理3", "风险管理的步骤是？", "识别、分析、应对、监控"},
		{"项目管理4", "需求分析的重要性是？", "确定项目目标和范围"},
		{"项目管理5", "团队协作的关键是？", "沟通和协调"},
		{"数学基础1", "线性代数在计算机中的应用是？", "图形变换、机器学习"},
		{"数学基础2", "概率论在AI中的作用是？", "不确定性建模"},
		{"数学基础3", "离散数学的重要性是？", "计算机科学的数学基础"},
		{"数学基础4", "微积分在优化中的应用是？", "求解最值问题"},
		{"数学基础5", "统计学在数据分析中的作用是？", "数据描述和推断"},
		{"系统设计1", "高可用系统的设计原则是？", "冗余、故障转移"},
		{"系统设计2", "缓存的作用是？", "提高系统性能"},
		{"系统设计3", "分布式系统的挑战是？", "一致性、可用性、分区容错"},
		{"系统设计4", "API设计的原则是？", "简洁、一致、可扩展"},
		{"系统设计5", "监控系统的重要性是？", "及时发现和解决问题"},
		{"技术趋势1", "物联网的核心技术是？", "传感器、通信、数据处理"},
		{"技术趋势2", "边缘计算的优势是？", "低延迟、减少带宽"},
		{"技术趋势3", "5G技术的特点是？", "高速率、低延迟、大连接"},
		{"技术趋势4", "量子计算的潜力是？", "指数级计算能力提升"},
		{"技术趋势5", "AR/VR技术的应用领域是？", "教育、娱乐、医疗"},
		{"职业发展1", "程序员的核心技能是？", "逻辑思维、学习能力"},
		{"职业发展2", "技术选型的考虑因素是？", "性能、成本、维护性"},
		{"职业发展3", "代码质量的重要性是？", "可读性、可维护性"},
		{"职业发展4", "持续学习的必要性是？", "技术快速发展"},
		{"职业发展5", "团队合作的价值是？", "提高效率、知识共享"},
	}

	for i, q := range singleChoiceQuestions {
		qs.CreateQuestionDraft(model.ReqQuestionUpdate{
			Questions: model.Questions{
				Title:        q.title,
				QuestionType: model.QuestionTypeJudge,
				Content:      q.content,
				Answer:       q.answer,
				Score:        5,
				Minutes:      2,
				Options:      "",
				// [{"id":1,"content":"111","is_correct":false}
			},
			CoursewareIDs: []int64{1},
			ChapterIDs:    []int64{1},
		}, 2)
		t.Logf("创建单选题 %d: %s", i+1, q.title)
	}

}

// TestCleanupGeneratedQuestions 清理 TestGenerateQuestions 生成的所有测试数据
func TestCleanupGeneratedQuestions(t *testing.T) {
	pgdb, err := test.InitPostgreSQL()
	if err != nil {
		t.Fatalf("Failed to connect to database: %v", err)
	}
	db.DB = pgdb

	// 开启事务
	tx := db.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 删除测试生成的问题记录
	// 根据用户ID=2和状态=draft来识别测试数据
	var questions []model.Questions
	if err := tx.Where("user_id = ? AND status = ?", 2, model.QuestionStatusDraft).Find(&questions).Error; err != nil {
		tx.Rollback()
		t.Fatalf("查询测试问题失败: %v", err)
	}

	if len(questions) == 0 {
		t.Log("没有找到需要清理的测试数据")
		tx.Commit()
		return
	}

	// 收集问题ID
	var questionIDs []int64
	for _, q := range questions {
		questionIDs = append(questionIDs, q.ID)
	}

	// 删除问题扩展关联记录
	if err := tx.Where("question_id IN (?)", questionIDs).Delete(&model.QuestionsExt{}).Error; err != nil {
		tx.Rollback()
		t.Fatalf("删除问题扩展关联失败: %v", err)
	}

	// 删除问题课件关联记录
	if err := tx.Where("question_id IN (?)", questionIDs).Delete(&model.QuestionCourseware{}).Error; err != nil {
		tx.Rollback()
		t.Fatalf("删除问题课件关联失败: %v", err)
	}

	// 删除问题记录
	if err := tx.Where("id IN (?)", questionIDs).Delete(&model.Questions{}).Error; err != nil {
		tx.Rollback()
		t.Fatalf("删除问题记录失败: %v", err)
	}

	// 提交事务
	tx.Commit()

	t.Logf("成功清理了 %d 条测试问题记录", len(questions))

	// 按类型统计删除的记录
	singleCount := 0
	analyzeCount := 0
	judgeCount := 0
	for _, q := range questions {
		switch q.QuestionType {
		case model.QuestionTypeSingle:
			singleCount++
		case model.QuestionTypeAnalyze:
			analyzeCount++
		case model.QuestionTypeJudge:
			judgeCount++
		}
	}

	t.Logf("删除统计 - 单选题: %d, 分析题: %d, 判断题: %d", singleCount, analyzeCount, judgeCount)
}
