package question

import (
	"testing"
	"tms/model"
	"tms/pkg/db"
	"tms/services/tech"
	"tms/test"
)

func TestGenerateQuestions(t *testing.T) {
	pgdb, err := test.InitPostgreSQL()
	if err != nil {
		t.Fatalf("Failed to connect to database: %v", err)
	}
	db.DB = pgdb

	qs := tech.NewQuestionService()

	// 生成100道单选题
	singleChoiceQuestions := []struct {
		title   string
		content string
		answer  string
	}{
		{"计算机基础知识1", "计算机的中央处理器CPU主要由什么组成？", "运算器和控制器"},
		{"计算机基础知识2", "下列哪个不是操作系统的主要功能？", "编译程序"},
		{"计算机基础知识3", "在计算机中，1KB等于多少字节？", "1024"},
		{"计算机基础知识4", "TCP/IP协议属于哪一层？", "传输层"},
		{"计算机基础知识5", "下列哪个是面向对象编程语言？", "Java"},
		{"数据结构1", "栈的特点是什么？", "后进先出"},
		{"数据结构2", "队列的特点是什么？", "先进先出"},
		{"数据结构3", "二叉树的遍历方式有几种？", "三种"},
		{"数据结构4", "哈希表的平均查找时间复杂度是？", "O(1)"},
		{"数据结构5", "链表相比数组的优势是什么？", "插入删除效率高"},
		{"算法基础1", "冒泡排序的时间复杂度是？", "O(n²)"},
		{"算法基础2", "快速排序的平均时间复杂度是？", "O(nlogn)"},
		{"算法基础3", "二分查找的前提条件是？", "数组有序"},
		{"算法基础4", "递归算法必须具备什么条件？", "终止条件"},
		{"算法基础5", "动态规划的核心思想是？", "最优子结构"},
		{"数据库1", "SQL中用于查询的关键字是？", "SELECT"},
		{"数据库2", "主键的作用是什么？", "唯一标识记录"},
		{"数据库3", "外键用于建立什么关系？", "表间关联"},
		{"数据库4", "事务的ACID特性中A代表什么？", "原子性"},
		{"数据库5", "索引的主要作用是？", "提高查询速度"},
		{"网络基础1", "HTTP协议默认端口号是？", "80"},
		{"网络基础2", "HTTPS协议默认端口号是？", "443"},
		{"网络基础3", "DNS的作用是什么？", "域名解析"},
		{"网络基础4", "IP地址分为几类？", "五类"},
		{"网络基础5", "子网掩码的作用是？", "划分网络"},
		{"编程语言1", "Java是什么类型的语言？", "面向对象"},
		{"编程语言2", "Python的特点是什么？", "简洁易读"},
		{"编程语言3", "C语言属于什么类型？", "过程式语言"},
		{"编程语言4", "JavaScript主要用于？", "网页开发"},
		{"编程语言5", "SQL是什么语言？", "结构化查询语言"},
		{"软件工程1", "软件生命周期包括哪些阶段？", "需求分析、设计、编码、测试、维护"},
		{"软件工程2", "瀑布模型的特点是？", "线性顺序"},
		{"软件工程3", "敏捷开发的核心理念是？", "快速迭代"},
		{"软件工程4", "单元测试的目的是？", "测试最小功能单元"},
		{"软件工程5", "版本控制的作用是？", "管理代码变更"},
		{"操作系统1", "进程和线程的区别是？", "进程是资源分配单位，线程是调度单位"},
		{"操作系统2", "死锁产生的必要条件有几个？", "四个"},
		{"操作系统3", "虚拟内存的作用是？", "扩大内存空间"},
		{"操作系统4", "文件系统的作用是？", "管理存储设备"},
		{"操作系统5", "中断的作用是？", "处理异步事件"},
		{"计算机组成1", "冯·诺依曼结构的特点是？", "程序和数据存储在同一存储器"},
		{"计算机组成2", "Cache的作用是？", "提高CPU访问速度"},
		{"计算机组成3", "总线的作用是？", "连接各部件"},
		{"计算机组成4", "指令周期包括哪些阶段？", "取指、译码、执行"},
		{"计算机组成5", "存储器的层次结构是？", "寄存器、Cache、内存、外存"},
		{"信息安全1", "对称加密的特点是？", "加密解密使用同一密钥"},
		{"信息安全2", "非对称加密的优势是？", "密钥分发安全"},
		{"信息安全3", "数字签名的作用是？", "身份认证和完整性验证"},
		{"信息安全4", "防火墙的作用是？", "网络访问控制"},
		{"信息安全5", "病毒的特征是？", "传染性、破坏性、隐蔽性"},
		{"人工智能1", "机器学习的三种类型是？", "监督学习、无监督学习、强化学习"},
		{"人工智能2", "神经网络的基本单元是？", "神经元"},
		{"人工智能3", "深度学习的特点是？", "多层神经网络"},
		{"人工智能4", "决策树的优势是？", "可解释性强"},
		{"人工智能5", "聚类算法属于哪种学习？", "无监督学习"},
		{"Web开发1", "HTML的作用是？", "网页结构"},
		{"Web开发2", "CSS的作用是？", "网页样式"},
		{"Web开发3", "JavaScript的作用是？", "网页交互"},
		{"Web开发4", "HTTP请求方法有哪些？", "GET、POST、PUT、DELETE等"},
		{"Web开发5", "RESTful API的特点是？", "无状态、统一接口"},
		{"移动开发1", "Android开发主要使用什么语言？", "Java/Kotlin"},
		{"移动开发2", "iOS开发主要使用什么语言？", "Swift/Objective-C"},
		{"移动开发3", "跨平台开发的优势是？", "一次开发多平台运行"},
		{"移动开发4", "响应式设计的目的是？", "适配不同屏幕尺寸"},
		{"移动开发5", "移动应用的生命周期包括？", "创建、启动、暂停、销毁"},
		{"云计算1", "云计算的服务模式有哪些？", "IaaS、PaaS、SaaS"},
		{"云计算2", "虚拟化技术的优势是？", "资源利用率高"},
		{"云计算3", "容器技术的特点是？", "轻量级、可移植"},
		{"云计算4", "微服务架构的优势是？", "松耦合、独立部署"},
		{"云计算5", "负载均衡的作用是？", "分散请求压力"},
		{"大数据1", "大数据的特征是？", "4V：Volume、Velocity、Variety、Value"},
		{"大数据2", "Hadoop的核心组件是？", "HDFS和MapReduce"},
		{"大数据3", "NoSQL数据库的特点是？", "非关系型、高扩展性"},
		{"大数据4", "数据挖掘的目的是？", "发现数据中的模式"},
		{"大数据5", "流处理的特点是？", "实时处理数据流"},
		{"区块链1", "区块链的特点是？", "去中心化、不可篡改"},
		{"区块链2", "比特币使用的共识机制是？", "工作量证明"},
		{"区块链3", "智能合约的作用是？", "自动执行合约条款"},
		{"区块链4", "哈希函数的特性是？", "单向性、雪崩效应"},
		{"区块链5", "默克尔树的作用是？", "验证数据完整性"},
		{"项目管理1", "项目管理的三要素是？", "时间、成本、质量"},
		{"项目管理2", "敏捷开发的迭代周期通常是？", "1-4周"},
		{"项目管理3", "风险管理的步骤是？", "识别、分析、应对、监控"},
		{"项目管理4", "需求分析的重要性是？", "确定项目目标和范围"},
		{"项目管理5", "团队协作的关键是？", "沟通和协调"},
		{"数学基础1", "线性代数在计算机中的应用是？", "图形变换、机器学习"},
		{"数学基础2", "概率论在AI中的作用是？", "不确定性建模"},
		{"数学基础3", "离散数学的重要性是？", "计算机科学的数学基础"},
		{"数学基础4", "微积分在优化中的应用是？", "求解最值问题"},
		{"数学基础5", "统计学在数据分析中的作用是？", "数据描述和推断"},
		{"系统设计1", "高可用系统的设计原则是？", "冗余、故障转移"},
		{"系统设计2", "缓存的作用是？", "提高系统性能"},
		{"系统设计3", "分布式系统的挑战是？", "一致性、可用性、分区容错"},
		{"系统设计4", "API设计的原则是？", "简洁、一致、可扩展"},
		{"系统设计5", "监控系统的重要性是？", "及时发现和解决问题"},
		{"技术趋势1", "物联网的核心技术是？", "传感器、通信、数据处理"},
		{"技术趋势2", "边缘计算的优势是？", "低延迟、减少带宽"},
		{"技术趋势3", "5G技术的特点是？", "高速率、低延迟、大连接"},
		{"技术趋势4", "量子计算的潜力是？", "指数级计算能力提升"},
		{"技术趋势5", "AR/VR技术的应用领域是？", "教育、娱乐、医疗"},
		{"职业发展1", "程序员的核心技能是？", "逻辑思维、学习能力"},
		{"职业发展2", "技术选型的考虑因素是？", "性能、成本、维护性"},
		{"职业发展3", "代码质量的重要性是？", "可读性、可维护性"},
		{"职业发展4", "持续学习的必要性是？", "技术快速发展"},
		{"职业发展5", "团队合作的价值是？", "提高效率、知识共享"},
	}

	for i, q := range singleChoiceQuestions {
		qs.CreateQuestionDraft(model.ReqQuestionUpdate{
			Questions: model.Questions{
				Title:        q.title,
				QuestionType: model.QuestionTypeSingle,
				Content:      q.content,
				Answer:       q.answer,
				Score:        5,
				Minutes:      2,
				Options:      "",
				// [{"id":1,"content":"111","is_correct":false}
			},
			CoursewareIDs: []int64{1},
			ChapterIDs:    []int64{1},
		}, 2)
		t.Logf("创建单选题 %d: %s", i+1, q.title)
	}

	// 生成100道分析题
	analysisQuestions := []struct {
		title   string
		content string
		answer  string
	}{
		{"系统架构分析1", "请分析微服务架构相比单体架构的优势和挑战，并说明在什么情况下应该选择微服务架构。", "优势：独立部署、技术栈灵活、故障隔离、团队独立开发。挑战：分布式复杂性、数据一致性、网络延迟、运维复杂。适用场景：大型复杂系统、多团队协作、高可用要求。"},
		{"系统架构分析2", "分析分布式系统中CAP定理的含义，并举例说明在实际项目中如何权衡这三个特性。", "CAP定理：一致性(Consistency)、可用性(Availability)、分区容错性(Partition tolerance)。分布式系统最多只能同时保证其中两个。实际应用中需要根据业务需求权衡，如银行系统优先保证一致性，社交媒体优先保证可用性。"},
		{"数据库设计分析1", "请分析关系型数据库和NoSQL数据库的适用场景，并说明如何选择合适的数据库类型。", "关系型数据库适用于：事务要求高、数据结构稳定、复杂查询多的场景。NoSQL适用于：大数据量、高并发、数据结构灵活、水平扩展需求的场景。选择依据：数据特性、性能要求、一致性需求、扩展性要求。"},
		{"数据库设计分析2", "分析数据库索引的工作原理，并说明如何优化数据库查询性能。", "索引原理：通过B+树等数据结构加速数据查找。优化策略：合理创建索引、避免全表扫描、优化SQL语句、分区分表、读写分离、缓存机制。需要平衡查询性能和写入性能。"},
		{"算法复杂度分析1", "请分析快速排序算法的时间复杂度，并说明在什么情况下性能最好和最差。", "平均时间复杂度O(nlogn)，最好情况O(nlogn)，最坏情况O(n²)。最好情况：每次分区都能平均分割。最坏情况：每次分区都极不平衡，如已排序数组。优化方法：随机选择基准元素、三数取中法。"},
		{"算法复杂度分析2", "分析动态规划算法的设计思想，并举例说明如何识别和解决动态规划问题。", "设计思想：将复杂问题分解为子问题，利用最优子结构和重叠子问题特性。识别特征：最优化问题、子问题重叠、最优子结构。解决步骤：定义状态、状态转移方程、边界条件、计算顺序。经典例子：斐波那契数列、背包问题。"},
		{"网络安全分析1", "请分析常见的网络攻击类型，并说明相应的防护措施。", "常见攻击：SQL注入、XSS攻击、CSRF攻击、DDoS攻击、中间人攻击。防护措施：输入验证、参数化查询、HTTPS加密、防火墙、入侵检测系统、安全审计、员工安全培训。"},
		{"网络安全分析2", "分析HTTPS协议的工作原理，并说明它如何保证数据传输的安全性。", "工作原理：SSL/TLS握手建立安全连接，使用非对称加密交换密钥，使用对称加密传输数据。安全保证：身份认证（数字证书）、数据加密（防窃听）、完整性验证（防篡改）、防重放攻击。"},
		{"软件工程分析1", "请分析敏捷开发和瀑布模型的区别，并说明在什么项目中应该选择敏捷开发。", "瀑布模型：线性顺序、文档驱动、变更困难。敏捷开发：迭代增量、客户协作、拥抱变化。敏捷适用场景：需求不明确、变化频繁、快速交付、小团队、创新项目。瀑布适用：需求稳定、规模大、合规要求高。"},
		{"软件工程分析2", "分析软件测试的重要性，并说明如何建立有效的测试策略。", "重要性：保证软件质量、降低维护成本、提高用户满意度、减少风险。测试策略：测试金字塔（单元测试、集成测试、端到端测试）、自动化测试、持续集成、测试驱动开发、风险驱动测试。"},
		{"性能优化分析1", "请分析Web应用性能优化的策略，并说明如何识别和解决性能瓶颈。", "优化策略：前端优化（压缩、缓存、CDN）、后端优化（数据库优化、缓存、负载均衡）、网络优化。识别瓶颈：性能监控、压力测试、代码分析。解决方法：针对性优化、分层优化、持续监控。"},
		{"性能优化分析2", "分析缓存技术在系统性能优化中的作用，并说明不同层次缓存的应用场景。", "作用：减少数据访问时间、降低系统负载、提高响应速度。缓存层次：浏览器缓存、CDN缓存、反向代理缓存、应用缓存、数据库缓存。应用场景：静态资源缓存、热点数据缓存、计算结果缓存。"},
		{"云计算架构分析1", "请分析云原生架构的特点，并说明容器化技术在云原生中的作用。", "云原生特点：微服务、容器化、动态编排、DevOps。容器化作用：环境一致性、资源隔离、快速部署、弹性扩展。技术栈：Docker、Kubernetes、服务网格、CI/CD。优势：提高开发效率、降低运维成本、增强系统可靠性。"},
		{"云计算架构分析2", "分析无服务器架构（Serverless）的优势和局限性，并说明适用场景。", "优势：按需付费、自动扩展、无需运维、快速开发。局限性：冷启动延迟、执行时间限制、厂商锁定、调试困难。适用场景：事件驱动、间歇性工作负载、快速原型、数据处理。"},
		{"大数据分析1", "请分析大数据处理的技术架构，并说明批处理和流处理的区别和应用场景。", "技术架构：数据采集、存储、处理、分析、可视化。批处理：离线处理大量数据，延迟高但吞吐量大，适用于历史数据分析。流处理：实时处理数据流，延迟低，适用于实时监控、推荐系统。技术选择需要根据业务需求权衡。"},
		{"大数据分析2", "分析数据仓库和数据湖的区别，并说明在什么情况下选择哪种架构。", "数据仓库：结构化数据、预定义模式、高质量、适合BI分析。数据湖：多种数据类型、灵活存储、原始数据、适合探索性分析。选择依据：数据类型、分析需求、成本考虑、技术能力。混合架构（湖仓一体）成为趋势。"},
		{"人工智能分析1", "请分析机器学习项目的完整流程，并说明每个阶段的关键任务。", "流程：问题定义→数据收集→数据预处理→特征工程→模型选择→训练验证→模型评估→部署监控。关键任务：明确业务目标、保证数据质量、选择合适算法、避免过拟合、持续优化。成功关键：数据质量、特征工程、模型选择。"},
		{"人工智能分析2", "分析深度学习和传统机器学习的区别，并说明在什么情况下选择深度学习。", "区别：特征提取（自动vs手工）、数据需求（大vs小）、计算资源（高vs低）、可解释性（低vs高）。深度学习适用：大数据量、复杂模式、图像语音处理、端到端学习。传统ML适用：小数据、可解释性要求、资源受限。"},
		{"移动开发分析1", "请分析原生开发和跨平台开发的优缺点，并说明如何选择合适的开发方案。", "原生开发：性能好、用户体验佳、功能完整，但开发成本高。跨平台：开发效率高、维护成本低，但性能略差、平台特性受限。选择依据：项目预算、时间要求、性能需求、团队技能、长期维护。"},
		{"移动开发分析2", "分析移动应用的性能优化策略，并说明如何提升用户体验。", "性能优化：启动优化、内存管理、网络优化、UI渲染优化、电池优化。用户体验：响应速度、界面流畅、离线功能、错误处理、个性化。优化方法：代码优化、资源优化、架构优化、测试优化。"},
		{"区块链技术分析1", "请分析区块链技术的核心特性，并说明其在不同行业的应用前景。", "核心特性：去中心化、不可篡改、透明性、共识机制。应用前景：金融（数字货币、支付）、供应链（溯源）、医疗（病历管理）、政务（证书认证）、版权保护。挑战：性能瓶颈、能耗问题、监管政策。"},
		{"区块链技术分析2", "分析智能合约的工作原理，并说明其优势和潜在风险。", "工作原理：代码即合约、自动执行、区块链存储。优势：自动化执行、降低成本、提高效率、减少纠纷。风险：代码漏洞、不可修改、法律地位、隐私问题。应用场景：去中心化金融、数字资产、自动化协议。"},
		{"项目管理分析1", "请分析敏捷项目管理中Scrum框架的核心要素，并说明如何有效实施。", "核心要素：产品负责人、Scrum Master、开发团队、Sprint、产品待办列表、Sprint待办列表、增量。实施要点：明确角色职责、制定清晰目标、定期回顾改进、保持透明沟通、持续交付价值。成功关键：团队协作、客户参与、适应变化。"},
		{"项目管理分析2", "分析软件项目风险管理的重要性，并说明如何建立有效的风险管理体系。", "重要性：预防项目失败、控制成本、保证质量、提高成功率。风险管理体系：风险识别、风险分析、风险评估、风险应对、风险监控。关键活动：定期风险评估、制定应对策略、建立预警机制、持续监控调整。"},
		{"用户体验分析1", "请分析用户体验设计的核心原则，并说明如何评估和改进用户体验。", "核心原则：以用户为中心、简洁易用、一致性、可访问性、反馈及时。评估方法：用户调研、可用性测试、数据分析、A/B测试。改进策略：迭代设计、用户反馈、数据驱动、跨团队协作。目标：提高用户满意度和产品价值。"},
		{"用户体验分析2", "分析移动应用界面设计的特点，并说明如何适配不同设备和场景。", "设计特点：触摸交互、屏幕限制、情境化使用、性能要求。适配策略：响应式设计、渐进式增强、设备特性优化、场景化设计。考虑因素：屏幕尺寸、操作方式、网络环境、使用场景。设计原则：简洁直观、操作便捷、性能优先。"},
		{"数据科学分析1", "请分析数据科学项目的生命周期，并说明每个阶段的关键成功因素。", "生命周期：业务理解→数据理解→数据准备→建模→评估→部署。成功因素：明确业务目标、保证数据质量、选择合适方法、严格验证评估、有效部署监控。挑战：数据质量、模型解释性、业务价值转化。"},
		{"数据科学分析2", "分析特征工程在机器学习中的重要性，并说明如何进行有效的特征工程。", "重要性：决定模型性能上限、影响学习效果、体现领域知识。特征工程方法：特征选择、特征变换、特征构造、特征组合。技术手段：统计分析、相关性分析、主成分分析、领域知识应用。评估标准：预测能力、稳定性、可解释性。"},
		{"系统监控分析1", "请分析分布式系统监控的挑战，并说明如何建立有效的监控体系。", "监控挑战：系统复杂性、数据量大、故障定位难、性能瓶颈识别。监控体系：指标监控、日志监控、链路追踪、告警机制。关键指标：可用性、响应时间、吞吐量、错误率。实施要点：全链路监控、实时告警、可视化展示、自动化处理。"},
		{"系统监控分析2", "分析APM（应用性能监控）的作用，并说明如何选择和实施APM解决方案。", "APM作用：性能监控、故障诊断、用户体验监控、容量规划。功能要求：代码级监控、数据库监控、外部服务监控、用户体验监控。选择标准：功能完整性、易用性、扩展性、成本效益。实施策略：分阶段部署、关键指标优先、团队培训。"},
		{"DevOps实践分析1", "请分析DevOps文化的核心理念，并说明如何在组织中推行DevOps实践。", "核心理念：协作文化、自动化、持续改进、快速反馈。推行策略：文化变革、工具链建设、流程优化、技能培训。实践要点：CI/CD、基础设施即代码、监控告警、快速恢复。成功关键：管理层支持、团队协作、持续学习。"},
		{"DevOps实践分析2", "分析持续集成/持续部署(CI/CD)的价值，并说明如何建立高效的CI/CD流水线。", "CI/CD价值：提高交付速度、保证代码质量、降低发布风险、增强团队协作。流水线设计：代码提交→自动构建→自动测试→自动部署→监控反馈。关键要素：版本控制、自动化测试、部署自动化、环境管理、回滚机制。"},
		{"技术架构演进分析1", "请分析单体架构向微服务架构演进的过程，并说明演进策略和注意事项。", "演进动机：系统复杂性增加、团队规模扩大、技术栈多样化需求。演进策略：绞杀者模式、数据库分离、服务拆分、API网关。注意事项：服务边界划分、数据一致性、分布式事务、运维复杂性。成功关键：渐进式演进、充分测试、团队准备。"},
		{"技术架构演进分析2", "分析云原生架构的演进趋势，并说明传统应用如何向云原生转型。", "演进趋势：容器化、微服务化、服务网格、无服务器。转型策略：应用现代化、容器化改造、微服务拆分、云原生工具链。转型挑战：技术复杂性、团队技能、成本控制、风险管理。成功要素：分阶段实施、技能培训、工具选择。"},
		{"信息系统安全分析1", "请分析企业信息系统的安全威胁，并说明如何建立多层次的安全防护体系。", "安全威胁：外部攻击、内部威胁、系统漏洞、数据泄露。防护体系：网络安全、主机安全、应用安全、数据安全。防护措施：访问控制、加密技术、安全审计、应急响应。管理要素：安全策略、人员培训、合规管理、持续改进。"},
		{"信息系统安全分析2", "分析零信任安全模型的核心思想，并说明如何在企业中实施零信任架构。", "核心思想：永不信任、始终验证、最小权限、持续监控。实施要点：身份认证、设备管理、网络分段、数据保护。技术组件：身份管理、访问控制、网络安全、数据加密。实施策略：分阶段部署、风险评估、持续优化、文化转变。"},
		{"软件质量保证分析1", "请分析软件质量保证的重要性，并说明如何建立全面的质量保证体系。", "重要性：用户满意度、成本控制、风险降低、竞争优势。质量保证体系：质量计划、质量控制、质量改进、质量管理。关键活动：需求评审、设计评审、代码评审、测试管理。成功要素：全员参与、过程改进、工具支持、度量分析。"},
		{"软件质量保证分析2", "分析代码质量管理的方法，并说明如何提高代码的可维护性和可读性。", "质量管理方法：代码规范、代码评审、静态分析、重构优化。提高可维护性：模块化设计、低耦合高内聚、清晰命名、充分注释。提高可读性：统一风格、简洁逻辑、合理结构、文档完善。工具支持：代码检查工具、版本控制、持续集成。"},
		{"技术团队管理分析1", "请分析技术团队管理的挑战，并说明如何建设高效的技术团队。", "管理挑战：技术复杂性、人才流动、项目压力、沟通协调。团队建设：人才招聘、技能培养、文化建设、激励机制。管理要点：目标明确、职责清晰、沟通顺畅、持续改进。成功要素：领导力、团队协作、学习氛围、创新文化。"},
		{"技术团队管理分析2", "分析敏捷团队的特征，并说明如何培养和维持高绩效的敏捷团队。", "团队特征：自组织、跨功能、持续改进、客户导向。培养策略：角色明确、技能互补、协作文化、持续学习。维持方法：定期回顾、及时反馈、激励机制、环境支持。成功关键：信任文化、开放沟通、共同目标、持续改进。"},
		{"数字化转型分析1", "请分析企业数字化转型的驱动因素，并说明数字化转型的关键成功要素。", "驱动因素：市场竞争、客户需求、技术发展、效率提升。成功要素：战略规划、技术架构、组织变革、文化转型。实施要点：顶层设计、分步实施、数据驱动、持续优化。挑战应对：变革阻力、技术复杂性、人才短缺、投资回报。"},
		{"数字化转型分析2", "分析数据驱动决策在数字化转型中的作用，并说明如何建立数据驱动的组织文化。", "数据驱动作用：提高决策质量、优化业务流程、发现商业机会、降低运营风险。文化建设：数据意识培养、分析能力提升、工具平台建设、制度流程完善。实施策略：从上而下推动、数据素养培训、成功案例分享、激励机制建立。"},
		{"创新技术应用分析1", "请分析人工智能技术在传统行业中的应用前景，并说明实施AI项目的关键考虑因素。", "应用前景：制造业智能化、金融风控、医疗诊断、教育个性化、零售推荐。关键考虑：业务价值、数据质量、技术可行性、成本效益、伦理风险。实施要点：场景选择、数据准备、模型开发、系统集成、效果评估。成功要素：明确目标、数据驱动、迭代优化。"},
		{"创新技术应用分析2", "分析物联网技术的发展趋势，并说明IoT系统设计的关键技术挑战。", "发展趋势：设备智能化、边缘计算、5G应用、工业互联网。技术挑战：设备连接、数据处理、安全防护、能耗管理。系统设计：感知层、网络层、平台层、应用层。关键技术：传感器技术、通信协议、数据分析、云边协同。应用场景：智慧城市、工业4.0、智能家居。"},
	}

	for i, q := range analysisQuestions {
		qs.CreateQuestionDraft(model.ReqQuestionUpdate{
			Questions: model.Questions{
				Title:        q.title,
				QuestionType: model.QuestionTypeAnalyze,
				Content:      q.content,
				Answer:       q.answer,
				Score:        15,
				Minutes:      10,
				Options:      "",
			},
			CoursewareIDs: []int64{1},
			ChapterIDs:    []int64{1},
		}, 2)
		t.Logf("创建分析题 %d: %s", i+1, q.title)
	}
}
