package utils

func UniqueInt64Slice(slice []int64) []int64 {
	seen := make(map[int64]bool)
	result := make([]int64, 0)

	for _, v := range slice {
		if !seen[v] {
			seen[v] = true
			result = append(result, v)
		}
	}
	return result
}
func HasContainsStrings(s []string, e string) bool {
	for _, a := range s {
		if a == e {
			return true
		}
	}
	return false
}
func HasContainsInts(slice []int, item int) bool {
	for _, v := range slice {
		if v == item {
			return true
		}
	}
	return false
}
