package utils

import (
	"fmt"
	"strconv"
	"strings"
)

// IncrementVersion 递增版本号，格式如 v1.0 -> v1.1
func IncrementVersion(version string) (string, error) {
	// 检查前缀是否为 v
	if !strings.HasPrefix(version, "v") {
		return "", fmt.<PERSON><PERSON><PERSON>("版本号格式错误：必须以 'v' 开头")
	}

	// 去掉前缀 v
	numStr := version[1:]

	// 按 . 分割
	parts := strings.Split(numStr, ".")
	if len(parts) != 2 {
		return "", fmt.<PERSON><PERSON><PERSON>("版本号格式错误：必须是类似 v1.0 的格式")
	}

	major, err := strconv.Atoi(parts[0])
	if err != nil {
		return "", fmt.<PERSON><PERSON><PERSON>("主版本号解析失败: %v", err)
	}

	minor, err := strconv.Atoi(parts[1])
	if err != nil {
		return "", fmt.<PERSON><PERSON><PERSON>("次版本号解析失败: %v", err)
	}

	// 次版本号递增
	minor++

	// 返回新版本号
	return fmt.Sprintf("v%d.%d", major, minor), nil
}
