package utils

import (
	"errors"
	"fmt"
	"log/slog"
	"os"
	"path/filepath"
	"strings"
	"tms/pkg/config"

	"github.com/spf13/cast"
)

// DownloadAndUnzipCourseware 下载并解压课件
// coursewareID: 课件ID
// chapterID: 章节ID
// filePath: 课件文件路径
// updateFunc: 可选的更新函数，用于更新解压路径到数据库
func DownloadAndUnzipCourseware(coursewareID, chapterID int64, filePath string, updateFunc func(coursewareID int64, unzipPath string) error) (string, error) {
	downloadDir := filepath.Join("data", "downloads", "coursewares", cast.ToString(chapterID))
	distFolder := filepath.Join("data", "scorm", cast.ToString(chapterID), cast.ToString(coursewareID))

	// 构建远程文件路径
	remote := config.Config.System.Scheme + "://" + config.Config.System.Host + "/"
	remoteFilePath := strings.TrimPrefix(filePath, "./")
	remoteFilePath = remote + remoteFilePath

	// 确定下载文件的保存路径
	downloadFilePath := filepath.Join(downloadDir, filepath.Base(filePath))

	// 下载文件
	slog.Info("开始下载课件", "URL", remoteFilePath, "保存路径", downloadFilePath)
	if err := DownloadFile(remoteFilePath, downloadFilePath); err != nil {
		slog.Error("下载课件失败", "URL", remoteFilePath, "错误", err)
		return "", fmt.Errorf("下载课件失败: %w", err)
	}

	slog.Info("课件下载成功", "保存路径", downloadFilePath)

	// 获取文件信息
	fileInfo, err := os.Stat(downloadFilePath)
	if err != nil {
		slog.Error("获取下载文件信息失败", "文件路径", downloadFilePath, "错误", err)
		return "", fmt.Errorf("获取下载文件信息失败: %w", err)
	}

	slog.Info("下载文件大小", "文件路径", downloadFilePath, "大小(字节)", fileInfo.Size())

	// 解压文件
	unzipPath, err := UnzipScorm(downloadFilePath, distFolder)
	if err != nil {
		slog.Error("解压课件失败", "文件路径", downloadFilePath, "错误", err)
		return "", errors.New("解压失败: " + err.Error())
	}
	slog.Info("课件解压成功", "保存路径", unzipPath)

	// 如果提供了更新函数，则更新数据库
	if updateFunc != nil {
		if err := updateFunc(coursewareID, unzipPath); err != nil {
			return unzipPath, err
		}
	}

	return unzipPath, nil
}
