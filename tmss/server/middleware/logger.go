package middleware

import (
	"bytes"
	"io"
	"log/slog"
	"strings"
	"time"
	"tms/model"
	"tms/pkg/config"
	"tms/pkg/db"

	"github.com/gin-gonic/gin"
)

// LoggerMiddleware 是一个简单的日志中间件
func LoggerMiddleware(handler slog.Handler) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取请求的 Host
		host := c.Request.Host
		// 获取请求的 Scheme
		scheme := "http"
		if c.Request.TLS != nil {
			scheme = "https"
		}
		config.Config.System.Host = host
		config.Config.System.Scheme = scheme
		url := c.Request.URL.Path
		// 检查 URL 是否以 /static/ 开头，如果是则跳过记录
		if IsStaticFile(url) {
			c.Next()
			return
		}

		// if !CheckIsNoAuthRoute(c, url) {
		// 	return
		// }
		startTime := time.Now()
		//log.Printf("请求开始:%s %s", c.Request.Method, c.Request.URL.Path)
		var bodyBytes []byte
		var isFileUpload bool
		if c.Request.Body != nil && c.Request.Method == "POST" {
			contentType := c.Request.Header.Get("Content-Type")
			isFileUpload = strings.Contains(contentType, "multipart/form-data")
			if !isFileUpload {
				bodyBytes, _ = c.GetRawData()
				// 恢复原始请求体
				c.Request.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))
			}
		}
		c.Next() // 执行后续的处理函数
		status := c.Writer.Status()
		if c.Request.Method == "POST" {
			duration := time.Since(startTime).Nanoseconds()
			//log.Printf("请求耗时:%d 纳秒", duration)
			userId := c.GetInt64("userId")
			dataStr := ""
			// 如果不是文件上传，则记录数据
			if !isFileUpload {
				dataStr = string(bodyBytes)
			}

			// 构造日志结构体
			logEntry := &model.RequestLog{
				Method:    c.Request.Method,
				Path:      url,
				UserId:    userId,
				Data:      string(dataStr),
				IP:        c.ClientIP(),
				UserAgent: c.Request.UserAgent(),
				Status:    status,
				Duration:  duration,
				CreatedAt: time.Now(),
			}
			//log.Printf("Request log saved: %+v", logEntry)
			// 异步保存到数据库
			go func() {
				if err := db.DB.Create(logEntry).Error; err != nil {
					slog.New(handler).Error("Failed to save request log", slog.Any("error", err))
				}

			}()
		}

		logger := slog.New(handler)
		// 记录请求的信息
		logger.Info("Request completed",
			slog.String("method", c.Request.Method),
			slog.String("path", url),
			slog.Any("status", status),
			slog.Duration("duration", time.Since(startTime)),
		)
	}
}
