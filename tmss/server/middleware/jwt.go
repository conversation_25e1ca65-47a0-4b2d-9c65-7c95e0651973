package middleware

import (
	"fmt"
	"log"
	"net"
	"net/http"
	"strconv"
	"strings"
	"tms/common"
	"tms/libs"
	"tms/model"
	"tms/pkg/cache"
	"tms/pkg/config"
	"tms/utils"

	"github.com/gin-gonic/gin"
)

// 定义认证类型常量
// const (
// 	AuthNone    = "guest"   // 无需认证
// 	AuthUser    = "user"    // 普通用户认证
// 	AuthTeacher = "teacher" // 教员认证
// 	AuthAdmin   = "admin"   // 管理员认证
// 	AuthAll     = "all"     // 所有认证
// )

// 定义角色检查常量
const (
	RoleAll    = "*"  // 所有角色
	RoleBypass = "-1" // 绕过角色检查
)

func JwtVerify() gin.HandlerFunc {
	return func(c *gin.Context) {
		url := c.Request.URL.Path
		log.Printf("url:%v", url)
		// 检查并跳过静态文件
		if IsStaticFile(url) {
			c.Next()
			return
		}

		routeData, ok := getRouteData(url)
		if !ok {
			handleUnregisteredRoute(c, url)
			return
		}
		if routeData.AuthType == model.AuthNone {
			c.Next()
			return
		}
		token := extractToken(c)
		if token == "" {
			libs.ErrorLogin(c, "token is empty")
			c.Abort()
			return
		}

		user, err := verifyTokenAndSetUser(c, token)
		if err != nil {
			libs.ErrorLogin(c, "token is invalid")
			c.Abort()
			return
		}
		log.Printf("UserCode: %v", user)
		// 跳过session验证
		if IsSyncRoute(url) {
			if user.ID == 0 || user.UserCode == "" {
				libs.ErrorLogin(c, "user is invalid")
				c.Abort()
				return
			}
			c.Next()
			return
		}

		key := fmt.Sprintf("%d-token", user.ID)
		cacheToken, err := cache.CacheStorage.Get(key)
		if err != nil || cacheToken == nil {
			log.Printf("Failed to get token from cache: %v", err)
			libs.ErrorLogin(c, "user cache is invalid")
			c.Abort()
			return
		}
		dataBytes := cache.GetValToByte(cacheToken)
		if string(dataBytes) != token {
			libs.ErrorLogin(c, "token is invalid")
			c.Abort()
			return
		}

		c.Set("userId", user.ID)
		c.Set("userCode", user.UserCode)
		c.Set("roleCodes", user.RoleCodes)
		c.Next()
	}
}

// func CheckIsNoAuthRoute(c *gin.Context, url string) bool {
// 	routeData, ok := getRouteData(url)
// 	if !ok {
// 		handleUnregisteredRoute(c, url)
// 		return false
// 	}
// 	if routeData.AuthType == model.AuthNone {
// 		c.Next()
// 		return false
// 	}
// 	return true
// }

// isStaticFile 检查 URL 是否是静态文件
func IsStaticFile(url string) bool {
	return strings.HasPrefix(url, "/static/") ||
		strings.HasPrefix(url, "/upload/") ||
		strings.HasPrefix(url, "/uploads/") ||
		strings.HasPrefix(url, "/courseware-assets/") ||
		strings.HasPrefix(url, "/resource-assets/")
}

func IsSyncRoute(url string) bool {
	return strings.HasSuffix(url, "/sync/get") || strings.HasSuffix(url, "/sync/post") || strings.HasSuffix(url, "/sync/query")
}

// handleUnregisteredRoute 处理未注册路由
func handleUnregisteredRoute(c *gin.Context, url string) {
	log.Printf("未注册路由:%v", url)
	libs.Error(c, "未注册路由")
	c.Abort()
}

// handleNoAuthRoute 处理无需认证的路由
// func handleNoAuthRoute(c *gin.Context) {
// 	c.Next() // 对于无需认证的路由，直接放行
// }

// extractToken 从请求头或查询参数中提取 token
func extractToken(c *gin.Context) string {
	token := c.GetHeader("Authorization")
	if token == "" {
		token = c.Query("token")
	}
	return token
}

// verifyTokenAndSetUser 验证 token 并将用户信息设置到 context
func verifyTokenAndSetUser(c *gin.Context, token string) (*utils.UserClaims, error) {
	user, err := utils.ParseToken(token)
	if err != nil {
		return nil, err
	}
	c.Set("user", user) // 将解析出的用户信息存储在 context 中
	return user, nil
}

func getRouteData(url string) (common.Route, bool) {
	//log.Printf("common.Routes:%v", common.Routes)
	for _, route := range common.Routes {
		if matchPath(url, route.Path) {
			return route, true
		}
	}
	return common.Route{}, false
}

// matchPath 检查 URL 是否匹配给定的路径模式
func matchPath(url, pattern string) bool {
	//log.Printf("Matching URL: %s vs Pattern: %s", url, pattern)

	parts := strings.Split(pattern, "/")
	urlParts := strings.Split(url, "/")

	if len(parts) != len(urlParts) {
		//log.Printf("Length mismatch: %d vs %d", len(parts), len(urlParts))
		return false
	}

	for i, part := range parts {
		if part == "" {
			continue
		}
		if part[0] == ':' {
			paramName := part[1:]
			if strings.Contains(paramName, "id") {
				if _, err := strconv.Atoi(urlParts[i]); err != nil {
					log.Printf("Param check failed for %s at index %d: %v", paramName, i, err)
					return false
				}
			}
		} else if part != urlParts[i] {
			//log.Printf("Mismatch at index %d: %s vs %s", i, part, urlParts[i])
			return false
		}
	}

	return true
}
func getUserId(userId interface{}) int64 {
	var userIdInt64 int64
	switch v := userId.(type) {
	case int32:
		userIdInt64 = int64(v)
	case int64:
		userIdInt64 = int64(v)
	default: // 终止后续处理
		userIdInt64 = 0
	}
	return userIdInt64
}

// func checkRoles(rules string, url string) bool {
// 	if rules == RoleAll || rules == RoleBypass {
// 		return true
// 	}
// 	if rules == "" {
// 		return false
// 	}
// 	userRoleArray := strings.Split(rules, ",")
// 	hasAuth := false
// 	for _, path := range userRoleArray {
// 		if matchPath(url, path) {
// 			hasAuth = true
// 			break
// 		}
// 	}
// 	return hasAuth
// }

// CheckIp 验证客户端 IP 是否允许访问
func CheckIp(r *http.Request) bool {
	if strings.HasPrefix(r.URL.Scheme, "wails") {
		return true
	}

	clientIP := r.RemoteAddr
	host := r.Host
	ip, _, err := net.SplitHostPort(clientIP)
	if err != nil {
		//fmt.Printf("Error splitting host and port: %v\n", err)
		return false
	}

	// 解析 IP 地址
	ipAddress := net.ParseIP(ip)
	//fmt.Printf("clientIP:%v, host:%v\n", clientIP, host)

	// 允许本机访问
	if ipAddress.IsLoopback() || host == "localhost" {
		return true
	}

	// 获取允许的 IP 和域名列表
	ipAndDomainList := config.Config.System.IpAccess
	//fmt.Printf("ipAndDomainList:%v\n", ipAndDomainList)
	if len(ipAndDomainList) == 0 {
		return true
	}
	// 检查 IP 地址
	for _, allowed := range ipAndDomainList {
		if ipAddress.String() == allowed {
			return true
		}
		// 检查域名
		if host == allowed {
			return true
		}
	}

	return false
}
