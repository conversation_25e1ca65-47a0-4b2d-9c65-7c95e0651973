package middleware

import (
	"tms/pkg/config"
	"tms/pkg/db"

	gormsessions "github.com/gin-contrib/sessions/gorm"
	"github.com/gin-contrib/sessions/memstore"
)

func GetSessionStore() gormsessions.Store {
	switch config.Config.System.SessionType {
	case "mysql":
		return gormsessions.NewStore(db.DB, true, []byte(config.Config.System.SessionSecret))
	// case "cookie":
	// 	// 创建一个 cookie store
	// 	store := cookie.NewStore([]byte(config.Config.System.SessionSecret))

	// 	// 配置 cookie，使用 github.com/gin-contrib/sessions.Options 类型
	// 	store.Options(sessions.Options{
	// 		Path:     "/",
	// 		HttpOnly: true,
	// 		Secure:   false, // 如果使用 HTTPS
	// 		SameSite: http.SameSiteNoneMode,
	// 		MaxAge:   int(24 * time.Hour / time.Second), // MaxAge 单位为秒
	// 	})

	// 	return store
	default:
		return memstore.NewStore([]byte(config.Config.System.SessionSecret))
	}
}
