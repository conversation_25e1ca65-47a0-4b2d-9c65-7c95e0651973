package middleware

import (
	"log"
	"runtime/debug"
	"tms/libs"

	"github.com/gin-gonic/gin"
)

// HandleNotFound 自定义 404 处理器
func HandleNotFound(c *gin.Context) {
	// 重定向到自定义的 404 页面
	c.Redirect(302, "/home/<USER>/")
}

// Recover 自定义 panic 处理器
func Recover(c *gin.Context) {
	defer func() {
		if r := recover(); r != nil {
			// 打印错误堆栈信息
			log.Printf("panic: %v\n", r)
			libs.Error(c, "服务器内部错误")
			stack := debug.Stack()
			log.Printf("Stack trace:\n%s", stack)
			c.Abort()
			return
		}
	}()

	// 加载完 defer recover，继续后续接口调用
	c.Next()
}
