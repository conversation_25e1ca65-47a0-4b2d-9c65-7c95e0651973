package middleware

import (
	"fmt"
	"log"
	"net/http"

	"tms/common"
	"tms/libs"
	"tms/model"
	"tms/pkg/db"
	"tms/services/auth"

	"github.com/casbin/casbin/v2"
	gormadapter "github.com/casbin/gorm-adapter/v3"
	"github.com/gin-gonic/gin"
)

var GlobalEnforcer *casbin.Enforcer

func InitCasbin() error {
	adapter, err := gormadapter.NewAdapterByDB(db.DB)
	if err != nil {
		log.Fatalf("Failed to create GORM adapter: %v", err)
		return err
	}
	enforcer, err := casbin.NewEnforcer("config/rbac_model.conf", adapter)
	if err != nil {
		log.Fatalf("Failed to create Casbin enforcer: %v", err)
		return err
	}

	for _, route := range common.Routes {
		if route.AuthType == model.AuthNone {
			continue
		}
		_, err = enforcer.AddPolicy(route.AuthType, route.Path, route.Method)
		if err != nil {
			log.Printf("Failed to add policy for route %s %s: %v", route.Method, route.Path, err)
			return err
		}
	}
	customRoles, err := model.GetCustomRolePaths(db.DB)
	if err != nil {
		log.Printf("Failed to get custom roles: %v", err)
		return nil
	}
	for roleCode, paths := range customRoles {
		for _, path := range paths {
			_, err = enforcer.AddPolicy(roleCode, path.Path, path.Method)
			if err != nil {
				log.Printf("Failed to add policy for route %s %s: %v", "*", path, err)
				return err
			}
		}
	}
	// 启用日志
	enforcer.EnableLog(true)
	GlobalEnforcer = enforcer
	return nil
}

// CasbinMiddleware 是一个 Gin 中间件，用于 Casbin 权限检查
func CasbinMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {

		// 获取请求的资源和操作
		obj := c.Request.URL.Path
		act := c.Request.Method
		pass := false
		if IsStaticFile(obj) || IsSyncRoute(obj) {
			c.Next()
			return
		}

		routeData, ok := getRouteData(obj)
		if !ok {
			handleUnregisteredRoute(c, obj)
			return
		}
		if routeData.AuthType == model.AuthNone {
			c.Next()
			return
		}
		claims, err := auth.GetUserClaims(c)
		if err != nil {
			libs.Error(c, "获取用户信息失败")
			c.AbortWithError(http.StatusUnauthorized, err)
			return
		}

		log.Printf("obj: %s, act: %s", obj, act)
		if routeData.AuthType == model.AuthAll {
			c.Next()
			return
		}
		// 获取当前用户的角色
		for _, roleCode := range claims.RoleCodes {
			// if roleCode == "all" {
			// 	pass = true
			// 	break
			// }
			ok, err := GlobalEnforcer.Enforce(roleCode, obj, act)
			if err != nil {
				log.Printf("Casbin enforce error: %v", err)
				libs.Error(c, "权限验证失败")
				c.Abort()
				return
			}
			if ok {
				pass = true
				break
			}
		}

		if !pass {
			libs.Error(c, "无权访问")
			c.AbortWithStatus(http.StatusForbidden)
			return
		}

		c.Next()
	}
}

// AddRolePermission 添加一个新的角色权限策略
func AddRolePermission(authCode string, rules []model.RolePermissions) error {
	if GlobalEnforcer == nil {
		return fmt.Errorf("casbin enforcer is not initialized")
	}

	for _, rule := range rules {
		_, err := GlobalEnforcer.AddPolicy(authCode, rule.Path, rule.Method)
		if err != nil {
			log.Printf("Failed to add policy for %s %s %s: %v", authCode, rule.Path, rule.Method, err)
			return err
		}
	}

	return nil
}

// UpdateRolePermission 更新指定角色的权限策略
func UpdateRolePermission(code string, rules []model.RolePermissions) error {
	if GlobalEnforcer == nil {
		return fmt.Errorf("casbin enforcer is not initialized")
	}

	// 先删除旧策略
	err := DeleteRolePermission(code)
	if err != nil {
		return err
	}

	// 再添加新策略
	return AddRolePermission(code, rules)
}

// DeleteRolePermission 删除指定角色的所有权限策略
func DeleteRolePermission(authCode string) error {
	if GlobalEnforcer == nil {
		return fmt.Errorf("casbin enforcer is not initialized")
	}

	if _, err := GlobalEnforcer.RemoveFilteredPolicy(0, authCode); err != nil {
		log.Printf("Failed to remove Casbin policies for role %s: %v", authCode, err)
		return fmt.Errorf("删除现有角色权限策略失败")
	}

	return nil
}
