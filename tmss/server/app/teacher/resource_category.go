package teacher

import (
	"tms/libs"
	"tms/model"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

type ResourceCategoryAPI struct{}

func init() {
	api := &ResourceCategoryAPI{}
	adminV1 := GetTeacherVersion()
	categoryGroup := adminV1.Group("resource_category", "资料分类管理接口")
	{
		categoryGroup.Register("POST", "/create", api.CreateResourceCategory, model.AuthTeacher, "创建资料分类")
		categoryGroup.Register("POST", "/update", api.UpdateResourceCategory, model.AuthTeacher, "更新资料分类")
		categoryGroup.Register("POST", "/delete/:id", api.DeleteResourceCategory, model.AuthTeacher, "删除资料分类")
		categoryGroup.Register("GET", "/list", api.GetResourceCategoryList, model.AuthTeacher, "获取资料分类列表")
	}
}

// CreateResourceCategory 创建资料分类
func (api *ResourceCategoryAPI) CreateResourceCategory(c *gin.Context) {
	var req model.ResourceCategory
	if err := c.ShouldBindJSON(&req); err != nil {
		libs.Error(c, "参数错误")
		return
	}

	userId := c.GetInt64("userId")
	err := resourceService.CreateResourceCategory(req, userId)
	if err != nil {
		libs.Error(c, "创建失败: "+err.Error())
		return
	}

	libs.Success(c, "创建成功", req)
}

// UpdateResourceCategory 更新资料分类
func (api *ResourceCategoryAPI) UpdateResourceCategory(c *gin.Context) {
	var req model.ResourceCategory
	if err := c.ShouldBindJSON(&req); err != nil {
		libs.Error(c, "参数错误")
		return
	}

	err := resourceService.UpdateResourceCategory(req)
	if err != nil {
		libs.Error(c, "更新失败: "+err.Error())
		return
	}

	libs.Success(c, "更新成功", nil)
}

// DeleteResourceCategory 删除资料分类
func (api *ResourceCategoryAPI) DeleteResourceCategory(c *gin.Context) {
	id := cast.ToInt64(c.Param("id"))
	err := resourceService.DeleteResourceCategory(id)
	if err != nil {
		libs.Error(c, "删除失败: "+err.Error())
		return
	}

	libs.Success(c, "删除成功", nil)
}

// GetResourceCategoryList 获取资料分类列表（支持搜索和树状结构）
func (api *ResourceCategoryAPI) GetResourceCategoryList(c *gin.Context) {
	var req model.ReqResourceCategorySearch
	if err := c.ShouldBind(&req); err != nil {
		libs.Error(c, "参数错误")
		return
	}
	tree, err := resourceService.GetResourceCategoryList(req)
	if err != nil {
		libs.Error(c, "获取数据失败")
		return
	}

	libs.Success(c, "获取资料分类列表成功", tree)
}
