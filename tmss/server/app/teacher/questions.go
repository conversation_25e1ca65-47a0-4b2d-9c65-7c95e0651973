package teacher

import (
	"tms/libs"
	"tms/model"
	"tms/services/tech"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

type QuestionAPI struct {
}

var questionService = tech.NewQuestionService()

func init() {
	api := &QuestionAPI{}
	adminV1 := GetTeacherVersion()
	questionGroup := adminV1.Group("question", "试题管理接口")
	{
		questionGroup.Register("POST", "/create-draft", api.CreateQuestionDraft, model.AuthTeacher, "创建试题草稿")
		questionGroup.Register("POST", "/update-draft", api.UpdateQuestionDraft, model.AuthTeacher, "更新试题草稿")

		questionGroup.Register("POST", "/delete/:id", api.DeleteQuestion, model.AuthTeacher, "删除试题")
		questionGroup.Register("GET", "/list", api.GetQuestionList, model.AuthTeacher, "获取试题列表")
		questionGroup.Register("GET", "/detail/:id", api.GetQuestion, model.AuthTeacher, "获取试题详情")
		questionGroup.Register("GET", "/courseware/:id", api.GetQuestionsByCoursewareID, model.AuthTeacher, "获取指定课件的试题列表")
		questionGroup.Register("GET", "/course/:id", api.GetCourseByID, model.AuthTeacher, "获取指定课程的试题列表")
		questionGroup.Register("GET", "/coursewares/:id", api.GetQuestionCoursewares, model.AuthTeacher, "获取试题关联课件列表")
		questionGroup.Register("GET", "/chapters", api.GetQuestionsByCourseIDAndChapterID, model.AuthTeacher, "获取指定课程和章节的试题列表")
	}
}

// CreateQuestionDraft 创建试题草稿并关联课件
func (api *QuestionAPI) CreateQuestionDraft(c *gin.Context) {
	var req model.ReqQuestionUpdate

	if err := c.ShouldBindJSON(&req); err != nil {
		libs.Error(c, "参数错误："+err.Error())
		return
	}

	userId := c.GetInt64("userId")
	err := questionService.CreateQuestionDraft(req, userId)
	if err != nil {
		libs.Error(c, "创建失败: "+err.Error())
		return
	}
	libs.Success(c, "创建成功", nil)
}

// UpdateQuestionDraft 更新试题草稿并重新设置关联课件
func (api *QuestionAPI) UpdateQuestionDraft(c *gin.Context) {
	var req model.ReqQuestionUpdate

	if err := c.ShouldBindJSON(&req); err != nil {
		libs.Error(c, "参数错误")
		return
	}

	err := questionService.UpdateQuestionDraft(req)
	if err != nil {
		libs.Error(c, "更新失败: "+err.Error())
		return
	}
	libs.Success(c, "更新成功", nil)
}

// GetQuestionCoursewares 获取试题关联的课件列表
func (api *QuestionAPI) GetQuestionCoursewares(c *gin.Context) {
	id := cast.ToInt64(c.Param("id"))
	coursewares, err := questionService.GetQuestionCoursewares(id)
	if err != nil {
		libs.Error(c, "查询失败: "+err.Error())
		return
	}
	libs.Success(c, "查询成功", coursewares)
}

// DeleteQuestion 删除试题
func (api *QuestionAPI) DeleteQuestion(c *gin.Context) {
	id := cast.ToInt64(c.Param("id"))
	err := questionService.DeleteQuestion(id)
	if err != nil {
		libs.Error(c, "删除失败: "+err.Error())
		return
	}

	libs.Success(c, "删除成功", nil)
}
func (api *QuestionAPI) GetQuestion(c *gin.Context) {
	id := cast.ToInt64(c.Param("id"))
	if id == 0 {
		libs.Error(c, "参数错误")
		return
	}
	question, err := questionService.GetQuestion(id)
	if err != nil {
		libs.Error(c, err.Error())
		return
	}

	libs.Success(c, "获取试题详情成功", question)
}

// GetQuestionList 获取试题列表
func (api *QuestionAPI) GetQuestionList(c *gin.Context) {
	var req model.ReqQuestionSearch
	if err := c.ShouldBind(&req); err != nil {
		libs.Error(c, "参数错误")
		return
	}

	res, err := questionService.GetQuestionList(c, req)
	if err != nil {
		libs.Error(c, "查询失败: "+err.Error())
		return
	}

	libs.Success(c, "查询成功", res)
}

// GetQuestionsByCoursewareID 获取指定课件关联的试题列表（联表查询 + 分页）
func (api *QuestionAPI) GetQuestionsByCoursewareID(c *gin.Context) {
	id := cast.ToInt64(c.Param("id"))
	if id <= 0 {
		libs.Error(c, "参数错误")
		return
	}
	page := model.GetPage(c)
	list, total, err := questionService.GetQuestionsByCoursewareID(id, page)
	if err != nil {
		libs.Error(c, "查询失败: "+err.Error())
		return
	}
	// 返回结果
	libs.Success(c, "查询成功", gin.H{
		"list":  list,
		"total": total,
	})
}

// GetCourseByID 获取课程下的试题列表（联表查询 + 分页）
func (api *QuestionAPI) GetCourseByID(c *gin.Context) {
	id := cast.ToInt64(c.Param("id"))
	if id <= 0 {
		libs.Error(c, "参数错误")
		return
	}
	page := model.GetPage(c)
	list, total, err := questionService.GetQuestionsByCourseID(id, page)
	if err != nil {
		libs.Error(c, "查询失败: "+err.Error())
		return
	}
	// 返回结果
	libs.Success(c, "查询成功", gin.H{
		"list":  list,
		"total": total,
	})
}
func (api *QuestionAPI) GetQuestionsByCourseIDAndChapterID(c *gin.Context) {
	cuourseID := cast.ToInt64(c.Query("course_id"))
	chapterID := cast.ToInt64(c.Query("chapter_id"))
	page := cast.ToInt(c.DefaultQuery("page", "1"))
	pageSize := cast.ToInt(c.DefaultQuery("page_size", "10"))
	name := c.Query("name")
	reqPage := model.ReqPage{
		Name:     name,
		Page:     page,
		PageSize: pageSize,
	}

	//log.Printf("page: %d, page_size: %d", page, pageSize)
	list, total, err := questionService.GetQuestionsByCourseIDAndChapterID(cuourseID, chapterID, reqPage)
	if err != nil {
		libs.Error(c, "查询失败: "+err.Error())
		return
	}
	// 返回结果
	libs.Success(c, "查询成功", gin.H{
		"list":  list,
		"total": total,
	})
}
