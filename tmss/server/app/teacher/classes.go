package teacher

import (
	"log"
	"strconv"

	"tms/libs"
	"tms/model"
	"tms/services/tech"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

// ClassAPI 班级管理API结构体
type ClassAPI struct{}

func init() {
	classAPI := &ClassAPI{}

	adminV1 := GetTeacherVersion()
	classGroup := adminV1.Group("class", "班级管理接口")
	{
		classGroup.Register("GET", "/list", classAPI.GetClassList, model.AuthTeacher, "获取班级列表")
		classGroup.Register("POST", "/create", classAPI.CreateClass, model.AuthTeacher, "创建班级")
		classGroup.Register("POST", "/update", classAPI.UpdateClass, model.AuthTeacher, "更新班级信息")
		classGroup.Register("POST", "/delete/:id", classAPI.DeleteClass, model.AuthTeacher, "删除班级")
		classGroup.Register("POST", "/batch-delete", classAPI.BatchDeleteClasses, model.AuthTeacher, "批量删除班级")
		classGroup.Register("GET", "/detail/:id", classAPI.GetClassDetail, model.AuthTeacher, "获取班级详情")

	}
}

var classService = tech.NewClassService()

// RespClassList 分页返回结果

func (api *ClassAPI) CreateClass(c *gin.Context) {
	var req model.ReqClassUpdate
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Printf("Failed to bind JSON: %v", err)
		libs.Error(c, "参数错误"+err.Error())
		return
	}
	userId := c.GetInt64("userId")
	if userId == 0 {
		libs.Error(c, "无效的用户ID")
		return
	}

	if err := classService.CreateClass(req, userId); err != nil {
		libs.Error(c, err.Error())
		return
	}

	libs.Success(c, "创建班级成功", nil)
}

func (api *ClassAPI) UpdateClass(c *gin.Context) {
	var req model.ReqClassUpdate
	if err := c.ShouldBindJSON(&req); err != nil {
		//log.Printf("Failed to bind JSON: %v", err)
		libs.Error(c, "参数错误"+err.Error())
		return
	}

	if err := classService.UpdateClass(req); err != nil {
		libs.Error(c, err.Error())
		return
	}

	libs.Success(c, "更新班级成功", nil)
}

// DeleteClass 删除班级
func (api *ClassAPI) DeleteClass(c *gin.Context) {
	id := cast.ToInt64(c.Param("id"))

	if err := classService.DeleteClass(id); err != nil {
		libs.Error(c, "删除班级失败: "+err.Error())
		return
	}

	libs.Success(c, "删除班级成功", nil)
}

// BatchDeleteClasses 批量删除班级
func (api *ClassAPI) BatchDeleteClasses(c *gin.Context) {
	type ReqBatchDelete struct {
		IDs []int64 `json:"ids" binding:"required"`
	}
	var req ReqBatchDelete
	if err := c.ShouldBindJSON(&req); err != nil {
		libs.Error(c, "参数错误")
		return
	}

	if err := classService.BatchDeleteClasses(req.IDs); err != nil {
		libs.Error(c, "批量删除失败: "+err.Error())
		return
	}

	libs.Success(c, "批量删除成功", nil)
}
func (api *ClassAPI) GetClassList(c *gin.Context) {
	var req model.ReqClassSearch
	if err := c.ShouldBind(&req); err != nil {
		libs.Error(c, "参数错误")
		return
	}
	classes, err := classService.GetClassList(req)
	if err != nil {
		libs.Error(c, err.Error())
		return
	}

	libs.Success(c, "获取班级列表成功", classes)
}
func (api *ClassAPI) GetClassDetail(c *gin.Context) {
	classID, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		libs.Error(c, "无效的班级ID")
		return
	}
	class, err := classService.GetClassDetail(classID)
	if err != nil {
		libs.Error(c, err.Error())
		return
	}
	libs.Success(c, "获取班级详情成功", class)
}
