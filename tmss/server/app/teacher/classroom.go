package teacher

import (
	"log"
	"strconv"

	"tms/libs"
	"tms/model"
	"tms/services/tech"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

// ClassroomAPI 教室管理API结构体
type ClassroomAPI struct{}

func init() {
	classAPI := &ClassroomAPI{}

	adminV1 := GetTeacherVersion()
	classGroup := adminV1.Group("classroom", "教室管理接口")
	{
		classGroup.Register("GET", "/list", classAPI.GetClassList, model.AuthTeacher, "获取教室列表")
		classGroup.Register("POST", "/create", classAPI.CreateClass, model.AuthTeacher, "创建教室")
		classGroup.Register("POST", "/update", classAPI.UpdateClass, model.AuthTeacher, "更新教室信息")
		classGroup.Register("POST", "/delete/:id", classAPI.DeleteClass, model.AuthTeacher, "删除教室")
		classGroup.Register("POST", "/batch-delete", classAPI.BatchDeleteClasses, model.AuthTeacher, "批量删除教室")
		classGroup.Register("GET", "/detail/:id", classAPI.GetClassDetail, model.AuthTeacher, "获取教室详情")

	}
}

var classroomService = tech.NewClassroomService()

// RespClassList 分页返回结果

func (api *ClassroomAPI) CreateClass(c *gin.Context) {
	var req model.Classroom
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Printf("Failed to bind JSON: %v", err)
		libs.Error(c, "参数错误"+err.Error())
		return
	}
	userId := c.GetInt64("userId")
	if userId == 0 {
		libs.Error(c, "无效的用户ID")
		return
	}

	if err := classroomService.CreateClassroom(req, userId); err != nil {
		libs.Error(c, err.Error())
		return
	}

	libs.Success(c, "创建教室成功", nil)
}

func (api *ClassroomAPI) UpdateClass(c *gin.Context) {
	var req model.Classroom
	if err := c.ShouldBindJSON(&req); err != nil {
		//log.Printf("Failed to bind JSON: %v", err)
		libs.Error(c, "参数错误"+err.Error())
		return
	}

	if err := classroomService.UpdateClassroom(req); err != nil {
		libs.Error(c, err.Error())
		return
	}

	libs.Success(c, "更新教室成功", nil)
}

// DeleteClass 删除教室
func (api *ClassroomAPI) DeleteClass(c *gin.Context) {
	id := cast.ToInt64(c.Param("id"))

	if err := classroomService.DeleteClassroom(id); err != nil {
		libs.Error(c, "删除教室失败: "+err.Error())
		return
	}

	libs.Success(c, "删除教室成功", nil)
}

// BatchDeleteClasses 批量删除教室
func (api *ClassroomAPI) BatchDeleteClasses(c *gin.Context) {
	type ReqBatchDelete struct {
		IDs []int64 `json:"ids" binding:"required"`
	}
	var req ReqBatchDelete
	if err := c.ShouldBindJSON(&req); err != nil {
		libs.Error(c, "参数错误")
		return
	}

	if err := classroomService.BatchDeleteClassroomes(req.IDs); err != nil {
		libs.Error(c, "批量删除失败: "+err.Error())
		return
	}

	libs.Success(c, "批量删除成功", nil)
}
func (api *ClassroomAPI) GetClassList(c *gin.Context) {
	var req model.ReqClassroomSearch
	if err := c.ShouldBind(&req); err != nil {
		libs.Error(c, "参数错误")
		return
	}
	classes, err := classroomService.GetClassroomList(req)
	if err != nil {
		libs.Error(c, err.Error())
		return
	}

	libs.Success(c, "获取教室列表成功", classes)
}
func (api *ClassroomAPI) GetClassDetail(c *gin.Context) {
	classID, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		libs.Error(c, "无效的教室ID")
		return
	}
	class, err := classroomService.GetClassroomDetail(classID)
	if err != nil {
		libs.Error(c, err.Error())
		return
	}
	libs.Success(c, "获取教室详情成功", class)
}
