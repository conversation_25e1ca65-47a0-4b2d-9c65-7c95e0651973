package teacher

import (
	"log/slog"
	"os"
	"path/filepath"
	"sync"
	"tms/libs"
	"tms/model"
	"tms/pkg/db"
	"tms/services/auth"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

type VirtualCoursewareAPI struct{}

func init() {
	api := &VirtualCoursewareAPI{}

	teachersV1 := GetTeacherVersion()
	coursewareGroup := teachersV1.Group("virtual_courseware", "虚拟课件接口")
	{
		coursewareGroup.Register("POST", "/initialize", api.Initialize, model.AuthTeacher, "虚拟课件初始化")
		coursewareGroup.Register("POST", "/navigate", api.Navigate, model.AuthTeacher, "虚拟课件导航")
		coursewareGroup.Register("GET", "/answer/:id", api.GetAnswerHistoryDetail, model.AuthTeacher, "虚拟课件答题历史详情")
	}
}

func (api *VirtualCoursewareAPI) Initialize(c *gin.Context) {
	slog.Info("虚拟课件初始化")
	var req struct {
		CoursewareID int64 `json:"courseware_id" binding:"required"`
		Subtype      int   `json:"subtype" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		slog.Error("初始化失败", "error", err)
		libs.Error(c, "初始化失败")
		return
	}
	claims, err := auth.GetUserClaims(c)
	if err != nil {
		slog.Error("初始化失败", "error", err)
		libs.Error(c, "初始化失败")
		return
	}

	id, err := coursewareService.InitializeVirtualCourseware(claims.ID, req.CoursewareID, req.Subtype)
	if err != nil {
		slog.Error("初始化失败", "error", err)
		libs.Error(c, "初始化失败")
		return
	}

	libs.Success(c, "初始化成功", gin.H{"id": id})
}

func (api *VirtualCoursewareAPI) Navigate(c *gin.Context) {
	var req struct {
		CoursewareID int64 `json:"courseware_id" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		slog.Error("导航失败", "error", err)
		libs.Error(c, "导航失败")
		return
	}

	var courseware model.Courseware
	if err := db.DB.Where("id = ? AND courseware_type = ?", req.CoursewareID, model.CoursewareTypeVirtual).First(&courseware).Error; err != nil {
		slog.Error("导航失败", "error", err)
		libs.Error(c, "导航失败")
		return
	}

	// 查看是否解压
	exist, err := coursewareService.CheckCoursewareExists(courseware.ID)
	if err != nil {
		slog.Error("导航失败", "error", err)
		libs.Error(c, "导航失败")
		return
	}

	var wg sync.WaitGroup
	var downloadErr error
	if !exist {
		// 不存在，必须同步等待下载解压完成
		wg.Add(1)
		go func() {
			defer wg.Done()
			if err := coursewareService.DownloadAndUnzipCourseware([]int64{courseware.ID}); err != nil {
				slog.Error("导航失败", "error", err)
				downloadErr = err
			}
		}()
	}
	wg.Wait()
	if downloadErr != nil {
		libs.Error(c, "导航失败")
		return
	}

	distDir := filepath.Join("data", "scorm", cast.ToString(courseware.ChapterID), cast.ToString(courseware.ID))

	// 递归查找 distDir 下的 index.html 文件
	var indexPath string
	err = filepath.Walk(distDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		if !info.IsDir() && info.Name() == "index.html" {
			// 找到第一个 index.html 就停止
			indexPath = path
			return filepath.SkipDir // 找到后跳过当前目录，避免继续遍历
		}
		return nil
	})

	if err != nil {
		slog.Error("导航失败", "error", err)
		libs.Error(c, "查找 index.html 失败")
		return
	}

	if indexPath == "" {
		slog.Error("未找到 index.html 文件", "distDir", distDir)
		libs.Error(c, "未找到 index.html 文件")
		return
	}

	// 计算相对于 distDir 的路径，这会自动去除 "data/scorm/id/id" 前缀
	relPath, err := filepath.Rel(distDir, indexPath)
	if err != nil {
		slog.Error("计算相对路径失败", "error", err)
		libs.Error(c, "计算相对路径失败")
		return
	}

	libs.Success(c, "success", relPath)
}

func (api *VirtualCoursewareAPI) GetAnswerHistoryDetail(c *gin.Context) {
	historyID := cast.ToInt64(c.Param("id"))
	data, err := coursewareService.GetVirtualCoursewareAnswerHistory(historyID)
	if err != nil {
		slog.Error("获取答题历史失败", "error", err)
		libs.Error(c, "获取答题历史失败")
		return
	}

	libs.Success(c, "success", data)
}
