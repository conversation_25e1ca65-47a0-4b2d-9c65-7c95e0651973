package teacher

import (
	"log"
	"tms/libs"
	"tms/model"
	"tms/services/tech"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

type TeachingDocumentAPI struct{}

var teachingDocumentService = tech.NewTeachingDocumentService()

func init() {
	api := &TeachingDocumentAPI{}
	adminV1 := GetTeacherVersion()
	teachingDocumentGroup := adminV1.Group("teaching-documents", "教案管理接口")
	{
		teachingDocumentGroup.Register("POST", "/create-draft", api.CreateTeachingDocumentDraft, model.AuthTeacher, "创建教案草稿")
		teachingDocumentGroup.Register("POST", "/update-draft", api.UpdateTeachingDocumentDraft, model.AuthTeacher, "更新教案草稿")
		teachingDocumentGroup.Register("POST", "/delete/:id", api.DeleteTeachingDocument, model.AuthTeacher, "删除教案")
		teachingDocumentGroup.Register("GET", "/list", api.GetTeachingDocumentsList, model.AuthTeacher, "获取教案列表")
		teachingDocumentGroup.Register("GET", "/detail/:id", api.GetTeachingDocumentDetail, model.AuthTeacher, "获取教案详情")
	}
}

// CreateTeachingDocumentDraft 创建教案草稿
func (api *TeachingDocumentAPI) CreateTeachingDocumentDraft(c *gin.Context) {
	var req model.TeachingDocument
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Printf("参数错误: %v", err.Error())
		libs.Error(c, "参数错误")
		return
	}
	userId := c.GetInt64("userId")
	if err := teachingDocumentService.CreateTeachingDocumentDraft(req, userId); err != nil {
		libs.Error(c, err.Error())
		return
	}

	libs.Success(c, "创建成功", nil)
}

// UpdateTeachingDocumentDraft 更新教案草稿
func (api *TeachingDocumentAPI) UpdateTeachingDocumentDraft(c *gin.Context) {
	var req model.TeachingDocument
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Printf("参数错误: %v", err.Error())
		libs.Error(c, "参数错误")
		return
	}

	if err := teachingDocumentService.UpdateTeachingDocumentDraft(req); err != nil {
		libs.Error(c, err.Error())
		return
	}

	libs.Success(c, "更新成功", nil)
}

// DeleteTeachingDocument 删除教案
func (api *TeachingDocumentAPI) DeleteTeachingDocument(c *gin.Context) {
	id := cast.ToInt64(c.Param("id"))
	if err := teachingDocumentService.DeleteTeachingDocument(id); err != nil {
		libs.Error(c, err.Error())
		return
	}

	libs.Success(c, "删除成功", nil)
}

// GetTeachingDocumentsList 获取教案列表
func (api *TeachingDocumentAPI) GetTeachingDocumentsList(c *gin.Context) {
	var req model.ReqTeachingDocumentSearch
	if err := c.ShouldBind(&req); err != nil {
		libs.Error(c, "参数错误")
		return
	}

	resp, err := teachingDocumentService.GetTeachingDocumentsList(c, req)
	if err != nil {
		libs.Error(c, err.Error())
		return
	}

	libs.Success(c, "查询成功", resp)
}

// GetTeachingDocumentDetail 获取教案详情
func (api *TeachingDocumentAPI) GetTeachingDocumentDetail(c *gin.Context) {
	id := cast.ToInt64(c.Param("id"))
	if id == 0 {
		libs.Error(c, "无效的教案ID")
		return
	}

	resp, err := teachingDocumentService.GetTeachingDocumentDetail(id)
	if err != nil {
		libs.Error(c, err.Error())
		return
	}

	libs.Success(c, "查询成功", resp)
}
