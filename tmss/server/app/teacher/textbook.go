package teacher

import (
	"fmt"
	"log"
	"os"
	"path/filepath"
	"time"

	"tms/libs"
	"tms/model"
	"tms/services/tech"
	"tms/utils"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

// TextbookAPI 教材管理API结构体
type TextbookAPI struct{}

var textbookService = tech.NewTextbookService()

func init() {
	api := &TextbookAPI{}

	adminV1 := GetTeacherVersion()
	textbookGroup := adminV1.Group("textbook", "教材管理接口")
	{
		textbookGroup.Register("GET", "/list", api.GetTextbookList, model.AuthTeacher, "获取教材列表")
		textbookGroup.Register("POST", "/upload", api.UploadTextbook, model.AuthTeacher, "上传教材")
		textbookGroup.Register("POST", "/create", api.CreateTextbook, model.AuthTeacher, "创建教材")
		textbookGroup.Register("POST", "/update", api.UpdateTextbook, model.AuthTeacher, "更新教材信息")
		textbookGroup.Register("POST", "/delete/:id", api.DeleteTextbook, model.AuthTeacher, "删除教材")
		textbookGroup.Register("POST", "/batch-delete", api.BatchDeleteTextbooks, model.AuthTeacher, "批量删除教材")
	}
}

// UploadCoursewareFile 上传教材
func (api *TextbookAPI) UploadTextbook(c *gin.Context) {
	file, err := c.FormFile("file")
	if err != nil {
		libs.Error(c, "文件上传失败: "+err.Error())
		return
	}
	ext := filepath.Ext(file.Filename)
	log.Printf("ext: %s", ext)
	allowExt := []string{".pdf", ".docx", ".pptx"}
	if !utils.HasContainsStrings(allowExt, ext) {
		libs.Error(c, "文件格式错误")
		return
	}

	// 保存文件到服务器
	timeFormat := time.Now().Format("20060102")
	filePath := fmt.Sprintf("./uploads/textbook/%s/%s", timeFormat, file.Filename)
	if err := os.MkdirAll(filepath.Dir(filePath), os.ModePerm); err != nil {
		libs.Error(c, "创建文件夹失败: "+err.Error())
		return
	}
	if err := c.SaveUploadedFile(file, filePath); err != nil {
		libs.Error(c, "保存文件失败: "+err.Error())
		return
	}

	libs.Success(c, "文件上传成功", gin.H{
		"file_name": file.Filename,
		"file_path": filePath,
		"file_type": ext,
	})
}

// GetTextbookList 获取教材列表（支持分页、搜索）
func (api *TextbookAPI) GetTextbookList(c *gin.Context) {
	var req model.ReqTextbookSearch
	if err := c.ShouldBind(&req); err != nil {
		libs.Error(c, "参数错误")
		return
	}

	res, err := textbookService.GetTextbookList(req)
	if err != nil {
		libs.Error(c, "获取教材列表失败: "+err.Error())
		return
	}

	libs.Success(c, "获取教材列表成功", res)
}

// CreateTextbook 创建教材
func (api *TextbookAPI) CreateTextbook(c *gin.Context) {
	var req model.Textbook
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Printf("Failed to bind JSON: %v", err)
		libs.Error(c, "参数错误")
		return
	}
	userId := c.GetInt64("userId")
	err := textbookService.CreateTextbook(req, userId)
	if err != nil {
		libs.Error(c, "创建教材失败: "+err.Error())
		return
	}

	libs.Success(c, "创建教材成功", nil)
}

// UpdateTextbook 更新教材信息
func (api *TextbookAPI) UpdateTextbook(c *gin.Context) {
	var req model.Textbook
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Printf("Failed to bind JSON: %v", err)
		libs.Error(c, "参数错误")
		return
	}

	err := textbookService.UpdateTextbook(req)
	if err != nil {
		libs.Error(c, "更新教材失败: "+err.Error())
		return
	}

	libs.Success(c, "更新教材成功", nil)
}

// DeleteTextbook 删除教材
func (api *TextbookAPI) DeleteTextbook(c *gin.Context) {
	id := cast.ToInt64(c.Param("id"))
	err := textbookService.DeleteTextbook(id)
	if err != nil {
		libs.Error(c, "删除教材失败: "+err.Error())
		return
	}

	libs.Success(c, "删除教材成功", nil)
}

// BatchDeleteTextbooks 批量删除教材
func (api *TextbookAPI) BatchDeleteTextbooks(c *gin.Context) {
	type ReqBatchDelete struct {
		IDs []int64 `json:"ids" binding:"required"`
	}
	var req ReqBatchDelete
	if err := c.ShouldBindJSON(&req); err != nil {
		libs.Error(c, "参数错误")
		return
	}

	err := textbookService.BatchDeleteTextbooks(req.IDs)
	if err != nil {
		libs.Error(c, "批量删除失败: "+err.Error())
		return
	}

	libs.Success(c, "批量删除成功", nil)
}
