package teacher

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"
	"tms/libs"
	"tms/model"
	"tms/services/resource"
	"tms/utils"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

type ResourceAPI struct{}

// 定义资源搜索请求结构体
var resourceService = resource.NewResourceService()

func init() {
	api := &ResourceAPI{}
	adminV1 := GetTeacherVersion()
	resourceGroup := adminV1.Group("resource", "资料管理接口")
	{
		resourceGroup.Register("POST", "/create-draft", api.CreateResourceDraft, model.AuthTeacher, "批量添加资料")
		resourceGroup.Register("POST", "/upload", api.UploadResourceFile, model.AuthTeacher, "上传资料文件")
		resourceGroup.Register("POST", "/update-draft", api.UpdateResourceDraft, model.AuthTeacher, "更新资料草稿")
		resourceGroup.Register("POST", "/delete/:id", api.DeleteResource, model.AuthTeacher, "删除资料")
		resourceGroup.Register("GET", "/list", api.GetResourceList, model.AuthTeacher, "获取资料列表")
	}
}

// CreateResourceDraft 创建资料草稿（只需分类ID，其他从上传文件提取）
func (api *ResourceAPI) CreateResourceDraft(c *gin.Context) {

	// 获取用户ID
	userId := c.GetInt64("userId")

	// 获取上传的文件
	form, err := c.MultipartForm()
	if err != nil {
		libs.Error(c, "无法解析上传表单: "+err.Error())
		return
	}
	count, err := resourceService.CreateResourceDraft(form, userId)
	if err != nil {
		libs.Error(c, "创建失败: "+err.Error())
		return
	}
	libs.Success(c, "批量上传并创建成功", count)
}
func (api *ResourceAPI) UploadResourceFile(c *gin.Context) {
	file, err := c.FormFile("file")
	if err != nil {
		libs.Error(c, "文件上传失败: "+err.Error())
		return
	}
	ext := filepath.Ext(file.Filename)
	if !utils.HasContainsStrings(resource.AllowResourceExt, ext) {
		libs.Error(c, "文件格式错误")
		return
	}
	now := time.Now()
	uploadDir := fmt.Sprintf("./uploads/resource/%d/%02d/%02d", now.Year(), now.Month(), now.Day())

	// 创建目录（包括所有必要父目录）
	if err := os.MkdirAll(uploadDir, os.ModePerm); err != nil {
		libs.Error(c, "创建上传目录失败: "+err.Error())
	}
	filePath := filepath.Join(uploadDir, file.Filename)
	res := gin.H{
		"name":      strings.TrimSuffix(file.Filename, ext),
		"file_path": filePath,
		"file_type": ext,
		"size":      file.Size,
	}
	if libs.PathExists(filePath) {
		libs.Success(c, "文件已存在", res)
		return
	}

	if err := c.SaveUploadedFile(file, filePath); err != nil {
		libs.Error(c, "保存文件失败: "+err.Error())
		return
	}

	libs.Success(c, "文件上传成功", res)
}

// UpdateResourceDraft 更新资料草稿（仅允许修改分类、名称和描述）
func (api *ResourceAPI) UpdateResourceDraft(c *gin.Context) {
	var req model.ReqResourceUpdate

	if err := c.ShouldBindJSON(&req); err != nil {
		libs.Error(c, "参数错误："+err.Error())
		return
	}
	err := resourceService.UpdateResourceDraft(req)
	if err != nil {
		libs.Error(c, "更新失败: "+err.Error())
		return
	}
	libs.Success(c, "更新成功", nil)
}

// DeleteResource 删除资料（同时删除数据库记录和实际文件）
func (api *ResourceAPI) DeleteResource(c *gin.Context) {
	id := cast.ToInt64(c.Param("id"))
	err := resourceService.DeleteResource(id)
	if err != nil {
		libs.Error(c, "删除失败: "+err.Error())
		return
	}
	libs.Success(c, "删除成功", nil)
}

// GetResourceList 获取资料列表
func (api *ResourceAPI) GetResourceList(c *gin.Context) {
	var req model.ReqResourceSearch
	if err := c.ShouldBind(&req); err != nil {
		libs.Error(c, "参数错误")
		return
	}
	res, err := resourceService.GetResourceList(c, req)
	if err != nil {
		libs.Error(c, "查询失败: "+err.Error())
		return
	}
	libs.Success(c, "查询成功", res)
}
