package teacher

import (
	"tms/libs"
	"tms/model"
	"tms/services/tech"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

type ChapterAPI struct{}

var chapterService = tech.NewChapterService()

func init() {
	api := &ChapterAPI{}
	adminV1 := GetTeacherVersion()
	chapterGroup := adminV1.Group("chapters", "章节管理接口")
	{
		chapterGroup.Register("POST", "/create", api.CreateChapter, model.AuthTeacher, "创建章节")
		chapterGroup.Register("POST", "/update", api.UpdateChapter, model.AuthTeacher, "更新章节")
		chapterGroup.Register("POST", "/delete/:id", api.DeleteChapter, model.AuthTeacher, "删除章节")
		chapterGroup.Register("GET", "/list", api.GetChapterList, model.AuthTeacher, "获取章节列表")
		chapterGroup.Register("GET", "/courses/:id", api.GetChapterTreeByCourseID, model.AuthTeacher, "获取章节树")
	}
}

// CreateChapter 创建章节
func (api *ChapterAPI) CreateChapter(c *gin.Context) {
	var req model.Chapter
	if err := c.ShouldBindJSON(&req); err != nil {
		libs.Error(c, "参数错误")
		return
	}

	userID := c.GetInt64("userId")
	if err := chapterService.CreateChapter(&req, userID); err != nil {
		libs.Error(c, err.Error())
		return
	}

	libs.Success(c, "创建成功", req)
}

// UpdateChapter 更新章节
func (api *ChapterAPI) UpdateChapter(c *gin.Context) {
	var req model.Chapter
	if err := c.ShouldBindJSON(&req); err != nil {
		libs.Error(c, "参数错误")
		return
	}

	if err := chapterService.UpdateChapter(&req); err != nil {
		libs.Error(c, err.Error())
		return
	}

	libs.Success(c, "更新成功", nil)
}

// DeleteChapter 删除章节
func (api *ChapterAPI) DeleteChapter(c *gin.Context) {
	id := cast.ToInt64(c.Param("id"))
	if id == 0 {
		libs.Error(c, "无效的章节ID")
		return
	}

	if err := chapterService.DeleteChapter(id); err != nil {
		libs.Error(c, err.Error())
		return
	}

	libs.Success(c, "删除成功", nil)
}

// GetChapterList 获取章节列表（支持搜索和树状结构）
func (api *ChapterAPI) GetChapterList(c *gin.Context) {
	var req model.ReqChapterSearch
	if err := c.ShouldBind(&req); err != nil {
		libs.Error(c, "参数错误")
		return
	}

	list, err := chapterService.GetChapterList(req.Name, req.ParentID)
	if err != nil {
		libs.Error(c, err.Error())
		return
	}

	libs.Success(c, "获取章节列表成功", list)
}

// GetChapterTreeByCourseID 获取课程对应的章节树结构
func (api *ChapterAPI) GetChapterTreeByCourseID(c *gin.Context) {
	courseID := cast.ToInt64(c.Param("id"))
	if courseID == 0 {
		libs.Error(c, "无效的课程ID")
		return
	}

	tree, err := chapterService.GetChapterTreeByCourseID(courseID)
	if err != nil {
		libs.Error(c, err.Error())
		return
	}

	libs.Success(c, "获取章节树成功", tree)
}
