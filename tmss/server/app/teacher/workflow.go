package teacher

import (
	"tms/libs"
	"tms/model"
	"tms/services/workflow"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

type WorkflowAPI struct {
}

func init() {
	api := &WorkflowAPI{}
	adminV1 := GetTeacherVersion()
	usersGroup := adminV1.Group("workflow", "工作流接口")
	{
		usersGroup.Register("GET", "/list/:name", api.GetWorkflowList, model.AuthTeacher, "获取可用的工作流列表")
		usersGroup.Register("POST", "/create", api.CreateWorkflow, model.AuthTeacher, "创建工作流")
		usersGroup.Register("POST", "/batch-create", api.BatchCreateWorkflow, model.AuthTeacher, "批量创建工作流")
		usersGroup.Register("GET", "/approve/list/:key", api.ApproveDataList, model.AuthTeacher, "获取审核列表")
		usersGroup.Register("POST", "/approve/pass", api.ApproveData, model.AuthTeacher, "审核数据")
		usersGroup.Register("POST", "/approve/reject", api.RejectData, model.AuthTeacher, "打回数据")
		usersGroup.Register("GET", "/remark/:id", api.GetWorkflowRemarkList, model.AuthTeacher, "获取审核备注列表")
		usersGroup.Register("POST", "/approve/batch-pass", api.BatchApproveData, model.AuthTeacher, "批量审核通过")
		usersGroup.Register("POST", "/approve/batch-reject", api.BatchRejectData, model.AuthTeacher, "批量驳回")
	}
}
func (api *WorkflowAPI) GetWorkflowList(c *gin.Context) {
	moduleKey := c.Param("name")
	if !model.IsValidModuleKey(moduleKey) {
		libs.Error(c, "无效的模块名称")
		return
	}
	res, err := workflow.GetWorkFlowList(moduleKey)
	if err != nil {
		//libs.Error(c, err.Error())
		libs.Success(c, "审计未开启", nil)

		return
	}
	libs.Success(c, "查询成功", res)
}
func (api *WorkflowAPI) CreateWorkflow(c *gin.Context) {

	var req model.ReqWorkflowApprove
	if err := c.ShouldBindJSON(&req); err != nil {
		libs.Error(c, "参数错误")
		return
	}
	userId := c.GetInt64("userId")
	if userId == 0 {
		libs.Error(c, "无效的用户ID")
		return
	}
	req.UserID = userId
	res, err := workflow.CreateWorkflowApprove(req)
	if err != nil {
		libs.Error(c, err.Error())
		return
	}
	libs.Success(c, "提交成功", res)
}
func (api *WorkflowAPI) BatchCreateWorkflow(c *gin.Context) {

	var req []model.ReqWorkflowApprove
	if err := c.ShouldBindJSON(&req); err != nil {
		libs.Error(c, "参数错误")
		return
	}
	userId := c.GetInt64("userId")
	if userId == 0 {
		libs.Error(c, "无效的用户ID")
		return
	}
	for i := range req {
		req[i].UserID = userId
	}
	res, err := workflow.BatchCreateWorkflowApprove(req)
	if err != nil {
		libs.Error(c, err.Error())
		return
	}
	libs.Success(c, "提交成功", res)
}
func (api *WorkflowAPI) ApproveDataList(c *gin.Context) {
	key := c.Param("key")
	if !model.IsValidModuleKey(key) {
		libs.Error(c, "无效的模块名称")
		return
	}
	var req model.ReqWorkflowList
	if err := c.ShouldBindQuery(&req); err != nil {
		libs.Error(c, "参数错误")
		return
	}
	req.ModuleKey = key
	res, total, err := workflow.ListWorkflowApproves(c, req)
	if err != nil {
		libs.Error(c, "获取列表失败: "+err.Error())
		return
	}
	libs.Success(c, "查询成功", gin.H{
		"list":  res,
		"total": total,
	})
}
func (api *WorkflowAPI) ApproveData(c *gin.Context) {
	var req model.ReqWorkflowApprove
	if err := c.ShouldBindJSON(&req); err != nil {
		libs.Error(c, "参数错误")
		return
	}
	userId := c.GetInt64("userId")
	if userId == 0 {
		libs.Error(c, "无效的用户ID")
		return
	}
	req.UserID = userId
	err := workflow.ApproveData(req.ApproveID, req.Remark, req.UserID)
	if err != nil {
		libs.Error(c, err.Error())
		return
	}
	libs.Success(c, "提交成功", nil)
}
func (api *WorkflowAPI) RejectData(c *gin.Context) {
	var req model.ReqWorkflowApprove
	if err := c.ShouldBindJSON(&req); err != nil {
		libs.Error(c, "参数错误")
		return
	}
	userId := c.GetInt64("userId")
	if userId == 0 {
		libs.Error(c, "无效的用户ID")
		return
	}
	req.UserID = userId
	err := workflow.RejectData(req.ApproveID, req.RejectReason, req.Remark, req.UserID)
	if err != nil {
		libs.Error(c, err.Error())
		return
	}
	libs.Success(c, "提交成功", nil)
}
func (api *WorkflowAPI) GetWorkflowRemarkList(c *gin.Context) {
	id := cast.ToInt64(c.Param("id"))
	if id == 0 {
		libs.Error(c, "无效的ID")
		return
	}

	list, err := workflow.ListWorkflowRemarks(id)
	if err != nil {
		libs.Error(c, err.Error())
		return
	}
	libs.Success(c, "获取列表成功", list)
}

// 在WorkflowAPI结构体中添加批量方法
func (api *WorkflowAPI) BatchApproveData(c *gin.Context) {
	var req model.ReqBatchWorkflowApprove
	if err := c.ShouldBindJSON(&req); err != nil {
		libs.Error(c, "参数错误")
		return
	}

	userId := c.GetInt64("userId")
	if userId == 0 {
		libs.Error(c, "无效的用户ID")
		return
	}

	if len(req.ApproveIDs) == 0 {
		libs.Error(c, "请选择要审核的记录")
		return
	}

	err := workflow.BatchApproveData(req.ApproveIDs, req.Remark, userId)
	if err != nil {
		libs.Error(c, "批量审核失败: "+err.Error())
		return
	}
	libs.Success(c, "批量审核成功", nil)
}

func (api *WorkflowAPI) BatchRejectData(c *gin.Context) {
	var req model.ReqBatchWorkflowApprove
	if err := c.ShouldBindJSON(&req); err != nil {
		libs.Error(c, "参数错误")
		return
	}

	userId := c.GetInt64("userId")
	if userId == 0 {
		libs.Error(c, "无效的用户ID")
		return
	}

	if len(req.ApproveIDs) == 0 {
		libs.Error(c, "请选择要驳回的记录")
		return
	}

	err := workflow.BatchRejectData(req.ApproveIDs, req.RejectReason, req.Remark, userId)
	if err != nil {
		libs.Error(c, "批量驳回失败: "+err.Error())
		return
	}
	libs.Success(c, "批量驳回成功", nil)
}
