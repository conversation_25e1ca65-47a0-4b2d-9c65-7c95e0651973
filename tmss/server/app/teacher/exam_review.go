// teacher/review.go

package teacher

import (
	"tms/libs"
	"tms/model"
	"tms/services/exam"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

type ExamReviewAPI struct{}

var examReviewService = exam.NewExamReviewService()

func init() {
	api := &ExamReviewAPI{}
	teacherV1 := GetTeacherVersion()
	reviewGroup := teacherV1.Group("examreview", "阅卷管理接口")
	{
		reviewGroup.Register("GET", "/exams", api.GetTeacherReviewExams, model.AuthTeacher, "获取老师需要阅卷的考试列表")
		reviewGroup.Register("GET", "/list", api.GetReviewList, model.AuthTeacher, "获取老师的阅卷任务列表")
		reviewGroup.Register("GET", "/students", api.GetStudentExamProgress, model.AuthTeacher, "获取考试下学生的考试情况")
		reviewGroup.Register("POST", "/init-task", api.InitAllReviews, model.AuthTeacher, "下发老师阅卷任务")
		reviewGroup.Register("POST", "/change-reviewer", api.ChangeReviewer, model.AuthTeacher, "更换阅卷老师")
		reviewGroup.Register("GET", "/progress/:id", api.GetProgressDetail, model.AuthTeacher, "获取单个试卷的详细信息")
		reviewGroup.Register("POST", "/auto-grade/:exam_id", api.AutoGradeExam, model.AuthTeacher, "无主观题时自动评分并更新考试状态")
		reviewGroup.Register("POST", "/submit", api.SubmitReview, model.AuthTeacher, "提交阅卷信息")
		reviewGroup.Register("POST", "/generate-scores/:exam_id", api.GenerateScoreRecords, model.AuthTeacher, "手动生成考试成绩记录")
		reviewGroup.Register("POST", "/generate-certificates", api.GenerateCertificates, model.AuthTeacher, "手动生成证书记录")
	}
}

// GetTeacherReviewExams 获取老师需要阅卷的考试列表
func (api *ExamReviewAPI) GetTeacherReviewExams(c *gin.Context) {
	teacherID := c.GetInt64("userId")
	pageNum := cast.ToInt(c.DefaultQuery("page", "1"))
	pageSize := cast.ToInt(c.DefaultQuery("page_size", "10"))

	exams, total, err := examReviewService.GetTeacherReviewExams(teacherID, pageNum, pageSize)
	if err != nil {
		libs.Error(c, "查询失败: "+err.Error())
		return
	}

	libs.Success(c, "查询成功", gin.H{
		"list":  exams,
		"total": total,
	})
}

// GetReviewList 获取老师的阅卷任务列表
func (api *ExamReviewAPI) GetReviewList(c *gin.Context) {
	teacherID := c.GetInt64("userId")
	pageNum := cast.ToInt(c.DefaultQuery("page", "1"))
	pageSize := cast.ToInt(c.DefaultQuery("page_size", "10"))

	reviews, total, err := examReviewService.GetReviewList(teacherID, pageNum, pageSize)
	if err != nil {
		libs.Error(c, "查询失败: "+err.Error())
		return
	}

	libs.Success(c, "查询成功", gin.H{
		"list":  reviews,
		"total": total,
	})
}

// GetStudentExamProgress 获取考试下所有学生的考试情况
func (api *ExamReviewAPI) GetStudentExamProgress(c *gin.Context) {
	examID := cast.ToInt64(c.Query("exam_id"))
	if examID == 0 {
		libs.Error(c, "参数错误: exam_id 必填")
		return
	}
	pageNum := cast.ToInt(c.DefaultQuery("page", "1"))
	pageSize := cast.ToInt(c.DefaultQuery("page_size", "10"))

	progresses, err := examReviewService.GetStudentExamProgress(examID, pageNum, pageSize)
	if err != nil {
		libs.Error(c, "查询失败: "+err.Error())
		return
	}

	libs.Success(c, "查询成功", progresses)
}
func (api *ExamReviewAPI) AutoGradeExam(c *gin.Context) {
	examID := cast.ToInt64(c.Param("exam_id"))
	if examID == 0 {
		libs.Error(c, "参数错误: 缺少考试ID")
		return
	}

	// 先检查是否已完成评分
	isGraded, err := examReviewService.IsExamGraded(examID)
	if err != nil {
		libs.Error(c, "查询失败: "+err.Error())
		return
	}
	if isGraded {
		libs.Error(c, "该考试已全部完成阅卷，无需重复操作")
		return
	}

	err = examReviewService.AutoGradeExamIfNoQuestions(examID)
	if err != nil {
		libs.Error(c, "自动评分失败: "+err.Error())
		return
	}

	libs.Success(c, "考试已自动评分并完成", nil)
}
func (api *ExamReviewAPI) ChangeReviewer(c *gin.Context) {
	var req struct {
		TeacherID   int64   `json:"teacher_id"`
		ProgressIDs []int64 `json:"progress_ids"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		libs.Error(c, "参数错误")
		return
	}

	err := examReviewService.ChangeReviewer(req.ProgressIDs, req.TeacherID)
	if err != nil {
		libs.Error(c, "更改阅卷老师失败: "+err.Error())
		return
	}
}

// InitReviewRecord 下发老师阅卷任务并生成阅卷记录
func (api *ExamReviewAPI) InitAllReviews(c *gin.Context) {
	var req struct {
		ExamID      int64   `json:"exam_id"`
		TeacherID   int64   `json:"teacher_id"`
		ProgressIDs []int64 `json:"progress_ids"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		libs.Error(c, "参数错误")
		return
	}
	if req.ExamID == 0 {
		libs.Error(c, "参数错误: 缺少考试ID")
		return
	}
	if req.TeacherID == 0 {
		libs.Error(c, "参数错误: 缺少老师ID")
		return
	}

	_, err := examReviewService.InitReviewRecord(req.ExamID, req.ProgressIDs, req.TeacherID)
	if err != nil {
		libs.Error(c, "初始化老师阅卷任务失败: "+err.Error())
		return
	}

	libs.Success(c, "下发老师阅卷任务成功", nil)
}

// SubmitReview 提交阅卷信息
func (api *ExamReviewAPI) SubmitReview(c *gin.Context) {
	var req struct {
		Reviews []model.ExamReview `json:"reviews"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		libs.Error(c, "参数错误")
		return
	}

	if len(req.Reviews) == 0 {
		libs.Error(c, "无阅卷数据")
		return
	}

	err := examReviewService.SubmitReview(req.Reviews)
	if err != nil {
		libs.Error(c, "提交失败: "+err.Error())
		return
	}

	libs.Success(c, "阅卷提交成功", nil)
}
func (api *ExamReviewAPI) GetProgressDetail(c *gin.Context) {
	id := cast.ToInt64(c.Param("id"))
	detail, err := examReviewService.GetExamProgressDetail(id)
	if err != nil {
		libs.Error(c, "查询失败: "+err.Error())
		return
	}
	libs.Success(c, "查询成功", detail)
}

// GenerateScoreRecords 手动生成考试成绩记录
func (api *ExamReviewAPI) GenerateScoreRecords(c *gin.Context) {
	examID := cast.ToInt64(c.Param("exam_id"))
	if examID == 0 {
		libs.Error(c, "参数错误: 缺少考试ID")
		return
	}

	err := examReviewService.GenerateExamScoreRecords(examID)
	if err != nil {
		libs.Error(c, "生成成绩记录失败: "+err.Error())
		return
	}

	libs.Success(c, "成绩记录生成成功", nil)
}
func (api *ExamReviewAPI) GenerateCertificates(c *gin.Context) {
	var req model.ReqCertificateCreate
	if err := c.ShouldBindJSON(&req); err != nil {
		libs.Error(c, "参数错误")
		return
	}
	err := examReviewService.GenerateCertificates(req)
	if err != nil {
		libs.Error(c, "生成证书失败: "+err.Error())
		return
	}
	libs.Success(c, "证书生成成功", nil)

}
