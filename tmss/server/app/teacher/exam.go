package teacher

import (
	"tms/libs"
	"tms/model"
	"tms/services/exam"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

type ExamAPI struct{}

var examService = exam.NewExamService()

func init() {
	api := &ExamAPI{}
	adminV1 := GetTeacherVersion()
	examGroup := adminV1.Group("exam", "考试管理接口")
	{
		examGroup.Register("POST", "/create-draft", api.CreateExamDraft, model.AuthTeacher, "创建考试草稿")
		examGroup.Register("POST", "/update-draft", api.UpdateExamDraft, model.AuthTeacher, "更新考试草稿")
		examGroup.Register("POST", "/delete/:id", api.DeleteExam, model.AuthTeacher, "删除考试")
		examGroup.Register("GET", "/list", api.GetExamList, model.AuthTeacher, "获取考试列表")
		examGroup.Register("GET", "/detail/:id", api.GetExamDetail, model.AuthTeacher, "获取考试详情")
		examGroup.Register("GET", "/user-list", api.GetUserList, model.AuthTeacher, "获取考试关联用户列表")

	}
}

// CreateExamDraft 创建考试草稿
func (api *ExamAPI) CreateExamDraft(c *gin.Context) {
	var req model.ReqCreateExam

	if err := c.ShouldBindJSON(&req); err != nil {
		libs.Error(c, "参数错误："+err.Error())
		return
	}

	userId := c.GetInt64("userId")
	err := examService.CreateExamDraft(req, userId)
	if err != nil {
		libs.Error(c, "创建失败: "+err.Error())
		return
	}
	libs.Success(c, "创建成功", nil)
}

// UpdateExamDraft 更新考试草稿
func (api *ExamAPI) UpdateExamDraft(c *gin.Context) {
	var req model.ReqCreateExam

	if err := c.ShouldBindJSON(&req); err != nil {
		libs.Error(c, "参数错误")
		return
	}

	err := examService.UpdateExamDraft(req)
	if err != nil {
		libs.Error(c, "更新失败: "+err.Error())
		return
	}
	libs.Success(c, "更新成功", nil)
}

// DeleteExam 删除考试
func (api *ExamAPI) DeleteExam(c *gin.Context) {
	id := cast.ToInt64(c.Param("id"))
	err := examService.DeleteExam(id)
	if err != nil {
		libs.Error(c, "删除失败: "+err.Error())
		return
	}
	libs.Success(c, "删除成功", nil)
}

// GetExamList 获取考试列表
func (api *ExamAPI) GetExamList(c *gin.Context) {
	var req model.ReqExamSearch
	if err := c.ShouldBind(&req); err != nil {
		libs.Error(c, "参数错误")
		return
	}
	res, err := examService.GetExamList(c, req)
	if err != nil {
		libs.Error(c, "查询失败: "+err.Error())
		return
	}
	libs.Success(c, "查询成功", res)
}

// GetExamDetail 获取考试详情
func (api *ExamAPI) GetExamDetail(c *gin.Context) {
	id := cast.ToInt64(c.Param("id"))
	detail, err := examService.GetExamDetail(id)
	if err != nil {
		libs.Error(c, "查询失败: "+err.Error())
		return
	}
	libs.Success(c, "查询成功", detail)
}

// GetUserList 获取考试关联的用户列表（学生/阅卷老师/监考老师）
func (api *ExamAPI) GetUserList(c *gin.Context) {
	examID := cast.ToInt64(c.Query("exam_id"))
	extKey := c.Query("ext_key")
	page := cast.ToInt(c.DefaultQuery("page", "1"))
	pageSize := cast.ToInt(c.DefaultQuery("page_size", "10"))

	if examID == 0 || extKey == "" {
		libs.Error(c, "参数错误: exam_id 和 ext_key 必填")
		return
	}

	list, total, err := examService.GetUserList(examID, extKey, page, pageSize)
	if err != nil {
		libs.Error(c, "查询失败: "+err.Error())
		return
	}

	libs.Success(c, "查询成功", gin.H{
		"list":  list,
		"total": total,
	})
}
