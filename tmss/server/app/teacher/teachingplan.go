package teacher

import (
	"tms/libs"
	"tms/model"
	"tms/services/tech"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

type TeachingPlanAPI struct{}

var planService = tech.NewTeachingPlanService()

func init() {
	api := &TeachingPlanAPI{}
	adminV1 := GetTeacherVersion()
	planGroup := adminV1.Group("teaching-plan", "教学计划管理接口")
	{
		planGroup.Register("POST", "/create-draft", api.CreateTeachingPlan, model.AuthTeacher, "创建教学计划草稿")
		planGroup.Register("POST", "/update-draft", api.UpdateTeachingPlan, model.AuthTeacher, "编辑教学计划草稿")
		planGroup.Register("POST", "/delete/:id", api.DeleteTeachingPlan, model.AuthTeacher, "删除教学计划")
		planGroup.Register("GET", "/list", api.GetTeachingPlanList, model.AuthTeacher, "获取教学计划列表")
		planGroup.Register("GET", "/detail/:id", api.GetTeachingPlanDetail, model.AuthTeacher, "获取教学计划详情")
		// planGroup.Register("POST", "/submit-review/:id", api.SubmitForReview, model.AuthTeacher, "提交审核")
		// planGroup.Register("POST", "/approve/:id", api.ApproveTeachingPlan, model.AuthTeacher, "审核教学计划")
		// planGroup.Register("POST", "/reject/:id", api.RejectTeachingPlan, model.AuthTeacher, "驳回教学计划")
		// planGroup.Register("GET", "/reviewing-list", api.GetReviewingPlans, model.AuthTeacher, "获取待审核教学计划")
	}
}
func (api *TeachingPlanAPI) CreateTeachingPlan(c *gin.Context) {
	var req model.ReqTeachingPlanUpdate
	if err := c.ShouldBindJSON(&req); err != nil {
		libs.Error(c, "参数错误")
		return
	}

	userId := c.GetInt64("userId")
	err := planService.CreateTeachingPlan(req, userId)
	if err != nil {
		libs.Error(c, err.Error())
		return
	}

	libs.Success(c, "创建成功", nil)
}
func (api *TeachingPlanAPI) UpdateTeachingPlan(c *gin.Context) {
	var req model.ReqTeachingPlanUpdate
	if err := c.ShouldBindJSON(&req); err != nil {
		libs.Error(c, "参数错误")
		return
	}

	err := planService.UpdateTeachingPlan(req)
	if err != nil {
		libs.Error(c, err.Error())
		return
	}
	libs.Success(c, "更新成功", nil)
}

func (api *TeachingPlanAPI) DeleteTeachingPlan(c *gin.Context) {
	id := cast.ToInt64(c.Param("id"))
	err := planService.DeleteTeachingPlan(id)
	if err != nil {
		libs.Error(c, err.Error())
		return
	}

	libs.Success(c, "删除成功", nil)
}
func (api *TeachingPlanAPI) GetTeachingPlanList(c *gin.Context) {
	var req model.ReqTeachingPlanSearch
	if err := c.ShouldBind(&req); err != nil {
		libs.Error(c, "参数错误")
		return
	}
	res, err := planService.GetTeachingPlanList(c, req)
	if err != nil {
		libs.Error(c, err.Error())
		return
	}
	libs.Success(c, "查询成功", res)
}

// 获取教学计划详情
func (api *TeachingPlanAPI) GetTeachingPlanDetail(c *gin.Context) {
	id := cast.ToInt64(c.Param("id"))
	if id == 0 {
		libs.Error(c, "无效的ID")
		return
	}

	detail, err := planService.GetTeachingPlanDetail(id)
	if err != nil {
		libs.Error(c, err.Error())
		return
	}

	libs.Success(c, "查询成功", detail)
}

// func (api *TeachingPlanAPI) SubmitForReview(c *gin.Context) {
// 	id := cast.ToInt64(c.Param("id"))
// 	err := planService.SubmitForReview(id)
// 	if err != nil {
// 		libs.Error(c, err.Error())
// 		return
// 	}

// 	libs.Success(c, "提交审核成功", nil)
// }
// func (api *TeachingPlanAPI) ApproveTeachingPlan(c *gin.Context) {
// 	id := cast.ToInt64(c.Param("id"))
// 	err := planService.ApproveTeachingPlan(id)
// 	if err != nil {
// 		libs.Error(c, err.Error())
// 		return
// 	}

// 	libs.Success(c, "审核通过并发布成功", nil)
// }

// // RejectTeachingPlan 驳回教学计划审核
// func (api *TeachingPlanAPI) RejectTeachingPlan(c *gin.Context) {
// 	id := cast.ToInt64(c.Param("id"))
// 	if id == 0 {
// 		libs.Error(c, "无效的教学计划ID")
// 		return
// 	}

// 	if err := planService.RejectTeachingPlan(id); err != nil {
// 		libs.Error(c, err.Error())
// 		return
// 	}

// 	libs.Success(c, "教学计划已驳回", nil)
// }
// func (api *TeachingPlanAPI) GetReviewingPlans(c *gin.Context) {
// 	var req model.ReqTeachingPlanSearch
// 	if err := c.ShouldBind(&req); err != nil {
// 		libs.Error(c, "参数错误")
// 		return
// 	}
// 	res, err := planService.GetReviewingPlans(c, req)
// 	if err != nil {
// 		libs.Error(c, err.Error())
// 		return
// 	}

// 	libs.Success(c, "查询成功", res)
// }
