package teacher

import (
	"log/slog"
	"tms/libs"
	"tms/model"
	"tms/services/tech"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

type ClassScheduleAPI struct{}

var scheduleService = tech.NewClassScheduleService()

func init() {
	api := &ClassScheduleAPI{}
	adminV1 := GetTeacherVersion()
	scheduleGroup := adminV1.Group("class-schedule", "课表管理接口")
	{
		scheduleGroup.Register("POST", "/generate", api.GenerateClassSchedule, model.AuthTeacher, "生成课表")
		scheduleGroup.Register("POST", "/update", api.UpdateClassSchedule, model.AuthTeacher, "更新课表")
		scheduleGroup.Register("POST", "/delete/:id", api.DeleteClassSchedule, model.AuthTeacher, "删除课表")
		scheduleGroup.Register("GET", "/list", api.GetClassScheduleList, model.AuthTeacher, "获取课表列表")
		scheduleGroup.Register("GET", "/detail/:id", api.GetClassScheduleDetail, model.AuthTeacher, "获取课表详情")
	}
}

// GenerateClassSchedule 生成课表
func (api *ClassScheduleAPI) GenerateClassSchedule(c *gin.Context) {
	var req model.ReqGenerateClassSchedule
	if err := c.ShouldBindJSON(&req); err != nil {
		slog.Info("生成课表参数错误" + err.Error())
		libs.Error(c, "参数错误")
		return
	}

	userId := c.GetInt64("userId")
	err := scheduleService.GenerateClassSchedule(req, userId)
	if err != nil {
		libs.Error(c, err.Error())
		return
	}

	libs.Success(c, "课表生成成功", nil)
}

// UpdateClassSchedule 更新课表
func (api *ClassScheduleAPI) UpdateClassSchedule(c *gin.Context) {
	var schedule model.ClassSchedule
	if err := c.ShouldBindJSON(&schedule); err != nil {
		libs.Error(c, "参数错误")
		return
	}

	err := scheduleService.UpdateClassSchedule(schedule)
	if err != nil {
		libs.Error(c, err.Error())
		return
	}

	libs.Success(c, "课表更新成功", nil)
}

// DeleteClassSchedule 删除课表
func (api *ClassScheduleAPI) DeleteClassSchedule(c *gin.Context) {
	id := cast.ToInt64(c.Param("id"))
	err := scheduleService.DeleteClassSchedule(id)
	if err != nil {
		libs.Error(c, err.Error())
		return
	}

	libs.Success(c, "课表删除成功", nil)
}

// GetClassScheduleList 获取课表列表
func (api *ClassScheduleAPI) GetClassScheduleList(c *gin.Context) {
	var req model.ReqClassScheduleSearch
	if err := c.ShouldBind(&req); err != nil {
		libs.Error(c, "参数错误")
		return
	}

	res, err := scheduleService.GetClassScheduleList(c, req)
	if err != nil {
		libs.Error(c, err.Error())
		return
	}

	libs.Success(c, "查询成功", res)
}

// GetClassScheduleDetail 获取课表详情
func (api *ClassScheduleAPI) GetClassScheduleDetail(c *gin.Context) {
	id := cast.ToInt64(c.Param("id"))
	detail, err := scheduleService.GetClassScheduleDetail(id)
	if err != nil {
		libs.Error(c, err.Error())
		return
	}

	libs.Success(c, "查询成功", detail)
}
