package teacher

import (
	"tms/libs"
	"tms/model"
	"tms/pkg/db"

	"github.com/gin-gonic/gin"
)

// ReqUsersSearch 搜索请求参数

type UsersAPI struct{}

func init() {
	api := &UsersAPI{}
	adminV1 := GetTeacherVersion()
	usersGroup := adminV1.Group("users", "用户管理接口")
	{
		usersGroup.Register("GET", "/list", api.GetUsersList, model.AuthTeacher, "获取用户列表")
	}
}

// GetUsersList 获取用户列表
func (api *UsersAPI) GetUsersList(c *gin.Context) {
	var req model.ReqUsersSearch
	if err := c.ShouldBind(&req); err != nil {
		libs.Error(c, "参数错误")
		return
	}

	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 || req.PageSize > 100 {
		req.PageSize = 10
	}

	// 调用模型层方法获取用户列表
	users, total, err := model.GetUsersBySysCode(db.DB, req)
	if err != nil {
		libs.Error(c, "查询失败: "+err.Error())
		return
	}

	libs.Success(c, "查询成功", gin.H{
		"list":  users,
		"total": total,
	})
}
