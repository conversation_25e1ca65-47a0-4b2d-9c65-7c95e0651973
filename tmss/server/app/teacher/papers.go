package teacher

import (
	"log"
	"tms/libs"
	"tms/model"
	"tms/services/exam"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

type PaperAPI struct{}

var paperService = exam.NewPaperService()

func init() {
	api := &PaperAPI{}
	adminV1 := GetTeacherVersion()
	paperGroup := adminV1.Group("paper", "试卷管理接口")
	{
		paperGroup.Register("POST", "/create-draft", api.CreatePaperDraft, model.AuthTeacher, "创建试卷草稿")
		paperGroup.Register("POST", "/update-draft", api.UpdatePaperDraft, model.AuthTeacher, "更新试卷草稿")
		paperGroup.Register("POST", "/delete/:id", api.DeletePaper, model.AuthTeacher, "删除试卷")
		paperGroup.Register("POST", "/submit-review/:id", api.SubmitForReview, model.AuthTeacher, "提交试卷审核")
		paperGroup.Register("POST", "/approve/:id", api.ApprovePaper, model.AuthTeacher, "审核试卷")
		paperGroup.Register("POST", "/reject/:id", api.RejectPaper, model.AuthTeacher, "拒绝试卷")
		paperGroup.Register("GET", "/list", api.GetPaperList, model.AuthTeacher, "获取试卷列表")
		paperGroup.Register("GET", "/detail/:id", api.GetPaperDetail, model.AuthTeacher, "获取试卷详情")
		paperGroup.Register("GET", "/reviewing-list", api.GetReviewingPaperList, model.AuthTeacher, "获取待审核试卷列表")
	}
}

// CreatePaperDraft 创建试卷草稿
func (api *PaperAPI) CreatePaperDraft(c *gin.Context) {
	var req model.ReqPaperUpdate

	if err := c.ShouldBindJSON(&req); err != nil {
		libs.Error(c, "参数错误："+err.Error())
		return
	}

	userId := c.GetInt64("userId")
	err := paperService.CreatePaperDraft(req, userId)
	if err != nil {
		libs.Error(c, "创建失败: "+err.Error())
		return
	}
	libs.Success(c, "创建成功", nil)
}

// UpdatePaperDraft 更新试卷草稿
func (api *PaperAPI) UpdatePaperDraft(c *gin.Context) {
	var req model.ReqPaperUpdate

	if err := c.ShouldBindJSON(&req); err != nil {
		log.Printf("error: %v", err.Error())
		libs.Error(c, "参数错误")
		return
	}

	err := paperService.UpdatePaperDraft(req)
	if err != nil {
		libs.Error(c, "更新失败: "+err.Error())
		return
	}
	libs.Success(c, "更新成功", nil)
}

// DeletePaper 删除试卷
func (api *PaperAPI) DeletePaper(c *gin.Context) {
	id := cast.ToInt64(c.Param("id"))
	err := paperService.DeletePaper(id)
	if err != nil {
		libs.Error(c, "删除失败: "+err.Error())
		return
	}

	libs.Success(c, "删除成功", nil)
}

// SubmitForReview 提交试卷审核
func (api *PaperAPI) SubmitForReview(c *gin.Context) {
	id := cast.ToInt64(c.Param("id"))
	err := paperService.SubmitForReview(id)
	if err != nil {
		libs.Error(c, "提交失败: "+err.Error())
		return
	}

	libs.Success(c, "提交审核成功", nil)
}

// ApprovePaper 审核试卷
func (api *PaperAPI) ApprovePaper(c *gin.Context) {
	id := cast.ToInt64(c.Param("id"))
	err := paperService.ApprovePaper(id)
	if err != nil {
		libs.Error(c, "审核失败: "+err.Error())
		return
	}
	libs.Success(c, "审核通过并发布成功", nil)
}
func (api *PaperAPI) RejectPaper(c *gin.Context) {
	id := cast.ToInt64(c.Param("id"))
	err := paperService.RejectPaper(id)
	if err != nil {
		libs.Error(c, "驳回失败: "+err.Error())
		return
	}
	libs.Success(c, "已成功驳回", nil)
}

// GetPaperList 获取试卷列表
func (api *PaperAPI) GetPaperList(c *gin.Context) {
	var req model.ReqPaperSearch
	if err := c.ShouldBind(&req); err != nil {
		libs.Error(c, "参数错误")
		return
	}
	res, err := paperService.GetPaperList(c, req)
	if err != nil {
		libs.Error(c, "查询失败: "+err.Error())
		return
	}
	libs.Success(c, "查询成功", res)
}

// GetReviewingPaperList 获取待审核试卷列表
func (api *PaperAPI) GetReviewingPaperList(c *gin.Context) {
	var req model.ReqPaperSearch
	if err := c.ShouldBind(&req); err != nil {
		libs.Error(c, "参数错误")
		return
	}
	res, err := paperService.GetReviewingPaperList(c, req)
	if err != nil {
		libs.Error(c, "查询失败: "+err.Error())
		return
	}
	libs.Success(c, "查询成功", res)
}
func (api *PaperAPI) GetPaperDetail(c *gin.Context) {
	id := cast.ToInt64(c.Param("id"))
	res, err := paperService.GetPaperDetail(id)
	if err != nil {
		libs.Error(c, "查询失败: "+err.Error())
		return
	}
	libs.Success(c, "查询成功", res)
}
