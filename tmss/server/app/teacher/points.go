package teacher

import (
	"log"
	"tms/libs"
	"tms/model"
	"tms/services/tech"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

type KnowledgePointsAPI struct{}

var knowledgePointsService = tech.NewKnowledgePointsService()

func init() {
	api := &KnowledgePointsAPI{}
	adminV1 := GetTeacherVersion()
	knowledgePointsGroup := adminV1.Group("knowledge-points", "知识点管理接口")
	{
		knowledgePointsGroup.Register("POST", "/create-draft", api.CreateKnowledgePointsDraft, model.AuthTeacher, "创建知识点草稿")
		knowledgePointsGroup.Register("POST", "/update-draft", api.UpdateKnowledgePointsDraft, model.AuthTeacher, "更新知识点草稿")
		knowledgePointsGroup.Register("POST", "/delete/:id", api.DeleteKnowledgePoints, model.AuthTeacher, "删除知识点")
		knowledgePointsGroup.Register("GET", "/list", api.GetKnowledgePointssList, model.AuthTeacher, "获取知识点列表")
		knowledgePointsGroup.Register("GET", "/detail/:id", api.GetKnowledgePointsDetail, model.AuthTeacher, "获取知识点详情")
	}
}

// CreateKnowledgePointsDraft 创建知识点草稿
func (api *KnowledgePointsAPI) CreateKnowledgePointsDraft(c *gin.Context) {
	var req model.KnowledgePoints
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Printf("参数错误: %v", err.Error())
		libs.Error(c, "参数错误")
		return
	}
	userId := c.GetInt64("userId")
	if err := knowledgePointsService.CreateKnowledgePointsDraft(req, userId); err != nil {
		libs.Error(c, err.Error())
		return
	}

	libs.Success(c, "创建成功", nil)
}

// UpdateKnowledgePointsDraft 更新知识点草稿
func (api *KnowledgePointsAPI) UpdateKnowledgePointsDraft(c *gin.Context) {
	var req model.KnowledgePoints
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Printf("参数错误: %v", err.Error())
		libs.Error(c, "参数错误")
		return
	}

	if err := knowledgePointsService.UpdateKnowledgePointsDraft(req); err != nil {
		libs.Error(c, err.Error())
		return
	}

	libs.Success(c, "更新成功", nil)
}

// DeleteKnowledgePoints 删除知识点
func (api *KnowledgePointsAPI) DeleteKnowledgePoints(c *gin.Context) {
	id := cast.ToInt64(c.Param("id"))
	if err := knowledgePointsService.DeleteKnowledgePoints(id); err != nil {
		libs.Error(c, err.Error())
		return
	}

	libs.Success(c, "删除成功", nil)
}

// GetKnowledgePointssList 获取知识点列表
func (api *KnowledgePointsAPI) GetKnowledgePointssList(c *gin.Context) {
	var req model.ReqKnowledgePointsSearch
	if err := c.ShouldBind(&req); err != nil {
		libs.Error(c, "参数错误")
		return
	}

	resp, err := knowledgePointsService.GetKnowledgePointsList(c, req)
	if err != nil {
		libs.Error(c, err.Error())
		return
	}

	libs.Success(c, "查询成功", resp)
}

// GetKnowledgePointsDetail 获取知识点详情
func (api *KnowledgePointsAPI) GetKnowledgePointsDetail(c *gin.Context) {
	id := cast.ToInt64(c.Param("id"))
	if id == 0 {
		libs.Error(c, "无效的知识点ID")
		return
	}

	resp, err := knowledgePointsService.GetKnowledgePointsDetail(id)
	if err != nil {
		libs.Error(c, err.Error())
		return
	}

	libs.Success(c, "查询成功", resp)
}
