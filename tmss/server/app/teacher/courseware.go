package teacher

import (
	"fmt"
	"log"
	"os"
	"path/filepath"
	"time"

	"tms/libs"
	"tms/model"
	"tms/services/tech"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

var coursewareService = tech.NewCoursewareService()

type CoursewareAPI struct{}

func init() {
	api := &CoursewareAPI{}
	adminV1 := GetTeacherVersion()
	coursewareGroup := adminV1.Group("courseware", "课件管理接口")
	{
		coursewareGroup.Register("POST", "/upload", api.UploadCoursewareFile, model.AuthTeacher, "上传课件文件")
		coursewareGroup.Register("POST", "/create-draft", api.CreateCoursewareDraft, model.AuthTeacher, "创建课件草稿")
		coursewareGroup.Register("POST", "/update-draft", api.UpdateCoursewareDraft, model.AuthTeacher, "更新课件草稿")
		coursewareGroup.Register("POST", "/delete/:id", api.DeleteCourseware, model.AuthTeacher, "删除课件")
		// coursewareGroup.Register("POST", "/submit-review/:id", api.SubmitForReview, model.AuthTeacher, "提交课件审核")
		// coursewareGroup.Register("POST", "/approve/:id", api.ApproveCourseware, model.AuthTeacher, "审核课件")
		// coursewareGroup.Register("POST", "/reject/:id", api.RejectCourseware, model.AuthTeacher, "驳回课件审核")
		coursewareGroup.Register("GET", "/list", api.GetCoursewareList, model.AuthTeacher, "获取课件列表")
		//coursewareGroup.Register("GET", "/reviewing-list", api.GetReviewingCoursewareList, model.AuthTeacher, "获取待审核课件列表")
		coursewareGroup.Register("GET", "/published", api.GetCoursewaresHasPublished, model.AuthTeacher, "获取已发布的课件列表")
	}
}

// UploadCoursewareFile 上传课件文件
func (api *CoursewareAPI) UploadCoursewareFile(c *gin.Context) {
	file, err := c.FormFile("file")
	if err != nil {
		libs.Error(c, "文件上传失败: "+err.Error())
		return
	}
	ext := filepath.Ext(file.Filename)
	if ext != ".zip" {
		libs.Error(c, "文件格式错误")
		return
	}

	// 保存文件到服务器
	timeFormat := time.Now().Format("20060102")
	filePath := fmt.Sprintf("./uploads/courseware/%s/%s", timeFormat, file.Filename)
	if err := os.MkdirAll(filepath.Dir(filePath), os.ModePerm); err != nil {
		libs.Error(c, "创建文件夹失败: "+err.Error())
		return
	}
	if err := c.SaveUploadedFile(file, filePath); err != nil {
		libs.Error(c, "保存文件失败: "+err.Error())
		return
	}

	libs.Success(c, "文件上传成功", gin.H{
		"file_name": file.Filename,
		"file_path": filePath,
		"file_type": ext,
	})
}

// CreateCoursewareDraft 创建课件草稿
func (api *CoursewareAPI) CreateCoursewareDraft(c *gin.Context) {
	var req model.Courseware
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Println(err)
		libs.Error(c, "参数错误")
		return
	}
	userId := c.GetInt64("userId")
	err := coursewareService.CreateCoursewareDraft(req, userId)
	if err != nil {
		libs.Error(c, "创建失败: "+err.Error())
		return
	}

	// TODO: 暂时，创建时下载解压, 之后移交至审核通过后触发
	if err := coursewareService.DownloadAndUnzipCourseware([]int64{req.ID}); err != nil {
		libs.Error(c, "解压失败: "+err.Error())
		return
	}

	libs.Success(c, "创建成功", nil)
}

// UpdateCoursewareDraft 更新课件草稿
func (api *CoursewareAPI) UpdateCoursewareDraft(c *gin.Context) {
	var req model.Courseware
	if err := c.ShouldBindJSON(&req); err != nil {
		libs.Error(c, "参数错误")
		return
	}

	err := coursewareService.UpdateCoursewareDraft(req)
	if err != nil {
		libs.Error(c, "更新失败: "+err.Error())
		return
	}

	libs.Success(c, "更新成功", nil)
}

// DeleteCourseware 删除课件
func (api *CoursewareAPI) DeleteCourseware(c *gin.Context) {
	id := cast.ToInt64(c.Param("id"))
	err := coursewareService.DeleteCourseware(id)
	if err != nil {
		libs.Error(c, "删除失败: "+err.Error())
		return
	}

	libs.Success(c, "删除成功", nil)
}

// SubmitForReview 提交课件审核
// func (api *CoursewareAPI) SubmitForReview(c *gin.Context) {
// 	id := cast.ToInt64(c.Param("id"))
// 	err := coursewareService.SubmitForReview(id)
// 	if err != nil {
// 		libs.Error(c, "提交失败: "+err.Error())
// 		return
// 	}

// 	libs.Success(c, "提交审核成功", nil)
// }

// // ApproveCourseware 审核课件
// func (api *CoursewareAPI) ApproveCourseware(c *gin.Context) {
// 	id := cast.ToInt64(c.Param("id"))
// 	err := coursewareService.ApproveCourseware(id)
// 	if err != nil {
// 		libs.Error(c, "审核失败: "+err.Error())
// 		return
// 	}

// 	libs.Success(c, "审核通过并发布成功", nil)
// }

// 驳回课件
// func (api *CoursewareAPI) RejectCourseware(c *gin.Context) {
// 	id := cast.ToInt64(c.Param("id"))
// 	err := coursewareService.RejectCourseware(id)
// 	if err != nil {
// 		libs.Error(c, "驳回失败: "+err.Error())
// 		return
// 	}
// 	libs.Success(c, "驳回成功", nil)
// }

// GetCoursewareList 获取课件列表
func (api *CoursewareAPI) GetCoursewareList(c *gin.Context) {
	var req model.ReqCoursewareSearch
	if err := c.ShouldBind(&req); err != nil {
		libs.Error(c, "参数错误")
		return
	}
	list, err := coursewareService.GetCoursewareList(c, req)
	if err != nil {
		libs.Error(c, "查询失败: "+err.Error())
		return
	}

	libs.Success(c, "查询成功", list)
}

// // GetReviewingCoursewareList 获取待审核课件列表
// func (api *CoursewareAPI) GetReviewingCoursewareList(c *gin.Context) {
// 	var req model.ReqCoursewareSearch
// 	if err := c.ShouldBind(&req); err != nil {
// 		libs.Error(c, "参数错误")
// 		return
// 	}
// 	list, err := coursewareService.GetReviewingCoursewareList(c, req)
// 	if err != nil {
// 		libs.Error(c, "查询失败: "+err.Error())
// 		return
// 	}

// 	libs.Success(c, "查询成功", list)
// }

// GetCoursewaresByCourse 获取指定课程的课件列表（带分页）
func (api *CoursewareAPI) GetCoursewaresHasPublished(c *gin.Context) {

	var req model.ReqCoursewareSearch

	if err := c.ShouldBind(&req); err != nil {
		libs.Error(c, "参数错误："+err.Error())
		return
	}
	list, err := coursewareService.GetCoursewaresHasPublished(c, req)
	if err != nil {
		libs.Error(c, "查询失败: "+err.Error())
		return
	}

	libs.Success(c, "查询成功", list)
}
