package teacher

import (
	"log"
	"tms/libs"
	"tms/model"
	"tms/services/tech"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

type AssignmentAPI struct{}

var assignmentService = tech.NewAssignmentService()

func init() {
	api := &AssignmentAPI{}
	adminV1 := GetTeacherVersion()
	assignmentGroup := adminV1.Group("assignment", "作业管理接口")
	{
		assignmentGroup.Register("POST", "/create-draft", api.CreateAssignmentDraft, model.AuthTeacher, "创建作业草稿")
		assignmentGroup.Register("POST", "/update-draft", api.UpdateAssignmentDraft, model.AuthTeacher, "更新作业草稿")
		assignmentGroup.Register("POST", "/delete/:id", api.DeleteAssignment, model.AuthTeacher, "删除作业")
		assignmentGroup.Register("GET", "/list", api.GetAssignmentsList, model.AuthTeacher, "获取作业列表")
		assignmentGroup.Register("GET", "/detail/:id", api.GetAssignmentDetail, model.AuthTeacher, "获取作业详情")
	}
}

// CreateAssignmentDraft 创建作业草稿
func (api *AssignmentAPI) CreateAssignmentDraft(c *gin.Context) {
	var req model.AssignmentUpdate
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Printf("参数错误: %v", err.Error())
		libs.Error(c, "参数错误")
		return
	}
	userId := c.GetInt64("userId")
	if err := assignmentService.CreateAssignmentDraft(req, userId); err != nil {
		libs.Error(c, err.Error())
		return
	}

	libs.Success(c, "创建成功", nil)
}

// UpdateAssignmentDraft 更新作业草稿
func (api *AssignmentAPI) UpdateAssignmentDraft(c *gin.Context) {
	var req model.AssignmentUpdate
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Printf("参数错误: %v", err.Error())
		libs.Error(c, "参数错误")
		return
	}

	if err := assignmentService.UpdateAssignmentDraft(req); err != nil {
		libs.Error(c, err.Error())
		return
	}

	libs.Success(c, "更新成功", nil)
}

// DeleteAssignment 删除作业
func (api *AssignmentAPI) DeleteAssignment(c *gin.Context) {
	id := cast.ToInt64(c.Param("id"))
	if err := assignmentService.DeleteAssignment(id); err != nil {
		libs.Error(c, err.Error())
		return
	}

	libs.Success(c, "删除成功", nil)
}

// GetAssignmentsList 获取作业列表
func (api *AssignmentAPI) GetAssignmentsList(c *gin.Context) {
	var req model.ReqPage
	if err := c.ShouldBind(&req); err != nil {
		libs.Error(c, "参数错误")
		return
	}
	userId := c.GetInt64("userId")
	if userId == 0 {
		libs.Error(c, "用户不存在")
		return
	}

	resp, err := assignmentService.GetAssignmentList(c, req, userId)
	if err != nil {
		libs.Error(c, err.Error())
		return
	}

	libs.Success(c, "查询成功", resp)
}

// GetAssignmentDetail 获取作业详情
func (api *AssignmentAPI) GetAssignmentDetail(c *gin.Context) {
	id := cast.ToInt64(c.Param("id"))
	if id == 0 {
		libs.Error(c, "无效的作业ID")
		return
	}

	resp, err := assignmentService.GetAssignmentDetail(id)
	if err != nil {
		libs.Error(c, err.Error())
		return
	}

	libs.Success(c, "查询成功", resp)
}
