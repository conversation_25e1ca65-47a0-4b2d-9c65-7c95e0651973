package teacher

import (
	"log"

	"tms/libs"
	"tms/model"
	"tms/services/tech"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

// RevisionAPI 修订记录管理API结构体
type RevisionAPI struct{}

func init() {
	revisionAPI := &RevisionAPI{}

	adminV1 := GetTeacherVersion()
	revisionGroup := adminV1.Group("revision", "修订记录管理接口")
	{
		revisionGroup.Register("GET", "/list", revisionAPI.GetRevisionList, model.AuthTeacher, "获取修订记录列表")
		revisionGroup.Register("POST", "/create", revisionAPI.CreateRevision, model.AuthTeacher, "创建修订记录")
		revisionGroup.Register("POST", "/update", revisionAPI.UpdateRevision, model.AuthTeacher, "更新修订记录")
		revisionGroup.Register("POST", "/delete/:id", revisionAPI.DeleteRevision, model.AuthTeacher, "删除修订记录")
		revisionGroup.Register("GET", "/detail/:id", revisionAPI.GetRevisionDetail, model.AuthTeacher, "获取修订记录详情")
		revisionGroup.Register("POST", "/publish/:id", revisionAPI.PublishRevision, model.AuthTeacher, "发布修订记录")
	}
}

var revisionService = tech.NewRevisionService()

// CreateRevision 创建修订记录
func (api *RevisionAPI) CreateRevision(c *gin.Context) {
	var req model.RevisionRecord
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Printf("Failed to bind JSON: %v", err)
		libs.Error(c, "参数错误: "+err.Error())
		return
	}

	userId := c.GetInt64("userId")
	if userId == 0 {
		libs.Error(c, "无效的用户ID")
		return
	}

	if err := revisionService.CreateRevision(req.ModuleKey, req.OriginalID, userId, req.Notes, req.Changes); err != nil {
		libs.Error(c, err.Error())
		return
	}

	libs.Success(c, "创建修订记录成功", nil)
}

// UpdateRevision 更新修订记录
func (api *RevisionAPI) UpdateRevision(c *gin.Context) {
	var req model.RevisionRecord

	if err := c.ShouldBindJSON(&req); err != nil {
		libs.Error(c, "参数错误: "+err.Error())
		return
	}
	if req.ID <= 0 {
		libs.Error(c, "无效的修订记录ID")
		return
	}
	if err := revisionService.UpdateRevision(req.ID, req.Notes, req.Changes); err != nil {
		libs.Error(c, err.Error())
		return
	}

	libs.Success(c, "更新修订记录成功", nil)
}

// DeleteRevision 删除修订记录
func (api *RevisionAPI) DeleteRevision(c *gin.Context) {
	id := cast.ToInt64(c.Param("id"))

	if err := revisionService.DeleteRevision(id); err != nil {
		libs.Error(c, "删除修订记录失败: "+err.Error())
		return
	}

	libs.Success(c, "删除修订记录成功", nil)
}

// GetRevisionList 获取修订记录列表
func (api *RevisionAPI) GetRevisionList(c *gin.Context) {
	var req model.ReqRevisionSearch
	if err := c.ShouldBindQuery(&req); err != nil {
		libs.Error(c, "参数错误: "+err.Error())
		return
	}

	list, total, err := revisionService.GetRevisionList(req)
	if err != nil {
		libs.Error(c, err.Error())
		return
	}

	response := map[string]interface{}{
		"list":  list,
		"total": total,
	}
	libs.Success(c, "获取修订记录列表成功", response)
}

// GetRevisionDetail 获取修订记录详情
func (api *RevisionAPI) GetRevisionDetail(c *gin.Context) {
	id := cast.ToInt64(c.Param("id"))
	if id == 0 {
		libs.Error(c, "无效的修订记录ID")
		return
	}

	revision, err := revisionService.GetRevisionDetail(id)
	if err != nil {
		libs.Error(c, err.Error())
		return
	}

	libs.Success(c, "获取修订记录详情成功", revision)
}

// PublishRevision 发布修订记录
func (api *RevisionAPI) PublishRevision(c *gin.Context) {
	id := cast.ToInt64(c.Param("id"))
	if id == 0 {
		libs.Error(c, "无效的ID")
		return
	}

	userId := c.GetInt64("userId")
	if userId == 0 {
		libs.Error(c, "无效的用户ID")
		return
	}

	newID, err := revisionService.PublishRevision(id, userId)
	if err != nil {
		libs.Error(c, err.Error())
		return
	}

	libs.Success(c, "发布修订记录成功", newID)
}
