package teacher

import (
	"errors"
	"log"
	"log/slog"
	"tms/libs"
	"tms/model"
	"tms/pkg/db"
	"tms/services/auth"
	"tms/services/lms"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

// ScormRequestWithSessionID 是包含会话ID的通用请求结构体
type ScormRequestWithSessionID struct {
	SessionID uint `json:"session_id"`
}

// SetValueRequest 包含设置 CMI 值的请求数据
type SetValueRequest struct {
	SessionID uint   `json:"session_id"`
	Key       string `json:"key"`
	Value     string `json:"value"`
}

// GetValueRequest 包含获取 CMI 值的请求数据
type GetValueRequest struct {
	SessionID uint   `json:"session_id"`
	Key       string `json:"key"`
}

type LMSAPI struct{}

var lmsService *lms.ScormService

func init() {
	lmsAPI := &LMSAPI{}
	lmsService = lms.NewScormService()
	teachersV1 := GetTeacherVersion()
	lmsGroup := teachersV1.Group("lms", "LMS接口")
	scormGroup := lmsGroup.Group("scorm", "scorm接口")
	{
		scormGroup.Register("POST", "/initialize", lmsAPI.Initialize, model.AuthTeacher, "initialize")
		scormGroup.Register("POST", "/set-value", lmsAPI.SetValue, model.AuthTeacher, "setValue")
		scormGroup.Register("GET", "/get-value", lmsAPI.GetValue, model.AuthTeacher, "getValue")
		scormGroup.Register("POST", "/terminate", lmsAPI.Terminate, model.AuthTeacher, "terminate")
		scormGroup.Register("POST", "/commit", lmsAPI.Commit, model.AuthTeacher, "commit")
		scormGroup.Register("GET", "/get-last-error", lmsAPI.GetLastError, model.AuthTeacher, "getLastError")
		scormGroup.Register("GET", "/get-error-string", lmsAPI.GetErrorString, model.AuthTeacher, "getErrorString")
		scormGroup.Register("GET", "/get/diagnostic", lmsAPI.GetDiagnostic, model.AuthTeacher, "getDiagnostic")
		scormGroup.Register("POST", "/navigate", lmsAPI.Navigate, model.AuthTeacher, "navigate")
	}
}

// getScormSession 从数据库加载 SCORM 会话
func getScormSession(sessionID uint) (*model.ScormSessions, error) {
	if sessionID == 0 {
		return nil, errors.New("无效的会话ID")
	}
	var session model.ScormSessions
	result := db.DB.First(&session, sessionID)
	if result.Error != nil {
		return nil, errors.New("SCORM会话未找到")
	}
	return &session, nil
}

// NavigateRequest 包含导航请求的数据
type NavigateRequest struct {
	SessionID uint   `json:"session_id"`
	Request   string `json:"request"` // e.g., "start", "continue", "previous", "choice"
}

func (s *LMSAPI) Navigate(c *gin.Context) {
	var req NavigateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		libs.Error(c, "参数错误")
		return
	}

	session, err := getScormSession(req.SessionID)
	if err != nil {
		libs.Error(c, err.Error())
		return
	}

	nextActivity, err := lmsService.Navigate(req.Request, session)
	if err != nil {
		libs.Error(c, "导航失败: "+err.Error())
		return
	}

	// 保存导航后更新的会话状态
	if err := db.DB.Save(session).Error; err != nil {
		libs.Error(c, "无法保存会话状态")
		return
	}

	if nextActivity == nil {
		// 课程已结束
		libs.Success(c, "课程已结束", gin.H{"course_completed": true})
		return
	}

	// 返回下一个活动的信息
	libs.Success(c, "导航成功", gin.H{
		"identifier": nextActivity.Identifier,
		"href":       nextActivity.Resource.Href,
		"title":      nextActivity.Title,
	})
}

func (s *LMSAPI) Initialize(c *gin.Context) {
	var req struct {
		ScoID uint `json:"sco_id"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		libs.Error(c, "参数错误")
		return
	}

	// 从 Gin Context 中获取当前登录的用户信息
	claims, err := auth.GetUserClaims(c)
	if err != nil {
		log.Printf("Fail get use claims")
		libs.Error(c, "获取用户信息失败")
		return
	}

	session, err := lmsService.Initialize(c.Request.Context(), claims.ID, req.ScoID)
	if err != nil {
		libs.Error(c, "初始化失败")
		return
	}

	libs.Success(c, "初始化成功", gin.H{"session_id": session.ID})
}

func (s *LMSAPI) SetValue(c *gin.Context) {
	var req SetValueRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		libs.Error(c, "参数错误")
		return
	}

	session, err := getScormSession(req.SessionID)
	if err != nil {
		libs.Error(c, err.Error())
		return
	}

	if err := lmsService.SetValue(session, req.Key, req.Value); err != nil {
		libs.Error(c, "保存失败")
		return
	}
	libs.Success(c, "保存成功", nil)
}

func (s *LMSAPI) GetValue(c *gin.Context) {
	var req GetValueRequest
	req.SessionID = cast.ToUint(c.Query("session_id"))
	req.Key = c.Query("key")

	session, err := getScormSession(req.SessionID)
	if err != nil {
		libs.Error(c, err.Error())
		return
	}

	value, err := lmsService.GetValue(session, req.Key)
	if err != nil {
		slog.Error("GetValue 失败", slog.Any("err", err))
		libs.Error(c, "获取失败")
		return
	}
	libs.Success(c, "获取成功", value)
}

func (s *LMSAPI) Terminate(c *gin.Context) {
	var req ScormRequestWithSessionID
	if err := c.ShouldBindJSON(&req); err != nil {
		libs.Error(c, "参数错误")
		return
	}

	session, err := getScormSession(req.SessionID)
	if err != nil {
		libs.Error(c, err.Error())
		return
	}

	if err := lmsService.Terminate(session); err != nil {
		libs.Error(c, "终止失败")
		return
	}
	libs.Success(c, "终止成功", nil)
}

func (s *LMSAPI) Commit(c *gin.Context) {
	var req ScormRequestWithSessionID
	if err := c.ShouldBindJSON(&req); err != nil {
		libs.Error(c, "参数错误")
		return
	}

	session, err := getScormSession(req.SessionID)
	if err != nil {
		libs.Error(c, err.Error())
		return
	}

	if err := lmsService.Commit(session); err != nil {
		libs.Error(c, "提交失败")
		return
	}
	libs.Success(c, "提交成功", nil)
}

func (s *LMSAPI) GetLastError(c *gin.Context) {
	// 前端SCORM调用: API_1484_11.GetLastError()
	// 含义: 返回上次API调用产生的错误代码。
	// 作用: 课件在每次API调用后，通常会立即调用此函数来检查操作是否成功，并获取错误代码。
	// 后端处理:
	// 1. 返回上次API调用（如Initialize, SetValue, GetValue, Terminate, Commit）产生的错误代码。
	// 2. 如果上次调用成功，返回 "0" (No Error)。

	sessionID := cast.ToUint(c.Query("session_id"))
	errorCode := lmsService.GetLastError(sessionID)
	libs.Success(c, "获取错误代码成功", errorCode)
}

func (s *LMSAPI) GetErrorString(c *gin.Context) {
	// 前端SCORM调用: API_1484_11.GetErrorString(errorCode)
	// 含义: 返回指定错误代码的简短描述字符串。
	// 作用: 课件根据GetLastError返回的错误代码，调用此函数获取可读的错误信息，用于调试或显示给学员。
	// 后端处理:
	// 1. 接收前端传入的错误代码。
	// 2. 根据错误代码返回对应的简短错误描述字符串。
	errorCode := c.Query("errorCode")
	errorString := lmsService.GetErrorString(errorCode)
	libs.Success(c, "获取错误字符串成功", errorString)
}

func (s *LMSAPI) GetDiagnostic(c *gin.Context) {
	// 前端SCORM调用: API_1484_11.GetDiagnostic(errorCode)
	// 含义: 返回指定错误代码的详细诊断信息。
	// 作用: 提供比GetErrorString更详细的错误信息，通常用于深入调试。
	// 后端处理:
	// 1. 接收前端传入的错误代码。
	// 2. 根据错误代码返回对应的详细诊断信息。
	errorCode := c.Query("errorCode")
	diagnostic := lmsService.GetDiagnostic(errorCode)
	libs.Success(c, "获取诊断信息成功", diagnostic)
}
