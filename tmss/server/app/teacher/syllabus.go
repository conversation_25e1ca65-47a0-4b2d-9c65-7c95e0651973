package teacher

import (
	"log"

	"tms/libs"
	"tms/model"
	"tms/services/tech"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

// SyllabusAPI 教学大纲管理API结构体
type SyllabusAPI struct{}

func init() {
	api := &SyllabusAPI{}

	adminV1 := GetTeacherVersion()
	syllabusGroup := adminV1.Group("syllabus", "教学大纲管理接口")
	{
		syllabusGroup.Register("GET", "/list", api.GetSyllabusList, model.AuthTeacher, "获取大纲列表")
		syllabusGroup.Register("GET", "/detail/:id", api.GetSyllabusDetail, model.AuthTeacher, "获取大纲详情")
		syllabusGroup.Register("POST", "/create-draft", api.CreateDraft, model.AuthTeacher, "创建草稿")
		syllabusGroup.Register("POST", "/update-draft", api.UpdateDraft, model.AuthTeacher, "编辑草稿")
		syllabusGroup.Register("POST", "/publish/:id", api.PublishDraft, model.AuthTeacher, "发布草稿")
		syllabusGroup.Register("POST", "/delete-draft/:id", api.DeleteDraft, model.AuthTeacher, "删除草稿")

		// syllabusGroup.Register("GET", "/revision/list", api.GetSyllabusRevisonList, model.AuthTeacher, "获取修订的大纲列表")
		// syllabusGroup.Register("POST", "/revision/create", api.CreateRevision, model.AuthTeacher, "创建修订")
		// syllabusGroup.Register("POST", "/revision/update", api.UpdateRevision, model.AuthTeacher, "更新修订内容")
		// syllabusGroup.Register("POST", "/revision/publish/:rev_id", api.PublishRevision, model.AuthTeacher, "发布修订版本")
		// syllabusGroup.Register("POST", "/revision/delete/:rev_id", api.DeleteRevision, model.AuthTeacher, "删除修订版本")

		// syllabusGroup.Register("GET", "/reviewing/list", api.GetReviewingSyllabusList, model.AuthTeacher, "获取审查中的大纲列表")
		// syllabusGroup.Register("POST", "/approve/:id", api.ApproveReview, model.AuthTeacher, "审核大纲并发布")
		// syllabusGroup.Register("POST", "/reject/:id", api.RejectReviw, model.AuthTeacher, "驳回大纲修订")
		// syllabusGroup.Register("GET", "/textbooks/:id", api.GetTextbooksBySyllabusID, model.AuthTeacher, "根据大纲ID获取教材列表")

	}
}

var syllabusService = tech.NewSyllabusService()

// GetSyllabusList 获取大纲列表（支持分页、搜索、状态过滤，并关联最新修订）
func (api *SyllabusAPI) GetSyllabusList(c *gin.Context) {
	var req model.ReqSyllabusSearch
	if err := c.ShouldBindQuery(&req); err != nil {
		libs.Error(c, "参数错误")
		return
	}

	res, err := syllabusService.GetSyllabusList(c, req)
	if err != nil {
		libs.Error(c, "获取大纲列表失败: "+err.Error())
		return
	}
	libs.Success(c, "获取大纲列表成功", res)
}

// GetReviewingSyllabusList 获取正在审查中的大纲列表（审计专用）
// func (api *SyllabusAPI) GetReviewingSyllabusList(c *gin.Context) {
// 	var req model.ReqSyllabusSearch
// 	if err := c.ShouldBindQuery(&req); err != nil {
// 		libs.Error(c, "参数错误")
// 		return
// 	}
// 	req.Status = model.SyllabusStatusReviewing

// 	list, total, err := syllabusService.GetSyllabusList(c, req)
// 	if err != nil {
// 		libs.Error(c, "获取大纲列表失败: "+err.Error())
// 		return
// 	}
// 	libs.Success(c, "获取大纲列表成功", gin.H{
// 		"list":  list,
// 		"total": total,
// 	})
// }

// GetSyllabusRevisonList 获取修订的大纲列表
// func (api *SyllabusAPI) GetSyllabusRevisonList(c *gin.Context) {
// 	var req model.ReqSyllabusRevisionSearch
// 	if err := c.ShouldBindQuery(&req); err != nil {
// 		libs.Error(c, "参数错误")
// 		return
// 	}
// 	list, total, err := syllabusService.GetSyllabusRevisionList(req)
// 	if err != nil {
// 		libs.Error(c, "获取修订列表失败: "+err.Error())
// 		return
// 	}
// 	libs.Success(c, "获取修订列表成功", gin.H{
// 		"list":  list,
// 		"total": total,
// 	})
// }

// ApproveReview 审核大纲：将状态从 reviewing 改为 published，并处理修订逻辑
// func (api *SyllabusAPI) ApproveReview(c *gin.Context) {
// 	id := cast.ToInt64(c.Param("id"))
// 	err := syllabusService.ApproveReview(id)
// 	if err != nil {
// 		libs.Error(c, "审核失败: "+err.Error())
// 		return
// 	}
// 	// 返回成功响应
// 	libs.Success(c, "审核成功", nil)
// }
// func (api *SyllabusAPI) RejectReviw(c *gin.Context) {
// 	id := cast.ToInt64(c.Param("id"))
// 	err := syllabusService.RejectReview(id)
// 	if err != nil {
// 		libs.Error(c, "驳回失败: "+err.Error())
// 		return
// 	}
// 	// 返回成功响应
// 	libs.Success(c, "驳回成功", nil)
// }

// CreateDraft 创建草稿
func (api *SyllabusAPI) CreateDraft(c *gin.Context) {
	var req model.ReqSyllabusCreate
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Printf("Failed to bind JSON: %v", err)
		libs.Error(c, "参数错误")
		return
	}
	userId := c.GetInt64("userId")
	err := syllabusService.CreateDraft(req, userId)
	if err != nil {
		libs.Error(c, err.Error())
		return
	}
	libs.Success(c, "创建草稿成功", nil)
}

// UpdateDraft 编辑草稿
func (api *SyllabusAPI) UpdateDraft(c *gin.Context) {
	var req model.ReqSyllabusCreate
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Printf("Failed to bind JSON: %v", err)
		libs.Error(c, "参数错误")
		return
	}
	userId := c.GetInt64("userId")
	err := syllabusService.UpdateDraft(req, userId)
	if err != nil {
		libs.Error(c, "更新失败: "+err.Error())
		return
	}
	libs.Success(c, "更新草稿成功", nil)
}

// PublishDraft 发布草稿
func (api *SyllabusAPI) PublishDraft(c *gin.Context) {
	id := cast.ToInt64(c.Param("id"))
	err := syllabusService.PublishDraft(id)
	if err != nil {
		libs.Error(c, "发布失败: "+err.Error())
		return
	}

	libs.Success(c, "发布成功", nil)
}

// DeleteDraft 删除草稿
func (api *SyllabusAPI) DeleteDraft(c *gin.Context) {
	id := cast.ToInt64(c.Param("id"))
	err := syllabusService.DeleteDraft(id)
	if err != nil {
		libs.Error(c, "删除草稿失败: "+err.Error())
		return
	}
	libs.Success(c, "删除草稿成功", nil)
}
func (api *SyllabusAPI) GetSyllabusDetail(c *gin.Context) {
	id := cast.ToInt64(c.Param("id"))
	detail, err := syllabusService.GetSyllabusDetail(id)
	if err != nil {
		libs.Error(c, "获取大纲详情失败: "+err.Error())
		return
	}
	libs.Success(c, "获取大纲详情成功", detail)
}

// CreateRevision 创建修订版本 原大纲状态不变，新大纲状态为修订
// func (api *SyllabusAPI) CreateRevision(c *gin.Context) {
// 	var req model.ReqSyllabusRevision
// 	if err := c.ShouldBindJSON(&req); err != nil {
// 		log.Printf("Failed to bind JSON: %v", err)
// 		libs.Error(c, "参数错误")
// 		return
// 	}
// 	userId := c.GetInt64("userId")
// 	err := syllabusService.CreateRevision(req, userId)
// 	if err != nil {
// 		libs.Error(c, "创建修订失败: "+err.Error())
// 		return
// 	}
// 	libs.Success(c, "创建修订成功", nil)
// }

// // UpdateRevision 更新修订内容
// func (api *SyllabusAPI) UpdateRevision(c *gin.Context) {
// 	var req model.ReqSyllabusRevision
// 	if err := c.ShouldBindJSON(&req); err != nil {
// 		libs.Error(c, "参数错误")
// 		return
// 	}
// 	userId := c.GetInt64("userId")
// 	err := syllabusService.UpdateRevision(req, userId)
// 	if err != nil {
// 		libs.Error(c, "更新修订失败: "+err.Error())
// 		return
// 	}

// 	libs.Success(c, "更新修订成功", nil)
// }

// // PublishRevision 发布修订版本
// func (api *SyllabusAPI) PublishRevision(c *gin.Context) {
// 	id := cast.ToInt64(c.Param("rev_id"))
// 	err := syllabusService.PublishRevision(id)
// 	if err != nil {
// 		libs.Error(c, "发布修订失败: "+err.Error())
// 		return
// 	}

// 	libs.Success(c, "发布修订成功", nil)
// }

// // DeleteRevision 删除修订版本
// func (api *SyllabusAPI) DeleteRevision(c *gin.Context) {
// 	id := cast.ToInt64(c.Param("rev_id"))
// 	err := syllabusService.DeleteRevision(id)
// 	if err != nil {
// 		libs.Error(c, "删除修订失败: "+err.Error())
// 		return
// 	}
// 	libs.Success(c, "删除修订成功", nil)
// }

// GetTextbooksBySyllabusID 获取某个大纲关联的教材列表
// func (api *SyllabusAPI) GetTextbooksBySyllabusID(c *gin.Context) {
// 	id := cast.ToInt64(c.Param("id"))
// 	textbooks, err := syllabusService.GetTextbooksBySyllabusID(id)
// 	if err != nil {
// 		libs.Error(c, err.Error())
// 		return
// 	}

//		libs.Success(c, "获取教材列表成功", textbooks)
//	}
