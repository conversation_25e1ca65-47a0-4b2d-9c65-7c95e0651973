package teacher

import (
	"log"

	"tms/libs"
	"tms/model"
	"tms/services/tech"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

// MajorAPI 专业管理API结构体
type MajorAPI struct{}

var majorService = tech.NewMajorService()

func init() {
	majorAPI := &MajorAPI{}

	adminV1 := GetTeacherVersion()
	majorGroup := adminV1.Group("major", "专业管理接口")
	{
		majorGroup.Register("GET", "/list", majorAPI.GetMajorList, model.AuthTeacher, "获取专业列表")
		majorGroup.Register("POST", "/create", majorAPI.CreateMajor, model.AuthTeacher, "创建专业")
		majorGroup.Register("POST", "/update", majorAPI.UpdateMajor, model.AuthTeacher, "更新专业信息")
		majorGroup.Register("POST", "/delete/:id", majorAPI.DeleteMajor, model.AuthTeacher, "删除专业")
	}
}

// GetMajorList 获取专业列表
func (api *MajorAPI) GetMajorList(c *gin.Context) {
	var req model.ReqMajorSearch
	if err := c.ShouldBind(&req); err != nil {
		libs.Error(c, "参数错误")
		return
	}

	tree, err := majorService.GetMajorList(req.Name, req.MajorID)
	if err != nil {
		libs.Error(c, err.Error())
		return
	}

	libs.Success(c, "获取专业列表成功", tree)
}

// CreateMajor 创建专业
func (api *MajorAPI) CreateMajor(c *gin.Context) {
	var req model.Majors
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Printf("Failed to bind JSON: %v", err)
		libs.Error(c, "参数错误")
		return
	}

	userId := c.GetInt64("userId")
	if err := majorService.CreateMajor(&req, userId); err != nil {
		libs.Error(c, err.Error())
		return
	}

	libs.Success(c, "创建专业成功", nil)
}

// UpdateMajor 更新专业信息
func (api *MajorAPI) UpdateMajor(c *gin.Context) {
	var req model.Majors
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Printf("Failed to bind JSON: %v", err)
		libs.Error(c, "参数错误")
		return
	}

	if err := majorService.UpdateMajor(&req); err != nil {
		libs.Error(c, err.Error())
		return
	}

	libs.Success(c, "更新专业成功", nil)
}

// DeleteMajor 删除专业
func (api *MajorAPI) DeleteMajor(c *gin.Context) {
	id := cast.ToInt64(c.Param("id"))
	if err := majorService.DeleteMajor(id); err != nil {
		libs.Error(c, err.Error())
		return
	}

	libs.Success(c, "删除专业成功", nil)
}
