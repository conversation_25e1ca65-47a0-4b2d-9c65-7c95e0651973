package teacher

import (
	"tms/libs"
	"tms/model"
	"tms/services/tech"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

type CoursesAPI struct{}

var coursesService = tech.NewCoursesService()

func init() {
	api := &CoursesAPI{}
	adminV1 := GetTeacherVersion()
	coursesGroup := adminV1.Group("courses", "课程管理接口")
	{
		coursesGroup.Register("POST", "/create-draft", api.CreateCourseDraft, model.AuthTeacher, "创建课程草稿")
		coursesGroup.Register("POST", "/update-draft", api.UpdateCourseDraft, model.AuthTeacher, "编辑课程草稿")
		coursesGroup.Register("POST", "/delete/:id", api.DeleteCourse, model.AuthTeacher, "删除课程")
		coursesGroup.Register("GET", "/list", api.GetCoursesList, model.AuthTeacher, "获取课程列表")
		coursesGroup.Register("GET", "/detail/:id", api.GetCourseDetail, model.AuthTeacher, "获取课程详情")
	}
}

// CreateCourseDraft 创建课程草稿
func (api *CoursesAPI) CreateCourseDraft(c *gin.Context) {
	var req model.ReqCoursesUpdate
	if err := c.ShouldBindJSON(&req); err != nil {
		libs.Error(c, "参数错误")
		return
	}
	userId := c.GetInt64("userId")
	if err := coursesService.CreateCourseDraft(req, userId); err != nil {
		libs.Error(c, err.Error())
		return
	}

	libs.Success(c, "创建成功", req)
}

// UpdateCourseDraft 更新课程草稿
func (api *CoursesAPI) UpdateCourseDraft(c *gin.Context) {
	var req model.ReqCoursesUpdate
	if err := c.ShouldBindJSON(&req); err != nil {
		libs.Error(c, "参数错误")
		return
	}

	if err := coursesService.UpdateCourseDraft(req); err != nil {
		libs.Error(c, err.Error())
		return
	}

	libs.Success(c, "更新成功", nil)
}

// DeleteCourse 删除课程
func (api *CoursesAPI) DeleteCourse(c *gin.Context) {
	id := cast.ToInt64(c.Param("id"))
	if err := coursesService.DeleteCourse(id); err != nil {
		libs.Error(c, err.Error())
		return
	}

	libs.Success(c, "删除成功", nil)
}

// GetCoursesList 获取课程列表
func (api *CoursesAPI) GetCoursesList(c *gin.Context) {
	var req model.ReqCoursesSearch
	if err := c.ShouldBind(&req); err != nil {
		libs.Error(c, "参数错误")
		return
	}

	resp, err := coursesService.GetCoursesList(c, req)
	if err != nil {
		libs.Error(c, err.Error())
		return
	}

	libs.Success(c, "查询成功", resp)
}

// 获取单个课程详情
func (api *CoursesAPI) GetCourseDetail(c *gin.Context) {
	id := cast.ToInt64(c.Param("id"))
	if id == 0 {
		libs.Error(c, "无效的课程ID")
		return
	}

	resp, err := coursesService.GetCourseDetailByID(id)
	if err != nil {
		libs.Error(c, err.Error())
		return
	}

	libs.Success(c, "查询成功", resp)
}
