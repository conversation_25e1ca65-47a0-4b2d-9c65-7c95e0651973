package admin

import (
	"log"
	"tms/libs"
	"tms/model"
	"tms/services/user"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

// RoleAPI 角色管理API结构体
type RoleAPI struct {
	userService *user.UserService
}

// NewRoleAPI 创建RoleAPI实例
func NewRoleAPI() *RoleAPI {
	return &RoleAPI{
		userService: user.NewUserService(),
	}
}

func init() {
	roleAPI := NewRoleAPI()
	AdminV1 := GetAdminVersion()
	roleV1 := AdminV1.Group("roles", "角色管理接口")
	{
		roleV1.Register("GET", "/list", roleAPI.GetRoles, model.AuthAdmin, "获取所有角色")
		roleV1.Register("POST", "/create", roleAPI.CreateRole, model.AuthAdmin, "创建新角色")
		roleV1.Register("POST", "/update", roleAPI.UpdateRole, model.AuthAdmin, "更新角色")
		roleV1.Register("POST", "/delete/:id", roleAPI.DeleteRole, model.AuthAdmin, "删除角色")
		roleV1.Register("GET", "/rules/:role_code/:sys_code", roleAPI.GetRules, model.AuthAdmin, "获取角色权限")
		roleV1.Register("POST", "/rules", roleAPI.SetRules, model.AuthAdmin, "设置角色权限")
		roleV1.Register("GET", "/data-configs/list", roleAPI.ListConfigs, model.AuthAdmin, "获取数据权限配置列表")
		roleV1.Register("GET", "/data-configs/role", roleAPI.GetDataConfig, model.AuthAdmin, "根据角色获取配置列表")
		roleV1.Register("POST", "/data-configs/create", roleAPI.AddConfig, model.AuthAdmin, "创建数据权限配置")
		roleV1.Register("POST", "/data-configs/update", roleAPI.UpdateConfig, model.AuthAdmin, "更新数据权限配置")
		roleV1.Register("POST", "/data-configs/delete/:id", roleAPI.DeleteConfig, model.AuthAdmin, "删除数据权限配置")
		roleV1.Register("POST", "/data-configs/set", roleAPI.SetDataConfig, model.AuthAdmin, "设置角色数据权限配置")

	}
}
func (api *RoleAPI) GetRules(c *gin.Context) {
	roleCode := c.Param("role_code")
	sysCode := c.Param("sys_code")
	ruleTree, checkeds := api.userService.GetApiList(roleCode, sysCode)
	libs.Success(c, "获取角色成功", gin.H{
		"rules":    ruleTree,
		"checkeds": checkeds,
	})

}
func (api *RoleAPI) SetRules(c *gin.Context) {
	var req user.ReqRules
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Printf("Failed to bind JSON: %v", err)
		libs.Error(c, "参数错误")
		return
	}
	err := api.userService.SetApiList(req)
	if err != nil {
		libs.Error(c, err.Error())
		return
	}
	libs.Success(c, "设置角色权限成功", nil)
}

// GetRoles 获取所有角色
func (api *RoleAPI) GetRoles(c *gin.Context) {
	roles, err := api.userService.GetRoles()
	if err != nil {
		libs.Error(c, err.Error())
		return
	}
	libs.Success(c, "获取所有角色成功", roles)
}

// CreateRole 创建新角色
func (api *RoleAPI) CreateRole(c *gin.Context) {
	var req model.Roles
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Printf("Failed to bind JSON: %v", err)
		libs.Error(c, "参数错误")
		return
	}
	createdRole, err := api.userService.CreateRole(&req)
	if err != nil {
		libs.Error(c, err.Error())
		return
	}
	libs.Success(c, "创建角色成功", createdRole)
}

// UpdateRole 更新角色
func (api *RoleAPI) UpdateRole(c *gin.Context) {
	var req model.Roles
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Printf("Failed to bind JSON: %v", err)
		libs.Error(c, "参数错误")
		return
	}

	updatedRole, err := api.userService.UpdateRole(req.ID, &req)
	if err != nil {
		libs.Error(c, err.Error())
		return
	}
	libs.Success(c, "更新角色成功", updatedRole)
}

// DeleteRole 删除角色
func (api *RoleAPI) DeleteRole(c *gin.Context) {
	roleID := cast.ToInt64(c.Param("id"))
	err := api.userService.DeleteRole(roleID)
	if err != nil {
		libs.Error(c, err.Error())
		return
	}
	libs.Success(c, "删除角色成功", nil)
}

// ListConfigs 获取数据权限配置列表
func (api *RoleAPI) ListConfigs(c *gin.Context) {
	page := cast.ToInt(c.DefaultQuery("page", "1"))
	pageSize := cast.ToInt(c.DefaultQuery("pageSize", "10"))
	search := c.Query("search")

	configs, total, err := api.userService.DataConfigList(page, pageSize, "", search)
	if err != nil {
		libs.Error(c, "获取配置列表失败")
		return
	}
	libs.Success(c, "获取配置列表成功", gin.H{
		"list":     configs,
		"total":    total,
		"codelist": model.RoleDataCodeList,
		"page":     page,
	})
}

// GetDataConfig 根据角色获取配置列表
func (api *RoleAPI) GetDataConfig(c *gin.Context) {
	page := cast.ToInt(c.DefaultQuery("page", "1"))
	pageSize := cast.ToInt(c.DefaultQuery("pageSize", "10"))
	search := c.Query("search")
	roleCode := c.Query("role_code")
	if roleCode == "" {
		libs.Error(c, "参数错误: role_code 必填")
		return
	}
	sysCode := c.Query("sys_code")
	if sysCode == "" {
		libs.Error(c, "参数错误: sys_code 必填")
		return
	}

	ids, list, total, err := api.userService.GetDataConfig(roleCode, sysCode, page, pageSize, search)
	if err != nil {
		libs.Error(c, "获取配置列表失败")
		return
	}
	libs.Success(c, "获取配置列表成功", gin.H{
		"ids":   ids,
		"list":  list,
		"total": total,
	})
}

// DeleteConfig 删除角色数据权限配置
func (api *RoleAPI) DeleteConfig(c *gin.Context) {
	id := cast.ToInt64(c.Param("id"))
	if id == 0 {
		libs.Error(c, "参数错误")
		return
	}

	err := api.userService.DeleteDataConfig(id)
	if err != nil {
		libs.Error(c, err.Error())
		return
	}

	libs.Success(c, "删除配置成功", nil)
}

// AddConfig 创建数据权限配置
func (api *RoleAPI) AddConfig(c *gin.Context) {
	var req model.RoleDataConfig
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Printf("Failed to bind JSON: %v", err)
		libs.Error(c, "参数错误")
		return
	}
	err := api.userService.CreateDataConfig(req)
	if err != nil {
		libs.Error(c, err.Error())
		return
	}
	libs.Success(c, "创建配置成功", nil)
}

// UpdateConfig 更新数据权限配置
func (api *RoleAPI) UpdateConfig(c *gin.Context) {
	var req model.RoleDataConfig
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Printf("Failed to bind JSON: %v", err)
		libs.Error(c, "参数错误")
		return
	}
	err := api.userService.UpdateDataConfig(req)
	if err != nil {
		libs.Error(c, "更新配置失败: "+err.Error())
		return
	}
	libs.Success(c, "更新配置成功", nil)
}

// SetDataConfig 设置角色数据权限配置
func (api *RoleAPI) SetDataConfig(c *gin.Context) {
	var req struct {
		RoleCode string  `json:"role_code"`
		IDs      []int64 `json:"ids" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Printf("Failed to bind JSON: %v", err)
		libs.Error(c, "参数错误")
		return
	}
	err := api.userService.SetDataConfig(req.RoleCode, req.IDs)
	if err != nil {
		libs.Error(c, err.Error())
		return
	}
	libs.Success(c, "设置数据权限成功", nil)
}
