package admin

import (
	"encoding/csv"
	"fmt"
	"strconv"
	"time"
	"tms/libs"
	"tms/model"
	"tms/pkg/db"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

// RequestLogAPI 日志管理API结构体
type RequestLogAPI struct{}

func init() {
	requestLogAPI := &RequestLogAPI{}

	adminV1 := GetAdminVersion()
	logGroup := adminV1.Group("logs", "日志管理接口")
	{
		logGroup.Register("GET", "/list", requestLogAPI.GetRequestLogList, model.AuthAdmin, "获取日志列表")
		logGroup.Register("POST", "/clear/:days", requestLogAPI.ClearLogsByDays, model.AuthAdmin, "清除指定天数前的日志")
		logGroup.Register("GET", "/export", requestLogAPI.ExportRequestLogs, model.AuthAdmin, "导出日志文件")
	}
}

// ReqLogSearch 分页搜索请求参数
type ReqLogSearch struct {
	Page     int    `form:"page,default=1" json:"page"`
	PageSize int    `form:"page_size,default=10" json:"page_size"`
	Path     string `form:"path" json:"path"` // 按路径模糊搜索
	Method   string `form:"method" json:"method"`
	IP       string `form:"ip" json:"ip"`
}

// RespLogList 分页返回结果
type RespLogList struct {
	List  []model.RequestLog `json:"list"`
	Total int64              `json:"total"`
}

// GetRequestLogList 获取日志列表（支持分页和搜索）
func (api *RequestLogAPI) GetRequestLogList(c *gin.Context) {
	var req ReqLogSearch
	if err := c.ShouldBind(&req); err != nil {
		libs.Error(c, "参数错误")
		return
	}

	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 || req.PageSize > 100 {
		req.PageSize = 10
	}

	dbQuery := db.DB.Model(&model.RequestLog{})
	if req.Path != "" {
		dbQuery = dbQuery.Where("path ILIKE ?", "%"+req.Path+"%")
	}
	if req.Method != "" {
		dbQuery = dbQuery.Where("method = ?", req.Method)
	}
	if req.IP != "" {
		dbQuery = dbQuery.Where("ip = ?", req.IP)
	}

	var total int64
	if err := dbQuery.Count(&total).Error; err != nil {
		libs.Error(c, "获取总数失败")
		return
	}

	var logs []model.RequestLog
	offset := (req.Page - 1) * req.PageSize
	if err := dbQuery.Offset(offset).Limit(req.PageSize).Order("created_at DESC").Find(&logs).Error; err != nil {
		libs.Error(c, "获取数据失败")
		return
	}

	libs.Success(c, "获取日志列表成功", RespLogList{
		List:  logs,
		Total: total,
	})
}

// ClearLogsByDays 清除指定天数前的日志
func (api *RequestLogAPI) ClearLogsByDays(c *gin.Context) {
	daysStr := c.Param("days")
	days := cast.ToInt(daysStr)

	if days <= 0 {
		libs.Error(c, "无效的天数")
		return
	}

	thresholdTime := time.Now().AddDate(0, 0, -days)

	if err := db.DB.Where("created_at < ?", thresholdTime).Delete(&model.RequestLog{}).Error; err != nil {
		libs.Error(c, "删除日志失败: "+err.Error())
		return
	}

	libs.Success(c, "已清理"+strconv.Itoa(days)+"天前的日志", nil)
}

// ExportRequestLogs 导出日志接口
func (api *RequestLogAPI) ExportRequestLogs(c *gin.Context) {
	var req ReqLogSearch
	if err := c.ShouldBind(&req); err != nil {
		libs.Error(c, "参数错误")
		return
	}

	dbQuery := db.DB.Model(&model.RequestLog{})
	if req.Path != "" {
		dbQuery = dbQuery.Where("path ILIKE ?", "%"+req.Path+"%")
	}
	if req.Method != "" {
		dbQuery = dbQuery.Where("method = ?", req.Method)
	}
	if req.IP != "" {
		dbQuery = dbQuery.Where("ip = ?", req.IP)
	}

	var logs []model.RequestLog
	if err := dbQuery.Order("created_at DESC").Find(&logs).Error; err != nil {
		libs.Error(c, "查询日志失败: "+err.Error())
		return
	}

	// 设置 CSV 响应头
	c.Header("Content-Type", "text/csv")
	c.Header("Content-Disposition", "attachment;filename=request_logs.csv")

	// 写入 CSV 数据
	writer := csv.NewWriter(c.Writer)
	defer writer.Flush()

	// 写入表头
	headers := []string{"ID", "请求路径", "请求方法", "IP地址", "User-Agent", "请求人ID", "请求数据", "时间"}
	if err := writer.Write(headers); err != nil {
		libs.Error(c, "写入CSV失败: "+err.Error())
		return
	}

	// 写入每一行数据
	for _, log := range logs {
		record := []string{
			log.Path,
			log.Method,
			log.IP,
			log.UserAgent,
			fmt.Sprint(log.UserId),
			string(log.Data),
			log.CreatedAt.Format("2006-01-02 15:04:05"),
		}
		if err := writer.Write(record); err != nil {
			libs.Error(c, "写入CSV失败: "+err.Error())
			return
		}
	}
}
