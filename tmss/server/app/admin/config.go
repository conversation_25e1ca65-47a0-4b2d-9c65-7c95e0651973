package admin

import (
	"context"
	"fmt"
	"time"
	"tms/libs"
	"tms/model"
	"tms/pkg/config"
	"tms/pkg/db"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

// ConfigAPI 系统配置管理API结构体
type ConfigAPI struct{}

func init() {
	api := &ConfigAPI{}

	adminV1 := GetAdminVersion()
	group := adminV1.Group("system", "系统配置管理接口")
	{
		group.Register("GET", "/config", api.GetSystemConfig, model.AuthAdmin, "获取系统配置")
		group.Register("POST", "/config", api.UpdateSystemConfig, model.AuthAdmin, "更新系统配置")
	}

	dbGroup := adminV1.Group("database", "系统数据库管理接口")
	{
		dbGroup.Register("GET", "/config", api.GetDatabaseConfig, model.AuthAdmin, "获取数据库配置")
		dbGroup.Register("POST", "/config", api.UpdateDatabaseConfig, model.AuthAdmin, "更新数据库配置")
		dbGroup.Register("POST", "/test", api.TestDatabaseConnection, model.AuthAdmin, "测试数据库连接")

	}
	logGroup := adminV1.Group("mongodb", "日志数据库管理接口")
	{
		logGroup.Register("GET", "config", api.GetMongoDBConfig, model.AuthAdmin, "获取MongoDB配置")
		logGroup.Register("POST", "config", api.UpdateMongoDBConfig, model.AuthAdmin, "更新MongoDB配置")
		logGroup.Register("POST", "test", api.TestMongoDBConnection, model.AuthAdmin, "测试MongoDB连接")
	}
}

// GetSystemConfig 获取系统配置
func (api *ConfigAPI) GetSystemConfig(c *gin.Context) {
	cfg := config.SystemEnv{
		Course: config.Config.Course,
		Upload: config.Config.Upload,
		Backup: config.Config.Backup,
	}
	libs.Success(c, "获取系统配置成功", cfg)
}

// UpdateSystemConfig 更新系统配置
func (api *ConfigAPI) UpdateSystemConfig(c *gin.Context) {
	type SystemEnv struct {
		Course *config.Course `json:"course,omitempty"`
		Upload *config.Upload `json:"upload,omitempty"`
		Backup *config.Backup `json:"backup,omitempty"`
	}
	var req SystemEnv
	if err := c.ShouldBindJSON(&req); err != nil {
		libs.Error(c, "参数错误")
		return
	}

	// 更新内存中的配置
	if req.Course != nil {
		config.Config.Course = *req.Course
	}
	if req.Upload != nil {
		config.Config.Upload = *req.Upload
	}
	if req.Backup != nil {
		config.Config.Backup = *req.Backup
	}

	// 保存到文件
	if err := config.SaveSystemConfig(); err != nil {
		libs.Error(c, "保存配置失败: "+err.Error())
		return
	}

	libs.Success(c, "系统配置更新成功", nil)
}

// GetDatabaseConfig 获取数据库配置
func (api *ConfigAPI) GetDatabaseConfig(c *gin.Context) {
	cfg := db.AppConfig.PostgreSQL
	libs.Success(c, "获取数据库配置成功", cfg)
}

// UpdateDatabaseConfig 更新数据库配置
func (api *ConfigAPI) UpdateDatabaseConfig(c *gin.Context) {
	var req db.PostgreSQL
	if err := c.ShouldBindJSON(&req); err != nil {
		libs.Error(c, "参数错误")
		return
	}

	// 更新数据库配置
	db.AppConfig.PostgreSQL = req

	// 保存到文件
	if err := db.SaveConfig(); err != nil {
		libs.Error(c, "保存数据库配置失败: "+err.Error())
		return
	}

	libs.Success(c, "数据库配置更新成功", nil)
}

// TestDatabaseConnection 使用 GORM 测试数据库连接
func (api *ConfigAPI) TestDatabaseConnection(c *gin.Context) {
	var req db.PostgreSQL
	if err := c.ShouldBindJSON(&req); err != nil {
		libs.Error(c, "参数错误")
		return
	}

	// 构造 DSN
	dsn := fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=%s",
		req.Host,
		req.Port,
		req.User,
		req.Password,
		req.DBName,
		req.SSLMode,
	)

	// 使用 GORM 初始化连接
	dbConn, err := gorm.Open(postgres.Open(dsn), &gorm.Config{})
	if err != nil {
		libs.Error(c, "连接数据库失败: "+err.Error())
		return
	}

	// 获取原生 DB 对象以进行 ping 操作
	sqlDB, err := dbConn.DB()
	if err != nil {
		libs.Error(c, "获取数据库对象失败: "+err.Error())
		return
	}

	// 设置连接超时时间
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// 测试连接是否有效
	err = sqlDB.PingContext(ctx)
	if err != nil {
		libs.Error(c, "数据库连接失败: "+err.Error())
		return
	}

	// 关闭连接
	sqlDB.Close()

	libs.Success(c, "数据库连接测试成功", nil)
}

// GetMongoDBConfig 获取 MongoDB 配置
func (api *ConfigAPI) GetMongoDBConfig(c *gin.Context) {
	cfg := db.AppConfig.MongoDB
	libs.Success(c, "获取MongoDB配置成功", cfg)
}

// UpdateMongoDBConfig 更新 MongoDB 配置
func (api *ConfigAPI) UpdateMongoDBConfig(c *gin.Context) {
	var req db.MongosDB
	if err := c.ShouldBindJSON(&req); err != nil {
		libs.Error(c, "参数错误")
		return
	}

	// 更新 MongoDB 配置
	db.AppConfig.MongoDB = req

	// 保存到文件
	if err := db.SaveConfig(); err != nil {
		libs.Error(c, "保存MongoDB配置失败: "+err.Error())
		return
	}

	libs.Success(c, "MongoDB配置更新成功", nil)
}

// TestMongoDBConnection 测试 MongoDB 连接
func (api *ConfigAPI) TestMongoDBConnection(c *gin.Context) {
	var req db.MongosDB
	if err := c.ShouldBindJSON(&req); err != nil {
		libs.Error(c, "参数错误")
		return
	}

	clientOptions := options.Client().ApplyURI(req.URI)
	client, err := mongo.Connect(context.Background(), clientOptions)
	if err != nil {
		libs.Error(c, "连接MongoDB失败: "+err.Error())
		return
	}

	// 设置连接健康检查
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	if err = client.Ping(ctx, nil); err != nil {
		libs.Error(c, "MongoDB连接失败: "+err.Error())
		return
	}

	client.Disconnect(ctx)

	libs.Success(c, "MongoDB连接测试成功", nil)
}
