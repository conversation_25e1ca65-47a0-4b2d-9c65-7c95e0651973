package admin

import (
	"log"

	"tms/libs"
	"tms/model"
	"tms/pkg/db"
	"tms/services/workflow"
	"tms/utils"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

// WorkflowAPI 工作流管理API结构体
type WorkflowAPI struct{}

func init() {
	workflowAPI := &WorkflowAPI{}

	adminV1 := GetAdminVersion()
	workflowGroup := adminV1.Group("workflow", "流程管理接口")
	{
		workflowGroup.Register("GET", "/list", workflowAPI.GetWorkflowList, model.AuthAdmin, "获取流程列表")
		workflowGroup.Register("GET", "/detail/:id", workflowAPI.GetWorkflowDetail, model.AuthAdmin, "获取流程详情")
		workflowGroup.Register("POST", "/create", workflowAPI.CreateWorkflow, model.AuthAdmin, "创建流程")
		workflowGroup.Register("POST", "/update", workflowAPI.UpdateWorkflow, model.AuthAdmin, "更新流程信息")
		workflowGroup.Register("POST", "/delete/:id", workflowAPI.DeleteWorkflow, model.AuthAdmin, "删除流程")
		workflowGroup.Register("POST", "/change-status", workflowAPI.ChangeWorkflowStatus, model.AuthAdmin, "启用/停用流程")
		workflowGroup.Register("GET", "/userlist", workflowAPI.GetUsersList, "admin", "根据角色获取老师列表")

		workflowGroup.Register("GET", "/configs", workflowAPI.GetModuleConfigs, model.AuthAdmin, "获取所有模块配置")
		workflowGroup.Register("POST", "/config/update", workflowAPI.UpdateModuleConfig, model.AuthAdmin, "更新模块配置")
		workflowGroup.Register("POST", "/config/toggle", workflowAPI.ToggleModule, model.AuthAdmin, "切换模块启用状态")

	}
}

var workflowService = workflow.NewWorkflowService()

// ReqWorkflowCreate 流程创建请求结构体
type ReqWorkflowCreate struct {
	Name        string                    `json:"name" binding:"required"`       // 流程名称
	Description string                    `json:"description"`                   // 流程描述
	ModuleKey   string                    `json:"module_key" binding:"required"` // 模块类型：courseware等
	Nodes       []model.WorkflowNodeUsers `json:"nodes" binding:"required"`      // 节点配置
}

// ReqWorkflowUpdate 流程更新请求结构体
type ReqWorkflowUpdate struct {
	ID          int64                     `json:"id" binding:"required"`
	Name        string                    `json:"name" binding:"required"`
	Description string                    `json:"description"`
	ModuleKey   string                    `json:"module_key" binding:"required"`
	Nodes       []model.WorkflowNodeUsers `json:"nodes" binding:"required"`
}

// ReqChangeStatus 状态变更请求
type ReqChangeStatus struct {
	ID     int64  `json:"id" binding:"required"`
	Status string `json:"status" binding:"required"` // enable / disable
}

// CreateWorkflow 创建流程
func (api *WorkflowAPI) CreateWorkflow(c *gin.Context) {
	var req ReqWorkflowCreate
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Printf("Failed to bind JSON: %v", err)
		libs.Error(c, "参数错误"+err.Error())
		return
	}

	userId := c.GetInt64("userId")
	if userId == 0 {
		libs.Error(c, "无效的用户ID")
		return
	}

	// 构造主表数据
	workflow := &model.Workflow{
		Name:        req.Name,
		Description: req.Description,
		ModuleKey:   req.ModuleKey,
		UserID:      userId,
		NodeLen:     int64(len(req.Nodes)),
		Status:      model.WorkflowStatusEnable, // 默认启用
	}

	// 调用服务层
	if err := workflowService.CreateWorkflow(workflow, req.Nodes); err != nil {
		libs.Error(c, err.Error())
		return
	}

	libs.Success(c, "创建流程成功", nil)
}

// UpdateWorkflow 更新流程
func (api *WorkflowAPI) UpdateWorkflow(c *gin.Context) {
	var req ReqWorkflowUpdate
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Printf("Failed to bind JSON: %v", err)
		libs.Error(c, "参数错误"+err.Error())
		return
	}

	// 构造主表数据
	workflow := &model.Workflow{
		ID:          req.ID,
		Name:        req.Name,
		Description: req.Description,
		ModuleKey:   req.ModuleKey,
	}

	// 调用服务层
	if err := workflowService.UpdateWorkflow(workflow, req.Nodes); err != nil {
		libs.Error(c, err.Error())
		return
	}

	libs.Success(c, "更新流程成功", nil)
}

// DeleteWorkflow 删除流程
func (api *WorkflowAPI) DeleteWorkflow(c *gin.Context) {
	id := cast.ToInt64(c.Param("id"))

	if err := workflowService.DeleteWorkflow(id); err != nil {
		libs.Error(c, "删除流程失败: "+err.Error())
		return
	}

	libs.Success(c, "删除流程成功", nil)
}

// GetWorkflowList 获取流程列表
func (api *WorkflowAPI) GetWorkflowList(c *gin.Context) {
	type ReqQuery struct {
		Name      string `form:"name"`
		Status    string `form:"status"`
		ModuleKey string `form:"module_key"`
		Page      int    `form:"page" default:"1"`
		Size      int    `form:"size" default:"10"`
	}
	var req ReqQuery
	if err := c.ShouldBind(&req); err != nil {
		libs.Error(c, "参数错误")
		return
	}

	workflows, total, err := workflowService.GetWorkflowList(req.Name, req.Status, req.ModuleKey, req.Page, req.Size)
	if err != nil {
		libs.Error(c, err.Error())
		return
	}

	// 构造返回结果
	var result []map[string]interface{}
	for _, w := range workflows {
		result = append(result, map[string]interface{}{
			"id":          w.ID,
			"name":        w.Name,
			"description": w.Description,
			"moduleName":  model.GetModuleName(w.ModuleKey),
			"moduleKey":   w.ModuleKey,
			"nodeCount":   w.NodeLen,
			"createTime":  utils.FormatUnixTimeByMinits(w.CreatedAt),
			"status":      w.Status,
		})
	}

	libs.Success(c, "获取流程列表成功", gin.H{
		"list":       result,
		"total":      total,
		"moduleList": model.GetModuleList(),
	})
}
func (api *WorkflowAPI) GetWorkflowDetail(c *gin.Context) {
	id := c.Param("id")
	workflow, nodes, err := workflowService.GetWorkflowByID(cast.ToInt64(id))
	if err != nil {
		libs.Error(c, err.Error())
		return
	}
	libs.Success(c, "获取流程详情成功", gin.H{
		"workflow": workflow,
		"nodes":    nodes,
	})
}

// ChangeWorkflowStatus 修改流程状态
func (api *WorkflowAPI) ChangeWorkflowStatus(c *gin.Context) {
	var req ReqChangeStatus
	if err := c.ShouldBindJSON(&req); err != nil {
		libs.Error(c, "参数错误")
		return
	}

	if err := workflowService.ChangeWorkflowStatus(req.ID, req.Status); err != nil {
		libs.Error(c, "修改状态失败: "+err.Error())
		return
	}

	libs.Success(c, "状态修改成功", nil)
}

// 根据角色获取用户列表
func (api *WorkflowAPI) GetUsersList(c *gin.Context) {
	var req model.ReqUsersSearch
	if err := c.ShouldBind(&req); err != nil {
		libs.Error(c, "参数错误")
		return
	}
	if req.SysCode == "" {
		req.SysCode = model.AuthTeacher
	}
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 || req.PageSize > 100 {
		req.PageSize = 10
	}

	// 调用模型层方法获取用户列表
	users, total, err := model.GetUsersBySysCode(db.DB, req)
	if err != nil {
		libs.Error(c, "查询失败: "+err.Error())
		return
	}

	libs.Success(c, "查询成功", gin.H{
		"list":  users,
		"total": total,
	})
}

// GetModuleConfigs 获取所有模块配置
func (api *WorkflowAPI) GetModuleConfigs(c *gin.Context) {
	configService := workflow.GetModuleConfigService()
	if configService == nil {
		libs.Error(c, "获取模块配置失败")
		return
	}
	// 获取所有模块配置
	configs := configService.GetAllConfigs()

	libs.Success(c, "获取模块配置成功", configs)
}

// UpdateModuleConfig 更新模块配置
func (api *WorkflowAPI) UpdateModuleConfig(c *gin.Context) {
	var req model.ModuleConfig
	if err := c.ShouldBindJSON(&req); err != nil {
		libs.Error(c, "参数错误: "+err.Error())
		return
	}
	configService := workflow.GetModuleConfigService()
	if configService == nil {
		libs.Error(c, "获取模块配置失败")
		return
	}
	// 更新模块配置
	if err := configService.UpdateConfig(req); err != nil {
		libs.Error(c, "更新模块配置失败: "+err.Error())
		return
	}

	libs.Success(c, "更新模块配置成功", nil)
}

// ToggleModule 切换模块启用状态
func (api *WorkflowAPI) ToggleModule(c *gin.Context) {
	var req struct {
		ModuleKey string `json:"module_key" binding:"required"`
		Enable    bool   `json:"enable" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		libs.Error(c, "参数错误: "+err.Error())
		return
	}
	configService := workflow.GetModuleConfigService()
	if configService == nil {
		libs.Error(c, "获取模块配置失败")
		return
	}
	// 切换模块启用状态
	if err := configService.ToggleModule(req.ModuleKey, req.Enable); err != nil {
		libs.Error(c, "切换模块状态失败: "+err.Error())
		return
	}

	libs.Success(c, "切换模块状态成功", nil)
}
