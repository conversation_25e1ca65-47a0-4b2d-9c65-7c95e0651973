package admin

import (
	"log"
	"tms/libs"
	"tms/model"
	"tms/pkg/db"
	"tms/services/auth"
	eventSync "tms/services/sync"
	"tms/services/tech"
	us "tms/services/user"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"gorm.io/gorm"
)

// UserQuery 定义用户查询参数结构
type UserQuery struct {
	Query      string `form:"query"`
	SearchType string `form:"searchType"`
}

// UserAPI 用户管理API结构体
type UserAPI struct {
	userService *us.UserService
}
type BatchDeleteRequest struct {
	IDs []int64 `json:"ids" binding:"required"`
}

// NewUserAPI 创建UserAPI实例
func NewUserAPI() *UserAPI {
	return &UserAPI{
		userService: us.NewUserService(),
	}
}

var classService = tech.NewClassService()

func init() {
	userAPI := NewUserAPI()
	AdminV1 := GetAdminVersion()
	usersManagementV1 := AdminV1.Group("users", "用户管理")
	{
		usersManagementV1.Register("GET", "/list", userAPI.GetAllUsers, model.AuthAdmin, "获取用户列表")

		usersManagementV1.Register("GET", "/get/:id", userAPI.GetUserByID, model.AuthAdmin, "根据ID获取用户")
		usersManagementV1.Register("POST", "/create", userAPI.CreateUser, model.AuthAdmin, "创建新用户")
		usersManagementV1.Register("POST", "/update", userAPI.UpdateUser, model.AuthAdmin, "更新用户")
		usersManagementV1.Register("POST", "/delete/:id", userAPI.DeleteUser, model.AuthAdmin, "删除用户")
		usersManagementV1.Register("POST", "/batch-delete", userAPI.BatchDeleteUsers, model.AuthAdmin, "批量删除用户")
		usersManagementV1.Register("POST", "/set-student-info", userAPI.SetStudentInfo, model.AuthAdmin, "设置学生信息")
		usersManagementV1.Register("GET", "/classes", userAPI.GetClassList, model.AuthAdmin, "获取班级列表")
		usersManagementV1.Register("GET", "/majors/:id", userAPI.GetMajorsByClassID, model.AuthAdmin, "根据班级获取专业")

	}
}

// GetAllUsers 处理获取所有用户
func (api *UserAPI) GetAllUsers(c *gin.Context) {
	var userQuery UserQuery
	if err := c.ShouldBindQuery(&userQuery); err != nil {
		libs.Error(c, "参数错误")
		return
	}

	pageParam := libs.GetPageParam(c)

	var total int64
	var users []model.Users

	// 构建查询范围
	scopes := func(db *gorm.DB) *gorm.DB {
		if userQuery.Query != "" && userQuery.SearchType != "" {
			if userQuery.SearchType == "account" {
				db = db.Where("account LIKE ?", "%"+userQuery.Query+"%")
			}
			if userQuery.SearchType == "username" {
				db = db.Where("username LIKE ?", "%"+userQuery.Query+"%")
			}
			if userQuery.SearchType == "phone" {
				db = db.Where("phone LIKE ?", "%"+userQuery.Query+"%")
			}
			if userQuery.SearchType == "email" {
				db = db.Where("email LIKE ?", "%"+userQuery.Query+"%")
			}
			if userQuery.SearchType == "roles" {
				userIds, err := api.userService.GetUserIdsByRoleName(userQuery.Query)
				if err == nil && len(userIds) > 0 {
					db = db.Where("id IN ?", userIds)
				}
			}
			if userQuery.SearchType == "class" {
				userIds, err := api.userService.GetUsersByClassName(userQuery.Query)
				if err == nil && len(userIds) > 0 {
					db = db.Where("id IN ?", userIds)
				}
			}
			if userQuery.SearchType == "major" {
				userIds, err := api.userService.GetUsersByMajorName(userQuery.Query)
				if err == nil && len(userIds) > 0 {
					db = db.Where("id IN ?", userIds)
				}
			}
		}

		return db
	}

	// 获取总数
	if err := db.DB.Model(&model.Users{}).Scopes(scopes).Count(&total).Error; err != nil {
		libs.Error(c, "获取用户总数失败")
		return
	}

	// 获取分页用户数据
	users = api.userService.GetUsersByCondition(func(db *gorm.DB) *gorm.DB {
		return scopes(db).Limit(pageParam.PageSize).Offset(pageParam.GetOffset())
	})

	if users == nil {
		users = []model.Users{} // 确保返回空数组而不是nil
	}
	usersList := api.userService.GetUserList(users)

	pageResult := libs.GeneratePageResult(usersList, total, pageParam)
	libs.Success(c, "获取用户列表成功", pageResult)
}

// CreateUser 处理创建新用户
func (api *UserAPI) CreateUser(c *gin.Context) {
	var u us.UpdateUser
	if err := c.ShouldBindJSON(&u); err != nil {
		libs.Error(c, "参数错误")
		return
	}

	if err := api.userService.CreateUser(&u); err != nil {
		if err == gorm.ErrInvalidData {
			libs.Error(c, "Username, password, and account cannot be empty")
			return
		}
		if err == gorm.ErrDuplicatedKey {
			libs.Error(c, "Account already exists")
			return
		}
		libs.Error(c, err.Error())
		return
	}

	libs.Success(c, "创建用户成功", u)
}

// GetUserByID 处理根据ID获取用户
func (api *UserAPI) GetUserByID(c *gin.Context) {
	id := cast.ToInt64(c.Param("id"))
	if id == 0 {
		libs.Error(c, "Invalid user ID")
		return
	}
	user := api.userService.GetUserByID(id)
	if user == nil {
		libs.Error(c, "用户不存在")
		return
	}

	libs.Success(c, "获取用户成功", user)
}

// UpdateUser 处理更新用户
func (api *UserAPI) UpdateUser(c *gin.Context) {
	var user us.UpdateUser
	if err := c.ShouldBindJSON(&user); err != nil {
		libs.Error(c, "参数错误")
		return
	}

	if err := api.userService.UpdateUser(&user); err != nil {
		libs.Error(c, err.Error())
		return
	}

	if api.userService.IsUserRole(user.User.ID) {
		claims, err := auth.GetUserClaims(c)
		if err != nil {
			log.Printf("Fail get use claims")
		}

		err = eventSync.NewEventSyncBuilder(user.User.ID).
			Operator(claims.ID).
			SyncClient().
			EventType(eventSync.UpdateUserInfoSyncType).
			Data(user).
			Save()
		if err != nil {
			log.Printf("Failed to write sync data: %v", err)
			return
		}
	}

	libs.Success(c, "更新用户成功", user)
}

// DeleteUser 处理删除用户
func (api *UserAPI) DeleteUser(c *gin.Context) {
	id := cast.ToInt64(c.Param("id"))
	if id == 0 {
		libs.Error(c, "Invalid user ID")
		return
	}

	if err := api.userService.DeleteUser(id); err != nil {
		libs.Error(c, err.Error())
		return
	}

	if api.userService.IsUserRole(id) {
		claims, err := auth.GetUserClaims(c)
		if err != nil {
			log.Printf("Fail get use claims")
		}
		if claims.ID != 0 {
			err = eventSync.NewEventSyncBuilder(id).
				Operator(claims.ID).
				SyncClient().
				EventType(eventSync.DeleteUserInfoSyncType).
				Data("").
				Save()
			if err != nil {
				log.Printf("Failed to write sync data: %v", err)
				return
			}
		}
	}

	libs.Success(c, "删除用户成功", nil)
}

// 添加批量删除处理方法
func (api *UserAPI) BatchDeleteUsers(c *gin.Context) {
	var req BatchDeleteRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		libs.Error(c, "参数错误")
		return
	}

	if len(req.IDs) == 0 {
		libs.Error(c, "请选择要删除的用户")
		return
	}

	if err := api.userService.BatchDeleteUsers(req.IDs); err != nil {
		libs.Error(c, err.Error())
		return
	}

	libs.Success(c, "批量删除用户成功", nil)
}
func (api *UserAPI) GetClassList(c *gin.Context) {
	var req model.ReqClassSearch
	if err := c.ShouldBind(&req); err != nil {
		libs.Error(c, "参数错误")
		return
	}
	classes, err := classService.GetClassList(req)
	if err != nil {
		libs.Error(c, err.Error())
		return
	}

	libs.Success(c, "获取班级列表成功", classes)
}
func (api *UserAPI) GetMajorsByClassID(c *gin.Context) {
	classID := cast.ToInt64(c.Param("id"))
	majors, err := classService.GetMajorsByClassID(classID)
	if err != nil {
		libs.Error(c, err.Error())
		return
	}
	libs.Success(c, "获取专业列表成功", majors)
}
func (api *UserAPI) SetStudentInfo(c *gin.Context) {
	var req model.ReqSetStudentClassID
	if err := c.ShouldBindJSON(&req); err != nil {
		libs.Error(c, err.Error())
		return
	}
	if err := api.userService.SetStudentClassID(req); err != nil {
		libs.Error(c, err.Error())
		return
	}
	libs.Success(c, "设置学生信息成功", nil)
}
