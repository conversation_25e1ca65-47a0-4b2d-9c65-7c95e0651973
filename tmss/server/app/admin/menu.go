package admin

import (
	"log"
	"time"
	"tms/libs"
	"tms/model"
	"tms/pkg/db"
	"tms/services/user"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

// MenuAPI 菜单权限管理API结构体
type MenuAPI struct {
	userService *user.UserService
}

// NewMenuAPI 创建MenuAPI实例
func NewMenuAPI() *MenuAPI {
	return &MenuAPI{
		userService: user.NewUserService(),
	}
}

func init() {
	menuAPI := NewMenuAPI()
	AdminV1 := GetAdminVersion()
	menuPermissionV1 := AdminV1.Group("menu", "菜单权限管理接口")
	{
		menuPermissionV1.Register("GET", "/list", menuAPI.GetMenuList, model.AuthAdmin, "获取菜单列表")
		menuPermissionV1.Register("POST", "/create", menuAPI.CreateMenu, model.AuthAdmin, "创建新菜单")
		menuPermissionV1.Register("POST", "/update", menuAPI.UpdateMenu, model.AuthAdmin, "更新菜单")
		menuPermissionV1.Register("POST", "/delete/:id", menuAPI.DeleteMenu, model.AuthAdmin, "删除菜单")
		menuPermissionV1.Register("GET", "/listrole/:role_code/:sys_code", menuAPI.GetMenuPermissions, model.AuthAdmin, "获取菜单权限")
		menuPermissionV1.Register("POST", "/updaterole", menuAPI.UpdateMenuPermission, model.AuthAdmin, "更新菜单权限")
	}
}

func (api *MenuAPI) GetMenuList(c *gin.Context) {
	menuTree, err := api.userService.GetMenuList()
	if err != nil {
		libs.Error(c, err.Error())
		return
	}
	libs.Success(c, "获取菜单成功", menuTree)
}
func (api *MenuAPI) CreateMenu(c *gin.Context) {
	// 解析请求体中的菜单数据
	var req model.Menus
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Printf("Failed to bind JSON: %v", err)
		libs.Error(c, "参数错误")
		return
	}
	err := db.DB.Create(&model.Menus{
		ParentID:  req.ParentID,
		MenuName:  req.MenuName,
		Path:      req.Path,
		Icon:      req.Icon,
		OrderNum:  req.OrderNum,
		SysCode:   req.SysCode,
		CreatedAt: time.Now().Unix(),
	}).Error
	if err != nil {
		libs.Error(c, err.Error())
		return
	}
	libs.Success(c, "创建菜单成功", nil)
}
func (api *MenuAPI) UpdateMenu(c *gin.Context) {
	var req model.Menus
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Printf("Failed to bind JSON: %v", err)
		libs.Error(c, "参数错误")
		return
	}
	if req.ID <= 0 {
		log.Printf("Invalid menu ID: %v", req.ID)
		libs.Error(c, "参数错误")
		return
	}
	var menu model.Menus
	if err := db.DB.Where("id = ?", req.ID).First(&menu).Error; err != nil {
		libs.Error(c, err.Error())
		return
	}

	// 使用 map 精确控制更新哪些字段
	updates := map[string]interface{}{
		"parent_id":  req.ParentID,
		"menu_name":  req.MenuName,
		"path":       req.Path,
		"icon":       req.Icon, // 允许空字符串
		"order_num":  req.OrderNum,
		"sys_code":   req.SysCode,
		"updated_at": time.Now().Unix(),
	}

	if err := db.DB.Model(&model.Menus{}).
		Where("id = ?", menu.ID).
		Updates(updates).Error; err != nil {
		libs.Error(c, err.Error())
		return
	}

	libs.Success(c, "更新菜单成功", nil)
}
func (api *MenuAPI) DeleteMenu(c *gin.Context) {
	id := cast.ToInt64(c.Param("id"))
	if id == 0 {
		libs.Error(c, "Invalid user ID")
		return
	}
	var existRoleMenu model.RoleMenu
	if db.DB.Where("menu_id = ?", id).First(&existRoleMenu).Error == nil {
		libs.Error(c, "请先删除该菜单下的角色")
		return
	}
	if err := db.DB.Where("id = ?", id).Delete(&model.Menus{}).Error; err != nil {
		libs.Error(c, err.Error())
		return
	}
	if err := db.DB.Where("menu_id = ?", id).Delete(&model.RoleMenu{}).Error; err != nil {
		libs.Error(c, err.Error())
		return
	}
	libs.Success(c, "删除菜单成功", nil)
}

// GetMenuPermissions 获取菜单权限
func (api *MenuAPI) GetMenuPermissions(c *gin.Context) {
	roleCode := c.Param("role_code")
	sysCode := c.Param("sys_code")
	menuTree, ids, err := api.userService.GetMenuRoles(sysCode, roleCode)
	if err != nil {
		libs.Error(c, err.Error())
		return
	}
	libs.Success(c, "获取菜单权限成功", gin.H{"menuTree": menuTree, "ids": ids})
}

// UpdateMenuPermission 更新菜单权限
func (api *MenuAPI) UpdateMenuPermission(c *gin.Context) {
	var req user.ReqMenuRole
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Printf("Failed to bind JSON: %v", err)
		libs.Error(c, "参数错误")
		return
	}

	err := api.userService.UpdateMenuRole(req)
	if err != nil {
		libs.Error(c, err.Error())
		return
	}
	libs.Success(c, "更新菜单权限成功", nil)
}
