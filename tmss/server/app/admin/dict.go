package admin

import (
	"log"

	"tms/libs"
	"tms/model"
	"tms/pkg/dict"

	"github.com/gin-gonic/gin"
)

// DictAPI 字典管理API结构体
type DictAPI struct{}

func init() {
	dictAPI := &DictAPI{}

	adminV1 := GetAdminVersion()
	group := adminV1.Group("dict", "字典管理接口")
	{
		// 分类管理
		group.Register("GET", "/category/list", dictAPI.GetDictCategoryList, model.AuthAdmin, "获取字典分类列表")
		group.Register("POST", "/category/create", dictAPI.CreateDictCategory, model.AuthAdmin, "创建字典分类")
		group.Register("POST", "/category/update", dictAPI.UpdateDictCategory, model.AuthAdmin, "更新字典分类")
		group.Register("POST", "/category/delete/:id", dictAPI.DeleteDictCategory, model.AuthAdmin, "删除字典分类")

		// 字典条目管理
		group.Register("GET", "/entry/list", dictAPI.GetDictEntryList, model.AuthAdmin, "获取字典条目列表")
		group.Register("POST", "/entry/create", dictAPI.CreateDictEntry, model.AuthAdmin, "创建字典条目")
		group.Register("POST", "/entry/update", dictAPI.UpdateDictEntry, model.AuthAdmin, "更新字典条目")
		group.Register("POST", "/entry/delete/:name", dictAPI.DeleteDictEntry, model.AuthAdmin, "删除字典条目")
		group.Register("GET", "/entry/list/by-category", dictAPI.GetDictEntriesByCategory, model.AuthAdmin, "按分类获取字典条目")
	}
}

// ================== 分类相关 ===================

// ReqDictCategory 分类请求参数
type ReqDictCategory struct {
	ID   string `json:"id" binding:"required"`   // 分类ID
	Name string `json:"name" binding:"required"` // 分类名称
}

// GetDictCategoryList 获取所有分类列表
func (api *DictAPI) GetDictCategoryList(c *gin.Context) {
	categories := dict.GetAllDictCategories()
	libs.Success(c, "获取字典分类成功", categories)
}

// CreateDictCategory 创建分类
func (api *DictAPI) CreateDictCategory(c *gin.Context) {
	var req ReqDictCategory
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Printf("Failed to bind JSON: %v", err)
		libs.Error(c, "参数错误")
		return
	}

	if err := dict.AddDictCategory(req.ID, req.Name); err != nil {
		libs.Error(c, "创建分类失败: "+err.Error())
		return
	}

	libs.Success(c, "创建分类成功", nil)
}

// UpdateDictCategory 更新分类
func (api *DictAPI) UpdateDictCategory(c *gin.Context) {
	var req ReqDictCategory
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Printf("Failed to bind JSON: %v", err)
		libs.Error(c, "参数错误")
		return
	}

	if err := dict.UpdateDictCategory(req.ID, req.Name); err != nil {
		libs.Error(c, "更新分类失败: "+err.Error())
		return
	}

	libs.Success(c, "更新分类成功", nil)
}

// DeleteDictCategory 删除分类
func (api *DictAPI) DeleteDictCategory(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		libs.Error(c, "无效的分类ID")
		return
	}

	if err := dict.DeleteDictCategory(id); err != nil {
		libs.Error(c, "删除分类失败: "+err.Error())
		return
	}

	libs.Success(c, "删除分类成功", nil)
}

// ================== 字典条目相关 ===================

// ReqDictEntry 字典条目请求参数
type ReqDictEntry struct {
	Name       string `json:"name" binding:"required"`
	CategoryID string `json:"category_id" binding:"required"`
	Value      any    `json:"value" binding:"required"`
}

// GetDictEntryList 获取所有字典条目
func (api *DictAPI) GetDictEntryList(c *gin.Context) {
	entries := make([]dict.DictEntry, 0)
	dict.DictEntryMap.Range(func(key, value interface{}) bool {
		entries = append(entries, value.(dict.DictEntry))
		return true
	})
	libs.Success(c, "获取字典条目成功", entries)
}

// CreateDictEntry 创建字典条目
func (api *DictAPI) CreateDictEntry(c *gin.Context) {
	var req ReqDictEntry
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Printf("Failed to bind JSON: %v", err)
		libs.Error(c, "参数错误")
		return
	}

	entry := dict.DictEntry{
		Name:       req.Name,
		CategoryID: req.CategoryID,
		Value:      req.Value,
	}

	if err := dict.SaveOrUpdateDictEntry(entry); err != nil {
		libs.Error(c, "添加字典条目失败: "+err.Error())
		return
	}

	libs.Success(c, "添加字典条目成功", nil)
}

// UpdateDictEntry 更新字典条目
func (api *DictAPI) UpdateDictEntry(c *gin.Context) {
	var req ReqDictEntry
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Printf("Failed to bind JSON: %v", err)
		libs.Error(c, "参数错误")
		return
	}

	entry := dict.DictEntry{
		Name:       req.Name,
		CategoryID: req.CategoryID,
		Value:      req.Value,
	}

	if err := dict.SaveOrUpdateDictEntry(entry); err != nil {
		libs.Error(c, "更新字典条目失败: "+err.Error())
		return
	}

	libs.Success(c, "更新字典条目成功", nil)
}

// DeleteDictEntry 删除字典条目
func (api *DictAPI) DeleteDictEntry(c *gin.Context) {
	name := c.Param("name")
	if name == "" {
		libs.Error(c, "无效的字典键名")
		return
	}

	if err := dict.DeleteDictEntry(name); err != nil {
		libs.Error(c, "删除字典条目失败: "+err.Error())
		return
	}

	libs.Success(c, "删除字典条目成功", nil)
}

// GetDictEntriesByCategory 按分类获取字典条目
func (api *DictAPI) GetDictEntriesByCategory(c *gin.Context) {
	categoryID := c.Query("category_id")
	if categoryID == "" {
		libs.Error(c, "缺少分类ID参数")
		return
	}

	entries := dict.GetDictEntriesByCategory(categoryID)
	libs.Success(c, "按分类获取字典条目成功", entries)
}
