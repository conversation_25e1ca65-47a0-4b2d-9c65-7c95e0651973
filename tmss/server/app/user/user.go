package user

import (
	"log/slog"
	"tms/libs"
	"tms/model"
	"tms/services/user"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

var userService *user.UserService

func init() {
	userService = user.NewUserService()

	usersV1 := GetVersion()
	userGroup := usersV1.Group("users", "用户接口")
	{
		userGroup.Register("GET", "/info", GetUserInfo, "user", "获取用户信息")
		userGroup.Register("GET", "/list", GetUserList, "user", "本地用户列表")
		userGroup.Register("POST", "/update", UpdateUser, "user", "更新用户信息")
		userGroup.Register("POST", "/delete/:id", DeleteUser, "user", "删除用户")
	}
}

func GetUserInfo(c *gin.Context) {
	libs.Success(c, "success", nil)
}

func GetUserList(c *gin.Context) {
	var req model.ReqUsersSearch
	if err := c.ShouldBind(&req); err != nil {
		slog.Error("参数错误", "error", err)
		libs.Error(c, "参数错误")
		return
	}
	resp, total, err := userService.GetUsersBySearch(req)
	if err != nil {
		slog.Error("获取用户列表失败", "error", err)
		libs.Error(c, "获取用户列表失败")
		return
	}

	result := libs.GeneratePageResult(resp, total, libs.PageParam{
		Page:     req.Page,
		PageSize: req.PageSize,
	})

	libs.Success(c, "success", result)
}

func UpdateUser(c *gin.Context) {
	var req user.UpdateUser
	if err := c.ShouldBindJSON(&req); err != nil {
		slog.Error("参数错误", "error", err)
		libs.Error(c, "参数错误")
		return
	}

	if err := userService.UpdateUser(&req); err != nil {
		slog.Error("更新用户失败", "error", err)
		libs.Error(c, "更新用户失败")
		return
	}

	libs.Success(c, "success", nil)
}

func DeleteUser(c *gin.Context) {
	id := cast.ToInt64(c.Param("id"))
	if id == 0 {
		libs.Error(c, "用户ID不能为空")
		return
	}

	if err := userService.DeleteUser(id); err != nil {
		slog.Error("删除用户失败", "error", err)
		libs.Error(c, "删除用户失败")
		return
	}

	libs.Success(c, "success", nil)
}
