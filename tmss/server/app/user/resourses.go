package user

import (
	"tms/libs"
	"tms/model"
	"tms/services/auth"
	"tms/services/resource"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

type ResoursesAPI struct{}

var resourceService = resource.NewResourceService()

func init() {
	resourceAPI := &ResoursesAPI{}
	userV1 := GetVersion()
	resourceV1 := userV1.Group("resources", "资源管理接口")
	{
		resourceV1.Register("GET", "/category", resourceAPI.GetResourceCategory, "user", "资料分类")
		resourceV1.Register("GET", "/list", resourceAPI.GetResourceList, "user", "资料列表")
		resourceV1.Register("GET", "/content/:id", resourceAPI.GetResourceContent, "user", "资料内容")
		resourceV1.Register("POST", "/download", resourceAPI.DownloadResource, "user", "下载资料")
		resourceV1.Register("GET", "/download/history", resourceAPI.DownloadResourceHistory, "user", "下载记录")
	}
}

func (api *ResoursesAPI) GetResourceCategory(c *gin.Context) {
	reqParam := model.ReqResourceCategorySearch{
		Name:     c.Query("name"),
		ParentID: cast.ToInt64(c.Query("parent_id")),
	}
	resp, err := resourceService.GetResourceCategoryList(reqParam)
	if err != nil {
		libs.Error(c, "获取资料分类失败")
		return
	}
	libs.Success(c, "success", resp)
}

func (api *ResoursesAPI) GetResourceList(c *gin.Context) {
	ps := libs.GetPageParam(c)
	reqParam := model.ReqResourceSearch{
		Page:       ps.Page,
		PageSize:   ps.PageSize,
		Name:       c.Query("name"),
		Status:     c.DefaultQuery("status", model.ResourceStatusPublished),
		CategoryID: cast.ToInt64(c.Query("category_id")),
		ChapterID:  cast.ToInt64(c.Query("chapter_id")),
	}
	resp, err := resourceService.GetResourceList(c, reqParam)
	if err != nil {
		libs.Error(c, "获取资料列表失败")
		return
	}

	result := libs.GeneratePageResult(resp.List, resp.Total, ps)
	libs.Success(c, "success", result)
}

func (api *ResoursesAPI) GetResourceContent(c *gin.Context) {
	id := cast.ToInt64(c.Param("id"))
	if id == 0 {
		libs.Error(c, "资料ID不能为空")
		return
	}

	resp, err := resourceService.GetResourceContent(id)
	if err != nil {
		libs.Error(c, "获取资料内容失败")
		return
	}
	libs.Success(c, "success", resp)
}

func (api *ResoursesAPI) DownloadResource(c *gin.Context) {
	var req struct {
		ResourceIDS []int64 `json:"resources_ids" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {

		libs.Error(c, "获取资料内容失败")
		return
	}
	claims, err := auth.GetUserClaims(c)
	if claims.ID == 0 || err != nil {
		libs.Error(c, "用户ID不能为空")
		return
	}

	if err := resourceService.DownloadResource(claims.ID, req.ResourceIDS); err != nil {
		libs.Error(c, "下载资料失败")
		return
	}

	libs.Success(c, "success", nil)
}

func (api *ResoursesAPI) DownloadResourceHistory(c *gin.Context) {
	claims, err := auth.GetUserClaims(c)
	if claims.ID == 0 || err != nil {
		libs.Error(c, "用户ID不能为空")
		return
	}
	ps := libs.GetPageParam(c)
	req := model.ResourceDownloadQuery{
		Page:       ps.Page,
		PageSize:   ps.PageSize,
		ResourceID: cast.ToInt64(c.Query("resource_id")),
		UserID:     claims.ID,
		StartAt:    cast.ToInt64(c.Query("start_at")),
		EndAt:      cast.ToInt64(c.Query("end_at")),
	}

	resp, total, err := resourceService.DownloadResourceHistory(req)
	if err != nil {
		libs.Error(c, "获取下载记录失败")
		return
	}

	result := libs.GeneratePageResult(resp, total, ps)
	libs.Success(c, "success", result)
}
