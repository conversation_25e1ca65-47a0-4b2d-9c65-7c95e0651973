// app/user/training.go
package user

import (
	"strconv"
	"tms/libs"
	"tms/model"
	"tms/services/exam"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

type TrainingAPI struct{}

var trainingService = exam.NewTrainingService()

func init() {
	api := &TrainingAPI{}
	adminV1 := GetVersion()
	trainingGroup := adminV1.Group("training", "理论试题训练接口")
	{
		trainingGroup.Register("POST", "/start", api.StartTraining, model.AuthUser, "开始训练")
		trainingGroup.Register("POST", "/submit", api.SubmitTraining, model.AuthUser, "提交训练答案")
		trainingGroup.Register("GET", "/detail/:id", api.GetTrainingDetail, model.AuthUser, "获取训练进度详情")
	}
}

// StartTraining 开始训练
func (api *TrainingAPI) StartTraining(c *gin.Context) {
	userID := c.GetInt64("userId")
	coursewareID := c.PostForm("courseware_id")

	if userID == 0 || coursewareID == "" {
		libs.Error(c, "参数错误")
		return
	}

	id, _ := strconv.ParseInt(coursewareID, 10, 64)
	err := trainingService.StartTraining(userID, id)
	if err != nil {
		libs.Error(c, err.Error())
		return
	}

	libs.Success(c, "训练已开始", nil)
}

// SubmitTraining 提交训练答案
func (api *TrainingAPI) SubmitTraining(c *gin.Context) {
	var req struct {
		ProgressID int64                  `json:"id"`
		Answers    []model.QuestionAnswer `json:"answers"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		libs.Error(c, "参数错误")
		return
	}

	err := trainingService.SubmitTraining(req.ProgressID, req.Answers)
	if err != nil {
		libs.Error(c, err.Error())
		return
	}

	libs.Success(c, "答题记录已更新", nil)
}

// GetTrainingDetail 获取训练进度详情
func (api *TrainingAPI) GetTrainingDetail(c *gin.Context) {
	progressID := cast.ToInt64(c.Param("id"))
	if progressID == 0 {
		libs.Error(c, "参数错误")
		return
	}

	progress, err := trainingService.GetTrainingProgress(progressID)
	if err != nil {
		libs.Error(c, err.Error())
		return
	}

	libs.Success(c, "查询成功", progress)
}
