package user

import (
	"encoding/json"
	"log"
	"log/slog"
	"strconv"
	"strings"
	"time"
	"tms/libs"
	"tms/model"
	"tms/pkg/cache"
	"tms/utils"

	"github.com/gin-gonic/gin"

	images "github.com/wenlng/go-captcha-assets/resources/images_v2"
	"github.com/wenlng/go-captcha-assets/resources/tiles"
	"github.com/wenlng/go-captcha/v2/slide"
)

func init() {
	UserV1 := GetVersion()
	authV1 := UserV1.Group("captcha", "验证码接口")
	{
		authV1.Register("GET", "get", slideCaptchaHandler, model.AuthNone, "获取图形验证码")
		authV1.Register("POST", "check", checkSlideDataHandler, model.AuthNone, "校验验证码")
	}
	buildSlideCaptcha()
}

var slideCapt slide.Captcha

func buildSlideCaptcha() {
	builder := slide.NewBuilder(
	// slide.WithGenGraphNumber(2),
	// slide.WithEnableGraphVerticalRandom(true),
	)

	// background images
	imgs, err := images.GetImages()
	if err != nil {
		log.Fatalln(err)
	}

	graphs, err := tiles.GetTiles()
	if err != nil {
		log.Fatalln(err)
	}

	var newGraphs = make([]*slide.GraphImage, 0, len(graphs))
	for i := 0; i < len(graphs); i++ {
		graph := graphs[i]
		newGraphs = append(newGraphs, &slide.GraphImage{
			OverlayImage: graph.OverlayImage,
			MaskImage:    graph.MaskImage,
			ShadowImage:  graph.ShadowImage,
		})
	}

	// set resources
	builder.SetResources(
		slide.WithGraphImages(newGraphs),
		slide.WithBackgrounds(imgs),
	)

	slideCapt = builder.Make()
}

func slideCaptchaHandler(c *gin.Context) {
	captData, err := slideCapt.Generate()
	if err != nil || captData == nil {
		slog.Error("generate captcha error", "err", err)
		libs.Error(c, "生成验证码失败")
		return
	}
	blockData := captData.GetData()
	if blockData == nil {
		libs.Error(c, "gen captcha data failed")
		return
	}

	var masterImageBase64, tileImageBase64 string
	masterImageBase64, err = captData.GetMasterImage().ToBase64()
	if err != nil {
		libs.Error(c, "生成验证码失败")
		return
	}
	tileImageBase64, err = captData.GetTileImage().ToBase64()
	if err != nil {
		libs.Error(c, "生成验证码失败")
		return
	}
	dotsByte, _ := json.Marshal(blockData)
	//log.Printf("====dotsByte: %v", dotsByte)
	key := utils.StringToMD5(string(dotsByte))
	//log.Printf("====key: %v", key)
	cache.CacheStorage.Set(key, dotsByte, 3*time.Minute)

	libs.Success(c, "生成验证码成功", gin.H{
		"captcha_key":  key,
		"tile_width":   blockData.Width,
		"tile_height":  blockData.Height,
		"tile_x":       blockData.TileX,
		"tile_y":       blockData.TileY,
		"image_base64": masterImageBase64,
		"tile_base64":  tileImageBase64,
	})
}

// CheckSlideData 处理滑动验证码的验证
func checkSlideDataHandler(c *gin.Context) {
	type SlideData struct {
		Point string `json:"point" binding:"required"`
		Key   string `json:"key" binding:"required"`
	}
	var req SlideData
	if err := c.ShouldBindJSON(&req); err != nil {
		libs.Error(c, "参数错误")
		return
	}
	//log.Printf("check slide data: %v", req)
	// 获取请求参数
	point := req.Point
	key := req.Key

	// 参数校验
	if point == "" || key == "" {
		libs.Error(c, "point or key param is empty")
		return
	}

	// 从缓存中读取数据
	cacheDataByte, _ := cache.CacheStorage.Get(key)
	if cacheDataByte == nil {
		libs.Error(c, "illegal key")
		return
	}
	//log.Printf("cacheDataByte: %v", cacheDataByte)
	dataBytes := cache.GetValToByte(cacheDataByte)
	//log.Printf("dataBytes: %v", dataBytes)
	// 反序列化缓存中的数据
	var dct *slide.Block
	if err := json.Unmarshal(dataBytes, &dct); err != nil {
		//slog.Error("json unmarshal error", "err", err)
		libs.Error(c, "illegal key")
		return
	}

	// 解析 point 参数
	src := strings.Split(point, ",")
	chkRet := false
	if len(src) == 2 {
		sx, err := strconv.ParseFloat(src[0], 64)
		if err != nil {
			//slog.Error("parse point error", "err", err)
			libs.Error(c, "invalid point formatr")
			return
		}
		sy, err := strconv.ParseFloat(src[1], 64)
		if err != nil {
			//slog.Error("parse point error", "err", err)
			libs.Error(c, "invalid point format")
			return
		}

		// 检查用户的滑动位置是否正确
		chkRet = slide.CheckPoint(int64(sx), int64(sy), int64(dct.X), int64(dct.Y), 4)
	}

	// 如果验证成功，返回 code 0
	if chkRet {
		libs.Success(c, "验证成功", nil)
		return
	}

	libs.Error(c, "验证失败")
}
