package user

import (
	"log"
	"tms/libs"
	"tms/model"
	"tms/pkg/db"
	"tms/services/auth"

	"github.com/gin-gonic/gin"
)

func init() {
	UserV1 := GetVersion()
	authV1 := UserV1.Group("auth", "认证接口")
	{
		authV1.Register("POST", "/login", UserLogin, model.AuthNone, "用户登录")
		authV1.Register("POST", "/change-roles", ChangeRoles, model.AuthAll, "切换角色")
		authV1.Register("POST", "/logout", UserLogout, model.AuthAll, "用户登出")
	}
}

// UserLogin 处理后台用户登录
func UserLogin(c *gin.Context) {
	var loginReq struct {
		Account  string `json:"account" binding:"required"`
		Password string `json:"password" binding:"required"`
		UserCode string `json:"user_code"`
	}
	if err := c.ShouldBindJSON(&loginReq); err != nil {
		log.Printf("Failed to bind JSON: %v", err)
		libs.Error(c, "参数错误")
		return
	}

	result, err := auth.Login(loginReq.Account, loginReq.Password, loginReq.UserCode, c)
	if err != nil {
		libs.Error(c, err.Error())
		return
	}

	libs.Success(c, "User login", result)
}
func ChangeRoles(c *gin.Context) {
	type reqCode struct {
		UserCode string `json:"user_code" binding:"required"`
	}
	var req reqCode
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Printf("Failed to bind JSON: %v", err)
		libs.Error(c, "参数错误")
		return
	}

	// 获取当前用户ID
	userId := c.GetInt64("userId")

	menus, err := auth.ChangeRoles(req.UserCode)
	if err != nil {
		libs.Error(c, err.Error())
		return
	}
	newToken, err := auth.GetToken(userId, c.GetStringSlice("roleCodes"), req.UserCode)
	if err != nil {
		libs.Error(c, err.Error())
		return
	}
	var sysCode string
	err = db.DB.Model(&model.Roles{}).Where("role_code = ?", req.UserCode).Pluck("sys_code", &sysCode).Error
	if err != nil {
		libs.Error(c, err.Error())
		return
	}

	libs.Success(c, "User change roles", gin.H{
		"menus":    menus,
		"token":    newToken, // 返回新生成的token
		"sys_code": sysCode,
	})
}

// UserLogout 处理后台用户登出
func UserLogout(c *gin.Context) {
	err := auth.Logout(c)
	if err != nil {
		log.Printf("Failed to logout: %v", err)
		libs.Error(c, err.Error())
		return
	}
	libs.Success(c, "Admin User logout", nil)
}
