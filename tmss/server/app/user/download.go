package user

import (
	"strings"
	"tms/libs"
	"tms/model"

	"tms/services/auth"
	"tms/services/tech"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

type DownloadAPI struct{}

var (
	coursewareService = tech.NewCoursewareService()
)

func init() {
	var api = &DownloadAPI{}

	userV1 := GetVersion()
	downloadV1 := userV1.Group("download", "下载接口")
	{
		downloadV1.Register("POST", "/confirm", api.DownloadConfirm, model.AuthUser, "下载确认")
		downloadV1.Register("POST", "/file", api.DownloadFile, model.AuthUser, "下载文件")
	}
}

type DownloadReq struct {
	Object string `json:"object" binding:"required"` // 下载对象 courseware, resource
	Value  string `json:"value" binding:"required"`  // ids [1,2,3,4]
}

// DownloadConfirm 确认下载文件是否存在
func (api *DownloadAPI) DownloadConfirm(c *gin.Context) {
	var req DownloadReq
	if err := c.ShouldBindJSON(&req); err != nil {
		libs.Error(c, "参数错误")
		return
	}

	// 解析ID列表
	var ids []int64
	for _, str := range strings.Split(req.Value, ",") {
		ids = append(ids, cast.ToInt64(str))
	}

	if len(ids) == 0 {
		libs.Error(c, "请至少选择一个文件")
		return
	}

	switch req.Object {
	case "courseware":
		// 检查课件是否存在
		for _, id := range ids {
			exists, err := coursewareService.CheckCoursewareExists(id)
			if err != nil {
				libs.Error(c, "检查课件失败: "+err.Error())
				return
			}
			if !exists {
				libs.Success(c, "课件不存在", map[string]bool{"exists": false})
				return
			}
		}

	case "resource":
		// 检查资源是否存在
		for _, id := range ids {
			exists, err := resourceService.CheckResourceExists(id)
			if err != nil {
				libs.Error(c, "检查资源失败: "+err.Error())
				return
			}
			if !exists {
				libs.Success(c, "资源不存在", map[string]bool{"exists": false})
				return
			}
		}

	default:
		libs.Error(c, "不支持的下载对象类型")
	}
}

// DownloadFile 下载文件
func (api *DownloadAPI) DownloadFile(c *gin.Context) {
	var req DownloadReq
	if err := c.ShouldBindJSON(&req); err != nil {
		libs.Error(c, "参数错误")
		return
	}

	claims, err := auth.GetUserClaims(c)
	if err != nil || claims.ID == 0 {
		libs.Error(c, "用户认证失败")
		return
	}

	// 解析ID列表
	var ids []int64
	for _, str := range strings.Split(req.Value, ",") {
		ids = append(ids, cast.ToInt64(str))
	}

	if len(ids) == 0 {
		libs.Error(c, "请至少选择一个文件")
		return
	}

	switch req.Object {
	case "courseware":
		// 下载并解压课件
		if err := coursewareService.DownloadAndUnzipCourseware(ids); err != nil {
			libs.Error(c, "下载课件失败: "+err.Error())
			return
		}
		libs.Success(c, "课件下载成功", nil)
	case "resource":
		// 下载资源
		if err := resourceService.DownloadResource(claims.ID, ids); err != nil {
			libs.Error(c, "下载资源失败: "+err.Error())
			return
		}
		libs.Success(c, "资源下载成功", nil)
	default:
		libs.Error(c, "不支持的下载对象类型")
	}
}
