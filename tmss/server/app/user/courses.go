package user

import (
	"log/slog"
	"tms/libs"
	"tms/model"
	"tms/services/tech"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

type CoursesAPI struct{}

var coursesService = tech.NewCoursesService()
var chapterService = tech.NewChapterService()
var questionService = tech.NewQuestionService()

func init() {
	api := &CoursesAPI{}
	adminV1 := GetVersion()
	coursesGroup := adminV1.Group("courses", "课程接口")
	{
		coursesGroup.Register("GET", "/list", api.GetCoursesList, model.AuthUser, "获取课程列表")
		coursesGroup.Register("GET", "chapter_list/:id", api.GetChapterList, model.AuthUser, "获取章节列表")
		coursesGroup.Register("GET", "courseware_list/:id", api.GetCoursewareList, model.AuthUser, "获取课件列表")
		coursesGroup.Register("GET", "question_list/:id", api.GetQuestionList, model.AuthUser, "获取理论试题列表")
		coursesGroup.Register("POST", "/clear_cache/:id", api.ClearCoursesCache, model.AuthUser, "清除课程缓存")
	}
}

// GetCoursesList 获取课程列表
func (api *CoursesAPI) GetCoursesList(c *gin.Context) {
	userId := c.GetInt64("userId")
	if userId == 0 {
		libs.Error(c, "无效的用户ID")
		return
	}
	page := cast.ToInt(c.DefaultQuery("page", "1"))
	pageSize := cast.ToInt(c.DefaultQuery("page_size", "10"))

	resp, err := coursesService.GetPublishedCoursesByUserID(userId, page, pageSize)
	if err != nil {
		libs.Error(c, err.Error())
		return
	}

	libs.Success(c, "查询成功", resp)
}
func (api *CoursesAPI) GetChapterList(c *gin.Context) {
	id := cast.ToInt64(c.Param("id"))
	if id == 0 {
		libs.Error(c, "无效的课程ID")
		return
	}
	resp, err := chapterService.GetChapterListByCourseID(id)
	if err != nil {
		libs.Error(c, err.Error())
		return
	}
	libs.Success(c, "查询成功", resp)

}
func (api *CoursesAPI) GetCoursewareList(c *gin.Context) {
	id := cast.ToInt64(c.Param("id"))
	if id == 0 {
		libs.Error(c, "无效的章节ID")
		return
	}
	resp, err := chapterService.GetCoursewareListByChapterID(id)
	if err != nil {
		libs.Error(c, err.Error())
		return
	}
	libs.Success(c, "查询成功", resp)
}

// 根据章节ID获取试题列表
func (api *CoursesAPI) GetQuestionList(c *gin.Context) {
	id := cast.ToInt64(c.Param("id"))
	if id == 0 {
		libs.Error(c, "无效的章节ID")
		return
	}
	resp, err := questionService.GetQuestionsByChapterID(id)
	if err != nil {
		libs.Error(c, err.Error())
		return
	}
	libs.Success(c, "查询成功", resp)
}

func (api *CoursesAPI) ClearCoursesCache(c *gin.Context) {
	id := cast.ToInt64(c.Param("id")) // course_id
	if id == 0 {
		libs.Error(c, "无效的章节ID")
		return
	}

	coursewares, resources, _, err := coursesService.GetAttachedAttributes(id)
	if err != nil {
		slog.Error("获取课程关联数据失败", "error", err)
		libs.Error(c, "清除缓存失败")
		return
	}

	for _, v := range coursewares {
		if err := coursewareService.ClearCourseCache(v.ID); err != nil {
			libs.Error(c, "清除缓存失败")
			return
		}
	}
	for _, v := range resources {
		if err := resourceService.ClearResourceCache(v.ID); err != nil {
			libs.Error(c, "清除缓存失败")
			return
		}
	}

	libs.Success(c, "清除成功", nil)
}
