# 定义应用程序名称
APP_NAME := tmss-server

# 定义编译输出目录
BUILD_DIR := bin

# 定义 Go 编译相关变量
GO := go
GOARCH ?= amd64
GO_BUILD := $(GO) build -o $(BUILD_DIR)/$(APP_NAME)
GO_TEST := $(GO) test ./...
GO_FMT := $(GO) fmt ./...
GO_MOD_TIDY := $(GO) mod tidy

# 定义静态检测工具 (需要安装 golangci-lint: curl -sSfL https://raw.githubusercontent.com/golangci/golangci-lint/master/install.sh | sh -s -- -b $(shell go env GOPATH)/bin v1.59.1)
LINTER := $(shell go env GOPATH)/bin/golangci-lint

.PHONY: all clean build run test fmt lint windows linux darwin

all: build

# 清理编译生成的文件
clean:
	@echo "清理编译生成的文件..."
	@rm -rf $(BUILD_DIR)
	@echo "清理完成。"

# 编译所有平台
build: windows linux darwin

# 编译 Windows 平台可执行文件
windows:
	@echo "编译 Windows 版本..."
	@mkdir -p $(BUILD_DIR)
	@GOOS=windows GOARCH=$(GOARCH) $(GO_BUILD)_windows.exe
	@echo "Windows 版本编译完成: $(BUILD_DIR)/$(APP_NAME)_windows.exe"

# 编译 Linux 平台可执行文件
linux:
	@echo "编译 Linux 版本..."
	@mkdir -p $(BUILD_DIR)
	@GOOS=linux GOARCH=$(GOARCH) $(GO_BUILD)_linux
	@echo "Linux 版本编译完成: $(BUILD_DIR)/$(APP_NAME)_linux"

# 编译 macOS 平台可执行文件
darwin:
	@echo "编译 macOS 版本..."
	@mkdir -p $(BUILD_DIR)
	@GOOS=darwin GOARCH=$(GOARCH) $(GO_BUILD)_darwin
	@echo "macOS 版本编译完成: $(BUILD_DIR)/$(APP_NAME)_darwin"

# 运行程序
run:
	@echo "运行程序..."
	@$(GO) run main.go

# 运行测试
test:
	@echo "运行测试..."
	@$(GO_TEST)

# 格式化 Go 代码并优化 import
fmt:
	@echo "格式化 Go 代码并优化 import..."
	@$(GO_FMT)
	@$(GO_MOD_TIDY)
	@echo "格式化和 import 优化完成。"

# 静态检测
lint:
	@echo "运行静态检测..."
	@if [ -x "$(LINTER)" ]; then \
		$(LINTER) run; \
	else \
		echo "golangci-lint 未安装或不在 PATH 中。请安装: curl -sSfL https://raw.githubusercontent.com/golangci/golangci-lint/master/install.sh | sh -s -- -b $(shell go env GOPATH)/bin v1.59.1"; \
		exit 1; \
	fi
	@echo "静态检测完成。"
