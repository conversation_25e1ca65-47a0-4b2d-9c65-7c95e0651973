package sync

import (
	"fmt"
	"log"
	"time"
	"tms/model"
	"tms/pkg/db"
)

// QueryTask 定义查询任务结构体
type QueryTask struct {
	UserID int64
}

// QueryTaskChan 查询任务队列
var (
	QueryTaskChan = make(chan QueryTask, 100)
)

// TaskQueue 用于处理 HandleProgressSync 的任务队列
var (
	ProgressTaskChan = make(chan model.SyncRecords, 100) // 队列缓冲大小可根据需要调整
)

// StartQueryWorker 启动查询任务工作协程
func StartQueryWorker() {
	go func() {
		log.Println("Starting query worker...")
		for task := range QueryTaskChan {
			fmt.Printf("Querying unsynced records for user %d\n", task.UserID)
			err := ProcessUserUnsyncedRecords(task.UserID)
			if err != nil {
				fmt.Printf("Error querying unsynced records for user %d: %v\n", task.UserID, err)
			}
		}
	}()
}
func GetUserUnsyncedRecord(userID int64) ([]model.SyncRecords, error) {
	var task []model.SyncRecords
	err := db.DB.Where("user_id = ? AND is_synced = ? AND sync_object = ?", userID, false, "client").Find(&task).Error
	HandleProgressSync(userID)

	return task, err
}

// ProcessUserUnsyncedRecords 查询用户所有未同步的数据并逐条入队处理
func ProcessUserUnsyncedRecords(userID int64) error {
	var syncRecords []model.SyncRecords
	if err := db.DB.Where("user_id = ? AND is_synced = ?", userID, false).Find(&syncRecords).Error; err != nil {
		return fmt.Errorf("failed to query unsynced records for user %d: %v", userID, err)
	}

	for _, record := range syncRecords {
		ProgressTaskChan <- record
	}

	return nil
}

// StartProgressWorker 启动一个后台协程来处理同步任务
func StartProgressWorker() {
	go func() {
		log.Println("Starting progress worker...")
		for task := range ProgressTaskChan {
			log.Printf("Processing task %v\n", task.EventID)
			err := ProcessSync(task)
			if err != nil {
				fmt.Printf("Error processing task %+v: %v\n", task, err)
			}
		}
	}()
}

// ProcessSync 统一处理同步逻辑
func ProcessSync(task model.SyncRecords) error {
	processor, ok := SyncProcessorsMap[task.SyncType]
	if !ok {
		return fmt.Errorf("no processor found for sync type: %s", task.SyncType)
	}

	err := processor(task)
	if err != nil {
		return fmt.Errorf("failed to process data for %s: %v", task.SyncType, err)
	}
	// 处理成功后更新 IsSynced 和 UpdatedAt
	now := time.Now().Unix()
	if err := db.DB.Model(&model.SyncRecords{}).
		Where("id = ?", task.ID).
		Updates(map[string]interface{}{
			"is_synced":  true,
			"updated_at": now,
		}).Error; err != nil {
		return fmt.Errorf("failed to update sync status for record %d: %v", task.ID, err)
	}

	return nil
}
