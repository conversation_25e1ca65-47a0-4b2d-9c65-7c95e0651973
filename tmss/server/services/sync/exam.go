package sync

import (
	"encoding/json"
	"fmt"
	"log/slog"
	"tms/model"
	"tms/pkg/db"

	"gorm.io/gorm"
)

// 写入考试记录
func WriteExamRecordsData(data string, userID int64) error {
	fmt.Printf("ExamRecords: Writing data for user %d: %s\n", userID, data)
	// 实际业务逻辑，例如数据库操作
	return nil
}

// 处理考试记录
func ProcessExamRecords(task model.SyncRecords) error {
	fmt.Printf("ExamRecords: Processing data for user %d\n", task.UserID)
	// 实际业务逻辑
	var data model.UserExamEntry
	if err := json.Unmarshal([]byte(task.SyncData), &data); err != nil {
		return err
	}

	if data.Exam != nil {
		if err := handleExamData(data.Exam); err != nil {
			return err
		}
	}

	if data.ExamProgress != nil {
		if err := handleExamProgressData(data.ExamProgress); err != nil {
			return err
		}
	}

	if data.ExamAnswers != nil {
		if err := handleExamAnswersData(data.ExamProgress, data.ExamAnswers); err != nil {
			return err
		}
	}

	return nil
}

func handleExamData(exam *model.Exams) error {
	// 处理考试数据
	fmt.Printf("ExamRecords: Handling exam data: %+v\n", exam)
	if err := db.DB.Model(&model.Exams{}).
		Where("id = ?", exam.ID).
		Updates(map[string]interface{}{
			"status": exam.Status,
		}).Error; err != nil {
		slog.Error("Failed to handle exam data", slog.Any("err", err))
		return err
	}

	return nil
}

func handleExamProgressData(progress *model.ExamProgress) error {
	// 处理考试进度数据
	fmt.Printf("ExamRecords: Handling exam progress data: %+v\n", progress)
	if err := db.DB.Model(&model.ExamProgress{}).
		Where("id = ?", progress.ID).
		Save(progress).Error; err != nil {
		slog.Error("Failed to handle exam progress data", slog.Any("err", err))
		return err
	}

	return nil
}

func handleExamAnswersData(progress *model.ExamProgress, answers []model.ExamAnswer) error {
	// 处理考试答案数据
	fmt.Printf("ExamRecords: Handling exam answers data: %+v\n", answers)
	if progress == nil || len(answers) == 0 {
		return nil
	}

	err := db.DB.Transaction(func(tx *gorm.DB) error {
		if err := tx.Model(&model.ExamAnswer{}).
			Where("exam_progress_id = ?", progress.ID).
			Delete(&model.ExamAnswer{}).Error; err != nil {
			return err
		}

		if err := tx.Model(&model.ExamAnswer{}).
			Create(&answers).Error; err != nil {
			return err
		}

		return nil
	})
	if err != nil {
		slog.Error("Failed to handle exam answers data", slog.Any("err", err))
		return err
	}

	return nil
}
