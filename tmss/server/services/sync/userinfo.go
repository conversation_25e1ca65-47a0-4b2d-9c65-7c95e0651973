package sync

import (
	"encoding/json"
	"fmt"
	"tms/model"
	us "tms/services/user"
)

// 写入用户信息
func WriteUserInfoData(data string, userID int64) error {
	fmt.Printf("UserInfo: Writing data for user %d: %s\n", userID, data)
	// 实际业务逻辑，例如数据库操作
	return nil
}

// 删除用户信息
func DeleteUserInfoData(data string, userID int64) error {
	fmt.Printf("UserInfo: Deleting data for user %d: %s\n", userID, data)
	// 实际业务逻辑，例如数据库操作
	return nil
}

// 处理用户信息
func ProcessUpdateUserInfo(task model.SyncRecords) error {
	fmt.Printf("UserInfo: Processing update data for user %d\n", task.UserID)
	// 实际业务逻辑

	switch task.SyncType {
	case UpdateUserInfoSyncType:
		var updateUserObj us.UpdateUser
		if err := json.Unmarshal([]byte(task.SyncData), &updateUserObj); err != nil {
			return err
		}
		userService := us.NewUserService()
		if err := userService.UpdateUser(&updateUserObj); err != nil {
			return err
		}

	default:
		return fmt.Errorf("unsupported sync type: %s", task.SyncType)
	}

	return nil
}

func ProcessDeleteUserInfo(task model.SyncRecords) error {
	fmt.Printf("UserInfo: Processing delete data for user %d\n", task.UserID)
	switch task.SyncType {
	case DeleteUserInfoSyncType:
		var updateUserObj us.UpdateUser
		if err := json.Unmarshal([]byte(task.SyncData), &updateUserObj); err != nil {
			return err
		}
		userService := us.NewUserService()
		if err := userService.DeleteUser(updateUserObj.User.ID); err != nil {
			return err
		}

	default:
		return fmt.Errorf("unsupported sync type: %s", task.SyncType)
	}

	return nil
}
