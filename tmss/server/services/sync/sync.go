package sync

import (
	"fmt"
	"time"
	"tms/model"
	"tms/pkg/db"
)

func init() {
	StartQueryWorker()
	StartProgressWorker()
}

// SyncHandlerFunc 定义同步处理器的函数签名
type SyncHandlerFunc func(data string, userID int64) error

// SyncProcessorFunc 定义同步数据处理的函数签名
type SyncProcessorFunc func(task model.SyncRecords) error

// SyncHandlersMap 存储同步类型的处理器
var SyncHandlersMap = map[string]SyncHandlerFunc{
	UpdateUserInfoSyncType: WriteUserInfoData,
	DeleteUserInfoSyncType: DeleteUserInfoData,
	"Learning":             WriteLearningProgressData,
	ExamSyncType:           WriteExamRecordsData,
	"StudyRecords":         WriteStudyRecordsData,
}

// SyncProcessorsMap 存储同步类型的数据处理逻辑
var SyncProcessorsMap = map[string]SyncProcessorFunc{
	UpdateUserInfoSyncType: ProcessUpdateUserInfo,
	DeleteUserInfoSyncType: ProcessDeleteUserInfo,
	"Learning":             ProcessLearningProgress,
	ExamSyncType:           ProcessExamRecords,
	"StudyRecords":         ProcessStudyRecords,
}

// 写入数据
func HandleWriteSync(syncType, syncObject, data string, userID int64, eventID string) error {
	handler, ok := SyncHandlersMap[syncType]
	if !ok {
		return fmt.Errorf("unsupported sync type: %s", syncType)
	}

	saveData := model.SyncRecords{
		EventID:    eventID,
		SyncType:   syncType,
		SyncObject: syncObject,
		SyncData:   data,
		CreatedAt:  time.Now().Unix(),
		UserID:     userID,
	}
	err := db.DB.Create(&saveData).Error
	if err != nil {
		return err
	}
	err = handler(data, userID)
	if err != nil {
		return fmt.Errorf("failed to write data for %s: %v", syncType, err)
	}

	return nil
}

// 处理数据
func HandleProgressSync(userID int64) error {
	QueryTaskChan <- QueryTask{UserID: userID}
	return nil
}
