package resource

import (
	"errors"
	"log"
	"time"
	"tms/model"
	"tms/pkg/db"
)

// CreateResourceCategory 创建资料分类
func (api *ResourceService) CreateResourceCategory(req model.ResourceCategory, userId int64) error {
	if userId == 0 {
		return errors.New("无效的用户ID")
	}
	req.UserID = userId
	req.CreatedAt = time.Now().Unix()
	req.UpdatedAt = req.CreatedAt

	if err := db.DB.Create(&req).Error; err != nil {
		return errors.New("创建失败: " + err.Error())
	}

	return nil
}

// UpdateResourceCategory 更新资料分类
func (api *ResourceService) UpdateResourceCategory(req model.ResourceCategory) error {

	var category model.ResourceCategory
	if err := db.DB.First(&category, req.ID).Error; err != nil {
		return errors.New("未找到对应分类")
	}
	log.Printf("category.ChapterID: %d, req.ChapterID: %d", category.ChapterID, req.ChapterID)
	if category.ChapterID != req.ChapterID {
		if err := db.DB.Model(&model.Resources{}).
			Where("category_id = ?", req.ID).
			Update("chapter_id", req.ChapterID).Error; err != nil {
			return errors.New("更新失败: " + err.Error())
		}
	}
	now := time.Now().Unix()

	updates := map[string]interface{}{
		"name":        req.Name,
		"description": req.Description,
		"parent_id":   req.ParentID,
		"chapter_id":  req.ChapterID,
		"updated_at":  now,
	}

	if err := db.DB.Model(&category).
		Where("id = ?", req.ID).
		Updates(updates).Error; err != nil {
		return errors.New("更新失败: " + err.Error())
	}

	return nil
}

// DeleteResourceCategory 删除资料分类
func (api *ResourceService) DeleteResourceCategory(id int64) error {
	if id == 0 {
		return errors.New("无效的分类ID")
	}

	var category model.ResourceCategory
	if err := db.DB.First(&category, id).Error; err != nil {
		return errors.New("未找到对应分类")
	}

	if err := db.DB.Delete(&category).Error; err != nil {
		return errors.New("删除失败: " + err.Error())
	}

	return nil
}

// GetResourceCategoryList 获取资料分类列表（支持搜索和树状结构）
func (api *ResourceService) GetResourceCategoryList(req model.ReqResourceCategorySearch) ([]*model.ResourceCategoryTreeNode, error) {

	dbQuery := db.DB.Model(&model.ResourceCategory{})
	if req.Name != "" {
		dbQuery = dbQuery.Where("name ILIKE ?", "%"+req.Name+"%")
	}
	if req.ParentID > 0 {
		dbQuery = dbQuery.Where("parent_id = ?", req.ParentID)
	}

	var categories []model.ResourceCategory
	if err := dbQuery.Order("id ASC").Find(&categories).Error; err != nil {
		return nil, errors.New("获取数据失败")
	}

	return buildResourceCategoryTree(categories), nil
}

// buildResourceCategoryTree 将平级数据构建成树状结构
func buildResourceCategoryTree(list []model.ResourceCategory) []*model.ResourceCategoryTreeNode {
	categoryMap := make(map[int64]*model.ResourceCategoryTreeNode)

	// 第一步：初始化所有节点为指针，并存入 map
	for _, category := range list {
		categoryMap[category.ID] = &model.ResourceCategoryTreeNode{
			ID:          category.ID,
			Name:        category.Name,
			Description: category.Description,
			ParentID:    category.ParentID,
			ChapterID:   category.ChapterID,
			Children:    make([]*model.ResourceCategoryTreeNode, 0),
		}
	}
	var tree []*model.ResourceCategoryTreeNode
	for _, m := range list {
		node := categoryMap[m.ID]

		if m.ParentID == 0 {
			tree = append(tree, node)
		} else {
			if parent, ok := categoryMap[m.ParentID]; ok {
				parent.Children = append(parent.Children, node)
			}
		}
	}

	return tree
}
