package resource

import (
	"errors"
	"fmt"
	"io"
	"log"
	"mime/multipart"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"
	"tms/libs"
	"tms/model"
	"tms/pkg/config"
	"tms/pkg/db"
	"tms/services/user"
	"tms/utils"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"gorm.io/gorm"
)

type ResourceService struct{}

func NewResourceService() *ResourceService {
	return &ResourceService{}
}

var AllowResourceExt = []string{".pdf", ".docx", ".pptx", ".md", ".xlsx", ".mp4", ".txt"}

// CreateResourceDraft 创建资料草稿（只需分类ID，其他从上传文件提取）
func (api *ResourceService) CreateResourceDraft(form *multipart.Form, userId int64) (int, error) {

	if userId == 0 {
		return 0, errors.New("无效的用户ID")
	}

	files := form.File["files"] // 假设前端通过字段名 files 上传多个文件
	if len(files) == 0 {
		return 0, errors.New("未上传任何文件")
	}
	values, ok := form.Value["category_id"]
	if !ok || len(values) == 0 {
		return 0, errors.New("缺少分类ID")
	}

	categoryId := cast.ToInt64(values[0])
	if categoryId == 0 {
		return 0, errors.New("无效的分类ID")
	}
	minutesValues, ok := form.Value["minutes"]
	if !ok || len(minutesValues) == 0 {
		return 0, errors.New("缺少时长")
	}
	minutes := cast.ToInt(minutesValues[0])
	chapterId, err := api.GetChapterID(categoryId)
	if err != nil {
		return 0, err
	}
	// 构建上传目录路径（按年月日分层）
	now := time.Now()
	uploadDir := fmt.Sprintf("./uploads/resource/%d/%02d/%02d", now.Year(), now.Month(), now.Day())

	// 创建目录（包括所有必要父目录）
	if err := os.MkdirAll(uploadDir, os.ModePerm); err != nil {
		return 0, errors.New("创建上传目录失败: " + err.Error())
	}

	// 开启事务
	tx := db.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 批量构建资源对象
	var resources []model.Resources
	for _, fileHeader := range files {
		// 从文件中提取信息
		name := fileHeader.Filename
		size := fileHeader.Size
		ext := filepath.Ext(name)
		if !utils.HasContainsStrings(AllowResourceExt, ext) {
			tx.Rollback()
			return 0, errors.New("文件格式错误")
		}
		nameWithoutExt := strings.TrimSuffix(name, ext)

		// 构建存储路径（可替换为唯一文件名）
		dstPath := fmt.Sprintf("%s/%s", uploadDir, name)
		if libs.PathExists(dstPath) {
			tx.Rollback()
			return 0, errors.New("文件已存在")
		}
		// 打开上传的文件
		file, err := fileHeader.Open()
		if err != nil {
			tx.Rollback()
			return 0, errors.New("打开文件失败: " + err.Error())
		}
		defer file.Close()

		// 创建目标文件
		out, err := os.Create(dstPath)
		if err != nil {
			tx.Rollback()
			return 0, errors.New("创建文件失败: " + err.Error())
		}
		defer out.Close()

		// 流式写入文件
		if _, err := io.Copy(out, file); err != nil {
			tx.Rollback()
			return 0, errors.New("写入文件失败: " + err.Error())
		}

		// 构建资源模型
		resource := model.Resources{
			Name:        nameWithoutExt,
			Path:        dstPath, // 或者相对路径
			Size:        size,
			Ext:         ext,
			Description: "", // 可选：支持前端传描述或默认为空
			UserID:      userId,
			ChapterID:   chapterId,
			Minutes:     minutes,
			Status:      model.ResourceStatusDraft,
			CreatedAt:   now.Unix(),
			CategoryID:  categoryId,
		}

		resources = append(resources, resource)
	}

	// 批量插入数据库
	if err := tx.Create(&resources).Error; err != nil {
		tx.Rollback()
		return 0, errors.New("批量创建失败: " + err.Error())
	}

	tx.Commit()
	return len(resources), nil
}

func (api *ResourceService) GetChapterID(categoryId int64) (int64, error) {
	var chapterId int64
	if err := db.DB.Model(&model.ResourceCategory{}).Where("id = ?", categoryId).Pluck("chapter_id", &chapterId).Error; err != nil {
		return 0, errors.New("未找到对应分类")
	}
	return chapterId, nil
}

// UpdateResourceDraft 更新资料草稿（仅允许修改分类、名称和描述）
func (api *ResourceService) UpdateResourceDraft(req model.ReqResourceUpdate) error {
	if req.ID <= 0 {
		return errors.New("无效的资料ID")
	}

	var resource model.Resources
	if err := db.DB.First(&resource, req.ID).Error; err != nil {
		return errors.New("未找到对应资料")
	}

	if resource.Status != model.ResourceStatusDraft {
		return errors.New("只能编辑草稿")
	}
	chapterId, err := api.GetChapterID(req.CategoryID)
	if err != nil {
		return errors.New("获取章节id失败: " + err.Error())
	}
	now := time.Now().Unix()
	if req.Path != resource.Path {
		// 删除旧文件
		if fileInfo, err := os.Stat(resource.Path); err == nil {
			if err := os.Remove(resource.Path); err != nil {
				return errors.New("删除文件失败: " + err.Error())
			}
			req.Size = fileInfo.Size()
		} else if !os.IsNotExist(err) {
			return errors.New("检查文件状态失败: " + err.Error())
		}
		resource.Path = req.Path
	}
	// 只更新允许修改的字段
	updates := map[string]interface{}{
		"name":        req.Name,
		"description": req.Description,
		"category_id": req.CategoryID,
		"path":        req.Path,
		"size":        req.Size,
		"ext":         filepath.Ext(req.Path),
		"chapter_id":  chapterId,
		"minutes":     req.Minutes,
		"updated_at":  now,
	}

	tx := db.DB.Begin()
	if err := tx.Model(&resource).Updates(updates).Error; err != nil {
		tx.Rollback()
		return errors.New("更新失败: " + err.Error())
	}

	tx.Commit()
	return nil
}

// DeleteResource 删除资料（同时删除数据库记录和实际文件）
func (api *ResourceService) DeleteResource(id int64) error {
	if id == 0 {
		return errors.New("无效的资料ID")
	}

	var resource model.Resources
	if err := db.DB.First(&resource, id).Error; err != nil {
		return errors.New("未找到对应资料")
	}

	if resource.Status != model.ResourceStatusDraft {
		return errors.New("只能删除草稿")
	}

	// 删除文件
	if _, err := os.Stat(resource.Path); err == nil {
		if err := os.Remove(resource.Path); err != nil {
			return errors.New("删除文件失败: " + err.Error())
		}
	} else if !os.IsNotExist(err) {
		return errors.New("检查文件状态失败: " + err.Error())
	}

	// 删除数据库记录
	if err := db.DB.Delete(&resource).Error; err != nil {
		return errors.New("删除失败: " + err.Error())
	}

	return nil
}

// GetResourceList 获取资料列表
func (api *ResourceService) GetResourceList(c *gin.Context, req model.ReqResourceSearch) (model.RespResourceList, error) {
	var res model.RespResourceList

	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 || req.PageSize > 100 {
		req.PageSize = 10
	}

	dbQuery := db.DB.Model(&model.Resources{})

	if req.Name != "" {
		dbQuery = dbQuery.Where("name ILIKE ?", "%"+req.Name+"%")
	}
	if req.Status != "" {
		dbQuery = dbQuery.Where("status = ?", req.Status)
	}
	// if req.UserID > 0 {
	// 	dbQuery = dbQuery.Where("user_id = ?", req.UserID)
	// }
	dbQuery, has := user.ApplyDataRules(c, dbQuery, model.ModuleSyllabus)
	if !has && req.UserID > 0 {
		dbQuery = dbQuery.Where("user_id = ?", req.UserID)
	}
	if req.CategoryID > 0 {
		dbQuery = dbQuery.Where("category_id = ?", req.CategoryID)
	}
	if req.ChapterID > 0 {
		dbQuery = dbQuery.Where("chapter_id = ?", req.ChapterID)
	}
	if err := dbQuery.Count(&res.Total).Error; err != nil {
		return res, errors.New("查询总数失败")
	}

	offset := (req.Page - 1) * req.PageSize
	var list []model.Resources
	if err := dbQuery.Offset(offset).Limit(req.PageSize).Order("created_at DESC").Find(&list).Error; err != nil {
		return res, errors.New("查询数据失败: " + err.Error())
	}
	var cids []int64
	for _, resource := range list {
		cids = append(cids, resource.CategoryID)
	}
	var categories []model.ResourceCategory
	err := db.DB.Where("id IN (?)", cids).Find(&categories).Error
	if err != nil {
		return res, errors.New("查询分类失败: " + err.Error())
	}
	cidsMap := make(map[int64]string)
	for _, category := range categories {
		cidsMap[category.ID] = category.Name
	}

	var chapterIds []int64
	for _, resource := range list {
		chapterIds = append(chapterIds, resource.ChapterID)
	}
	var chapters []*model.Chapter
	err = db.DB.Where("id IN (?)", chapterIds).Find(&chapters).Error
	if err != nil {
		return res, errors.New("查询章节失败: " + err.Error())
	}
	chapterMap := make(map[int64]string)
	for _, chapter := range chapters {
		chapterMap[chapter.ID] = chapter.Name
	}
	var respList []*model.RespResourceData
	var resourceIds []int64
	for _, r := range list {
		resourceIds = append(resourceIds, r.ID)
	}
	var revlist []model.RevisionRecord

	// 构建查询
	if err := db.DB.Model(&model.RevisionRecord{}).Where("module_key = ? AND original_id IN (?)", model.ModuleResources, resourceIds).
		Order("id DESC").Find(&revlist).Error; err != nil {
		return res, errors.New("获取修订数据失败: " + err.Error())
	}

	for _, data := range list {
		var downloadLog model.ResourceDownloadLog
		if err := db.DB.Where("resource_id = ?", data.ID).Order("download_time DESC").First(&downloadLog).Error; err != nil {
			if !errors.Is(err, gorm.ErrRecordNotFound) {
				return res, errors.New("查询数据失败: " + err.Error())
			}
		}
		detail := model.RespResourceData{
			Resources:    data,
			CategoryName: cidsMap[data.CategoryID],
			ChapterName:  chapterMap[data.ChapterID],
			DownloadURL:  strings.TrimPrefix(downloadLog.Path, "data/resources/"),
		}
		if len(revlist) > 0 {
			for _, r := range revlist {
				if r.OriginalID == data.ID {
					detail.Rev = &r
				}
			}
		}
		respList = append(respList, &detail)
	}
	res.List = respList
	return res, nil
}

func (s *ResourceService) GetResourceContent(id int64) (model.Resources, error) {
	var res model.Resources
	if err := db.DB.Where("id = ?", id).First(&res).Error; err != nil {
		return res, errors.New("查询数据失败: " + err.Error())
	}
	return res, nil
}

func (s *ResourceService) DownloadResource(userID int64, ids []int64) error {
	if len(ids) == 0 {
		return errors.New("请至少选择一个文件")
	}

	downLoadPathFolder := filepath.Join("data", "resources")
	if err := os.MkdirAll(downLoadPathFolder, os.ModePerm); err != nil {
		return errors.New("创建文件夹失败: " + err.Error())
	}

	// 使用 goroutine 并发处理下载，并使用 WaitGroup 等待所有下载完成
	var wg sync.WaitGroup
	errCh := make(chan error, len(ids)) // 用于收集下载过程中的错误

	// 创建一个速率限制器，每秒允许5个请求
	rateLimiter := make(chan struct{}, 5)
	ticker := time.NewTicker(200 * time.Millisecond) // 每200毫秒发送一个令牌

	for _, id := range ids {
		<-ticker.C                // 等待令牌
		rateLimiter <- struct{}{} // 获取一个许可

		wg.Add(1)
		go func(resourceID int64) {
			defer wg.Done()
			defer func() { <-rateLimiter }() // 释放许可

			var res model.Resources
			if err := db.DB.Where("id = ?", resourceID).First(&res).Error; err != nil {
				errCh <- fmt.Errorf("查询数据失败: %w", err)
				return
			}

			remote := config.Config.System.Scheme + "://" + config.Config.System.Host + "/"
			remoteFilePath := strings.TrimPrefix(res.Path, "./")
			remoteFilePath = remote + remoteFilePath
			var downloadFilePath string
			// 确定下载文件的保存路径
			now := time.Now()
			year := now.Year()
			month := cast.ToInt(now.Month())
			day := now.Day()
			downloadDir := filepath.Join("data", "resources", cast.ToString(year), cast.ToString(month), cast.ToString(day))
			if err := os.MkdirAll(downloadDir, os.ModePerm); err != nil {
				errCh <- fmt.Errorf("创建文件夹失败: %w", err)
				return
			}
			downloadFilePath = filepath.Join(downloadDir, filepath.Base(res.Path))
			log.Printf("下载资源远程路径: %s, 本地保存路径: %s\n", remoteFilePath, downloadFilePath)
			if err := utils.DownloadFile(remoteFilePath, downloadFilePath); err != nil {
				errCh <- fmt.Errorf("下载文件失败: %w", err)
				return
			}

			// 生成下载记录
			if err := db.DB.Create(&model.ResourceDownloadLog{
				ResourceID:   resourceID,
				UserID:       userID,
				DownloadTime: time.Now().Unix(),
				Path:         downloadFilePath,
			}).Error; err != nil {
				errCh <- fmt.Errorf("生成下载记录失败: %w", err)
				return
			}
			log.Printf("下载文件成功: %s\n", downloadFilePath)
		}(id)
	}

	wg.Wait()
	close(errCh) // 关闭错误通道

	// 检查是否有错误发生
	var allErrors []error
	for err := range errCh {
		log.Printf("下载过程中发生错误: %v\n", err)
		allErrors = append(allErrors, err)
	}

	if len(allErrors) > 0 {
		return fmt.Errorf("下载过程中发生错误")
	}

	return nil
}

func (s *ResourceService) DownloadResourceHistory(req model.ResourceDownloadQuery) ([]model.ResourceDownloadResponse, int64, error) {
	dbQuery := db.DB.Model(&model.ResourceDownloadLog{}).
		Joins("LEFT JOIN users ON users.id = resource_download_log.user_id").
		Joins("LEFT JOIN resources ON resources.id = resource_download_log.resource_id").
		Select("resource_download_log.*, users.username, resources.name as resource_name, resources.size as resource_size, resources.ext as resource_ext")

	if req.ResourceID != 0 {
		dbQuery = dbQuery.Where("resource_download_log.resource_id = ?", req.ResourceID)
	}
	if req.UserID != 0 {
		dbQuery = dbQuery.Where("resource_download_log.user_id = ?", req.UserID)
	}
	if req.StartAt != 0 {
		dbQuery = dbQuery.Where("download_time >= ?", req.StartAt)
	}
	if req.EndAt != 0 {
		dbQuery = dbQuery.Where("download_time <= ?", req.EndAt)
	}

	result := make([]model.ResourceDownloadResponse, 0)
	var total int64
	if err := dbQuery.Count(&total).Error; err != nil {
		return result, 0, errors.New("查询总数失败")
	}

	if req.Page > 0 && req.PageSize > 0 {
		offset := (req.Page - 1) * req.PageSize
		dbQuery = dbQuery.Offset(offset).Limit(req.PageSize)
	}

	if err := dbQuery.Find(&result).Error; err != nil {
		return result, 0, errors.New("查询数据失败: " + err.Error())
	}

	return result, total, nil
}

func (s *ResourceService) CheckResourceExists(id int64) (bool, error) {
	var history model.ResourceDownloadLog
	err := db.DB.Where("resource_id = ?", id).Order("download_time DESC").First(&history).Error
	if err != nil {
		return false, err
	}
	if _, err := os.Stat(history.Path); os.IsNotExist(err) {
		return false, nil
	}

	return true, nil
}

func (s *ResourceService) ClearResourceCache(id int64) error {
	var resource model.Resources
	if err := db.DB.First(&resource, id).Error; err != nil {
		return errors.New("未找到对应资料")
	}

	var downlog model.ResourceDownloadLog
	if err := db.DB.Where("resource_id = ?", id).Order("download_time DESC").First(&downlog).Error; err == nil {
		// 删除文件
		if _, err := os.Stat(downlog.Path); err == nil {
			if err := os.Remove(downlog.Path); err != nil {
				return errors.New("删除文件失败: " + err.Error())
			}
		} else if !os.IsNotExist(err) {
			return errors.New("检查文件状态失败: " + err.Error())
		}
	}

	return nil
}
