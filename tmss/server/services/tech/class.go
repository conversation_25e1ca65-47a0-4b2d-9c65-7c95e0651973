package tech

import (
	"fmt"
	"strings"
	"time"

	"tms/model"
	"tms/pkg/db"
)

// ClassService 定义班级相关的业务逻辑接口

type ClassService struct{}

func NewClassService() *ClassService {
	return &ClassService{}
}

// CreateClass 创建班级（包含专业存在性校验）
func (s *ClassService) CreateClass(req model.ReqClassUpdate, userID int64) error {

	//slog.Info("MajorIDs: %v", req.MajorIDS)
	if len(req.MajorIDS) == 0 {
		return fmt.Errorf("请选择专业")
	}

	var exits model.Class
	err := db.DB.Where("class_id = ?", req.Class.ClassID).First(&exits).Error
	if err == nil {
		return fmt.Errorf("班级编号已存在")
	}

	err = s.CheckMajorExists(req.MajorIDS)
	if err != nil {
		return err
	}

	req.Class.UserID = userID
	req.Class.CreatedAt = time.Now().Unix()
	req.Class.UpdatedAt = time.Now().Unix()

	if err := db.DB.Create(&req.Class).Error; err != nil {
		return fmt.Errorf("创建班级失败: %v", err)
	}
	err = s.SaveMajors(req.Class.ID, req.MajorIDS)
	if err != nil {
		return err
	}
	return nil
}
func (s *ClassService) CheckMajorExists(majorIDs []int64) error {
	var existingMajorIDs []int64
	if err := db.DB.Model(&model.Majors{}).
		Where("id IN (?)", majorIDs).
		Pluck("id", &existingMajorIDs).Error; err != nil {
		return fmt.Errorf("查询专业失败: %v", err)
	}

	// 检查是否有不存在的专业ID
	if len(existingMajorIDs) < len(majorIDs) {
		// 找出缺失的ID
		existingMap := make(map[int64]bool)
		for _, id := range existingMajorIDs {
			existingMap[id] = true
		}
		var missingIDs []int64
		for _, id := range majorIDs {
			if !existingMap[id] {
				missingIDs = append(missingIDs, id)
			}
		}
		return fmt.Errorf("以下专业ID不存在: %v", missingIDs)
	}
	return nil
}
func (s *ClassService) SaveMajors(id int64, majorIDs []int64) error {
	// 删除旧的关联记录
	err := db.DB.Where("class_id = ?", id).Delete(&model.ClassMajor{}).Error
	if err != nil {
		return fmt.Errorf("删除旧的关联记录失败: %v", err)
	}
	// 创建新的关联记录
	var saveData []model.ClassMajor
	for _, majorID := range majorIDs {
		saveData = append(saveData, model.ClassMajor{ClassID: id, MajorID: majorID})
	}
	if err := db.DB.Create(&saveData).Error; err != nil {
		return fmt.Errorf("创建新的关联记录失败: %v", err)
	}
	return nil

}

// UpdateClass 更新班级信息
func (s *ClassService) UpdateClass(req model.ReqClassUpdate) error {
	if req.Class.ID <= 0 {
		return fmt.Errorf("无效的班级ID")
	}

	var existingClass model.Class
	if err := db.DB.Where("id != ? AND class_id = ?", req.Class.ID, req.Class.ClassID).First(&existingClass).Error; err == nil {
		return fmt.Errorf("班级编号已存在")
	}

	err := s.CheckMajorExists(req.MajorIDS)
	if err != nil {
		return err
	}

	req.Class.UpdatedAt = time.Now().Unix()

	if err := db.DB.Model(&model.Class{}).
		Where("id = ?", req.Class.ID).
		Updates(map[string]interface{}{
			"class_id":    req.Class.ClassID,
			"name":        req.Class.Name,
			"description": req.Class.Description,
			"updated_at":  req.Class.UpdatedAt,
		}).Error; err != nil {
		return fmt.Errorf("更新班级失败: %v", err)
	}
	err = s.SaveMajors(req.Class.ID, req.MajorIDS)
	if err != nil {
		return err
	}
	return nil
}

// GetClassList 获取班级列表
func (s *ClassService) GetClassList(req model.ReqClassSearch) (model.RespClassList, error) {
	var res model.RespClassList
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 || req.PageSize > 100 {
		req.PageSize = 10
	}

	dbQuery := db.DB.Model(&model.Class{})
	if req.Name != "" {
		dbQuery = dbQuery.Where("name ILIKE ?", "%"+req.Name+"%")
	}
	if req.ClassID != "" {
		dbQuery = dbQuery.Where("class_id = ?", req.ClassID)
	}
	if req.MajorID > 0 {
		//dbQuery = dbQuery.Where("major_id = ?", req.MajorID)
		dbQuery = dbQuery.Where("id in (select class_id from class_major where major_id = ?)", req.MajorID)
	}
	if req.IDS != "" {
		ids := strings.ReplaceAll(req.IDS, "，", ",")
		idArr := strings.Split(ids, ",")
		dbQuery = dbQuery.Where("id IN (?)", idArr)
	}
	var total int64
	if err := dbQuery.Count(&total).Error; err != nil {
		return res, fmt.Errorf("获取总数失败: %v", err)
	}

	var classes []model.Class
	offset := (req.Page - 1) * req.PageSize
	if err := dbQuery.Offset(offset).Limit(req.PageSize).Order("created_at DESC").Find(&classes).Error; err != nil {
		return res, fmt.Errorf("获取数据失败: %v", err)
	}

	return model.RespClassList{List: classes, Total: total}, nil
}
func (s *ClassService) GetMajorsByClassID(classID int64) ([]model.Majors, error) {
	var majors []model.Majors
	if classID == 0 {
		err := db.DB.Model(&model.Majors{}).Find(&majors).Error
		if err != nil {
			return majors, fmt.Errorf("查询专业失败: %v", err)
		}
	} else {
		var majorIds []int64
		err := db.DB.Model(&model.ClassMajor{}).Where("class_id = ?", classID).Pluck("major_id", &majorIds).Error
		if err != nil {
			return majors, fmt.Errorf("查询专业失败: %v", err)
		}
		err = db.DB.Model(&model.Majors{}).Where("id IN (?)", majorIds).Find(&majors).Error
		if err != nil {
			return majors, fmt.Errorf("查询专业失败: %v", err)
		}
	}
	return majors, nil

}

// DeleteClass 删除班级
func (s *ClassService) DeleteClass(id int64) error {
	if id == 0 {
		return fmt.Errorf("无效的班级ID")
	}

	if err := db.DB.Where("id = ?", id).Delete(&model.Class{}).Error; err != nil {
		return fmt.Errorf("删除班级失败: %v", err)
	}
	if err := db.DB.Where("class_id = ?", id).Delete(&model.ClassMajor{}).Error; err != nil {
		return fmt.Errorf("删除班级专业关联失败: %v", err)
	}
	return nil
}

// BatchDeleteClasses 批量删除班级
func (s *ClassService) BatchDeleteClasses(ids []int64) error {
	if len(ids) == 0 {
		return fmt.Errorf("请选择要删除的班级")
	}

	if err := db.DB.Where("id IN (?)", ids).Delete(&model.Class{}).Error; err != nil {
		return fmt.Errorf("批量删除失败: %v", err)
	}
	if err := db.DB.Where("class_id IN (?)", ids).Delete(&model.ClassMajor{}).Error; err != nil {
		return fmt.Errorf("批量删除班级专业关联失败: %v", err)
	}
	return nil
}

// GetClassDetail 获取班级详情，包括关联的专业
func (s *ClassService) GetClassDetail(classID int64) (*model.ReqClassUpdate, error) {
	if classID == 0 {
		return nil, fmt.Errorf("无效的班级ID")
	}

	var class model.Class
	if err := db.DB.First(&class, classID).Error; err != nil {
		return nil, fmt.Errorf("未找到对应班级")
	}

	// 获取关联的专业IDs
	var majorIDs []int64
	err := db.DB.Model(&model.ClassMajor{}).
		Where("class_id = ?", classID).
		Pluck("major_id", &majorIDs).Error

	if err != nil {
		return nil, fmt.Errorf("获取专业失败: %v", err)
	}

	return &model.ReqClassUpdate{
		Class:    class,
		MajorIDS: majorIDs,
	}, nil
}
