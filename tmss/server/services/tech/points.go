package tech

import (
	"encoding/json"
	"errors"
	"time"

	"tms/model"
	"tms/pkg/db"
	"tms/services/user"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type KnowledgePointsService struct{}

func NewKnowledgePointsService() *KnowledgePointsService {
	return &KnowledgePointsService{}
}
func KnowledgePointsCreator(tx *gorm.DB, content string, userID int64) (int64, error) {
	var changeReq struct {
		Form model.KnowledgePoints `json:"form"`
	}
	if err := json.Unmarshal([]byte(content), &changeReq); err != nil {
		return 0, errors.New("解析教材内容失败: " + err.Error())
	}
	req := changeReq.Form
	err := createKnowledgePointsDraft(&req, userID)
	if err != nil {
		return 0, err
	}
	return req.ID, nil
}

// CreateKnowledgePointsDraft 创建知识点草稿
func (s *KnowledgePointsService) CreateKnowledgePointsDraft(doc model.KnowledgePoints, userId int64) error {
	return createKnowledgePointsDraft(&doc, userId)
}
func createKnowledgePointsDraft(doc *model.KnowledgePoints, userId int64) error {

	if userId == 0 {
		return errors.New("无效的用户ID")
	}
	if doc.Title == "" {
		return errors.New("请填写标题")
	}
	if doc.Content == "" {
		return errors.New("请填写内容")
	}
	// 设置基础信息
	doc.UserID = userId
	doc.Status = model.KnowledgePointsStatusDraft
	doc.CreatedAt = time.Now().Unix()

	// 校验章节是否存在
	if doc.ChapterID > 0 {
		if exists, err := checkChapterExists(doc.ChapterID); err != nil || !exists {
			return errors.New("关联的章节不存在")
		}
	}

	// 校验课件是否存在
	if doc.CoursewareID > 0 {
		if exists, err := checkCoursewareExists(doc.CoursewareID); err != nil || !exists {
			return errors.New("关联的课件不存在")
		}
	}
	// 校验关联的试题是否存在
	if doc.QuestionID > 0 {
		if exists, err := checkQuestionsExists(doc.QuestionID); err != nil || !exists {
			return errors.New("关联的理论试题不存在")
		}
	}

	// 创建知识点
	if err := db.DB.Create(&doc).Error; err != nil {
		return errors.New("创建知识点失败: " + err.Error())
	}
	return nil
}

// UpdateKnowledgePointsDraft 更新知识点草稿
func (s *KnowledgePointsService) UpdateKnowledgePointsDraft(req model.KnowledgePoints) error {
	if req.ID <= 0 {
		return errors.New("无效的知识点ID")
	}

	// 获取现有知识点
	var doc model.KnowledgePoints
	if err := db.DB.First(&doc, req.ID).Error; err != nil {
		return errors.New("未找到对应知识点")
	}

	// 状态校验：只能编辑草稿或驳回的知识点
	if doc.Status != model.KnowledgePointsStatusDraft &&
		doc.Status != model.KnowledgePointsStatusRejected {
		return errors.New("只能编辑草稿或驳回状态的知识点")
	}

	// 更新字段
	updates := map[string]interface{}{
		"title":         req.Title,
		"content":       req.Content,
		"chapter_id":    req.ChapterID,
		"question_id":   req.QuestionID,
		"courseware_id": req.CoursewareID,
		"updated_at":    time.Now().Unix(),
	}

	// 校验章节是否存在
	if doc.ChapterID > 0 {
		if exists, err := checkChapterExists(doc.ChapterID); err != nil || !exists {
			return errors.New("关联的课时计划不存在")
		}
	}

	// 校验课件是否存在
	if doc.CoursewareID > 0 {
		if exists, err := checkCoursewareExists(doc.CoursewareID); err != nil || !exists {
			return errors.New("关联的课件不存在")
		}
	}
	// 校验关联的试题是否存在
	if doc.QuestionID > 0 {
		if exists, err := checkQuestionsExists(doc.QuestionID); err != nil || !exists {
			return errors.New("关联的理论试题不存在")
		}
	}

	// 执行更新
	if err := db.DB.Model(&doc).Updates(updates).Error; err != nil {
		return errors.New("更新知识点失败: " + err.Error())
	}

	return nil
}

// DeleteKnowledgePoints 删除知识点
func (s *KnowledgePointsService) DeleteKnowledgePoints(id int64) error {
	if id == 0 {
		return errors.New("无效的知识点ID")
	}

	var doc model.KnowledgePoints
	if err := db.DB.First(&doc, id).Error; err != nil {
		return errors.New("未找到对应知识点")
	}

	// 状态校验：只能删除草稿或驳回的知识点
	if doc.Status != model.KnowledgePointsStatusDraft &&
		doc.Status != model.KnowledgePointsStatusRejected {
		return errors.New("只能删除草稿或驳回状态的知识点")
	}

	if err := db.DB.Delete(&doc).Error; err != nil {
		return errors.New("删除失败: " + err.Error())
	}

	return nil
}

// GetKnowledgePointsDetail 获取知识点详情
func (s *KnowledgePointsService) GetKnowledgePointsDetail(id int64) (model.RespKnowledgePointsDetail, error) {
	var resp model.RespKnowledgePointsDetail

	if id == 0 {
		return resp, errors.New("无效的知识点ID")
	}

	// 获取基础信息
	var doc model.KnowledgePoints
	if err := db.DB.First(&doc, id).Error; err != nil {
		return resp, errors.New("未找到对应知识点")
	}
	resp.KnowledgePoints = doc

	// 获取理论试题标题
	if doc.QuestionID > 0 {
		err := db.DB.Model(&model.Questions{}).
			Where("id = ?", doc.QuestionID).
			Pluck("title", &resp.QuestionTitle).Error
		if err != nil {
			return resp, errors.New("获取理论试题标题失败: " + err.Error())
		}
	}

	// 获取关联课件信息
	if doc.CoursewareID > 0 {
		err := db.DB.Model(&model.Courseware{}).
			Where("id = ?", doc.CoursewareID).
			Pluck("title", &resp.CoursewareTitle).Error
		if err != nil {
			return resp, errors.New("获取课件标题失败: " + err.Error())
		}
	}
	if doc.ChapterID > 0 {
		err := db.DB.Model(&model.Chapter{}).
			Where("id = ?", doc.ChapterID).
			Pluck("name", &resp.ChapterTitle).Error
		if err != nil {
			return resp, errors.New("获取章节标题失败: " + err.Error())
		}
	}
	if err := db.DB.Model(&model.Users{}).Where("id = ?", doc.UserID).Pluck("username", &resp.TeacherName).Error; err != nil {
		return resp, errors.New("获取教师名称失败: " + err.Error())
	}

	return resp, nil
}

// GetKnowledgePointsList 获取知识点列表
func (s *KnowledgePointsService) GetKnowledgePointsList(c *gin.Context, req model.ReqKnowledgePointsSearch) (model.RespKnowledgePointsList, error) {
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 || req.PageSize > 100 {
		req.PageSize = 10
	}
	var res model.RespKnowledgePointsList
	dbQuery := db.DB.Model(&model.KnowledgePoints{})

	// 应用过滤条件
	if req.Title != "" {
		dbQuery = dbQuery.Where("title ILIKE ?", "%"+req.Title+"%")
	}
	if req.Status != "" {
		dbQuery = dbQuery.Where("status = ?", req.Status)
	}
	if req.ChapterID > 0 {
		dbQuery = dbQuery.Where("chapter_id = ?", req.ChapterID)
	}
	if req.QuestionID > 0 {
		dbQuery = dbQuery.Where("question_id = ?", req.QuestionID)
	}
	if req.CoursewareID > 0 {
		dbQuery = dbQuery.Where("courseware_id = ?", req.CoursewareID)
	}
	if req.UserID > 0 {
		dbQuery = dbQuery.Where("user_id = ?", req.UserID)
	}

	// 应用数据权限规则
	dbQuery, _ = user.ApplyDataRules(c, dbQuery, model.ModuleKnowledgePoints)

	if err := dbQuery.Count(&res.Total).Error; err != nil {
		return res, errors.New("查询总数失败: " + err.Error())
	}

	var list []model.KnowledgePoints
	offset := (req.Page - 1) * req.PageSize
	if err := dbQuery.Offset(offset).Limit(req.PageSize).Order("created_at DESC").Find(&list).Error; err != nil {
		return res, errors.New("查询数据失败: " + err.Error())
	}
	var cursewareIds []int64
	for _, point := range list {
		cursewareIds = append(cursewareIds, point.CoursewareID)
	}
	var coursewares []model.Courseware
	if err := db.DB.Model(&model.Courseware{}).Where("id in (?)", cursewareIds).Find(&coursewares).Error; err != nil {
		return res, errors.New("查询数据失败: " + err.Error())
	}
	coursewareMap := make(map[int64]string)
	for _, courseware := range coursewares {
		coursewareMap[courseware.ID] = courseware.Title
	}
	var sids []int64
	for _, d := range list {
		sids = append(sids, d.ID)
	}
	revisionList, err := GetRevisionList(model.ModuleKnowledgePoints, sids)
	if err != nil {
		return res, errors.New("查询版本记录失败: " + err.Error())
	}
	for _, point := range list {
		var detail model.RespKnowledgePointsDetail
		detail.KnowledgePoints = point
		if point.CoursewareID > 0 {
			detail.CoursewareTitle = coursewareMap[point.CoursewareID]
		}
		for _, rev := range revisionList {
			if rev.OriginalID == point.ID {
				detail.Rev = &rev
			}
		}
		res.List = append(res.List, detail)
	}

	return res, nil
}
