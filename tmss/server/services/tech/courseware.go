package tech

import (
	"encoding/json"
	"errors"
	"fmt"
	"log/slog"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"tms/model"
	"tms/pkg/config"
	"tms/pkg/db"
	"tms/services/user"
	"tms/utils"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"gorm.io/gorm"
)

type CoursewareService struct{}

func NewCoursewareService() *CoursewareService {
	return &CoursewareService{}
}

// CreateCoursewareDraft 创建课件草稿
func (api *CoursewareService) CreateCoursewareDraft(req model.Courseware, userId int64) error {

	if userId == 0 {
		return errors.New("无效的用户ID")
	}

	req.UserID = userId
	req.Status = model.CoursewareStatusDraft
	req.CreatedAt = time.Now().Unix()
	req.ID = 0
	if err := db.DB.Create(&req).Error; err != nil {
		return errors.New("创建失败: " + err.Error())
	}

	return nil
}
func CoursewareCreator(tx *gorm.DB, content string, userId int64) (int64, error) {
	var changeReq struct {
		FormData model.Courseware `json:"formData"`
	}
	//var req model.ReqSyllabusCreate
	if err := json.Unmarshal([]byte(content), &changeReq); err != nil {
		return 0, errors.New("解析教材内容失败: " + err.Error())
	}
	req := changeReq.FormData
	if userId == 0 {
		return 0, errors.New("无效的用户ID")
	}

	req.UserID = userId
	req.Status = model.CoursewareStatusDraft
	req.CreatedAt = time.Now().Unix()
	req.ID = 0
	if err := db.DB.Create(&req).Error; err != nil {
		return 0, errors.New("创建失败: " + err.Error())
	}

	return req.ID, nil
}

// UpdateCoursewareDraft 更新课件草稿
func (api *CoursewareService) UpdateCoursewareDraft(req model.Courseware) error {

	if req.ID <= 0 {
		return errors.New("无效的课件ID")
	}

	var courseware model.Courseware
	if err := db.DB.First(&courseware, req.ID).Error; err != nil {
		return errors.New("未找到对应课件")
	}

	if courseware.Status != model.CoursewareStatusDraft && courseware.Status != model.CoursewareStatusRejected {
		return errors.New("只能编辑草稿")
	}

	now := time.Now().Unix()

	updates := map[string]interface{}{
		"file_name":       req.FileName,
		"file_type":       req.FileType,
		"courseware_type": req.CoursewareType,
		"file_path":       req.FilePath,
		"description":     req.Description,
		"target":          req.Target,
		"minutes":         req.Minutes,
		"score":           req.Score,
		"total_step":      req.TotalStep,
		"updated_at":      now,
		"title":           req.Title,
	}

	if err := db.DB.Model(&courseware).
		Where("id = ?", req.ID).
		Updates(updates).Error; err != nil {
		return errors.New("更新失败: " + err.Error())
	}

	return nil
}

// DeleteCourseware 删除课件
func (api *CoursewareService) DeleteCourseware(id int64) error {
	if id == 0 {
		return errors.New("无效的课件ID")
	}

	var courseware model.Courseware
	if err := db.DB.First(&courseware, id).Error; err != nil {
		return errors.New("未找到对应课件")
	}

	if courseware.Status != model.CoursewareStatusDraft {
		return errors.New("只能删除草稿")
	}

	if err := db.DB.Delete(&courseware).Error; err != nil {
		return errors.New("删除失败: " + err.Error())
	}

	// if err := db.DB.Where("courseware_id = ?", courseware.ID).Delete(&model.CoursewareExt{}).Error; err != nil {
	// 	return errors.New("删除关联失败: " + err.Error())
	// }

	return nil
}

// GetCoursewareList 获取课件列表
func (api *CoursewareService) GetCoursewareList(c *gin.Context, req model.ReqCoursewareSearch) (model.RespCoursewareList, error) {
	var resp model.RespCoursewareList
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 || req.PageSize > 100 {
		req.PageSize = 10
	}

	dbQuery := db.DB.Model(&model.Courseware{})

	if req.Name != "" {
		dbQuery = dbQuery.Where("file_name ILIKE ? OR title ILIKE ?", "%"+req.Name+"%", "%"+req.Name+"%")
	}
	if req.CourseID > 0 {
		dbQuery = dbQuery.Where("course_id = ?", req.CourseID)
	}
	if req.ChapterID > 0 {
		dbQuery = dbQuery.Where("chapter_id = ?", req.ChapterID)
	}
	if req.Status != "" {
		dbQuery = dbQuery.Where("status = ?", req.Status)
	}
	if req.CoursewareType != "" {
		dbQuery = dbQuery.Where("courseware_type = ?", req.CoursewareType)
	}
	dbQuery, has := user.ApplyDataRules(c, dbQuery, model.ModuleSyllabus)
	if !has && req.UserID > 0 {
		dbQuery = dbQuery.Where("user_id = ?", req.UserID)
	}
	// if req.UserID > 0 {
	// 	dbQuery = dbQuery.Where("user_id = ?", req.UserID)
	// }
	if err := dbQuery.Count(&resp.Total).Error; err != nil {
		return resp, errors.New("查询总数失败")
	}

	var list []model.Courseware
	offset := (req.Page - 1) * req.PageSize
	if err := dbQuery.Offset(offset).Limit(req.PageSize).Order("created_at DESC").Find(&list).Error; err != nil {
		return resp, errors.New("查询数据失败: " + err.Error())
	}
	//resp.List = list
	courseIds := make([]int64, len(list))
	for i, c := range list {
		courseIds[i] = c.CourseID
	}
	chapterIds := make([]int64, len(list))
	for i, c := range list {
		chapterIds[i] = c.ChapterID
	}
	var courses []model.Courses
	if err := db.DB.Model(&model.Courses{}).
		Where("id IN (?)", courseIds).
		Find(&courses).Error; err != nil {
	}
	var chapters []model.Chapter
	if err := db.DB.Model(&model.Chapter{}).
		Where("id IN (?)", chapterIds).
		Find(&chapters).Error; err != nil {
	}
	coursesMap := make(map[int64]string)
	for _, c := range courses {
		coursesMap[c.ID] = c.Name
	}
	chaptersMap := make(map[int64]string)
	for _, c := range chapters {
		chaptersMap[c.ID] = c.Name
	}
	var coursewareIds []int64
	for _, c := range list {
		coursewareIds = append(coursewareIds, c.ID)
	}
	revisionList, err := GetRevisionList(model.ModuleCourseware, coursewareIds)
	if err != nil {
		return resp, err
	}
	for _, c := range list {
		detail := model.RespCoursewareDetail{
			Courseware:  c,
			ChapterName: chaptersMap[c.ChapterID],
			CourseName:  coursesMap[c.CourseID],
		}
		courseware := model.RespCoursewareWithLatestRevision{Courseware: detail}
		for _, r := range revisionList {
			if r.OriginalID == c.ID {
				courseware.Rev = &r
			}
		}
		resp.List = append(resp.List, courseware)
	}

	return resp, nil
}

// GetReviewingCoursewareList 获取待审核课件列表
// func (api *CoursewareService) GetReviewingCoursewareList(c *gin.Context, req model.ReqCoursewareSearch) (model.RespCoursewareList, error) {
// 	req.Status = model.CoursewareStatusReviewing
// 	return api.GetCoursewareList(c, req)
// }

// GetCoursewaresByCourse 获取指定课程的课件列表
func (api *CoursewareService) GetCoursewaresHasPublished(c *gin.Context, req model.ReqCoursewareSearch) (model.RespCoursewareList, error) {
	req.Status = model.CoursewareStatusPublished
	return api.GetCoursewareList(c, req)
}

func CoursewareDownloadDir(chapterID int64) string {
	return filepath.Join("data", "downloads", "coursewares", cast.ToString(chapterID))
}

func ScormUnzipDir(chapterID int64, coursewareID int64) string {
	return filepath.Join("data", "scorm", cast.ToString(chapterID), cast.ToString(coursewareID))
}

// 根据课件id列表，解压scorm包
func (s *CoursewareService) unzipScormByCoursewareIDs(coursewareIDS []int64) error {
	var coursewares []model.Courseware
	if err := db.DB.Where("id IN (?)", coursewareIDS).Find(&coursewares).Error; err != nil {
		return errors.New("获取数据失败: " + err.Error())
	}

	for _, courseware := range coursewares {
		distFloder := ScormUnzipDir(courseware.ChapterID, courseware.ID)
		// courseware.FilePath = ./uploads/courseware/Quiz_test.zip
		// 包下载到本地
		remote := config.Config.System.Scheme + "://" + config.Config.System.Host + "/"
		remoteFilePath := strings.TrimPrefix(courseware.FilePath, "./")
		remoteFilePath = remote + remoteFilePath
		var downloadFilePath string

		// 确定下载文件的保存路径
		downloadDir := CoursewareDownloadDir(courseware.ChapterID)
		downloadFilePath = filepath.Join(downloadDir, filepath.Base(courseware.FilePath))

		// 下载文件
		slog.Info("开始下载课件", "URL", remoteFilePath, "保存路径", downloadFilePath)
		if err := utils.DownloadFile(remoteFilePath, downloadFilePath); err != nil {
			slog.Error("下载课件失败", "URL", remoteFilePath, "错误", err)
			return fmt.Errorf("下载课件失败: %w", err)
		}

		slog.Info("课件下载成功", "保存路径", downloadFilePath)

		fileInfo, err := os.Stat(downloadFilePath)
		if err != nil {
			slog.Error("获取下载文件信息失败", "文件路径", downloadFilePath, "错误", err)
			return fmt.Errorf("获取下载文件信息失败: %w", err)
		}

		slog.Info("下载文件大小", "文件路径", downloadFilePath, "大小(字节)", fileInfo.Size())

		unzipPath, err := utils.UnzipScorm(downloadFilePath, distFloder)
		if err != nil {
			slog.Error("解压课件失败", "文件路径", downloadFilePath, "错误", err)
			return errors.New("解压失败: " + err.Error())
		}
		slog.Info("课件解压成功", "保存路径", unzipPath)

		if err := db.DB.Model(&model.Courseware{}).Where("id = ?", courseware.ID).Update("unzip_path", unzipPath).Error; err != nil {
			return errors.New("更新数据失败: " + err.Error())
		}
	}

	return nil
}

func (s *CoursewareService) DownloadAndUnzipCourseware(ids []int64) error {
	// 将课件下载解压操作放入后台协程，避免阻塞主请求
	go func(coursewareIDs []int64) {
		// 定义并发限制
		concurrencyLimit := 5
		sem := make(chan struct{}, concurrencyLimit)
		var wg sync.WaitGroup

		for _, coursewareID := range coursewareIDs {
			wg.Add(1)
			sem <- struct{}{}
			go func(id int64) {
				defer wg.Done()
				defer func() { <-sem }()

				if err := s.unzipScormByCoursewareIDs([]int64{id}); err != nil {
					slog.Error("解压课件失败", "error", err)
				}
			}(coursewareID)
		}
		wg.Wait()
		slog.Info("所有课件解压完成")
	}(ids)

	return nil
}

func (s *CoursewareService) CheckCoursewareExists(id int64) (bool, error) {
	var courseware model.Courseware
	err := db.DB.Where("id = ?", id).First(&courseware).Error
	if err != nil {
		return false, err
	}
	// build path
	downloadDir := CoursewareDownloadDir(courseware.ChapterID)
	downloadFilePath := filepath.Join(downloadDir, filepath.Base(courseware.FilePath))
	// check file exists
	if _, err := os.Stat(downloadFilePath); os.IsNotExist(err) {
		return false, nil
	}

	// check unzip
	if courseware.UnzipPath == "" {
		return false, nil
	}
	if _, err := os.Stat(courseware.UnzipPath); os.IsNotExist(err) {
		return false, nil
	}

	return true, nil
}

func (s *CoursewareService) ClearCourseCache(courseID int64) error {
	var courseware model.Courseware
	err := db.DB.Where("id = ?", courseID).First(&courseware).Error
	if err != nil {
		return err
	}

	// build path
	downloadDir := CoursewareDownloadDir(courseware.ChapterID)
	downloadFilePath := filepath.Join(downloadDir, filepath.Base(courseware.FilePath))
	// 删除文件
	if _, err := os.Stat(downloadFilePath); err == nil {
		if err := os.Remove(downloadFilePath); err != nil {
			return errors.New("删除文件失败: " + err.Error())
		}
	} else if !os.IsNotExist(err) {
		return errors.New("检查文件状态失败: " + err.Error())
	}
	// build unzip path
	unzipPath := filepath.Join("data", "scorm", cast.ToString(courseware.ChapterID), cast.ToString(courseware.ID))
	if err := os.RemoveAll(unzipPath); err != nil {
		return err
	}

	return nil
}

// 虚拟课件初始化
func (s *CoursewareService) InitializeVirtualCourseware(userID, coursewareID int64, subtype int) (int64, error) {
	// 查找现有的未完成记录
	var history model.VirtualCoursewareSubmitHistory
	err := db.DB.Where("user_id = ? AND courseware_id = ? AND subtype = ? AND status = ?",
		userID, coursewareID, subtype, model.VirtualCoursewareStatusIncomplete.String()).
		First(&history).Error

	if err == nil {
		return history.ID, nil
	}

	if !errors.Is(err, gorm.ErrRecordNotFound) {
		slog.Error("查询历史记录失败", "error", err)
		return 0, err
	}

	// 创建新记录
	now := time.Now().Unix()
	newHistory := model.VirtualCoursewareSubmitHistory{
		UserID:       userID,
		CoursewareID: coursewareID,
		Subtype:      subtype,
		Status:       model.VirtualCoursewareStatusIncomplete.String(),
		CreatedAt:    now,
		UpdatedAt:    now,
	}

	if err := db.DB.Create(&newHistory).Error; err != nil {
		slog.Error("创建历史记录失败", "error", err)
		return 0, err
	}
	return newHistory.ID, nil
}

// 虚拟课件答题历史
func (s *CoursewareService) GetVirtualCoursewareAnswerHistory(id int64) (*model.VirtualCoursewareSubmitHistory, error) {
	var history model.VirtualCoursewareSubmitHistory
	err := db.DB.Where("id = ?", id).First(&history).Error
	if err != nil {
		slog.Error("查询历史记录失败", "error", err)
		return nil, err

	}

	return &history, nil
}

// 虚拟课件提交
func (s *CoursewareService) SubmitVirtualCoursewareAnswer(historyID int64, step uint32, score uint32, totalStep uint32) error {
	var history model.VirtualCoursewareSubmitHistory
	if err := db.DB.First(&history, historyID).Error; err != nil {
		return err
	}

	history.Step = step
	history.Score = score
	now := time.Now().Unix()
	history.TotalTime = now - history.CreatedAt
	history.UpdatedAt = now
	if totalStep == (step + 1) {
		history.Status = model.VirtualCoursewareStatusCompleted.String()
		history.CompletedAt = now
	}
	if err := db.DB.Save(&history).Error; err != nil {
		return err
	}

	return nil
}
