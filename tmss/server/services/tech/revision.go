package tech

import (
	"encoding/json"
	"errors"
	"time"

	"tms/model"
	"tms/pkg/db"

	"gorm.io/gorm"
)

// RecordCreatorFunc 创建新记录的函数类型
type RecordCreatorFunc func(tx *gorm.DB, content string, userId int64) (int64, error)

var RecordFuncMap = map[string]RecordCreatorFunc{
	model.ModuleSyllabus:        SyllabusCreator,
	model.ModuleTeachingPlan:    TeachingPlanCreator,
	model.ModuleCourses:         CourseCreator,
	model.ModuleCourseware:      CoursewareCreator,
	model.ModuleQuestions:       QuestionCreator,
	model.ModuleAssignment:      AssignmentCreator,
	model.ModuleResources:       ResourceCreator,
	model.ModuleKnowledgePoints: KnowledgePointsCreator,
	model.ModuleDocuments:       TeachingDocumentCreator,
}

// RevisionService 修订记录服务
type RevisionService struct{}

func NewRevisionService() *RevisionService {
	return &RevisionService{}
}

// GetRevisionList 获取修订记录列表（支持分页、搜索）
func (s *RevisionService) GetRevisionList(req model.ReqRevisionSearch) ([]model.RespRevisionRecord, int64, error) {
	var list []model.RespRevisionRecord
	var total int64

	// 设置默认分页
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 || req.PageSize > 100 {
		req.PageSize = 10
	}

	offset := (req.Page - 1) * req.PageSize

	// 构建查询
	dbQuery := db.DB.Model(&model.RevisionRecord{}).Select(
		"revision_record.*, users.username as user_name",
	).Joins("LEFT JOIN users ON revision_record.user_id = users.id")

	if req.ModuleKey != "" {
		dbQuery = dbQuery.Where("revision_record.module_key = ?", req.ModuleKey)
	}
	if req.Status != "" {
		dbQuery = dbQuery.Where("revision_record.status = ?", req.Status)
	}
	// if req.OriginalID != 0 {
	// 	dbQuery = dbQuery.Where("revision_record.original_id = ?", req.OriginalID)
	// }

	// 获取总数
	if err := dbQuery.Count(&total).Error; err != nil {
		return nil, 0, errors.New("获取总数失败: " + err.Error())
	}

	// 获取列表
	if err := dbQuery.Offset(offset).Limit(req.PageSize).
		Order("created_at DESC").
		Scan(&list).Error; err != nil {
		return nil, 0, errors.New("获取数据失败: " + err.Error())
	}

	return list, total, nil
}
func GetRevisionList(moduleKey string, dataIDs []int64) ([]model.RevisionRecord, error) {
	var list []model.RevisionRecord

	// 构建查询
	if err := db.DB.Model(&model.RevisionRecord{}).Where("module_key = ? AND original_id IN (?)", moduleKey, dataIDs).
		Order("id DESC").Find(&list).Error; err != nil {
		return nil, errors.New("获取数据失败: " + err.Error())
	}

	return list, nil
}

// CreateRevision 创建修订记录
func (s *RevisionService) CreateRevision(moduleKey string, originalID int64, userId int64, notes string, changes string) error {
	if userId == 0 {
		return errors.New("无效的用户ID")
	}
	if moduleKey == "" {
		return errors.New("模块名称不能为空")
	}
	if originalID == 0 {
		return errors.New("原始记录ID不能为空")
	}
	// 1. 查询原始记录
	//var oldData map[string]interface{}
	oldData, err := model.GetModelByModuleKey(moduleKey)
	if err != nil {
		return errors.New("获取模型失败: " + err.Error())
	}
	err = db.DB.Table(moduleKey).
		Where("id = ? AND status = ?", originalID, model.WorkflowApproveStatusPublished).
		First(&oldData).Error

	if errors.Is(err, gorm.ErrRecordNotFound) {
		return errors.New("原始记录不存在")
	} else if err != nil {
		return errors.New("查询原始记录失败: " + err.Error())
	}

	// 2. 序列化原始记录
	oldJson, err := json.Marshal(oldData)
	if err != nil {
		return errors.New("序列化原始记录失败: " + err.Error())
	}

	// 3. 序列化新内容
	// changesJSON, err := json.Marshal(changes)
	// if err != nil {
	// 	return errors.New("序列化变更内容失败: " + err.Error())
	// }

	// 4. 检查进行中的修订记录
	var exist model.RevisionRecord
	if err := db.DB.Where("module_key = ? AND original_id = ?",
		moduleKey, originalID).
		First(&exist).Error; err == nil {
		return errors.New("已存在修订记录")
	}
	revision := model.RevisionRecord{
		ModuleKey:  moduleKey,
		OriginalID: originalID,
		UserID:     userId,
		Notes:      notes,
		Content:    string(oldJson),
		Changes:    changes,
		Status:     model.RevisionStatusDraft,
		CreatedAt:  time.Now().Unix(),
		UpdatedAt:  time.Now().Unix(),
	}

	if err := db.DB.Create(&revision).Error; err != nil {
		return errors.New("创建修订记录失败: " + err.Error())
	}

	return nil
}

// UpdateRevision 更新修订记录
func (s *RevisionService) UpdateRevision(id int64, notes string, changes string) error {
	if id == 0 {
		return errors.New("无效的修订记录ID")
	}

	// 检查记录是否存在
	var revision model.RevisionRecord
	if err := db.DB.First(&revision, id).Error; err != nil {
		return errors.New("未找到修订记录")
	}
	if revision.Status != model.RevisionStatusDraft && revision.Status != model.RevisionStatusRejected {
		return errors.New("无法更新非草稿的修订记录")
	}

	// 只允许更新特定字段
	updates := map[string]interface{}{
		"notes":      notes,
		"changes":    changes,
		"updated_at": time.Now().Unix(),
	}

	if err := db.DB.Model(&model.RevisionRecord{}).
		Where("id = ?", id).
		Updates(updates).Error; err != nil {
		return errors.New("更新修订记录失败: " + err.Error())
	}

	return nil
}

// PublishRevision 发布修订记录
func (s *RevisionService) PublishRevision(id, userId int64) (int64, error) {
	if id == 0 {
		return 0, errors.New("无效的修订记录ID")
	}
	// 获取修订记录详情
	revision, err := s.GetRevisionDetail(id)
	if err != nil {
		return 0, err
	}

	creatorFunc, ok := RecordFuncMap[revision.ModuleKey]
	if !ok {
		return 0, errors.New("无效的创建函数")
	}

	tx := db.DB.Begin()
	// 创建新记录
	newID, err := creatorFunc(tx, revision.Changes, userId)
	if err != nil {
		tx.Rollback()
		return 0, errors.New("创建新记录失败: " + err.Error())
	}

	// 更新状态和新记录ID
	updates := map[string]interface{}{
		"status":     model.RevisionStatusSubmitted,
		"new_id":     newID,
		"updated_at": time.Now().Unix(),
	}

	if err := tx.Model(&model.RevisionRecord{}).
		Where("id = ?", id).
		Updates(updates).Error; err != nil {
		tx.Rollback()
		return 0, errors.New("发布修订记录失败: " + err.Error())
	}
	tx.Commit()
	return newID, nil
}

// GetRevisionDetail 获取修订记录详情
func (s *RevisionService) GetRevisionDetail(id int64) (*model.RevisionRecord, error) {
	if id == 0 {
		return nil, errors.New("无效的修订记录ID")
	}

	var revision model.RevisionRecord
	if err := db.DB.First(&revision, id).Error; err != nil {
		return nil, errors.New("未找到修订记录")
	}

	return &revision, nil
}

// DeleteRevision 删除修订记录
func (s *RevisionService) DeleteRevision(id int64) error {
	if id == 0 {
		return errors.New("无效的修订记录ID")
	}

	// 检查记录是否存在
	var revision model.RevisionRecord
	if err := db.DB.Where("id = ?", id).First(&revision).Error; err != nil {
		return errors.New("未找到修订记录")
	}
	if revision.Status == model.RevisionStatusPublished || revision.Status == model.RevisionStatusSubmitted {
		return errors.New("已发布的修订记录不能删除")
	}
	if err := db.DB.Delete(&revision).Error; err != nil {
		return errors.New("删除修订记录失败: " + err.Error())
	}

	return nil
}

func ResourceCreator(tx *gorm.DB, content string, userID int64) (int64, error) {
	var req model.Resources
	if err := json.Unmarshal([]byte(content), &req); err != nil {
		return 0, errors.New("解析资料内容失败: " + err.Error())
	}
	req.ID = 0
	if err := tx.Create(&req).Error; err != nil {
		return 0, errors.New("创建资料失败: " + err.Error())
	}

	return req.ID, nil
}
