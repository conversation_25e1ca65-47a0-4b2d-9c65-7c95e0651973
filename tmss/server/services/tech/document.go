package tech

import (
	"encoding/json"
	"errors"
	"time"

	"tms/model"
	"tms/pkg/db"
	"tms/services/user"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type TeachingDocumentService struct{}

func NewTeachingDocumentService() *TeachingDocumentService {
	return &TeachingDocumentService{}
}
func TeachingDocumentCreator(tx *gorm.DB, content string, userID int64) (int64, error) {
	var changeReq struct {
		Form model.TeachingDocument `json:"form"`
	}
	if err := json.Unmarshal([]byte(content), &changeReq); err != nil {
		return 0, errors.New("解析教材内容失败: " + err.Error())
	}
	req := changeReq.Form
	err := createTeachingDocumentDraft(&req, userID)
	if err != nil {
		return 0, err
	}
	return req.ID, nil
}

// CreateTeachingDocumentDraft 创建教案草稿
func (s *TeachingDocumentService) CreateTeachingDocumentDraft(doc model.TeachingDocument, userId int64) error {
	return createTeachingDocumentDraft(&doc, userId)
}
func createTeachingDocumentDraft(doc *model.TeachingDocument, userId int64) error {
	if userId == 0 {
		return errors.New("无效的用户ID")
	}
	if doc.Title == "" {
		return errors.New("请填写标题")
	}
	if doc.Content == "" {
		return errors.New("请填写内容")
	}
	// 设置基础信息
	doc.UserID = userId
	doc.Status = model.TeachingDocumentStatusDraft
	doc.CreatedAt = time.Now().Unix()

	// 校验课时计划是否存在
	if doc.ClassScheduleID > 0 {
		if exists, err := checkClassScheduleExists(doc.ClassScheduleID); err != nil || !exists {
			return errors.New("关联的课时计划不存在")
		}
	}

	// 校验课件是否存在
	if doc.CoursewareID > 0 {
		if exists, err := checkCoursewareExists(doc.CoursewareID); err != nil || !exists {
			return errors.New("关联的课件不存在")
		}
	}

	// 创建教案
	if err := db.DB.Create(&doc).Error; err != nil {
		return errors.New("创建教案失败: " + err.Error())
	}
	return nil
}

// UpdateTeachingDocumentDraft 更新教案草稿
func (s *TeachingDocumentService) UpdateTeachingDocumentDraft(req model.TeachingDocument) error {
	if req.ID <= 0 {
		return errors.New("无效的教案ID")
	}

	// 获取现有教案
	var doc model.TeachingDocument
	if err := db.DB.First(&doc, req.ID).Error; err != nil {
		return errors.New("未找到对应教案")
	}

	// 状态校验：只能编辑草稿或驳回的教案
	if doc.Status != model.TeachingDocumentStatusDraft &&
		doc.Status != model.TeachingDocumentStatusRejected {
		return errors.New("只能编辑草稿或驳回状态的教案")
	}

	// 更新字段
	updates := map[string]interface{}{
		"title":             req.Title,
		"content":           req.Content,
		"class_schedule_id": req.ClassScheduleID,
		"courseware_id":     req.CoursewareID,
		"updated_at":        time.Now().Unix(),
	}

	// 课时计划存在性校验
	if req.ClassScheduleID > 0 {
		if exists, err := checkClassScheduleExists(req.ClassScheduleID); err != nil || !exists {
			return errors.New("关联的课时计划不存在")
		}
	}

	// 课件存在性校验
	if req.CoursewareID > 0 {
		if exists, err := checkCoursewareExists(req.CoursewareID); err != nil || !exists {
			return errors.New("关联的课件不存在")
		}
	}

	// 执行更新
	if err := db.DB.Model(&doc).Updates(updates).Error; err != nil {
		return errors.New("更新教案失败: " + err.Error())
	}

	return nil
}

// DeleteTeachingDocument 删除教案
func (s *TeachingDocumentService) DeleteTeachingDocument(id int64) error {
	if id == 0 {
		return errors.New("无效的教案ID")
	}

	var doc model.TeachingDocument
	if err := db.DB.First(&doc, id).Error; err != nil {
		return errors.New("未找到对应教案")
	}

	// 状态校验：只能删除草稿或驳回的教案
	if doc.Status != model.TeachingDocumentStatusDraft &&
		doc.Status != model.TeachingDocumentStatusRejected {
		return errors.New("只能删除草稿或驳回状态的教案")
	}

	if err := db.DB.Delete(&doc).Error; err != nil {
		return errors.New("删除失败: " + err.Error())
	}

	return nil
}

// GetTeachingDocumentDetail 获取教案详情
func (s *TeachingDocumentService) GetTeachingDocumentDetail(id int64) (model.RespTeachingDocumentDetail, error) {
	var resp model.RespTeachingDocumentDetail

	if id == 0 {
		return resp, errors.New("无效的教案ID")
	}

	// 获取基础信息
	var doc model.TeachingDocument
	if err := db.DB.First(&doc, id).Error; err != nil {
		return resp, errors.New("未找到对应教案")
	}
	resp.TeachingDocument = doc

	// 获取课时计划标题
	if doc.ClassScheduleID > 0 {
		err := db.DB.Model(&model.ClassSchedule{}).
			Where("id = ?", doc.ClassScheduleID).
			Pluck("title", &resp.ClassScheduleTitle).Error
		if err != nil {
			return resp, errors.New("获取课时计划标题失败: " + err.Error())
		}
	}

	// 获取关联课件信息
	if doc.CoursewareID > 0 {
		err := db.DB.Model(&model.Courseware{}).
			Where("id = ?", doc.CoursewareID).
			Pluck("title", &resp.CoursewareTitle).Error
		if err != nil {
			return resp, errors.New("获取课件标题失败: " + err.Error())
		}
	}
	if err := db.DB.Model(&model.Users{}).Where("id = ?", doc.UserID).Pluck("username", &resp.TeacherName).Error; err != nil {
		return resp, errors.New("获取教师名称失败: " + err.Error())
	}

	return resp, nil
}

// GetTeachingDocumentsList 获取教案列表
func (s *TeachingDocumentService) GetTeachingDocumentsList(c *gin.Context, req model.ReqTeachingDocumentSearch) (model.RespTeachingDocumentList, error) {
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 || req.PageSize > 100 {
		req.PageSize = 10
	}
	var res model.RespTeachingDocumentList
	dbQuery := db.DB.Model(&model.TeachingDocument{})

	// 应用过滤条件
	if req.Title != "" {
		dbQuery = dbQuery.Where("title ILIKE ?", "%"+req.Title+"%")
	}
	if req.Status != "" {
		dbQuery = dbQuery.Where("status = ?", req.Status)
	}
	if req.ClassScheduleID > 0 {
		dbQuery = dbQuery.Where("class_schedule_id = ?", req.ClassScheduleID)
	}
	if req.CoursewareID > 0 {
		dbQuery = dbQuery.Where("courseware_id = ?", req.CoursewareID)
	}
	if req.UserID > 0 {
		dbQuery = dbQuery.Where("user_id = ?", req.UserID)
	}

	// 应用数据权限规则
	dbQuery, _ = user.ApplyDataRules(c, dbQuery, model.ModuleDocuments)

	if err := dbQuery.Count(&res.Total).Error; err != nil {
		return res, errors.New("查询总数失败: " + err.Error())
	}

	offset := (req.Page - 1) * req.PageSize
	if err := dbQuery.Offset(offset).Limit(req.PageSize).Order("created_at DESC").Find(&res.List).Error; err != nil {
		return res, errors.New("查询数据失败: " + err.Error())
	}
	var sids []int64
	for _, d := range res.List {
		sids = append(sids, d.ID)
	}
	revisionList, err := GetRevisionList(model.ModuleDocuments, sids)
	if err != nil {
		return res, errors.New("查询版本记录失败: " + err.Error())
	}
	for i, d := range res.List {
		for _, rev := range revisionList {
			if rev.OriginalID == d.ID {
				res.List[i].Rev = &rev
			}
		}
	}
	return res, nil
}

// --- 辅助函数 ---

// 检查课时计划是否存在
func checkClassScheduleExists(id int64) (bool, error) {
	var count int64
	err := db.DB.Model(&model.ClassSchedule{}).Where("id = ?", id).Count(&count).Error
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

// 检查课件是否存在
func checkCoursewareExists(id int64) (bool, error) {
	var count int64
	err := db.DB.Model(&model.Courseware{}).Where("id = ?", id).Count(&count).Error
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

// 检查章节是否存在
func checkChapterExists(id int64) (bool, error) {
	var count int64
	err := db.DB.Model(&model.Chapter{}).Where("id = ?", id).Count(&count).Error
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

// 检查理论试题是否存在
func checkQuestionsExists(id int64) (bool, error) {
	var count int64
	err := db.DB.Model(&model.Questions{}).Where("id = ?", id).Count(&count).Error
	if err != nil {
		return false, err
	}
	return count > 0, nil
}
