package tech

import (
	"encoding/json"
	"errors"
	"log"
	"time"

	"tms/model"
	"tms/pkg/db"
	"tms/services/user"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type AssignmentService struct{}

func NewAssignmentService() *AssignmentService {
	return &AssignmentService{}
}
func AssignmentCreator(tx *gorm.DB, content string, userID int64) (int64, error) {
	var changeReq struct {
		Form model.AssignmentUpdate `json:"form"`
	}
	if err := json.Unmarshal([]byte(content), &changeReq); err != nil {
		return 0, errors.New("解析教材内容失败: " + err.Error())
	}
	req := changeReq.Form
	err := createAssignmentDraft(&req, tx, userID)
	if err != nil {
		return 0, err
	}
	return req.Assignment.ID, nil
}

// CreateAssignmentDraft 创建作业草稿
func (api *AssignmentService) CreateAssignmentDraft(req model.AssignmentUpdate, userID int64) error {
	tx := db.DB.Begin()
	err := createAssignmentDraft(&req, tx, userID)
	if err != nil {
		tx.Rollback()
		return err
	}
	tx.Commit()
	return nil
}
func createAssignmentDraft(req *model.AssignmentUpdate, tx *gorm.DB, userID int64) error {
	if userID == 0 {
		return errors.New("无效的用户ID")
	}
	if req.Assignment.Name == "" {
		return errors.New("请填写作业名称")
	}
	if len(req.Actions) == 0 {
		return errors.New("请添加作业动作")
	}

	req.Assignment.UserID = userID
	req.Assignment.CreatedAt = time.Now().Unix()
	req.Assignment.Status = model.AssignmentStatusDraft
	req.Assignment.ID = 0
	// 创建作业主表
	if err := tx.Create(&req.Assignment).Error; err != nil {
		return errors.New("创建失败: " + err.Error())
	}
	api := NewAssignmentService()
	// 创建关联关系
	if err := api.AddExts(req, tx); err != nil {
		return err
	}

	// 创建作业动作
	if err := api.AddActions(req, tx); err != nil {
		return err
	}
	return nil
}

// AddExts 添加关联关系（班级、教师）
func (api *AssignmentService) AddExts(req *model.AssignmentUpdate, tx *gorm.DB) error {
	var exts []model.AssignmentExt
	for _, classID := range req.ClassIDs {
		exts = append(exts, model.AssignmentExt{
			AssignmentID: req.Assignment.ID,
			ExtKey:       "class",
			ExtValue:     classID,
		})
	}
	for _, majorID := range req.MajorIDs {
		exts = append(exts, model.AssignmentExt{
			AssignmentID: req.Assignment.ID,
			ExtKey:       "major",
			ExtValue:     majorID,
		})
	}
	var students []int64
	dbQuery := tx.Model(&model.StudentMap{})
	if len(req.ClassIDs) > 0 {
		dbQuery = dbQuery.Where("class_id IN (?)", req.ClassIDs)
	}
	if len(req.MajorIDs) > 0 {
		dbQuery = dbQuery.Where("major_id IN (?)", req.MajorIDs)
	}
	err := dbQuery.Pluck("user_id", &students).Error
	if err != nil {
		return errors.New("获取学生失败: " + err.Error())
	}
	for _, studentID := range students {
		exts = append(exts, model.AssignmentExt{
			AssignmentID: req.Assignment.ID,
			ExtKey:       "student",
			ExtValue:     studentID,
		})
	}
	for _, teacherID := range req.TeacherIDs {
		exts = append(exts, model.AssignmentExt{
			AssignmentID: req.Assignment.ID,
			ExtKey:       "teacher",
			ExtValue:     teacherID,
		})
	}
	if len(exts) > 0 {
		if err := tx.Create(&exts).Error; err != nil {
			return errors.New("创建关联失败: " + err.Error())
		}
	}
	return nil
}

// AddActions 批量添加作业动作
func (api *AssignmentService) AddActions(req *model.AssignmentUpdate, tx *gorm.DB) error {
	// 为每个动作设置 AssignmentID
	for i := range req.Actions {
		req.Actions[i].ID = 0
		req.Actions[i].AssignmentID = req.Assignment.ID
	}

	// 批量创建动作
	if err := tx.Create(&req.Actions).Error; err != nil {
		return errors.New("批量创建动作失败: " + err.Error())
	}
	return nil
}

// UpdateAssignmentDraft 更新作业草稿
func (api *AssignmentService) UpdateAssignmentDraft(req model.AssignmentUpdate) error {
	if req.Assignment.ID <= 0 {
		return errors.New("无效的作业ID")
	}
	var assignment model.Assignment
	if err := db.DB.First(&assignment, req.Assignment.ID).Error; err != nil {
		return errors.New("未找到对应作业")
	}

	now := time.Now().Unix()
	req.Assignment.UpdatedAt = now

	tx := db.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 更新基本信息
	updates := map[string]interface{}{
		"name":        req.Assignment.Name,
		"description": req.Assignment.Description,
		"start_at":    req.Assignment.StartAt,
		"end_at":      req.Assignment.EndAt,
		"updated_at":  now,
	}
	if err := tx.Model(&assignment).Updates(updates).Error; err != nil {
		tx.Rollback()
		return errors.New("更新失败: " + err.Error())
	}

	// 删除旧的关联关系
	if err := tx.Where("assignment_id = ?", req.Assignment.ID).Delete(&model.AssignmentExt{}).Error; err != nil {
		tx.Rollback()
		return errors.New("删除旧关联失败: " + err.Error())
	}

	// 删除学习进度表
	// if err := tx.Where("assignment_id = ?", req.Assignment.ID).Delete(&model.LearningProgress{}).Error; err != nil {
	// 	tx.Rollback()
	// 	return errors.New("删除学习进度失败: " + err.Error())
	// }
	// 添加新的关联关系和动作
	if err := api.AddExts(&req, tx); err != nil {
		tx.Rollback()
		return err
	}
	// 删除旧的动作
	if err := tx.Where("assignment_id = ?", req.Assignment.ID).Delete(&model.AssignmentActions{}).Error; err != nil {
		tx.Rollback()
		return errors.New("删除旧动作失败: " + err.Error())
	}
	if err := api.AddActions(&req, tx); err != nil {
		tx.Rollback()
		return err
	}

	tx.Commit()
	return nil
}

// DeleteAssignment 删除作业
func (api *AssignmentService) DeleteAssignment(id int64) error {
	if id == 0 {
		return errors.New("无效的作业ID")
	}
	var assignment model.Assignment
	if err := db.DB.First(&assignment, id).Error; err != nil {
		return errors.New("未找到对应作业")
	}
	if assignment.Status == model.WorkflowApproveStatusPublished {
		return errors.New("已发布作业不能删除")
	}
	tx := db.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 删除作业主表
	if err := tx.Delete(&assignment).Error; err != nil {
		tx.Rollback()
		return errors.New("删除失败: " + err.Error())
	}

	// 删除关联关系
	if err := tx.Where("assignment_id = ?", id).Delete(&model.AssignmentExt{}).Error; err != nil {
		tx.Rollback()
		return errors.New("删除关联失败: " + err.Error())
	}

	// 删除动作
	if err := tx.Where("assignment_id = ?", id).Delete(&model.AssignmentActions{}).Error; err != nil {
		tx.Rollback()
		return errors.New("删除动作失败: " + err.Error())
	}

	tx.Commit()
	return nil
}

// GetAssignmentList 获取作业列表
func (api *AssignmentService) GetAssignmentList(c *gin.Context, req model.ReqPage, userId int64) (model.AssignmentList, error) {
	var res model.AssignmentList
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 || req.PageSize > 100 {
		req.PageSize = 10
	}

	// 创建基础查询
	dbQuery := db.DB.Table("assignment").
		Select("assignment.*, courses.name AS course_name, chapters.name AS chapter_name").
		Joins("LEFT JOIN courses ON assignment.course_id = courses.id").
		Joins("LEFT JOIN chapters ON assignment.chapter_id = chapters.id")

	// 添加过滤条件
	if req.Name != "" {
		dbQuery = dbQuery.Where("assignment.name ILIKE ?", "%"+req.Name+"%")
	}
	if req.Status != "" {
		dbQuery = dbQuery.Where("assignment.status = ?", req.Status)
	}

	// 应用数据权限规则
	dbQuery, has := user.ApplyDataRules(c, dbQuery, model.ModuleAssignment)
	if !has && userId > 0 {
		dbQuery = dbQuery.Where("assignment.user_id = ?", userId)
	}

	// 获取总数
	if err := dbQuery.Count(&res.Total).Error; err != nil {
		log.Printf("查询总数失败: %v", err)
		return res, errors.New("查询总数失败")
	}

	// 分页查询
	offset := (req.Page - 1) * req.PageSize
	if err := dbQuery.Offset(offset).Limit(req.PageSize).
		Order("assignment.created_at DESC").
		Scan(&res.List).Error; err != nil {
		return res, errors.New("查询数据失败: " + err.Error())
	}
	var ids []int64
	for _, assignment := range res.List {
		ids = append(ids, assignment.ID)
	}
	revisionList, err := GetRevisionList(model.ModuleAssignment, ids)
	if err != nil {
		return res, err
	}
	for i, assignment := range res.List {
		for _, r := range revisionList {
			if r.OriginalID == assignment.ID {
				res.List[i].Rev = &r
			}
		}
	}

	return res, nil
}

// GetAssignmentDetail 获取作业详情
func (api *AssignmentService) GetAssignmentDetail(id int64) (model.AssignmentDetail, error) {
	if id == 0 {
		return model.AssignmentDetail{}, errors.New("无效的作业ID")
	}

	var detail model.AssignmentDetail
	var assignment model.Assignment
	if err := db.DB.First(&assignment, id).Error; err != nil {
		return detail, errors.New("未找到对应作业")
	}
	detail.Assignment = assignment

	// 获取关联关系
	var exts []model.AssignmentExt
	if err := db.DB.Where("assignment_id = ?", id).Find(&exts).Error; err != nil {
		return detail, errors.New("未找到关联信息")
	}
	var classIDs, majorIDs, teacherIDs []int64
	for _, ext := range exts {
		if ext.ExtKey == "class" {
			classIDs = append(classIDs, ext.ExtValue)
		}
		if ext.ExtKey == "major" {
			majorIDs = append(majorIDs, ext.ExtValue)
		}
		if ext.ExtKey == "teacher" {
			teacherIDs = append(teacherIDs, ext.ExtValue)
		}
	}

	// 获取班级信息
	if len(classIDs) > 0 {
		if err := db.DB.Where("id IN (?)", classIDs).Find(&detail.Classes).Error; err != nil {
			return detail, errors.New("获取班级信息失败")
		}
	}

	// 获取教师信息
	if len(teacherIDs) > 0 {
		if err := db.DB.Where("id IN (?)", teacherIDs).Omit("password", "salt").Find(&detail.Teachers).Error; err != nil {
			return detail, errors.New("获取教师信息失败")
		}
	}
	if len(majorIDs) > 0 {
		if err := db.DB.Where("id IN (?)", majorIDs).Find(&detail.Majors).Error; err != nil {
			return detail, errors.New("获取专业信息失败")
		}
	}
	// 获取动作信息
	if err := db.DB.Where("assignment_id = ?", id).Find(&detail.Actions).Error; err != nil {
		return detail, errors.New("获取动作信息失败")
	}

	return detail, nil
}
