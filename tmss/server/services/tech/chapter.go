package tech

import (
	"encoding/json"
	"errors"
	"log"
	"time"

	"tms/model"
	"tms/pkg/db"

	"github.com/spf13/cast"
	"gorm.io/gorm"
)

// ChapterService 章节相关服务
type ChapterService struct{}

// NewChapterService 创建服务实例
func NewChapterService() *ChapterService {
	return &ChapterService{}
}

// CreateChapter 创建章节
func (s *ChapterService) CreateChapter(req *model.Chapter, userID int64) error {
	if userID == 0 {
		return errors.New("无效的用户ID")
	}

	req.UserID = userID
	req.CreatedAt = time.Now().Unix()
	req.UpdatedAt = req.CreatedAt

	if err := db.DB.Create(req).Error; err != nil {
		return errors.New("创建章节失败: " + err.Error())
	}

	return nil
}

// UpdateChapter 更新章节信息
func (s *ChapterService) UpdateChapter(req *model.Chapter) error {
	if req.ID <= 0 {
		return errors.New("无效的章节ID")
	}

	var chapter model.Chapter
	if err := db.DB.First(&chapter, req.ID).Error; err != nil {
		return errors.New("未找到对应章节")
	}

	now := time.Now().Unix()
	req.UpdatedAt = now

	updates := map[string]interface{}{
		"name":        req.Name,
		"description": req.Description,
		"parent_id":   req.ParentID,
		"updated_at":  now,
	}

	if err := db.DB.Model(&model.Chapter{}).
		Where("id = ?", req.ID).
		Updates(updates).Error; err != nil {
		return errors.New("更新章节失败: " + err.Error())
	}

	return nil
}

// DeleteChapter 删除章节
func (s *ChapterService) DeleteChapter(id int64) error {
	if id == 0 {
		return errors.New("无效的章节ID")
	}

	var chapter model.Chapter
	if err := db.DB.First(&chapter, id).Error; err != nil {
		return errors.New("未找到对应章节")
	}

	if err := db.DB.Delete(&chapter).Error; err != nil {
		return errors.New("删除章节失败: " + err.Error())
	}

	return nil
}
func (s *ChapterService) GetChapterTreeByCourseID(courseID int64) ([]*model.ChapterTreeNode, error) {
	var course model.Courses
	if err := db.DB.Model(&model.Courses{}).
		Where("id = ?", courseID).
		First(&course).Error; err != nil {

		// 区分错误类型
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("未找到对应课程")
		}
		return nil, errors.New("查询课程失败: " + err.Error())
	}
	return s.GetChapterList("", course.ChapterID)
}

// GetChapterList 获取章节列表并构建树状结构
func (s *ChapterService) GetChapterList(name string, parentID int64) ([]*model.ChapterTreeNode, error) {
	// 查询所有章节数据
	var allChapters []model.Chapter
	dbQuery := db.DB.Model(&model.Chapter{})
	if name != "" {
		dbQuery = dbQuery.Where("name ILIKE ?", "%"+name+"%")
	}
	if err := dbQuery.Order("id ASC").Find(&allChapters).Error; err != nil {
		return nil, errors.New("获取数据失败: " + err.Error())
	}

	// 构建完整的章节树
	fullTree := buildChapterTree(allChapters)

	// 如果指定了父节点ID，则返回该节点的子树
	if parentID > 0 {
		return findSubtree(fullTree, parentID), nil
	}

	// 否则返回整个章节树
	return fullTree, nil
}

// findSubtree 在整棵树中查找指定节点的子树
func findSubtree(tree []*model.ChapterTreeNode, parentID int64) []*model.ChapterTreeNode {
	// 广度优先搜索查找节点
	queue := make([]*model.ChapterTreeNode, 0, len(tree))
	queue = append(queue, tree...) // 直接追加整个切片

	for len(queue) > 0 {
		node := queue[0]
		queue = queue[1:]

		if node.ID == parentID {
			return []*model.ChapterTreeNode{node}
		}

		// 直接追加整个子切片
		queue = append(queue, node.Children...)
	}

	// 未找到节点，返回空数组
	return []*model.ChapterTreeNode{}
}

// GetChapterListByCourseID 根据课程ID获取章节列表并构建树状结构
func (s *ChapterService) GetChapterListByCourseID(courseID int64) ([]*model.ChapterTreeNode, error) {
	var courses model.Courses
	err := db.DB.Where("id = ?", courseID).First(&courses).Error
	if err != nil {
		return nil, errors.New("未找到对应课程")
	}
	chapterIDS := []int64{courses.ChapterID}
	// 收集所有需要查询的章节ID
	finalChapterIDMap := make(map[int64]struct{})
	queue := make([]int64, 0)

	// 初始化队列，将所有初始章节ID加入队列
	for _, initialID := range chapterIDS {
		if _, ok := finalChapterIDMap[initialID]; !ok {
			finalChapterIDMap[initialID] = struct{}{}
			queue = append(queue, initialID)
		}
	}

	// BFS 遍历：只向下查找所有子章节
	for len(queue) > 0 {
		currentID := queue[0]
		queue = queue[1:]

		// 查找子节点
		var children []model.Chapter
		if err := db.DB.Where("parent_id = ?", currentID).Find(&children).Error; err != nil {
			log.Printf("获取章节子节点失败: %v", err)
			continue
		}
		for _, child := range children {
			if _, ok := finalChapterIDMap[child.ID]; !ok {
				finalChapterIDMap[child.ID] = struct{}{}
				queue = append(queue, child.ID)
			}
		}
	}

	// 将 map 的 key 转换为 slice
	var idsToQuery []int64
	for id := range finalChapterIDMap {
		idsToQuery = append(idsToQuery, id)
	}

	var chapters []model.Chapter
	if err := db.DB.Where("id IN (?)", idsToQuery).Order("id ASC").Find(&chapters).Error; err != nil {
		return nil, errors.New("获取数据失败: " + err.Error())
	}

	chapterIDs := make([]int64, len(chapters))
	for i, chapter := range chapters {
		chapterIDs[i] = chapter.ID
	}

	// 批量查询课件
	var coursewares []model.Courseware
	coursewareCounts := make(map[int64]int64) // 修改为存储数量
	if len(chapterIDs) > 0 {
		if err := db.DB.Where("chapter_id IN (?)", chapterIDs).Find(&coursewares).Error; err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("获取课件数据失败: " + err.Error())
		}
		for _, cw := range coursewares {
			coursewareCounts[cw.ChapterID]++ // 统计数量
		}
	}

	// 批量查询问题
	var questionsExt []model.QuestionsExt
	questionCounts := make(map[int64]int64) // 修改为存储数量
	if len(chapterIDs) > 0 {
		if err := db.DB.Where("ext_key = ? AND ext_value IN (?)", "chapter", chapterIDs).Find(&questionsExt).Error; err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("获取问题数据失败: " + err.Error())
		}
		for _, qe := range questionsExt {
			// ext_value 是 string 类型，需要转换为 int64
			chapterID := cast.ToInt64(qe.ExtValue)
			questionCounts[chapterID]++ // 统计数量
		}
	}

	// 批量查询章节下的资料
	resourceCounts := make(map[int64]int64) // 修改为存储数量
	if len(chapterIDs) > 0 {
		var resources []model.Resources
		if err := db.DB.Where("chapter_id IN (?)", chapterIDs).Find(&resources).Error; err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("获取资料数据失败: " + err.Error())
		}
		for _, r := range resources {
			resourceCounts[r.ChapterID]++ // 统计数量
		}
	}

	for i := range chapters {
		attachedAttributes := make([]model.AttachedAttribute, 0) // 每次循环初始化
		if count := coursewareCounts[chapters[i].ID]; count > 0 {
			attachedAttributes = append(attachedAttributes, model.AttachedAttribute{Key: "courseware_count", Value: cast.ToString(count)}) // 使用实际数量
		}

		if count := questionCounts[chapters[i].ID]; count > 0 {
			attachedAttributes = append(attachedAttributes, model.AttachedAttribute{Key: "question_count", Value: cast.ToString(count)}) // 使用实际数量
		}

		if count := resourceCounts[chapters[i].ID]; count > 0 {
			attachedAttributes = append(attachedAttributes, model.AttachedAttribute{Key: "resource_count", Value: cast.ToString(count)}) // 使用实际数量
		}

		if len(attachedAttributes) > 0 {
			attributesStr, _ := json.Marshal(&attachedAttributes)
			chapters[i].AttachedAttributeValue = string(attributesStr)
		}
	}

	return buildChapterTree(chapters), nil
}

// 根据章节id获取课件列表
func (s *ChapterService) GetCoursewareListByChapterID(chapterID int64) ([]*model.Courseware, error) {
	var coursewares []*model.Courseware
	if err := db.DB.Where("chapter_id = ?", chapterID).Find(&coursewares).Error; err != nil {
		return nil, errors.New("获取数据失败: " + err.Error())
	}
	return coursewares, nil
}

// buildChapterTree 将平级数据构建成树状结构
func buildChapterTree(list []model.Chapter) []*model.ChapterTreeNode {
	chapterMap := make(map[int64]*model.ChapterTreeNode)

	// 第一步：初始化所有节点
	for _, chapter := range list {
		chapterMap[chapter.ID] = &model.ChapterTreeNode{
			ID:                     chapter.ID,
			Name:                   chapter.Name,
			Description:            chapter.Description,
			ParentID:               chapter.ParentID,
			Children:               make([]*model.ChapterTreeNode, 0),
			AttachedAttributeValue: chapter.AttachedAttributeValue,
		}
	}

	// 第二步：构建树结构
	var tree []*model.ChapterTreeNode
	for _, chapter := range list {
		node := chapterMap[chapter.ID]

		if chapter.ParentID == 0 {
			tree = append(tree, node)
		} else {
			if parent, ok := chapterMap[chapter.ParentID]; ok {
				parent.Children = append(parent.Children, node)
			} else {
				// 父节点不存在，添加到树中
				tree = append(tree, node)
			}

		}
	}

	return tree
}
