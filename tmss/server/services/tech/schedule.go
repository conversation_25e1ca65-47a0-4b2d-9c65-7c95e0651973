package tech

import (
	"errors"
	"fmt"
	"strings"
	"time"
	"tms/model"
	"tms/pkg/db"
	"tms/services/user"
	"tms/utils"

	"github.com/gin-gonic/gin"
)

type ClassScheduleService struct{}

func NewClassScheduleService() *ClassScheduleService {
	return &ClassScheduleService{}
}

// 创建课表（批量）
func (api *ClassScheduleService) GenerateClassSchedule(req model.ReqGenerateClassSchedule, userId int64) error {
	if userId == 0 {
		return errors.New("无效的用户ID")
	}

	// 验证教学计划状态
	teachingPlan, err := api.getTeachingPlanPublished(req.TeachingPlanID)
	if err != nil {
		return err
	}

	// 验证教师有效性
	if req.TeacherID > 0 {
		exists, err := user.CheckUserExists(req.TeacherID)
		if err != nil || !exists {
			return errors.New("教师ID无效")
		}
	}
	if req.CourseID > 0 {
		exists, err := checkCourseExists(req.CourseID)
		if err != nil || !exists {
			return errors.New("课程ID无效")
		}
	}
	// 解析开始日期
	startDate, err := time.Parse("2006-01-02", req.StartDate)
	if err != nil {
		return errors.New("开始日期格式错误，应为 YYYY-MM-DD")
	}

	// 验证课时参数
	if req.LessonNum <= 0 || req.ClassHours <= 0 || len(req.Weekdays) == 0 {
		return errors.New("课时数、课时时长和上课日不能为空")
	}

	// 解析上课时间
	classTime, err := time.Parse("15:04", req.ClassTime)
	if err != nil {
		return errors.New("上课时间格式错误，应为 HH:MM")
	}

	// 生成课表
	now := time.Now().Unix()
	var prevID = req.PreviousID

	// 当前日期指针
	currentDate := startDate
	// 已生成的课时计数
	generatedCount := 0
	tx := db.DB.Begin()
	for generatedCount < req.LessonNum {
		// 检查当前日期是否是上课日
		currentWeekday := int(currentDate.Weekday())
		if utils.HasContainsInts(req.Weekdays, currentWeekday) {
			// 设置上课时间
			startTime := time.Date(
				currentDate.Year(), currentDate.Month(), currentDate.Day(),
				classTime.Hour(), classTime.Minute(), 0, 0, time.Local,
			)

			// 计算结束时间
			endTime := startTime.Add(time.Duration(req.ClassHours) * time.Minute)
			if teachingPlan.StartAt > 0 && startTime.Unix() < teachingPlan.StartAt {
				tx.Rollback()
				return errors.New("课表时间范围不能早于教学计划开始时间")
			}
			if teachingPlan.EndAt > 0 && endTime.Unix() > teachingPlan.EndAt {
				tx.Rollback()
				return errors.New("课表时间范围不能晚于教学计划结束时间")
			}
			// 创建课表
			schedule := model.ClassSchedule{
				TeachingPlanID: req.TeachingPlanID,
				Title:          req.Title + fmt.Sprintf("课时%d", generatedCount+1),
				UserID:         userId,
				Content:        req.Content,
				ClassroomID:    req.ClassroomID,
				TeacherID:      req.TeacherID,
				CourseID:       req.CourseID,
				Weekday:        currentWeekday,
				StartAt:        startTime.Unix(),
				EndAt:          endTime.Unix(),
				PreviousID:     prevID,
				Status:         model.ClassScheduleStatusDraft,
				CreatedAt:      now,
				UpdatedAt:      now,
			}

			//schedules = append(schedules, schedule)
			generatedCount++
			if err := tx.Create(&schedule).Error; err != nil {
				tx.Rollback()
				return errors.New("创建课表失败: " + err.Error())
			}
			// 更新前置ID
			prevID = schedule.ID
		}

		// 移到下一天
		currentDate = currentDate.AddDate(0, 0, 1)
	}

	// 提交事务
	return tx.Commit().Error
}

// 更新课表
func (api *ClassScheduleService) UpdateClassSchedule(schedule model.ClassSchedule) error {
	if schedule.ID <= 0 {
		return errors.New("无效的课表ID")
	}

	var existing model.ClassSchedule
	if err := db.DB.First(&existing, schedule.ID).Error; err != nil {
		return errors.New("课表不存在")
	}

	// 状态检查：只能修改草稿或已驳回状态
	if existing.Status != model.ClassScheduleStatusDraft &&
		existing.Status != model.ClassScheduleStatusRejected {
		return errors.New("只能修改草稿或已驳回的课表")
	}

	// 验证教学计划状态
	teachingPlan, err := api.getTeachingPlanPublished(schedule.TeachingPlanID)
	if err != nil {
		return err
	}

	// 验证时间有效性
	if schedule.StartAt == 0 || schedule.EndAt == 0 || schedule.StartAt >= schedule.EndAt {
		return errors.New("课表时间范围无效")
	}
	if teachingPlan.StartAt > 0 && schedule.StartAt < teachingPlan.StartAt {
		return errors.New("课表时间范围不能早于教学计划开始时间")
	}
	if teachingPlan.EndAt > 0 && schedule.EndAt > teachingPlan.EndAt {
		return errors.New("课表时间范围不能晚于教学计划结束时间")
	}
	//根据开始时间计算是星期几
	weekday := time.Unix(schedule.StartAt, 0).Weekday()
	// 验证教师是否存在
	if schedule.TeacherID > 0 {
		exists, err := user.CheckUserExists(schedule.TeacherID)
		if err != nil || !exists {
			return errors.New("教师ID无效")
		}
	}

	updates := map[string]interface{}{
		"title":            schedule.Title,
		"content":          schedule.Content,
		"classroom_id":     schedule.ClassroomID,
		"teacher_id":       schedule.TeacherID,
		"teaching_plan_id": schedule.TeachingPlanID,
		"start_at":         schedule.StartAt,
		"end_at":           schedule.EndAt,
		"weekday":          int(weekday),
		"previous_id":      schedule.PreviousID,
		"updated_at":       time.Now().Unix(),
	}

	return db.DB.Model(&existing).Where("id = ?", existing.ID).Updates(updates).Error
}

// 删除课表
func (api *ClassScheduleService) DeleteClassSchedule(id int64) error {
	if id <= 0 {
		return errors.New("无效的课表ID")
	}

	var schedule model.ClassSchedule
	if err := db.DB.First(&schedule, id).Error; err != nil {
		return errors.New("课表不存在")
	}

	// 只能删除草稿或已驳回状态
	if schedule.Status != model.ClassScheduleStatusDraft &&
		schedule.Status != model.ClassScheduleStatusRejected {
		return errors.New("只能删除草稿或已驳回的课表")
	}

	return db.DB.Delete(&schedule).Error
}

// 分页查询课表列表
func (api *ClassScheduleService) GetClassScheduleList(
	c *gin.Context,
	req model.ReqClassScheduleSearch,
) (model.RespClassScheduleList, error) {
	var res model.RespClassScheduleList
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 || req.PageSize > 100 {
		req.PageSize = 10
	}

	dbQuery := db.DB.Model(&model.ClassSchedule{})

	// 应用基础查询条件
	if req.TeachingPlanID > 0 {
		dbQuery = dbQuery.Where("teaching_plan_id = ?", req.TeachingPlanID)
	}
	if req.TeacherID > 0 {
		dbQuery = dbQuery.Where("teacher_id = ?", req.TeacherID)
	}
	if req.DateRange != "" {
		dates := strings.Split(req.DateRange, "~")
		if len(dates) == 2 {
			start, err := time.Parse("2006-01-02", strings.TrimSpace(dates[0]))
			if err == nil {
				dbQuery = dbQuery.Where("start_at >= ?", start.Unix())
			}

			end, err := time.Parse("2006-01-02", strings.TrimSpace(dates[1]))
			if err == nil {
				endTime := end.Add(24 * time.Hour).Unix() // 包含结束日
				dbQuery = dbQuery.Where("end_at <= ?", endTime)
			}
		}
	}
	if req.Status != "" {
		dbQuery = dbQuery.Where("status = ?", req.Status)
	}
	// 应用数据权限
	dbQuery, _ = user.ApplyDataRules(c, dbQuery, model.ModuleSchedule)

	// 获取总数
	if err := dbQuery.Count(&res.Total).Error; err != nil {
		return res, errors.New("查询总数失败")
	}

	// 分页查询
	var schedules []model.ClassSchedule
	offset := (req.Page - 1) * req.PageSize
	if err := dbQuery.Offset(offset).Limit(req.PageSize).
		Order("id DESC").
		Find(&schedules).Error; err != nil {
		return res, errors.New("查询数据失败")
	}

	// 获取教师ID列表
	var teacherIDs []int64
	for _, s := range schedules {
		if s.TeacherID > 0 {
			teacherIDs = append(teacherIDs, s.TeacherID)
		}
	}

	// 获取教师姓名映射
	teacherMap := make(map[int64]string)
	if len(teacherIDs) > 0 {
		teachers, err := user.GetUsersByIDs(teacherIDs)
		if err == nil {
			for _, t := range teachers {
				teacherMap[t.ID] = t.Username
			}
		}
	}
	// 获取教学计划ID列表
	var teachingPlanIds []int64
	for _, s := range schedules {
		if s.TeachingPlanID > 0 {
			teachingPlanIds = append(teachingPlanIds, s.TeachingPlanID)
		}
	}
	var teachingPlan []model.TeachingPlan
	var teachingPlanMap = make(map[int64]string)
	if len(teachingPlanIds) > 0 {
		err := db.DB.Model(&model.TeachingPlan{}).Where("id IN ?", teachingPlanIds).Find(&teachingPlan).Error
		if err != nil {
			return res, errors.New("查询教学计划失败: " + err.Error())
		}
		for _, t := range teachingPlan {
			teachingPlanMap[t.ID] = t.Name
		}
	}
	// 获取关联的课程ID
	var courseIDs []int64
	for _, s := range schedules {
		if s.CourseID > 0 {
			courseIDs = append(courseIDs, s.CourseID)
		}
	}
	var courses []model.Courses
	var courseMap = make(map[int64]string)
	if len(courseIDs) > 0 {
		err := db.DB.Model(&model.Courses{}).Where("id IN ?", courseIDs).Find(&courses).Error
		if err != nil {
			return res, errors.New("查询课程失败: " + err.Error())
		}
		for _, c := range courses {
			courseMap[c.ID] = c.Name
		}
	}
	// 获取教室ID列表
	var classroomIDs []int64
	for _, s := range schedules {
		if s.ClassroomID > 0 {
			classroomIDs = append(classroomIDs, s.ClassroomID)
		}
	}
	var classrooms []model.Classroom
	var classroomMap = make(map[int64]string)
	if len(classroomIDs) > 0 {
		err := db.DB.Model(&model.Classroom{}).Where("id IN ?", classroomIDs).Find(&classrooms).Error
		if err != nil {
			return res, errors.New("查询教室失败: " + err.Error())
		}
		for _, c := range classrooms {
			classroomMap[c.ID] = c.Title
		}
	}
	// 获取版本记录ID列表
	var sids []int64
	for _, schedule := range schedules {
		sids = append(sids, schedule.ID)
	}
	revisionList, err := GetRevisionList(model.ModuleSchedule, sids)
	if err != nil {
		return res, errors.New("查询版本记录失败: " + err.Error())
	}
	// 构建响应数据
	for _, s := range schedules {
		detail := model.RespClassSchedule{
			ClassSchedule:    s,
			TeacherName:      teacherMap[s.TeacherID],
			TeachingPlanName: teachingPlanMap[s.TeachingPlanID],
			CourseName:       courseMap[s.CourseID],
			ClassroomName:    classroomMap[s.ClassroomID],
		}
		for _, rev := range revisionList {
			if rev.OriginalID == s.ID {
				detail.Rev = &rev
			}
		}
		res.List = append(res.List, detail)
	}

	return res, nil
}

// 检查教学计划是否已发布
func (api *ClassScheduleService) getTeachingPlanPublished(planID int64) (model.TeachingPlan, error) {
	var res model.TeachingPlan
	if planID <= 0 {
		return res, errors.New("无效的教学计划ID")
	}

	err := db.DB.First(&res, planID).Error
	if err != nil {
		return res, errors.New("未找到对应计划")
	}
	if res.Status != model.TeachingPlanStatusPublished {
		return res, errors.New("请先发布计划")
	}
	return res, nil
}

// 查询课表详情
func (api *ClassScheduleService) GetClassScheduleDetail(id int64) (*model.RespClassSchedule, error) {
	if id <= 0 {
		return nil, errors.New("无效的课表ID")
	}

	var schedule model.ClassSchedule
	if err := db.DB.First(&schedule, id).Error; err != nil {
		return nil, errors.New("课表不存在")
	}

	// 获取教师姓名
	teacherName := ""
	if schedule.TeacherID > 0 {
		t, err := user.GetUserByID(schedule.TeacherID)
		if err == nil {
			teacherName = t.Username
		}
	}
	previousName := ""
	if schedule.PreviousID > 0 {
		var previous model.ClassSchedule
		if err := db.DB.First(&previous, schedule.PreviousID).Error; err == nil {
			previousName = previous.Title
		}
	}
	teachingPlanName := ""
	if schedule.TeachingPlanID > 0 {
		var teachingPlan model.TeachingPlan
		if err := db.DB.First(&teachingPlan, schedule.TeachingPlanID).Error; err == nil {
			teachingPlanName = teachingPlan.Name
		}
	}
	courseName := ""
	if schedule.CourseID > 0 {
		var course model.Courses
		if err := db.DB.First(&course, schedule.CourseID).Error; err == nil {
			courseName = course.Name
		}
	}
	ClassroomName := ""
	if schedule.ClassroomID > 0 {
		var classroom model.Classroom
		if err := db.DB.First(&classroom, schedule.ClassroomID).Error; err == nil {
			ClassroomName = classroom.Title
		}
	}
	result := &model.RespClassSchedule{
		ClassSchedule:    schedule,
		TeacherName:      teacherName,
		PreviousName:     previousName,
		TeachingPlanName: teachingPlanName,
		CourseName:       courseName,
		ClassroomName:    ClassroomName,
	}

	return result, nil
}
