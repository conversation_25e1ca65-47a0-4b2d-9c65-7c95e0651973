package tech

import (
	"encoding/json"
	"errors"
	"time"

	"tms/model"
	"tms/pkg/db"
	"tms/services/user"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// SyllabusService 定义教学大纲相关的业务逻辑
type SyllabusService struct{}

func NewSyllabusService() *SyllabusService { return &SyllabusService{} }

// TextbookCreator 教材创建器函数
func SyllabusCreator(tx *gorm.DB, content string, userID int64) (int64, error) {
	var changeReq struct {
		FormData model.ReqSyllabusCreate `json:"formData"`
	}
	//var req model.ReqSyllabusCreate
	if err := json.Unmarshal([]byte(content), &changeReq); err != nil {
		return 0, errors.New("解析教材内容失败: " + err.Error())
	}
	req := changeReq.FormData
	req.Syllabus.UserID = userID
	req.Syllabus.Status = model.WorkflowApproveStatusRevision
	req.Syllabus.CreatedAt = time.Now().Unix()
	req.Syllabus.ID = 0 // 重置ID，确保创建新记录
	if err := tx.Create(&req.Syllabus).Error; err != nil {
		return 0, errors.New("创建修订失败: " + err.Error())
	}
	if err := saveSyllabusExt(tx, &req); err != nil {
		return 0, errors.New("创建教材关联失败: " + err.Error())
	}
	return req.Syllabus.ID, nil
}

// CreateDraft 创建草稿
func (s *SyllabusService) CreateDraft(req model.ReqSyllabusCreate, userID int64) error {
	if userID == 0 {
		return errors.New("无效的用户ID")
	}
	req.Syllabus.UserID = userID
	req.Syllabus.Status = model.SyllabusStatusDraft
	req.Syllabus.CreatedAt = time.Now().Unix()
	tx := db.DB.Begin()
	if err := tx.Create(&req.Syllabus).Error; err != nil {
		tx.Rollback()
		return errors.New("创建草稿失败: " + err.Error())
	}
	if err := saveSyllabusExt(tx, &req); err != nil {
		tx.Rollback()
		return errors.New("创建教材关联失败: " + err.Error())
	}
	tx.Commit()
	return nil
}
func saveSyllabusExt(tx *gorm.DB, req *model.ReqSyllabusCreate) error {
	var syllabusExts []model.SyllabusExt
	for _, textbookID := range req.TextbookIDs {
		syllabusExts = append(syllabusExts, model.SyllabusExt{
			SyllabusID: req.Syllabus.ID,
			ExtKey:     "textbook_id",
			ExtValue:   textbookID,
		})
	}
	for _, resourceID := range req.ResourcesIDs {
		syllabusExts = append(syllabusExts, model.SyllabusExt{
			SyllabusID: req.Syllabus.ID,
			ExtKey:     "resource_id",
			ExtValue:   resourceID,
		})
	}

	if len(syllabusExts) > 0 {
		if err := tx.Create(&syllabusExts).Error; err != nil {
			return errors.New("创建教材关联失败: " + err.Error())
		}
	}
	return nil
}

// UpdateDraft 更新草稿
func (s *SyllabusService) UpdateDraft(req model.ReqSyllabusCreate, userID int64) error {
	if userID == 0 {
		return errors.New("无效的用户ID")
	}
	if req.Syllabus.ID <= 0 {
		return errors.New("无效的大纲")
	}
	var syllabus model.Syllabus
	if err := db.DB.First(&syllabus, req.Syllabus.ID).Error; err != nil {
		return errors.New("未找到对应大纲")
	}
	if syllabus.Status != model.SyllabusStatusDraft && syllabus.Status != model.SyllabusStatusRejected {
		return errors.New("只有草稿或驳回状态的大纲才能发布")
	}
	// if userID != syllabus.UserID {
	// 	return errors.New("无效的用户ID")
	// }
	req.Syllabus.UpdatedAt = time.Now().Unix()

	updates := map[string]interface{}{
		"name":        req.Syllabus.Name,
		"goals":       req.Syllabus.Goals,
		"description": req.Syllabus.Description,
		"reference":   req.Syllabus.Reference,
		"version":     req.Syllabus.Version,
		"updated_at":  req.Syllabus.UpdatedAt,
	}
	tx := db.DB.Begin()
	if err := tx.Model(&model.Syllabus{}).
		Where("id = ?", req.Syllabus.ID).
		Updates(updates).Error; err != nil {
		tx.Rollback()
		return errors.New("更新草稿失败: " + err.Error())
	}
	err := tx.Where("syllabus_id = ?", req.Syllabus.ID).Delete(&model.SyllabusExt{}).Error
	if err != nil {
		tx.Rollback()
		return errors.New("删除扩展信息失败: " + err.Error())
	}
	if err := saveSyllabusExt(tx, &req); err != nil {
		tx.Rollback()
		return errors.New("创建教材关联失败: " + err.Error())
	}
	tx.Commit()
	return nil
}

// PublishDraft 发布草稿
func (s *SyllabusService) PublishDraft(id int64) error {
	if id == 0 {
		return errors.New("无效的大纲ID")
	}
	var syllabus model.Syllabus
	if err := db.DB.First(&syllabus, id).Error; err != nil {
		return errors.New("未找到对应草稿")
	}

	if syllabus.Status != model.SyllabusStatusDraft && syllabus.Status != model.SyllabusStatusRejected {
		return errors.New("只有草稿或驳回状态的大纲才能发布")
	}
	now := time.Now().Unix()
	syllabus.Status = model.SyllabusStatusReviewing
	syllabus.SubmitAt = now

	if err := db.DB.Save(&syllabus).Error; err != nil {
		return errors.New("发布失败: " + err.Error())
	}

	return nil
}

// DeleteDraft 删除草稿
func (s *SyllabusService) DeleteDraft(id int64) error {
	if id == 0 {
		return errors.New("无效的大纲ID")
	}
	var syllabus model.Syllabus
	if err := db.DB.First(&syllabus, id).Error; err != nil {
		return errors.New("无效的大纲ID")
	}

	if syllabus.Status != model.SyllabusStatusDraft && syllabus.Status != model.SyllabusStatusRejected {
		return errors.New("只能删除草稿")
	}

	if err := db.DB.Delete(&syllabus).Error; err != nil {
		return errors.New("删除草稿失败: " + err.Error())
	}

	// 删除教材扩展字段
	if err := db.DB.Where("syllabus_id = ? AND ext_key = ?", syllabus.ID, "textbook_id").Delete(&model.SyllabusExt{}).Error; err != nil {
		return errors.New("删除教材扩展字段失败: " + err.Error())
	}

	return nil
}

// ApproveReview 审核大纲
func (s *SyllabusService) ApproveReview(id int64) error {
	if id == 0 {
		return errors.New("无效的大纲ID")
	}
	var syllabus model.Syllabus
	if err := db.DB.First(&syllabus, id).Error; err != nil {
		return errors.New("未找到对应大纲")
	}

	if syllabus.Status != model.SyllabusStatusReviewing {
		return errors.New("只有审查中的大纲才能审核")
	}

	now := time.Now().Unix()
	updates := map[string]interface{}{
		"status":         model.SyllabusStatusPublished,
		"published_at":   now,
		"effective_date": now,
		"updated_at":     now,
	}

	if err := db.DB.Model(&model.Syllabus{}).
		Where("id = ?", id).
		Updates(updates).Error; err != nil {
		return errors.New("审核失败: " + err.Error())
	}

	return nil
}
func (s *SyllabusService) RejectReview(id int64) error {
	if id == 0 {
		return errors.New("无效的大纲ID")
	}

	var syllabus model.Syllabus
	if err := db.DB.First(&syllabus, id).Error; err != nil {
		return errors.New("未找到对应大纲")
	}

	if syllabus.Status != model.SyllabusStatusReviewing {
		return errors.New("只有审查中的大纲才能驳回")
	}

	now := time.Now().Unix()

	updates := map[string]interface{}{
		"status":     model.SyllabusStatusRejected,
		"updated_at": now,
	}
	if err := db.DB.Model(&model.Syllabus{}).
		Where("id = ?", id).
		Updates(updates).Error; err != nil {
		return errors.New("驳回失败: " + err.Error())
	}

	return nil
}

// GetSyllabusList 获取大纲列表
func (s *SyllabusService) GetSyllabusList(c *gin.Context, req model.ReqSyllabusSearch) (model.RespSyllabusList, error) {
	var res model.RespSyllabusList
	dbQuery := db.DB.Model(&model.Syllabus{})
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 || req.PageSize > 100 {
		req.PageSize = 10
	}
	// 状态和名称筛选
	if req.Name != "" {
		dbQuery = dbQuery.Where("name ILIKE ?", "%"+req.Name+"%")
	}
	if req.Status != "" {
		dbQuery = dbQuery.Where("status = ?", req.Status)
	}
	dbQuery, has := user.ApplyDataRules(c, dbQuery, model.ModuleSyllabus)
	if !has && req.UserID > 0 {
		dbQuery = dbQuery.Where("user_id = ?", req.UserID)
	}
	if req.UserName != "" {
		dbQuery = dbQuery.Where("user_id IN (SELECT id FROM users WHERE name ILIKE ?)", "%"+req.UserName+"%")
	}

	// 获取总数
	if err := dbQuery.Count(&res.Total).Error; err != nil {
		return res, errors.New("获取总数失败: " + err.Error())
	}

	// 分页查询主表 syllabus
	var syllabuses []model.Syllabus
	offset := (req.Page - 1) * req.PageSize
	if err := dbQuery.Order("created_at DESC").Offset(offset).Limit(req.PageSize).Find(&syllabuses).Error; err != nil {
		return res, errors.New("获取数据失败: " + err.Error())
	}

	// 提取所有 syllabus ID
	var syllabusIDs []int64
	for _, s := range syllabuses {
		syllabusIDs = append(syllabusIDs, s.ID)
	}
	revisionList, err := GetRevisionList(model.ModuleSyllabus, syllabusIDs)
	if err != nil {
		return res, err
	}

	for _, s := range syllabuses {
		revDetail := model.RespSyllabusWithLatestRevision{Syllabus: s}
		for _, r := range revisionList {
			if r.OriginalID == s.ID {
				revDetail.Rev = &r
			}
		}
		res.List = append(res.List, revDetail)
	}
	return res, nil
}
func (s *SyllabusService) GetTextbooksBySyllabusID(id int64) ([]model.Textbook, error) {
	if id == 0 {
		return nil, errors.New("无效的大纲ID")
	}
	var syllabus model.Syllabus
	if err := db.DB.First(&syllabus, id).Error; err != nil {
		return nil, errors.New("未找到对应大纲")
	}

	// 查询 SyllabusExt 中 ext_key 为 textbook_id 的记录
	var exts []model.SyllabusExt
	if err := db.DB.Where("syllabus_id = ? AND ext_key = ?", id, "textbook_id").Find(&exts).Error; err != nil {
		return nil, errors.New("查询教材失败: " + err.Error())
	}

	// 提取所有教材 ID
	var textbookIDs []int64
	for _, ext := range exts {
		textbookIDs = append(textbookIDs, ext.ExtValue)
	}

	// 查询实际的教材信息（假设教材模型为 model.Textbook）
	var textbooks []model.Textbook
	if len(textbookIDs) > 0 {
		if err := db.DB.Where("id IN (?)", textbookIDs).Find(&textbooks).Error; err != nil {
			return nil, errors.New("获取教材列表失败: " + err.Error())
		}
	}

	return textbooks, nil
}

// GetSyllabusDetail 获取大纲详情，包含所有相关信息
func (s *SyllabusService) GetSyllabusDetail(id int64) (*model.RespSyllabusDetail, error) {
	if id == 0 {
		return nil, errors.New("无效的大纲ID")
	}

	// 查询大纲基本信息
	var syllabus model.Syllabus
	if err := db.DB.First(&syllabus, id).Error; err != nil {
		return nil, errors.New("未找到对应大纲: " + err.Error())
	}

	// 查询扩展字段（教材关联）
	var syllabusExts []model.SyllabusExt
	var textbooks []model.Textbook
	var resources []model.Resources
	db.DB.Where("syllabus_id = ?", id).Find(&syllabusExts)
	if len(syllabusExts) > 0 {
		// 提取教材 ID 列表
		var textbookIDs []int64
		var resourceIDs []int64
		for _, ext := range syllabusExts {
			if ext.ExtKey == "textbook_id" {
				textbookIDs = append(textbookIDs, ext.ExtValue)
			}
			if ext.ExtKey == "resource_id" {
				resourceIDs = append(resourceIDs, ext.ExtValue)
			}
		}
		// 查询教材详细信息
		if len(textbookIDs) > 0 {
			if err := db.DB.Where("id IN (?)", textbookIDs).Find(&textbooks).Error; err != nil {
				return nil, errors.New("获取教材列表失败: " + err.Error())
			}
		}
		if len(resourceIDs) > 0 {
			if err := db.DB.Where("id IN (?)", resourceIDs).Find(&resources).Error; err != nil {
				return nil, errors.New("获取资源列表失败: " + err.Error())
			}
		}
	}

	// 查询修订记录
	// var from_revisions model.SyllabusRevision
	// db.DB.Where("new_syllabus_id = ?", id).First(&from_revisions)
	// var to_revisions model.SyllabusRevision
	// db.DB.Where("original_syllabus_id = ?", id).First(&to_revisions)

	// 构造返回值
	resp := &model.RespSyllabusDetail{
		Syllabus:  syllabus,
		Textbooks: textbooks,
		Resources: resources,
		// FromRevision: from_revisions,
		// ToRevision:   to_revisions,
	}

	return resp, nil
}
