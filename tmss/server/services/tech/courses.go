package tech

import (
	"encoding/json"
	"errors"
	"fmt"
	"log/slog"
	"strconv"
	"strings"
	"time"

	"tms/model"
	"tms/pkg/db"
	"tms/services/user"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// ReqCoursesSearch 搜索请求参数

type CoursesService struct{}

func NewCoursesService() *CoursesService {
	return &CoursesService{}
}

// CreateCourseDraft 创建课程草稿
func (s *CoursesService) CreateCourseDraft(req model.ReqCoursesUpdate, userId int64) error {
	err := checkCourseReq(req, userId)
	if err != nil {
		return err
	}
	req.Course.UserID = userId
	req.Course.Status = model.CoursesStatusDraft
	req.Course.CreatedAt = time.Now().Unix()

	if err := db.DB.Create(&req.Course).Error; err != nil {
		return errors.New("创建失败: " + err.Error())
	}

	// 插入扩展字段（多个老师ID）
	if len(req.TeacherIDs) > 0 {
		ext := model.CourseExt{
			CourseID: req.Course.ID,
			ExtKey:   "teacher_ids",
			ExtValue: strings.Trim(strings.Join(strings.Fields(fmt.Sprint(req.TeacherIDs)), ","), "[]"),
		}
		if err := db.DB.Create(&ext).Error; err != nil {
			return errors.New("插入扩展字段失败: " + err.Error())
		}
	}

	return nil
}
func CourseCreator(tx *gorm.DB, content string, userId int64) (int64, error) {
	var changeReq struct {
		FormData model.ReqCoursesUpdate `json:"formData"`
	}
	//var req model.ReqSyllabusCreate
	if err := json.Unmarshal([]byte(content), &changeReq); err != nil {
		return 0, errors.New("解析教材内容失败: " + err.Error())
	}
	req := changeReq.FormData
	err := checkCourseReq(req, userId)
	if err != nil {
		return 0, err
	}
	req.Course.UserID = userId
	req.Course.Status = model.CoursesStatusDraft
	req.Course.CreatedAt = time.Now().Unix()
	req.Course.ID = 0
	if err := db.DB.Create(&req.Course).Error; err != nil {
		return 0, errors.New("创建失败: " + err.Error())
	}

	// 插入扩展字段（多个老师ID）
	if len(req.TeacherIDs) > 0 {
		ext := model.CourseExt{
			CourseID: req.Course.ID,
			ExtKey:   "teacher_ids",
			ExtValue: strings.Trim(strings.Join(strings.Fields(fmt.Sprint(req.TeacherIDs)), ","), "[]"),
		}
		if err := db.DB.Create(&ext).Error; err != nil {
			return 0, errors.New("插入扩展字段失败: " + err.Error())
		}
	}
	return req.Course.ID, nil
}
func checkCourseReq(req model.ReqCoursesUpdate, userId int64) error {
	if userId == 0 {
		return errors.New("无效的用户ID")
	}
	// 校验 plan_id 和 major_id 是否存在
	if req.Course.PlanID == 0 || req.Course.MajorID == 0 {
		return errors.New("plan_id 和 major_id 为必填字段")
	}

	planExists, err := CheckPlanExists(req.Course.PlanID)
	if err != nil {
		return errors.New("数据库错误: " + err.Error())
	}
	if !planExists {
		return errors.New("关联的教学计划ID不存在")
	}

	majorExists, err := CheckMajorExists(req.Course.MajorID)
	if err != nil {
		return errors.New("数据库错误: " + err.Error())
	}
	if !majorExists {
		return errors.New("关联的专业ID不存在")
	}
	if userId == 0 {
		return errors.New("无效的用户ID")
	}
	if req.Course.ChapterID == 0 {
		return errors.New("关联的章节ID不存在")
	}
	var existsSameCodeCourse model.Courses
	err = db.DB.Model(&existsSameCodeCourse).Where("course_code = ? ", req.Course.CourseCode).First(&existsSameCodeCourse).Error
	if err == nil {
		return errors.New("课程编号已存在")
	}
	return nil
}
func checkCourseExists(courseID int64) (bool, error) {
	var course model.Courses
	if err := db.DB.First(&course, courseID).Error; err != nil {
		return false, errors.New("未找到对应课程")
	}
	return true, nil
}

// UpdateCourseDraft 更新课程草稿
func (s *CoursesService) UpdateCourseDraft(req model.ReqCoursesUpdate) error {

	if req.Course.ID <= 0 {
		return errors.New("无效的课程ID")
	}

	// 校验 plan_id 和 major_id 是否存在
	if req.Course.PlanID == 0 || req.Course.MajorID == 0 {
		return errors.New("plan_id 和 major_id 为必填字段")
	}

	planExists, err := CheckPlanExists(req.Course.PlanID)
	if err != nil {
		return errors.New("数据库错误: " + err.Error())
	}
	if !planExists {
		return errors.New("关联的教学计划ID不存在")
	}

	majorExists, err := CheckMajorExists(req.Course.MajorID)
	if err != nil {
		return errors.New("数据库错误: " + err.Error())
	}
	if !majorExists {
		return errors.New("关联的专业ID不存在")
	}
	if req.Course.ChapterID == 0 {
		return errors.New("关联的章节ID不存在")
	}

	var course model.Courses
	if err := db.DB.First(&course, req.Course.ID).Error; err != nil {
		return errors.New("未找到对应课程")
	}
	var existsSameCodeCourse model.Courses
	err = db.DB.Model(&existsSameCodeCourse).Where("course_code = ? AND id != ?", req.Course.CourseCode, req.Course.ID).First(&existsSameCodeCourse).Error
	if err == nil {
		return errors.New("课程编号已存在")
	}
	if course.Status != model.CoursesStatusDraft && course.Status != model.CoursesStatusRejected {
		return errors.New("只能编辑草稿")
	}

	now := time.Now().Unix()
	req.Course.UpdatedAt = now

	updates := map[string]interface{}{
		"plan_id":     req.Course.PlanID,
		"major_id":    req.Course.MajorID,
		"course_code": req.Course.CourseCode,
		"name":        req.Course.Name,
		"description": req.Course.Description,
		"total_hours": req.Course.TotalHours,
		"updated_at":  now,
	}

	if err := db.DB.Model(&course).
		Where("id = ?", req.Course.ID).
		Updates(updates).Error; err != nil {
		return errors.New("更新失败: " + err.Error())
	}

	// 更新扩展字段（多个老师ID）
	if len(req.TeacherIDs) > 0 {
		ext := model.CourseExt{
			CourseID: req.Course.ID,
			ExtKey:   "teacher_ids",
			ExtValue: strings.Trim(strings.Join(strings.Fields(fmt.Sprint(req.TeacherIDs)), ","), "[]"),
		}
		if err := db.DB.Where("course_id = ? AND ext_key = ?", req.Course.ID, "teacher_ids").
			Assign(ext).FirstOrCreate(&ext).Error; err != nil {
			return errors.New("更新扩展字段失败: " + err.Error())
		}
	}

	return nil
}

// GetCourseDetailByID 根据课程ID获取完整详情
func (s *CoursesService) GetCourseDetailByID(courseID int64) (gin.H, error) {
	if courseID == 0 {
		return nil, errors.New("无效的课程ID")
	}

	var course model.Courses
	if err := db.DB.First(&course, courseID).Error; err != nil {
		return nil, errors.New("未找到对应课程")
	}

	// 获取扩展字段
	var exts []string
	err := db.DB.Model(&model.CourseExt{}).Where("course_id = ? AND ext_key = 'teacher_ids'", courseID).Pluck("ext_value", &exts).Error
	if err != nil {
		return nil, errors.New("获取扩展字段: " + err.Error())
	}

	var teacher_ids []int64
	if len(exts) > 0 {
		ids := strings.Split(exts[0], ",")
		for _, id := range ids {
			idInt, err := strconv.ParseInt(id, 10, 64)
			if err != nil {
				return nil, errors.New("转换老师ID失败: " + err.Error())
			}
			teacher_ids = append(teacher_ids, idInt)
		}
	}
	var teachers []model.Users
	if err := db.DB.Model(&model.Users{}).Where("id in ?", teacher_ids).Find(&teachers).Error; err != nil {
		return nil, errors.New("获取老师信息失败: " + err.Error())
	}
	var teacherList []gin.H
	for _, teacher := range teachers {
		teacherList = append(teacherList, gin.H{
			"id":       teacher.ID,
			"username": teacher.Username,
		})
	}
	// 获取教学计划名称
	var planName string
	db.DB.Model(&model.TeachingPlan{}).Where("id = ?", course.PlanID).Pluck("name", &planName)

	// 获取专业名称
	var majorName string
	db.DB.Model(&model.Majors{}).Where("id = ?", course.MajorID).Pluck("name", &majorName)

	var chapterName string
	db.DB.Model(&model.Chapter{}).Where("id = ?", course.ChapterID).Pluck("name", &chapterName)

	// 构造响应数据
	detail := gin.H{
		"id":           course.ID,
		"name":         course.Name,
		"course_code":  course.CourseCode,
		"description":  course.Description,
		"total_hours":  course.TotalHours,
		"plan_id":      course.PlanID,
		"plan_name":    planName,
		"major_id":     course.MajorID,
		"major_name":   majorName,
		"chapter_id":   course.ChapterID,
		"chapter_name": chapterName,
		"user_id":      course.UserID,
		"teacher_list": teacherList,
		"status":       course.Status,
		"created_at":   course.CreatedAt,
		"updated_at":   course.UpdatedAt,
		"submit_at":    course.SubmitAt,
		"published_at": course.PublishedAt,
		"version":      course.Version,
	}

	return detail, nil
}

// DeleteCourse 删除课程
func (s *CoursesService) DeleteCourse(id int64) error {
	if id == 0 {
		return errors.New("无效的课程ID")
	}

	var course model.Courses
	if err := db.DB.First(&course, id).Error; err != nil {
		return errors.New("未找到对应课程")
	}

	if course.Status != model.CoursesStatusDraft && course.Status != model.CoursesStatusRejected {
		return errors.New("只能删除草稿")
	}

	if err := db.DB.Delete(&course).Error; err != nil {
		return errors.New("删除失败: " + err.Error())
	}

	// 删除扩展字段
	db.DB.Where("course_id = ?", id).Delete(&model.CourseExt{})

	return nil
}

// GetCoursesList 获取课程列表
func (s *CoursesService) GetCoursesList(c *gin.Context, req model.ReqCoursesSearch) (model.RespCoursesList, error) {
	var res model.RespCoursesList
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 || req.PageSize > 100 {
		req.PageSize = 10
	}

	dbQuery := db.DB.Model(&model.Courses{})

	if req.Name != "" {
		dbQuery = dbQuery.Where("name ILIKE ?", "%"+req.Name+"%")
	}
	if req.Status != "" {
		dbQuery = dbQuery.Where("status = ?", req.Status)
	}
	dbQuery, has := user.ApplyDataRules(c, dbQuery, model.ModuleSyllabus)
	if !has && req.UserID > 0 {
		dbQuery = dbQuery.Where("user_id = ?", req.UserID)
	}
	// if req.UserID > 0 {
	// 	dbQuery = dbQuery.Where("user_id = ?", req.UserID)
	// }
	if req.PlanID > 0 {
		dbQuery = dbQuery.Where("plan_id = ?", req.PlanID)
	}

	if err := dbQuery.Count(&res.Total).Error; err != nil {
		return res, errors.New("查询总数失败")
	}

	var list []model.Courses
	offset := (req.Page - 1) * req.PageSize
	if err := dbQuery.Offset(offset).Limit(req.PageSize).Order("created_at DESC").Find(&list).Error; err != nil {
		return res, errors.New("查询数据失败: " + err.Error())
	}
	var courseIds []int64
	for _, course := range list {
		courseIds = append(courseIds, course.ID)
	}
	revisionList, err := GetRevisionList(model.ModuleCourses, courseIds)
	if err != nil {
		return res, err
	}
	for _, c := range list {
		revDetail := model.RespCoursesWithLatestRevision{Course: c}
		for _, r := range revisionList {
			if r.OriginalID == c.ID {
				revDetail.Rev = &r
			}
		}
		res.List = append(res.List, revDetail)
	}
	return res, nil
}

// 检查 plan_id 是否存在
func CheckPlanExists(planID int64) (bool, error) {
	var count int64
	err := db.DB.Model(&model.TeachingPlan{}).Where("id = ?", planID).Count(&count).Error
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

// 检查 major_id 是否存在
func CheckMajorExists(majorID int64) (bool, error) {
	var count int64
	err := db.DB.Model(&model.Majors{}).Where("id = ?", majorID).Count(&count).Error
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

// ReqGetCoursesByClassAndMajor 请求参数

// GetPublishedCoursesByClassAndMajor 获取当前时间段内通过班级和专业关联的课程列表
func (s *CoursesService) GetPublishedCoursesByUserID(userID int64, page, pageSize int) (gin.H, error) {
	now := time.Now().Unix()
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 || pageSize > 100 {
		pageSize = 10
	}

	// Step 1: 获取该班级关联的所有教学计划ID
	var studentMap model.StudentMap
	err := db.DB.Model(&model.StudentMap{}).Where("user_id = ?", userID).First(&studentMap).Error
	if err != nil {
		return nil, errors.New("查询学生映射失败: " + err.Error())
	}
	planIds := []int64{}
	err = db.DB.Model(&model.TeachingPlanExt{}).Select("plan_id").Where("ext_key = ? AND ext_value IN (?)", "class_id", studentMap.ClassID).Pluck("plan_id", &planIds).Error
	if err != nil {
		return nil, errors.New("获取班级ID失败: " + err.Error())
	}
	//log.Printf("plan_ids: %v", planIds)
	// Step 2: 获取这些教学计划中处于有效时间范围内的 PlanID
	var validPlanIDs []int64
	err = db.DB.Model(&model.TeachingPlan{}).
		Where("id IN (?) AND ? BETWEEN start_at AND end_at", planIds, now).
		Pluck("id", &validPlanIDs).Error
	if err != nil {
		return nil, errors.New("查询有效教学计划失败: " + err.Error())
	}
	//log.Printf("valid_plan_ids: %v", validPlanIDs)
	if len(validPlanIDs) == 0 {
		return gin.H{"list": []model.Courses{}, "total": 0}, nil
	}

	// Step 3: 获取这些教学计划关联的专业是否匹配，并且课程状态为已发布
	dbQuery := db.DB.Model(&model.Courses{}).
		Joins("JOIN teaching_plan ON courses.plan_id = teaching_plan.id").
		Where("courses.status = ?", model.CoursesStatusPublished).
		Where("teaching_plan.id IN (?)", validPlanIDs).
		Where("courses.major_id = ?", studentMap.MajorID)

	var total int64
	if err := dbQuery.Count(&total).Error; err != nil {
		return nil, errors.New("统计总数失败: " + err.Error())
	}

	var list []model.Courses
	offset := (page - 1) * pageSize
	if err := dbQuery.Offset(offset).Limit(pageSize).Find(&list).Error; err != nil {
		return nil, errors.New("查询课程数据失败: " + err.Error())
	}

	return gin.H{
		"list":  list,
		"total": total,
	}, nil
}

func (cs *CoursesService) GetAttachedAttributes(courseID int64) ([]model.Courseware, []model.Resources, []model.Questions, error) {
	var course model.Courses
	if err := db.DB.First(&course, courseID).Error; err != nil {
		return nil, nil, nil, errors.New("未找到对应课程")
	}

	// 收集所有需要查询的章节ID
	finalChapterIDMap := make(map[int64]struct{})
	queue := make([]int64, 0)

	// 初始化队列，将所有初始章节ID加入队列
	if _, ok := finalChapterIDMap[course.ChapterID]; !ok {
		finalChapterIDMap[course.ChapterID] = struct{}{}
		queue = append(queue, course.ChapterID)
	}

	// TODO:bfs适合于大数据量的情况
	// 若不想查询链接过多，可以将全部的id和parent_id 加载进内存
	// 然后进行 通过id，查询出需要构建的id 和 parent_id
	// 一次性查询出需要的数据，进行构建
	// BFS 遍历：只向下查找所有子章节
	for len(queue) > 0 {
		currentID := queue[0]
		queue = queue[1:]

		// 查找子节点
		var children []model.Chapter
		if err := db.DB.Where("parent_id = ?", currentID).Find(&children).Error; err != nil {
			slog.Error("获取章节子节点失败", "error", err)
			continue
		}
		for _, child := range children {
			if _, ok := finalChapterIDMap[child.ID]; !ok {
				finalChapterIDMap[child.ID] = struct{}{}
				queue = append(queue, child.ID)
			}
		}
	}

	// 将 map 的 key 转换为 slice
	var idsToQuery []int64
	for id := range finalChapterIDMap {
		idsToQuery = append(idsToQuery, id)
	}

	// 课件是挂载在章节上的
	var coursewares []model.Courseware
	if err := db.DB.Where("chapter_id IN (?)", idsToQuery).Find(&coursewares).Error; err != nil {
		return nil, nil, nil, errors.New("查询课件失败: " + err.Error())
	}

	// 资源是挂载在章节上的
	// 需要先找到课程下的所有子章节
	var resources []model.Resources
	if err := db.DB.Where("chapter_id IN (?)", idsToQuery).Find(&resources).Error; err != nil {
		return nil, nil, nil, errors.New("查询资料失败: " + err.Error())
	}

	// 试题是挂载在章节上的
	// 需要先找到课程下的所有子章节
	var questionIDS []int64
	// 查询该章节下所有关联的试题ID（来自 questions_ext 表）
	err := db.DB.Model(&model.QuestionsExt{}).
		Where("ext_key = ? AND ext_value IN (?)", "chapter", idsToQuery).
		Distinct("question_id").
		Pluck("question_id", &questionIDS).Error
	if err != nil {
		return nil, nil, nil, errors.New("查询试题ID失败: " + err.Error())
	}
	var questions []model.Questions
	if err := db.DB.Where("id IN (?)", questionIDS).Find(&questions).Error; err != nil {
		return nil, nil, nil, errors.New("查询试题失败: " + err.Error())
	}

	return coursewares, resources, questions, nil
}
