package tech

import (
	"encoding/json"
	"errors"
	"log"
	"strconv"
	"strings"
	"time"
	"tms/model"
	"tms/pkg/db"
	"tms/services/user"
	"tms/utils"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// ReqTeachingPlanSearch 搜索请求参数

type TeachingPlanService struct{}

// RespTeachingPlanList 分页返回结果

func NewTeachingPlanService() *TeachingPlanService { return &TeachingPlanService{} }

func (api *TeachingPlanService) CreateTeachingPlan(req model.ReqTeachingPlanUpdate, userId int64) error {
	if userId == 0 {
		return errors.New("无效的用户ID")
	}

	err := checkTeachingPlanReq(&req, userId)
	if err != nil {
		return err
	}
	if err := db.DB.Create(&req.TeachingPlan).Error; err != nil {
		return errors.New("创建失败: " + err.Error())
	}

	var exts []model.TeachingPlanExt
	for _, classID := range req.ClassIDs {
		exts = append(exts, model.TeachingPlanExt{
			PlanID:   req.TeachingPlan.ID,
			ExtKey:   "class_id",
			ExtValue: classID,
		})
	}
	db.DB.Create(&exts)

	return nil
}
func TeachingPlanCreator(tx *gorm.DB, content string, userId int64) (int64, error) {
	var changeReq struct {
		FormData model.ReqTeachingPlanUpdate `json:"formData"`
	}
	//var req model.ReqSyllabusCreate
	if err := json.Unmarshal([]byte(content), &changeReq); err != nil {
		return 0, errors.New("解析教材内容失败: " + err.Error())
	}
	req := changeReq.FormData
	err := checkTeachingPlanReq(&req, userId)
	if err != nil {
		return 0, err
	}
	req.TeachingPlan.ID = 0
	if err := db.DB.Create(&req.TeachingPlan).Error; err != nil {
		return 0, errors.New("创建失败: " + err.Error())
	}

	var exts []model.TeachingPlanExt
	for _, classID := range req.ClassIDs {
		exts = append(exts, model.TeachingPlanExt{
			PlanID:   req.TeachingPlan.ID,
			ExtKey:   "class_id",
			ExtValue: classID,
		})
	}
	db.DB.Create(&exts)
	return req.TeachingPlan.ID, nil
}
func checkTeachingPlanReq(req *model.ReqTeachingPlanUpdate, userId int64) error {
	req.TeachingPlan.UserID = userId
	req.TeachingPlan.Status = model.TeachingPlanStatusDraft
	req.TeachingPlan.CreatedAt = time.Now().Unix()

	// 时间字符串转 int64
	if req.StartAtStr != "" {
		startAt, err := time.Parse("2006-01-02", req.StartAtStr)
		if err != nil {
			return errors.New("开始时间格式错误，应为 YYYY-MM-DD")
		}
		req.TeachingPlan.StartAt = startAt.Unix()
	}

	if req.EndAtStr != "" {
		endAt, err := time.Parse("2006-01-02", req.EndAtStr)
		if err != nil {
			return errors.New("结束时间格式错误，应为 YYYY-MM-DD")
		}
		req.TeachingPlan.EndAt = endAt.Unix()
	}
	if req.TeachingPlan.StartAt == 0 || req.TeachingPlan.EndAt == 0 || req.TeachingPlan.StartAt >= req.TeachingPlan.EndAt {
		return errors.New("请选择正确的时间范围")
	}
	// 检查 syllabus 是否存在且已发布
	isValid, err := CheckSyllabusPublished(req.TeachingPlan.SyllabusID)
	if err != nil {
		return errors.New("数据库错误: " + err.Error())
	}
	if !isValid {
		return errors.New("关联的大纲不存在或未发布")
	}
	// 检查所有 class_id 是否存在
	if len(req.ClassIDs) > 0 {
		exist, err := CheckClassIDsExist(req.ClassIDs)
		if err != nil {
			return errors.New("数据库错误: " + err.Error())
		}
		if !exist {
			return errors.New("包含无效的班级ID，请确认所有班级都有效")
		}
	}
	// 检查前序计划是否存在
	if req.TeachingPlan.PreviousID > 0 {
		exist, err := checkPreviousPlanExists(req.TeachingPlan.PreviousID, false) // 要求前序计划是已发布的
		if err != nil {
			return errors.New("数据库错误: " + err.Error())
		}
		if !exist {
			return errors.New("前序教学计划不存在或未发布")
		}
	}
	return nil
}
func (api *TeachingPlanService) UpdateTeachingPlan(req model.ReqTeachingPlanUpdate) error {
	if req.TeachingPlan.ID <= 0 {
		return errors.New("无效的教学计划ID")
	}
	if req.TeachingPlan.ID == req.TeachingPlan.PreviousID {
		return errors.New("前序计划不能是自己")
	}
	var plan model.TeachingPlan
	if err := db.DB.First(&plan, req.TeachingPlan.ID).Error; err != nil {
		return errors.New("未找到对应计划")
	}

	if plan.Status != model.TeachingPlanStatusDraft && plan.Status != model.TeachingPlanStatusRejected {
		return errors.New("只能编辑草稿")
	}

	now := time.Now().Unix()
	req.TeachingPlan.UpdatedAt = now

	// 时间字符串转 int64
	if req.StartAtStr != "" {
		startAt, err := time.Parse("2006-01-02", req.StartAtStr)
		if err != nil {
			return errors.New("开始时间格式错误，应为 YYYY-MM-DD")
		}
		req.TeachingPlan.StartAt = startAt.Unix()
	}

	if req.EndAtStr != "" {
		endAt, err := time.Parse("2006-01-02", req.EndAtStr)
		if err != nil {
			return errors.New("结束时间格式错误，应为 YYYY-MM-DD")
		}
		req.TeachingPlan.EndAt = endAt.Unix()
	}
	if req.TeachingPlan.StartAt == 0 || req.TeachingPlan.EndAt == 0 || req.TeachingPlan.StartAt >= req.TeachingPlan.EndAt {
		return errors.New("时间范围错误")
	}
	// 检查 syllabus 是否存在且已发布
	isValid, err := CheckSyllabusPublished(req.TeachingPlan.SyllabusID)
	if err != nil {
		return errors.New("数据库错误: " + err.Error())
	}
	if !isValid {
		return errors.New("关联的大纲不存在或未发布")
	}
	// 检查所有 class_id 是否存在
	if len(req.ClassIDs) > 0 {
		exist, err := CheckClassIDsExist(req.ClassIDs)
		if err != nil {
			return errors.New("数据库错误: " + err.Error())
		}
		if !exist {
			return errors.New("包含无效的班级ID，请确认所有班级都有效")
		}
	}
	if req.TeachingPlan.PreviousID > 0 {
		exist, err := checkPreviousPlanExists(req.TeachingPlan.PreviousID, false) // 要求前序计划是已发布的
		if err != nil {
			return errors.New("数据库错误: " + err.Error())
		}
		if !exist {
			return errors.New("前序教学计划不存在或未发布")
		}
	}
	// 构建需要更新的字段
	updates := map[string]interface{}{
		"name":                   req.TeachingPlan.Name,
		"syllabus_id":            req.TeachingPlan.SyllabusID,
		"term":                   req.TeachingPlan.Term,
		"classroom_requirements": req.TeachingPlan.ClassroomRequirements,
		"updated_at":             now,
		"start_at":               req.TeachingPlan.StartAt,
		"end_at":                 req.TeachingPlan.EndAt,
	}

	if err := db.DB.Model(&plan).
		Where("id = ?", req.TeachingPlan.ID).
		Updates(updates).Error; err != nil {
		return errors.New("更新失败: " + err.Error())
	}

	// 删除旧的 class_id 扩展字段
	if err := db.DB.Where("plan_id = ? AND ext_key = ?", plan.ID, "class_id").
		Delete(&model.TeachingPlanExt{}).Error; err != nil {
		return errors.New("删除旧扩展字段失败: " + err.Error())
	}

	// 插入新的 class_id 扩展字段
	var exts []model.TeachingPlanExt
	for _, classID := range req.ClassIDs {
		exts = append(exts, model.TeachingPlanExt{
			PlanID:   plan.ID,
			ExtKey:   "class_id",
			ExtValue: classID,
		})
	}

	if len(exts) > 0 {
		if err := db.DB.Create(&exts).Error; err != nil {
			return errors.New("插入新扩展字段失败: " + err.Error())
		}
	}

	return nil
}

// CheckSyllabusPublished 检查 syllabus 是否存在且已发布
func CheckSyllabusPublished(syllabusID int64) (bool, error) {
	var syllabus model.Syllabus
	result := db.DB.Where("id = ? AND status = ?", syllabusID, model.SyllabusStatusPublished).
		First(&syllabus)
	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			return false, nil // 找不到记录，非错误
		}
		return false, result.Error // 数据库错误
	}
	return true, nil
}

// CheckClassIDsExist 检查多个 class_id 是否都存在
func CheckClassIDsExist(classIDs []int64) (bool, error) {
	if len(classIDs) == 0 {
		return true, nil
	}

	var count int64
	err := db.DB.Model(&model.Class{}).
		Where("id IN ?", classIDs).
		Count(&count).Error

	if err != nil {
		return false, err
	}

	return count == int64(len(classIDs)), nil
}

// checkPreviousPlanExists 检查前序计划是否存在（且可选是否为已发布状态）
func checkPreviousPlanExists(planID int64, checkPublished bool) (bool, error) {
	if planID <= 0 {
		return true, nil // 不需要检查
	}

	var count int64
	dbQuery := db.DB.Model(&model.TeachingPlan{}).Where("id = ?", planID)

	if checkPublished {
		dbQuery = dbQuery.Where("status = ?", model.TeachingPlanStatusPublished)
	}

	err := dbQuery.Count(&count).Error
	if err != nil {
		return false, err
	}

	return count == 1, nil
}
func (api *TeachingPlanService) DeleteTeachingPlan(id int64) error {
	if id == 0 {
		return errors.New("无效的教学计划ID")
	}

	var plan model.TeachingPlan
	if err := db.DB.First(&plan, id).Error; err != nil {
		return errors.New("未找到对应计划")
	}

	if plan.Status != model.TeachingPlanStatusDraft && plan.Status != model.TeachingPlanStatusRejected {
		return errors.New("只能删除草稿和驳回的计划")
	}

	if err := db.DB.Delete(&plan).Error; err != nil {
		return errors.New("删除失败: " + err.Error())
	}

	// 同时删除扩展字段
	db.DB.Where("plan_id = ?", id).Delete(&model.TeachingPlanExt{})

	return nil
}
func (api *TeachingPlanService) GetTeachingPlanList(c *gin.Context, req model.ReqTeachingPlanSearch) (model.RespTeachPlanList, error) {
	var res model.RespTeachPlanList
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 || req.PageSize > 100 {
		req.PageSize = 10
	}

	dbQuery := db.DB.Model(&model.TeachingPlan{})

	if req.Name != "" {
		dbQuery = dbQuery.Where("name ILIKE ?", "%"+req.Name+"%")
	}
	if req.Status != "" {
		dbQuery = dbQuery.Where("status = ?", req.Status)
	}
	dbQuery, has := user.ApplyDataRules(c, dbQuery, model.ModuleTeachingPlan)
	if !has && req.UserID > 0 {
		dbQuery = dbQuery.Where("user_id = ?", req.UserID)
	}
	// if req.UserID > 0 {
	// 	dbQuery = dbQuery.Where("user_id = ?", req.UserID)
	// }
	if req.SyllabusID > 0 {
		dbQuery = dbQuery.Where("syllabus_id = ?", req.SyllabusID)
	}
	if req.SyllabusName != "" {
		syllabusIds := []int64{}
		err := db.DB.Table("syllabus").Select("id").Where("name ILIKE ?", "%"+req.SyllabusName+"%").Pluck("id", &syllabusIds).Error
		if err != nil {
			return res, errors.New("获取大纲名称失败: " + err.Error())
		}
		if len(syllabusIds) > 0 {
			dbQuery = dbQuery.Where("syllabus_id IN (?)", syllabusIds)
		}
	}
	if req.ClassName != "" {
		classIds := []int64{}
		err := db.DB.Model(&model.Class{}).Where("name ILIKE ?", "%"+req.ClassName+"%").Pluck("id", &classIds).Error
		if err != nil {
			return res, errors.New("获取班级名称失败: " + err.Error())
		}
		if len(classIds) > 0 {
			classIDsStr := []string{}
			for _, classId := range classIds {
				classIDsStr = append(classIDsStr, strconv.FormatInt(classId, 10))
			}
			log.Printf("class_ids: %s", strings.Join(classIDsStr, ","))
			planIds := []int64{}
			err = db.DB.Model(&model.TeachingPlanExt{}).Select("plan_id").Where("ext_key = ? AND ext_value IN (?)", "class_id", classIDsStr).Pluck("plan_id", &planIds).Error
			if err != nil {
				return res, errors.New("获取班级ID失败: " + err.Error())
			}
			log.Printf("plan_ids: %v", planIds)
			dbQuery = dbQuery.Where("id IN (?)", planIds)
		}

	}

	var total int64
	if err := dbQuery.Count(&total).Error; err != nil {
		return res, errors.New("查询总数失败")
	}

	var list []model.TeachingPlan
	offset := (req.Page - 1) * req.PageSize
	if err := dbQuery.Offset(offset).Limit(req.PageSize).Order("created_at DESC").Find(&list).Error; err != nil {
		return res, errors.New("查询数据失败: " + err.Error())
	}

	// Step 1: 获取 SyllabusID 列表
	var syllabusIDs []int64
	for _, plan := range list {
		syllabusIDs = append(syllabusIDs, plan.SyllabusID)
	}

	// Step 2: 查询 Syllabus 信息
	var syllabuses []model.Syllabus
	err := db.DB.Model(&model.Syllabus{}).
		Where("id IN (?)", syllabusIDs).
		Find(&syllabuses).Error
	if err != nil {
		return res, errors.New("查询大纲名称失败: " + err.Error())
	}

	// Step 3: 构建 SyllabusID -> Name 映射
	syllabuseMap := make(map[int64]string)
	for _, syllabus := range syllabuses {
		syllabuseMap[syllabus.ID] = syllabus.Name
	}

	var planIds []int64
	for _, plan := range list {
		planIds = append(planIds, plan.ID)
	}
	var planExts []model.TeachingPlanExt
	err = db.DB.Model(&model.TeachingPlanExt{}).Where("plan_id IN (?)", planIds).Find(&planExts).Error
	if err != nil {
		return res, errors.New("查询扩展字段失败: " + err.Error())
	}
	var classIDs []int64
	planClassMapping := make(map[int64][]int64)
	for _, planExt := range planExts {
		if planExt.ExtKey == "class_id" {
			if _, ok := planClassMapping[planExt.PlanID]; !ok {
				planClassMapping[planExt.PlanID] = make([]int64, 0)
			}
			// classId, err := strconv.ParseInt(planExt.ExtValue, 10, 64)
			// if err != nil {
			// 	return res, errors.New("查询扩展字段失败: " + err.Error())
			// }
			planClassMapping[planExt.PlanID] = append(planClassMapping[planExt.PlanID], planExt.ExtValue)
			classIDs = append(classIDs, planExt.ExtValue)
		}

	}
	// Step 2: 去重 classIDs
	classIDs = utils.UniqueInt64Slice(classIDs)
	// Step 3: 查询所有班级信息
	var classes []model.Class
	if len(classIDs) > 0 {
		if err := db.DB.Where("id IN (?)", classIDs).Find(&classes).Error; err != nil {
			return res, errors.New("查询班级信息失败: " + err.Error())
		}
	}
	// Step 4: 构建 classID -> Class 映射
	classMap := make(map[int64]model.Class)
	for _, class := range classes {
		classMap[class.ID] = class
	}

	//关联planClassMapping数据
	revisionList, err := GetRevisionList(model.ModuleTeachingPlan, planIds)
	if err != nil {
		return res, err
	}
	var result []model.RespPlanWithLatestRevision
	for _, plan := range list {
		var classList []model.Class
		ids, ok := planClassMapping[plan.ID]
		if ok {
			// 填充班级信息
			for _, id := range ids {
				if class, exists := classMap[id]; exists {
					classList = append(classList, class)
				}
			}
		}
		syllabusName := ""
		if name, ok := syllabuseMap[plan.SyllabusID]; ok {
			syllabusName = name
		}
		plan := model.PlanWithFormattedTime{
			TeachingPlan: plan,
			SyllabusName: syllabusName,
			Classes:      classList,
			StartAtStr:   utils.FormatUnixTime(plan.StartAt),
			EndAtStr:     utils.FormatUnixTime(plan.EndAt),
		}
		revDetail := model.RespPlanWithLatestRevision{
			Plan: plan,
		}
		for _, r := range revisionList {
			if r.OriginalID == plan.ID {
				revDetail.Rev = &r
			}
		}
		result = append(result, revDetail)
	}
	return model.RespTeachPlanList{
		List:  result,
		Total: total,
	}, nil
	// return model.RespTeachingPlanList{
	// 	List:  result,
	// 	Total: total,
	// }, nil
}

// GetTeachingPlanDetail 获取教学计划详情
func (api *TeachingPlanService) GetTeachingPlanDetail(id int64) (model.RespTeachingPlanDetail, error) {
	var res model.RespTeachingPlanDetail
	if id == 0 {
		return res, errors.New("无效的教学计划ID")
	}

	// 获取教学计划基本信息
	var plan model.TeachingPlan
	if err := db.DB.First(&plan, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return res, errors.New("未找到教学计划")
		}
		return res, errors.New("查询失败: " + err.Error())
	}
	res.TeachingPlan = plan
	res.StartAtStr = utils.FormatUnixTime(plan.StartAt)
	res.EndAtStr = utils.FormatUnixTime(plan.EndAt)

	// 获取关联的班级ID
	var classExts []model.TeachingPlanExt
	if err := db.DB.Where("plan_id = ? AND ext_key = ?", id, "class_id").
		Find(&classExts).Error; err != nil {
		return res, errors.New("查询班级扩展失败: " + err.Error())
	}
	var classIDs []int64
	for _, ext := range classExts {
		classIDs = append(classIDs, ext.ExtValue)
	}
	if len(classIDs) > 0 {
		if err := db.DB.Where("id IN (?)", classIDs).Find(&res.Classes).Error; err != nil {
			return res, errors.New("查询班级信息失败: " + err.Error())
		}
	}
	// 获取大纲详情
	if plan.SyllabusID > 0 {
		var syllabus model.Syllabus
		if err := db.DB.First(&syllabus, plan.SyllabusID).Error; err != nil {
			log.Printf("大纲查询失败: %v", err)
		} else {
			res.Syllabus = syllabus
		}
	}

	// 获取前序计划详情
	if plan.PreviousID > 0 {
		var previousPlan model.TeachingPlan
		if err := db.DB.First(&previousPlan, plan.PreviousID).Error; err == nil {
			res.PreviousPlan = &previousPlan
		}
	}

	return res, nil
}
