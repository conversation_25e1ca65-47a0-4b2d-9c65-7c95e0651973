package tech

import (
	"errors"
	"time"

	"tms/model"
	"tms/pkg/db"
)

// MajorService 专业相关服务
type MajorService struct{}

// NewMajorService 创建服务实例
func NewMajorService() *MajorService {
	return &MajorService{}
}

// GetMajorList 获取专业列表（支持搜索和树状结构）
func (s *MajorService) GetMajorList(name, majorID string) ([]*model.MajorTreeNode, error) {
	dbQuery := db.DB.Model(&model.Majors{})
	if name != "" {
		dbQuery = dbQuery.Where("name ILIKE ?", "%"+name+"%")
	}
	if majorID != "" {
		dbQuery = dbQuery.Where("major_id = ?", majorID)
	}

	var majors []model.Majors
	if err := dbQuery.Order("id ASC").Find(&majors).Error; err != nil {
		return nil, errors.New("获取数据失败: " + err.Error())
	}

	return buildMajorTree(majors), nil
}

// CreateMajor 创建专业
func (s *MajorService) CreateMajor(req *model.Majors, userID int64) error {
	if userID == 0 {
		return errors.New("无效的用户ID")
	}
	// 检查 MajorID 是否已存在
	var count int64
	if err := db.DB.Model(&model.Majors{}).
		Where("major_id = ?", req.MajorID).
		Count(&count).Error; err != nil {
		return errors.New("检查专业ID失败: " + err.Error())
	}
	if count > 0 {
		return errors.New("专业ID已存在")
	}
	req.UserID = userID
	req.CreatedAt = time.Now().Unix()
	req.UpdatedAt = time.Now().Unix()

	if err := db.DB.Create(req).Error; err != nil {
		return errors.New("创建专业失败: " + err.Error())
	}
	return nil
}

// UpdateMajor 更新专业信息
func (s *MajorService) UpdateMajor(req *model.Majors) error {
	if req.ID <= 0 {
		return errors.New("无效的专业ID")
	}
	// 检查 MajorID 是否被其他专业使用
	var count int64
	if err := db.DB.Model(&model.Majors{}).
		Where("major_id = ? AND id != ?", req.MajorID, req.ID).
		Count(&count).Error; err != nil {
		return errors.New("检查专业ID失败: " + err.Error())
	}
	if count > 0 {
		return errors.New("专业ID已被其他专业使用")
	}
	req.UpdatedAt = time.Now().Unix()

	updates := map[string]interface{}{
		"major_id":    req.MajorID,
		"name":        req.Name,
		"description": req.Description,
		"updated_at":  req.UpdatedAt,
		"parent_id":   req.ParentID,
	}

	if err := db.DB.Model(&model.Majors{}).
		Where("id = ?", req.ID).
		Updates(updates).Error; err != nil {
		return errors.New("更新专业失败: " + err.Error())
	}
	return nil
}

// DeleteMajor 删除专业
func (s *MajorService) DeleteMajor(id int64) error {
	if id == 0 {
		return errors.New("无效的专业ID")
	}

	if err := db.DB.Where("id = ?", id).Delete(&model.Majors{}).Error; err != nil {
		return errors.New("删除专业失败: " + err.Error())
	}
	return nil
}

// 构建树状结构
func buildMajorTree(list []model.Majors) []*model.MajorTreeNode {
	majorMap := make(map[int64]*model.MajorTreeNode)

	// 初始化所有节点为指针，并存入 map
	for _, m := range list {
		majorMap[m.ID] = &model.MajorTreeNode{
			ID:          m.ID,
			MajorID:     m.MajorID,
			Name:        m.Name,
			ParentID:    m.ParentID,
			Description: m.Description,
			Children:    make([]*model.MajorTreeNode, 0),
		}
	}
	// 第二步：构建树结构
	var tree []*model.MajorTreeNode
	for _, m := range list {
		node := majorMap[m.ID]

		if m.ParentID == 0 {
			tree = append(tree, node)
		} else {
			if parent, ok := majorMap[m.ParentID]; ok {
				parent.Children = append(parent.Children, node)
			}
		}
	}

	return tree
}
