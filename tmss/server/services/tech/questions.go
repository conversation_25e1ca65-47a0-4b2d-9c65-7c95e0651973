package tech

import (
	"encoding/json"
	"errors"
	"time"
	"tms/model"
	"tms/pkg/db"
	"tms/services/user"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type QuestionService struct {
}

func NewQuestionService() *QuestionService {
	return &QuestionService{}
}

// CreateQuestionDraft 创建试题草稿并关联课件
func (api *QuestionService) CreateQuestionDraft(req model.ReqQuestionUpdate, userId int64) error {
	if userId == 0 {
		return errors.New("无效的用户ID")
	}
	// 开启事务
	tx := db.DB.Begin()
	err := createQuestionByReq(&req, tx, userId)
	if err != nil {
		tx.Rollback()
		return err
	}
	// 创建试题与课件的关联关系
	err = SaveQuestionsExts(req, tx)
	if err != nil {
		tx.Rollback()
		return err
	}
	tx.Commit()
	return nil
}
func QuestionCreator(tx *gorm.DB, content string, userId int64) (int64, error) {
	var changeReq struct {
		FormData model.ReqQuestionUpdate `json:"formData"`
	}
	//var req model.ReqSyllabusCreate
	if err := json.Unmarshal([]byte(content), &changeReq); err != nil {
		return 0, errors.New("解析教材内容失败: " + err.Error())
	}
	req := changeReq.FormData
	if userId == 0 {
		return 0, errors.New("无效的用户ID")
	}
	err := createQuestionByReq(&req, tx, userId)
	if err != nil {
		return 0, err
	}
	// 创建试题与课件的关联关系
	err = SaveQuestionsExts(req, tx)
	if err != nil {
		return 0, err
	}
	return req.Questions.ID, nil
}
func createQuestionByReq(req *model.ReqQuestionUpdate, tx *gorm.DB, userId int64) error {
	req.Questions.UserID = userId
	req.Questions.Status = model.QuestionStatusDraft
	req.Questions.CreatedAt = time.Now().Unix()
	// 检查所有 CoursewareIDs 是否存在
	if len(req.CoursewareIDs) > 0 {
		exist, err := checkCoursewaressExist(req.CoursewareIDs)
		if err != nil {
			return errors.New("数据库错误: " + err.Error())
		}
		if !exist {
			return errors.New("包含无效的课件ID，请确认所有课件都有效")
		}
	}
	req.Questions.ID = 0
	// 创建试题
	if err := tx.Create(&req.Questions).Error; err != nil {
		return errors.New("创建失败: " + err.Error())
	}
	return nil

}
func SaveQuestionsExts(req model.ReqQuestionUpdate, tx *gorm.DB) error {
	questionExts := []model.QuestionsExt{}
	if len(req.CoursewareIDs) > 0 {
		for _, coursewareID := range req.CoursewareIDs {
			questionExt := model.QuestionsExt{
				QuestionID: req.Questions.ID,
				ExtKey:     "courseware",
				ExtValue:   coursewareID,
			}
			questionExts = append(questionExts, questionExt)
		}
	}
	if len(req.ChapterIDs) > 0 {
		for _, chapterID := range req.ChapterIDs {
			questionExt := model.QuestionsExt{
				QuestionID: req.Questions.ID,
				ExtKey:     "chapter",
				ExtValue:   chapterID,
			}
			questionExts = append(questionExts, questionExt)
		}
	}
	if len(questionExts) > 0 {
		if err := tx.Model(&model.QuestionsExt{}).Create(&questionExts).Error; err != nil {
			return errors.New("创建失败: " + err.Error())
		}
	}
	return nil
}

// UpdateQuestionDraft 更新试题草稿并重新设置关联课件
func (api *QuestionService) UpdateQuestionDraft(req model.ReqQuestionUpdate) error {
	if req.Questions.ID <= 0 {
		return errors.New("无效的试题ID")
	}

	var question model.Questions
	if err := db.DB.First(&question, req.Questions.ID).Error; err != nil {
		return errors.New("未找到对应试题")
	}

	if question.Status != model.QuestionStatusDraft && question.Status != model.QuestionStatusRejected {
		return errors.New("只能编辑草稿")
	}

	now := time.Now().Unix()
	req.Questions.UpdatedAt = now
	// 检查所有 CoursewareIDs 是否存在
	if len(req.CoursewareIDs) > 0 {
		exist, err := checkCoursewaressExist(req.CoursewareIDs)
		if err != nil {
			return errors.New("数据库错误: " + err.Error())
		}
		if !exist {
			return errors.New("包含无效的课件ID，请确认所有课件都有效")
		}
	}
	// 开启事务
	tx := db.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 更新试题基本信息
	updates := map[string]interface{}{
		"title":         req.Questions.Title,
		"question_type": req.Questions.QuestionType,
		"content":       req.Questions.Content,
		"answer":        req.Questions.Answer,
		"minutes":       req.Questions.Minutes,
		"score_points":  req.Questions.ScorePoints,
		"score":         req.Questions.Score,
		"options":       req.Questions.Options,
		"updated_at":    now,
	}
	if err := tx.Model(&question).Updates(updates).Error; err != nil {
		tx.Rollback()
		return errors.New("更新失败: " + err.Error())
	}

	// 删除旧的关联关系
	err := tx.Where("question_id = ?", req.Questions.ID).Delete(&model.QuestionsExt{}).Error
	if err != nil {
		tx.Rollback()
		return errors.New("删除关联失败: " + err.Error())
	}
	// 创建试题与课件的关联关系
	err = SaveQuestionsExts(req, tx)
	if err != nil {
		tx.Rollback()
		return err
	}

	tx.Commit()
	return nil
}
func checkCoursewaressExist(coursewareIDs []int64) (bool, error) {
	if len(coursewareIDs) == 0 {
		return true, nil
	}

	var count int64
	err := db.DB.Model(&model.Courseware{}).
		Where("id IN ?", coursewareIDs).
		Count(&count).Error

	if err != nil {
		return false, err
	}

	return count == int64(len(coursewareIDs)), nil
}

// GetQuestionCoursewares 获取试题关联的课件列表
func (api *QuestionService) GetQuestionCoursewares(id int64) ([]model.Courseware, error) {
	var res []model.Courseware
	if id == 0 {
		return res, errors.New("无效的试题ID")
	}

	var associations []model.QuestionCourseware
	if err := db.DB.Where("question_id = ?", id).Find(&associations).Error; err != nil {
		return res, errors.New("查询关联失败: " + err.Error())
	}

	// 提取课件ID列表
	var coursewareIDs []int64
	for _, association := range associations {
		coursewareIDs = append(coursewareIDs, association.CoursewareID)
	}
	if err := db.DB.Where("id IN ?", coursewareIDs).Find(&res).Error; err != nil {
		return res, errors.New("查询课件失败: " + err.Error())
	}
	return res, nil
}

// DeleteQuestion 删除试题
func (api *QuestionService) DeleteQuestion(id int64) error {
	if id == 0 {
		return errors.New("无效的试题ID")
	}

	var question model.Questions
	if err := db.DB.First(&question, id).Error; err != nil {
		return errors.New("未找到对应试题")
	}

	if question.Status != model.QuestionStatusDraft && question.Status != model.QuestionStatusRejected {
		return errors.New("只能删除草稿")
	}

	if err := db.DB.Delete(&question).Error; err != nil {
		return errors.New("删除失败: " + err.Error())
	}
	if err := db.DB.Where("question_id = ?", question.ID).Delete(&model.QuestionCourseware{}).Error; err != nil {
		return errors.New("删除关联失败: " + err.Error())
	}

	return nil
}

// GetQuestionList 获取试题列表
func (api *QuestionService) GetQuestionList(c *gin.Context, req model.ReqQuestionSearch) (model.RespQuestionList, error) {
	var res model.RespQuestionList
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 || req.PageSize > 100 {
		req.PageSize = 10
	}

	dbQuery := db.DB.Model(&model.Questions{})

	if req.Name != "" {
		dbQuery = dbQuery.Where("title ILIKE ?", "%"+req.Name+"%")
	}
	if req.Status != "" {
		dbQuery = dbQuery.Where("status = ?", req.Status)
	}
	// if req.UserID > 0 {
	// 	dbQuery = dbQuery.Where("user_id = ?", req.UserID)
	// }
	dbQuery, has := user.ApplyDataRules(c, dbQuery, model.ModuleSyllabus)
	if !has && req.UserID > 0 {
		dbQuery = dbQuery.Where("user_id = ?", req.UserID)
	}
	if err := dbQuery.Count(&res.Total).Error; err != nil {
		return res, errors.New("查询总数失败")
	}

	offset := (req.Page - 1) * req.PageSize
	var list []model.Questions
	if err := dbQuery.Offset(offset).Limit(req.PageSize).Order("created_at DESC").Find(&list).Error; err != nil {
		return res, errors.New("查询数据失败: " + err.Error())
	}
	var questionIds []int64
	for _, question := range list {
		questionIds = append(questionIds, question.ID)
	}
	revisionList, err := GetRevisionList(model.ModuleQuestions, questionIds)
	if err != nil {
		return res, err
	}
	for _, c := range list {
		revDetail := model.RespQuestionDetailWithRev{Questions: c}
		for _, r := range revisionList {
			if r.OriginalID == c.ID {
				revDetail.Rev = &r
			}
		}
		res.List = append(res.List, revDetail)
	}
	return res, nil
}

func (api *QuestionService) GetQuestionsByCourseOrChapter(req model.ReqQuestionSearch) (gin.H, error) {
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 || req.PageSize > 100 {
		req.PageSize = 10
	}

	// 构建试题查询
	dbQuery := db.DB.Model(&model.Questions{})

	// 根据条件构建关联查询
	if req.CourseID > 0 {
		// 通过课程ID检索：关联课件表，再通过课件关联试题
		dbQuery = dbQuery.
			Joins("JOIN questions_ext qe ON questions.id = qe.question_id AND qe.ext_key = 'courseware'").
			Joins("JOIN courseware cw ON qe.ext_value = cw.id").
			Where("cw.course_id = ?", req.CourseID)
	} else if req.ChapterID > 0 {
		// 通过章节ID检索：直接关联章节扩展表
		dbQuery = dbQuery.
			Joins("JOIN questions_ext qe ON questions.id = qe.question_id AND qe.ext_key = 'chapter'").
			Where("qe.ext_value = ?", req.ChapterID)
	} else {
		return nil, errors.New("必须提供课程ID或章节ID进行检索")
	}

	// 附加其他过滤条件
	if req.Status == "" {
		req.Status = model.QuestionStatusPublished
	}
	dbQuery = dbQuery.Where("questions.status = ?", req.Status)
	if req.UserID > 0 {
		dbQuery = dbQuery.Where("questions.user_id = ?", req.UserID)
	}
	if req.Name != "" {
		dbQuery = dbQuery.Where("questions.title ILIKE ?", "%"+req.Name+"%")
	}

	// 查询总数
	var total int64
	if err := dbQuery.Count(&total).Error; err != nil {
		return nil, errors.New("查询总数失败: " + err.Error())
	}

	// 分页查询试题列表
	var questions []model.Questions
	offset := (req.Page - 1) * req.PageSize
	err := dbQuery.Offset(offset).
		Limit(req.PageSize).
		Order("questions.created_at DESC").
		Find(&questions).Error

	if err != nil {
		return nil, errors.New("查询试题失败: " + err.Error())
	}

	return gin.H{
		"list":  questions,
		"total": total,
	}, nil
}

// GetQuestionsByChapterID 根据章节ID获取关联的试题列表（不分页）
func (api *QuestionService) GetQuestionsByChapterID(chapterID int64) ([]model.Questions, error) {
	if chapterID == 0 {
		return nil, errors.New("无效的章节ID")
	}

	var questionIDs []int64
	// 查询该章节下所有关联的试题ID（来自 questions_ext 表）
	err := db.DB.Model(&model.QuestionsExt{}).
		Where("ext_key = ? AND ext_value = ?", "chapter", chapterID).
		Distinct("question_id").
		Pluck("question_id", &questionIDs).Error

	if err != nil {
		return nil, errors.New("查询试题ID失败: " + err.Error())
	}

	if len(questionIDs) == 0 {
		return []model.Questions{}, nil
	}

	// 查询试题详情
	var questions []model.Questions
	err = db.DB.Model(&model.Questions{}).
		Omit("created_at", "answer").
		Where("id IN (?)", questionIDs).
		Find(&questions).Error

	if err != nil {
		return nil, errors.New("查询试题失败: " + err.Error())
	}

	return questions, nil
}
func (api *QuestionService) GetQuestionsByIDs(questionIDs []int64, p model.ReqPage) ([]model.Questions, int, error) {
	total := len(questionIDs)
	if total == 0 {
		return []model.Questions{}, 0, nil
	}
	//log.Printf("page: %d, page_size: %d", p.Page, p.PageSize)
	// 验证分页参数
	if p.Page < 1 {
		p.Page = 1
	}
	if p.PageSize < 1 || p.PageSize > 100 {
		p.PageSize = 10
	}

	offset := (p.Page - 1) * p.PageSize
	if offset >= total {
		p.Page = 1
		offset = 0
	}

	var questions []model.Questions
	dbQuery := db.DB.Model(&model.Questions{}).Where("id IN (?)", questionIDs)
	if p.Name != "" {
		dbQuery = dbQuery.Where("title ILIKE ?", "%"+p.Name+"%")
	}
	dbQuery = dbQuery.Where("status = ?", model.QuestionStatusPublished)
	err := dbQuery.
		Order("id DESC").
		Offset(offset).
		Limit(p.PageSize).
		Find(&questions).Error

	if err != nil {
		return nil, 0, errors.New("查询试题失败: " + err.Error())
	}

	return questions, total, nil
}
func (api *QuestionService) GetQuestionsByCoursewareID(coursewareID int64, p model.ReqPage) ([]model.Questions, int, error) {
	if coursewareID == 0 {
		return nil, 0, errors.New("无效的课件ID")
	}

	var questionIDs []int64
	// 查询该课件下所有关联的试题ID
	err := db.DB.Model(&model.QuestionsExt{}).
		Where("ext_key = ? AND ext_value = ?", "courseware", coursewareID).
		Distinct("question_id").
		Pluck("question_id", &questionIDs).Error

	if err != nil {
		return nil, 0, errors.New("查询试题ID失败: " + err.Error())
	}
	return api.GetQuestionsByIDs(questionIDs, p)
}

// GetQuestionsByCourseID 根据课程ID获取关联的试题列表（支持分页和查询条件）
func (api *QuestionService) GetQuestionsByCourseID(courseID int64, p model.ReqPage) ([]model.Questions, int, error) {
	if courseID == 0 {
		return nil, 0, errors.New("无效的课程ID")
	}

	var coursewareIDs []int64
	// 查询该课程下所有的课件
	dbQuery := db.DB.Model(&model.Courseware{})

	err := dbQuery.Where("course_id = ? AND status = ?", courseID, model.CoursewareStatusPublished).
		Pluck("id", &coursewareIDs).Error
	//log.Printf("coursewareIDs: %v", coursewareIDs)
	if err != nil {
		return nil, 0, errors.New("查询课程ID失败: " + err.Error())
	}
	var questionIDs []int64
	err = db.DB.Model(&model.QuestionsExt{}).
		Where("ext_key = ? AND ext_value IN (?)", "courseware", coursewareIDs).
		Distinct("question_id").
		Pluck("question_id", &questionIDs).Error

	if err != nil {
		return nil, 0, errors.New("查询试题ID失败: " + err.Error())
	}
	return api.GetQuestionsByIDs(questionIDs, p)
}

// GetQuestionsByCourseID 根据课程ID获取关联的试题列表（支持分页和查询条件）
func (api *QuestionService) GetQuestionsByCourseIDAndChapterID(courseID int64, chapterID int64, p model.ReqPage) ([]model.Questions, int, error) {
	if courseID == 0 {
		return nil, 0, errors.New("无效的课程ID")
	}

	idsMap := make(map[int64]bool)
	var coursewareIDs []int64
	// 查询该课程下所有的课件
	dbQuery := db.DB.Model(&model.Courseware{})

	err := dbQuery.Where("course_id = ? AND status = ?", courseID, model.CoursewareStatusPublished).
		Pluck("id", &coursewareIDs).Error
	//log.Printf("coursewareIDs: %v", coursewareIDs)
	if err != nil {
		return nil, 0, errors.New("查询课程ID失败: " + err.Error())
	}
	var questionIDs []int64
	err = db.DB.Model(&model.QuestionsExt{}).
		Where("ext_key = ? AND ext_value IN (?)", "courseware", coursewareIDs).
		Distinct("question_id").
		Pluck("question_id", &questionIDs).Error

	if err != nil {
		return nil, 0, errors.New("查询试题ID失败: " + err.Error())
	}
	for _, id := range questionIDs {
		idsMap[id] = true
	}

	if chapterID > 0 {
		var chapterIDs []int64
		err := db.DB.Model(&model.QuestionsExt{}).
			Where("ext_key = ? AND ext_value = ?", "chapter", chapterID).
			Distinct("question_id").
			Pluck("question_id", &chapterIDs).Error
		if err != nil {
			return nil, 0, errors.New("查询章节ID失败: " + err.Error())
		}
		for _, id := range chapterIDs {
			idsMap[id] = true
		}

	}

	var qids []int64
	for id := range idsMap {
		qids = append(qids, id)
	}
	//log.Printf("qids: %v", qids)
	return api.GetQuestionsByIDs(qids, p)
}

// GetQuestion 根据ID获取单个试题的完整信息
func (api *QuestionService) GetQuestion(id int64) (model.RespQuestionDetail, error) {
	var res model.RespQuestionDetail

	// 1. 获取试题基本信息
	var question model.Questions
	if err := db.DB.First(&question, id).Error; err != nil {
		return res, errors.New("未找到对应试题")
	}
	res.Questions = question

	// 2. 获取关联的课件信息
	exts := []model.QuestionsExt{}
	if err := db.DB.Where("question_id = ?", id).Find(&exts).Error; err != nil {
		return res, err
	}
	var coursewareIDs []int64
	var chapterIDs []int64
	for _, ext := range exts {
		if ext.ExtKey == "courseware" {
			coursewareIDs = append(coursewareIDs, ext.ExtValue)
		}
		if ext.ExtKey == "chapter" {
			chapterIDs = append(chapterIDs, ext.ExtValue)
		}
	}

	// 4. 获取关联课件的详细信息
	if len(coursewareIDs) > 0 {
		if err := db.DB.Where("id IN ?", coursewareIDs).Find(&res.Coursewares).Error; err != nil {
			return res, errors.New("获取课件详情失败: " + err.Error())
		}
	}
	if len(chapterIDs) > 0 {
		if err := db.DB.Where("id IN ?", chapterIDs).Find(&res.Chapters).Error; err != nil {
			return res, errors.New("获取章节详情失败: " + err.Error())
		}
	}
	return res, nil
}

// GetQuestionExtValues 获取试题扩展信息的值列表
func (api *QuestionService) GetQuestionExtValues(questionID int64, extKey string) ([]int64, error) {
	var extValues []int64
	err := db.DB.Model(&model.QuestionsExt{}).
		Where("question_id = ? AND ext_key = ?", questionID, extKey).
		Pluck("ext_value", &extValues).Error
	return extValues, err
}
