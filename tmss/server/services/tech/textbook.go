package tech

import (
	"errors"
	"time"

	"tms/model"
	"tms/pkg/db"
)

// TextbookService 教材管理API结构体
type TextbookService struct{}

func NewTextbookService() *TextbookService {
	return &TextbookService{}
}

// GetTextbookList 获取教材列表（支持分页、搜索）
func (api *TextbookService) GetTextbookList(req model.ReqTextbookSearch) (model.RespTextbookList, error) {
	var res model.RespTextbookList

	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 || req.PageSize > 100 {
		req.PageSize = 10
	}

	dbQuery := db.DB.Model(&model.Textbook{})
	if req.Title != "" {
		dbQuery = dbQuery.Where("title ILIKE ?", "%"+req.Title+"%")
	}
	if req.Author != "" {
		dbQuery = dbQuery.Where("author ILIKE ?", "%"+req.Author+"%")
	}
	if req.UserID != 0 {
		dbQuery = dbQuery.Where("user_id = ?", req.UserID)
	}
	if req.Username != "" {
		dbQuery = dbQuery.Where("user_id IN (SELECT id FROM users WHERE name ILIKE ?)", "%"+req.Username+"%")
	}
	if err := dbQuery.Count(&res.Total).Error; err != nil {
		return res, errors.New("获取总数失败")
	}

	offset := (req.Page - 1) * req.PageSize
	if err := dbQuery.Offset(offset).Limit(req.PageSize).Order("created_at DESC").Find(&res.List).Error; err != nil {
		return res, errors.New("获取数据失败")
	}

	return res, nil
}

// CreateTextbook 创建教材
func (api *TextbookService) CreateTextbook(req model.Textbook, userId int64) error {
	if userId == 0 {
		return errors.New("无效的用户ID")
	}
	req.UserID = userId
	req.CreatedAt = time.Now().Unix()
	req.UpdatedAt = time.Now().Unix()

	if err := db.DB.Create(&req).Error; err != nil {
		return errors.New("创建教材失败: " + err.Error())
	}

	return nil
}

// UpdateTextbook 更新教材信息
func (api *TextbookService) UpdateTextbook(req model.Textbook) error {
	if req.ID == 0 {
		return errors.New("无效的教材ID")
	}

	var textbook model.Textbook
	if err := db.DB.First(&textbook, req.ID).Error; err != nil {
		return errors.New("未找到教材")
	}

	updates := map[string]interface{}{
		"title":      req.Title,
		"author":     req.Author,
		"isbn":       req.Isbn,
		"publisher":  req.Publisher,
		"edition":    req.Edition,
		"file_name":  req.FileName,
		"file_path":  req.FilePath,
		"file_type":  req.FileType,
		"updated_at": time.Now().Unix(),
	}

	if err := db.DB.Model(&model.Textbook{}).
		Where("id = ?", req.ID).
		Updates(updates).Error; err != nil {
		return errors.New("更新教材失败: " + err.Error())
	}

	return nil
}

// DeleteTextbook 删除教材
func (api *TextbookService) DeleteTextbook(id int64) error {
	if id == 0 {
		return errors.New("无效的教材ID")
	}
	var textbook model.Textbook
	if err := db.DB.Where("id = ?", id).First(&textbook).Error; err != nil {
		return errors.New("未找到教材")
	}

	if err := db.DB.Where("id = ?", id).Delete(&model.Textbook{}).Error; err != nil {
		return errors.New("删除教材失败: " + err.Error())
	}

	return nil
}

// BatchDeleteTextbooks 批量删除教材
func (api *TextbookService) BatchDeleteTextbooks(ids []int64) error {

	if len(ids) == 0 {
		return errors.New("请选择要删除的教材")
	}

	if err := db.DB.Where("id IN (?)", ids).Delete(&model.Textbook{}).Error; err != nil {
		return errors.New("批量删除失败: " + err.Error())
	}

	return nil
}
