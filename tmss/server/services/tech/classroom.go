package tech

import (
	"fmt"
	"time"

	"tms/model"
	"tms/pkg/db"
)

// ClassroomService 定义教室相关的业务逻辑接口

type ClassroomService struct{}

func NewClassroomService() *ClassroomService {
	return &ClassroomService{}
}

// CreateClassroom 创建教室（包含专业存在性校验）
func (s *ClassroomService) CreateClassroom(req model.Classroom, userID int64) error {
	var exits model.Classroom
	err := db.DB.Where("title = ?", req.Title).First(&exits).Error
	if err == nil {
		return fmt.Errorf("教室名称已存在")
	}

	req.UserID = userID
	req.CreatedAt = time.Now().Unix()
	req.UpdatedAt = time.Now().Unix()

	if err := db.DB.Create(&req).Error; err != nil {
		return fmt.Errorf("创建教室失败: %v", err)
	}
	return nil
}

// UpdateClassroom 更新教室信息
func (s *ClassroomService) UpdateClassroom(req model.Classroom) error {
	if req.ID <= 0 {
		return fmt.Errorf("无效的教室ID")
	}

	var existingClassroom model.Classroom
	if err := db.DB.Where("id != ? AND title = ?", req.ID, req.Title).First(&existingClassroom).Error; err == nil {
		return fmt.Errorf("教室编号已存在")
	}

	req.UpdatedAt = time.Now().Unix()

	if err := db.DB.Model(&model.Classroom{}).
		Where("id = ?", req.ID).
		Updates(map[string]interface{}{
			"title":       req.Title,
			"description": req.Description,
			"updated_at":  req.UpdatedAt,
		}).Error; err != nil {
		return fmt.Errorf("更新教室失败: %v", err)
	}
	return nil
}

// GetClassroomList 获取教室列表
func (s *ClassroomService) GetClassroomList(req model.ReqClassroomSearch) (model.RespClassroomList, error) {
	var res model.RespClassroomList
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 || req.PageSize > 100 {
		req.PageSize = 10
	}

	dbQuery := db.DB.Model(&model.Classroom{})
	if req.Title != "" {
		dbQuery = dbQuery.Where("title ILIKE ?", "%"+req.Title+"%")
	}
	var total int64
	if err := dbQuery.Count(&total).Error; err != nil {
		return res, fmt.Errorf("获取总数失败: %v", err)
	}

	var classes []model.Classroom
	offset := (req.Page - 1) * req.PageSize
	if err := dbQuery.Offset(offset).Limit(req.PageSize).Order("created_at DESC").Find(&classes).Error; err != nil {
		return res, fmt.Errorf("获取数据失败: %v", err)
	}

	return model.RespClassroomList{List: classes, Total: total}, nil
}

// DeleteClassroom 删除教室
func (s *ClassroomService) DeleteClassroom(id int64) error {
	if id == 0 {
		return fmt.Errorf("无效的教室ID")
	}

	if err := db.DB.Where("id = ?", id).Delete(&model.Classroom{}).Error; err != nil {
		return fmt.Errorf("删除教室失败: %v", err)
	}
	return nil
}

// BatchDeleteClassroomes 批量删除教室
func (s *ClassroomService) BatchDeleteClassroomes(ids []int64) error {
	if len(ids) == 0 {
		return fmt.Errorf("请选择要删除的教室")
	}

	if err := db.DB.Where("id IN (?)", ids).Delete(&model.Classroom{}).Error; err != nil {
		return fmt.Errorf("批量删除失败: %v", err)
	}
	return nil
}

// GetClassroomDetail 获取教室详情
func (s *ClassroomService) GetClassroomDetail(classID int64) (*model.Classroom, error) {
	if classID == 0 {
		return nil, fmt.Errorf("无效的教室ID")
	}

	var class *model.Classroom
	if err := db.DB.First(&class, classID).Error; err != nil {
		return nil, fmt.Errorf("未找到对应教室")
	}

	return class, nil
}
