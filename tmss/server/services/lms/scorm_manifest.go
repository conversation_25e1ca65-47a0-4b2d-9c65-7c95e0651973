package lms

import "encoding/xml"

// Manifest 结构体用于解析 SCORM 课件的元数据文件 imsmanifest.xml。
// imsmanifest.xml 是 SCORM 课件的“身份证”，定义了课件的结构、资源和启动信息。
type Manifest struct {
	XMLName              xml.Name             `xml:"manifest"`                                                // XML元素的名称，通常是"manifest"
	Identifier           string               `xml:"identifier,attr"`                                         // 唯一标识符
	Version              string               `xml:"version,attr"`                                            // SCORM版本
	XMLBase              string               `xml:"xml:base,attr"`                                           // 根URL
	Metadata             Metadata             `xml:"metadata"`                                                // 元数据
	Organizations        Organizations        `xml:"organizations"`                                           // 组织结构
	Resources            Resources            `xml:"resources"`                                               // 资源列表
	SequencingCollection SequencingCollection `xml:"http://www.imsglobal.org/xsd/imsss sequencingCollection"` // 排序规则集合
}

// Metadata 结构体表示 <metadata> 元素
type Metadata struct {
	XMLName       xml.Name      `xml:"metadata"`
	Schema        string        `xml:"schema"`
	SchemaVersion string        `xml:"schemaversion"`
	Location      ADLCPLocation `xml:"http://www.adlnet.org/xsd/adlcp_v1p3 location"` // 外部元数据文件位置
}

// ADLCPLocation 结构体表示 adlcp:location 元素
type ADLCPLocation struct {
	XMLName xml.Name `xml:"http://www.adlnet.org/xsd/adlcp_v1p3 location"`
	Href    string   `xml:",chardata"` // 外部元数据文件的URL
}

// Organizations 结构体表示 <organizations> 元素
type Organizations struct {
	XMLName      xml.Name       `xml:"organizations"` // XML元素的名称，通常是"organizations"
	Default      string         `xml:"default,attr"`  // 默认组织标识符
	Organization []Organization `xml:"organization"`  // 组织列表
}

// Organization 结构体表示 <organization> 元素
type Organization struct {
	XMLName                        xml.Name   `xml:"organization"`                                                        // XML元素的名称，通常是"organization"
	Identifier                     string     `xml:"identifier,attr"`                                                     // 组织唯一标识符
	Title                          string     `xml:"title"`                                                               // 组织标题
	Items                          []Item     `xml:"item"`                                                                // 组织内的活动项列表
	Sequencing                     Sequencing `xml:"http://www.imsglobal.org/xsd/imsss sequencing"`                       // 组织排序规则
	Structure                      string     `xml:"structure,attr"`                                                      // 组织结构
	ADLSeqObjectivesGlobalToSystem bool       `xml:"http://www.adlnet.org/xsd/adlseq_v1p3 objectivesGlobalToSystem,attr"` // 目标是否全局可见
	ADLCPSharedDataGlobalToSystem  bool       `xml:"http://www.adlnet.org/xsd/adlcp_v1p3 sharedDataGlobalToSystem,attr"`  // 共享数据是否全局可见
	Metadata                       Metadata   `xml:"metadata"`                                                            // 组织元数据
}

// Item 结构体表示 <item> 元素，是活动树中的一个节点
type Item struct {
	XMLName                  xml.Name            `xml:"item"`                                                     // XML元素的名称，通常是"item"
	Identifier               string              `xml:"identifier,attr"`                                          // 活动项唯一标识符
	IdentifierRef            string              `xml:"identifierref,attr"`                                       // 引用资源的标识符
	IsVisible                bool                `xml:"isvisible,attr"`                                           // 活动项是否可见
	Parameters               string              `xml:"parameters,attr"`                                          // 传递给内容的参数
	Title                    string              `xml:"title"`                                                    // 活动项标题
	Items                    []Item              `xml:"item"`                                                     // 嵌套的子活动项
	Sequencing               Sequencing          `xml:"http://www.imsglobal.org/xsd/imsss sequencing"`            // 活动项排序规则
	Metadata                 Metadata            `xml:"metadata"`                                                 // 活动项元数据
	ADLCPData                ADLCPData           `xml:"http://www.adlnet.org/xsd/adlcp_v1p3 data"`                // ADLCP数据
	ADLCPTimeLimitAction     string              `xml:"http://www.adlnet.org/xsd/adlcp_v1p3 timeLimitAction"`     // 时间限制动作
	ADLCPDataFromLMS         string              `xml:"http://www.adlnet.org/xsd/adlcp_v1p3 dataFromLMS"`         // LMS数据
	ADLCPCompletionThreshold CompletionThreshold `xml:"http://www.adlnet.org/xsd/adlcp_v1p3 completionThreshold"` // 完成阈值
	ADLNavPresentation       ADLNavPresentation  `xml:"http://www.adlnet.org/xsd/adlnav_v1p3 presentation"`       // 导航界面
}

// Dependency 结构体表示 <dependency> 元素
type Dependency struct {
	XMLName       xml.Name `xml:"dependency"`
	IdentifierRef string   `xml:"identifierref,attr"` // 引用资源的标识符
}

// CompletionThreshold 结构体表示 <adlcp:completionThreshold> 元素
type CompletionThreshold struct {
	XMLName            xml.Name `xml:"http://www.adlnet.org/xsd/adlcp_v1p3 completionThreshold"`
	CompletedByMeasure bool     `xml:"completedByMeasure,attr"` // 是否通过测量完成
	MinProgressMeasure float64  `xml:"minProgressMeasure,attr"` // 最小进度测量值
	ProgressWeight     float64  `xml:"progressWeight,attr"`     // 进度权重
}

// ADLNavPresentation 结构体表示 <adlnav:presentation> 元素
type ADLNavPresentation struct {
	XMLName             xml.Name            `xml:"http://www.adlnet.org/xsd/adlnav_v1p3 presentation"`
	NavigationInterface NavigationInterface `xml:"navigationInterface"` // 导航界面
}

// NavigationInterface 结构体表示 <navigationInterface> 元素
type NavigationInterface struct {
	XMLName   xml.Name    `xml:"navigationInterface"`
	HideLMSUI []HideLMSUI `xml:"hideLMSUI"` // 隐藏LMS UI元素
}

// HideLMSUI 结构体表示 <hideLMSUI> 元素
type HideLMSUI struct {
	XMLName xml.Name `xml:"hideLMSUI"`
	Control string   `xml:"control,attr"` // 要隐藏的控制项
}

// Resources 结构体表示 imsmanifest.xml 中的 <resources> 元素。
type Resources struct {
	XMLName  xml.Name   `xml:"resources"`     // XML元素的名称，通常是"resources"
	Resource []Resource `xml:"resource"`      // 资源列表
	XMLBase  string     `xml:"xml:base,attr"` // 根URL
}

// Resource 结构体表示 <resource> 元素
type Resource struct {
	XMLName    xml.Name     `xml:"resource"`                                            // XML元素的名称，通常是"resource"
	Identifier string       `xml:"identifier,attr"`                                     // 资源唯一标识符
	Type       string       `xml:"type,attr"`                                           // 资源类型
	ScormType  string       `xml:"http://www.adlnet.org/xsd/adlcp_v1p3 scormType,attr"` // SCORM资源类型
	Href       string       `xml:"href,attr"`                                           // 资源入口文件路径
	XMLBase    string       `xml:"xml:base,attr"`                                       // 根URL
	Metadata   Metadata     `xml:"metadata"`                                            // 资源元数据
	Dependency []Dependency `xml:"dependency"`                                          // 依赖资源
	Files      []File       `xml:"file"`                                                // 资源包含的文件列表
}

// File 结构体表示 <file> 元素
type File struct {
	XMLName  xml.Name `xml:"file"`      // XML元素的名称，通常是"file"
	Href     string   `xml:"href,attr"` // 文件路径
	Metadata Metadata `xml:"metadata"`  // 文件元数据
}

// --- Sequencing Structs ---

// SequencingCollection 结构体表示 <sequencingCollection> 元素
type SequencingCollection struct {
	XMLName    xml.Name     `xml:"sequencingCollection"` // XML元素的名称，通常是"sequencingCollection"
	Sequencing []Sequencing `xml:"sequencing"`           // 排序规则列表
}

// Sequencing 结构体表示 <imsss:sequencing> 元素
type Sequencing struct {
	XMLName                         xml.Name                         `xml:"http://www.imsglobal.org/xsd/imsss sequencing"`                         // XML元素的名称，通常是"sequencing"
	IDRef                           string                           `xml:"IDRef,attr"`                                                            // 引用排序规则的ID
	ControlMode                     ControlMode                      `xml:"controlMode"`                                                           // 控制模式
	SequencingRules                 SequencingRules                  `xml:"sequencingRules"`                                                       // 排序规则
	LimitConditions                 LimitConditions                  `xml:"limitConditions"`                                                       // 限制条件
	ADLSeqObjectives                *ADLSeqObjectives                `xml:"http://www.adlnet.org/xsd/adlseq_v1p3 objectives"`                      // ADL扩展目标
	Objectives                      Objectives                       `xml:"objectives"`                                                            // 目标
	Randomization                   Randomization                    `xml:"randomization"`                                                         // 随机化设置
	DeliveryControls                DeliveryControls                 `xml:"deliveryControls"`                                                      // 交付控制
	RollupRules                     RollupRules                      `xml:"rollupRules"`                                                           // 汇总规则
	ConstrainedChoiceConsiderations *ConstrainedChoiceConsiderations `xml:"http://www.adlnet.org/xsd/adlseq_v1p3 constrainedChoiceConsiderations"` // 限制选择考虑
	ADLSeqRollupConsiderations      *ADLSeqRollupConsiderations      `xml:"http://www.adlnet.org/xsd/adlseq_v1p3 rollupConsiderations"`            // ADL汇总考虑
}

// ControlMode 结构体表示 <imsss:controlMode> 元素
type ControlMode struct {
	XMLName                        xml.Name `xml:"controlMode"`                         // XML元素的名称，通常是"controlMode"
	Choice                         bool     `xml:"choice,attr"`                         // 是否允许选择活动
	ChoiceExit                     bool     `xml:"choiceExit,attr"`                     // 是否允许选择退出
	Flow                           bool     `xml:"flow,attr"`                           // 是否允许顺序流
	ForwardOnly                    bool     `xml:"forwardOnly,attr"`                    // 是否只允许向前导航
	UseCurrentAttemptObjectiveInfo bool     `xml:"useCurrentAttemptObjectiveInfo,attr"` // 是否使用当前尝试的目标信息
	UseCurrentAttemptProgressInfo  bool     `xml:"useCurrentAttemptProgressInfo,attr"`  // 是否使用当前尝试的进度信息
}

// SequencingRules 结构体表示 <imsss:sequencingRules> 元素
type SequencingRules struct {
	XMLName          xml.Name            `xml:"sequencingRules"`  // XML元素的名称，通常是"sequencingRules"
	PreConditionRule []PreConditionRule  `xml:"preConditionRule"` // 前置条件规则列表
	ExitCondition    []ExitConditionRule `xml:"exitCondition"`    // 退出条件规则列表
	PostCondition    []PostConditionRule `xml:"postCondition"`    // 后置条件规则列表
}

// PreConditionRule 结构体表示 <imsss:preConditionRule> 元素
type PreConditionRule struct {
	Rule                  // 规则基类
	RuleAction RuleAction `xml:"ruleAction"` // 规则动作
}

// ExitConditionRule 结构体表示 <imsss:exitConditionRule> 元素
type ExitConditionRule struct {
	Rule                  // 规则基类
	RuleAction RuleAction `xml:"ruleAction"` // 规则动作
}

// PostConditionRule 结构体表示 <imsss:postConditionRule> 元素
type PostConditionRule struct {
	Rule                  // 规则基类
	RuleAction RuleAction `xml:"ruleAction"` // 规则动作
}

// Rule 结构体是各类规则的基类
type Rule struct {
	Conditions RuleConditions `xml:"ruleConditions"` // 规则条件
}

// RuleConditions 结构体表示 <imsss:ruleConditions> 元素
type RuleConditions struct {
	ConditionCombination string          `xml:"conditionCombination,attr"` // 条件组合方式 (any 或 all)
	RuleCondition        []RuleCondition `xml:"ruleCondition"`             // 规则条件列表
}

// SequencingRuleInterface 定义了所有排序规则共有的行为
type SequencingRuleInterface interface {
	GetConditions() RuleConditions
	GetRuleAction() RuleAction
}

// GetConditions 实现 SequencingRuleInterface 接口
func (p PreConditionRule) GetConditions() RuleConditions {
	return p.Conditions
}

// GetRuleAction 实现 SequencingRuleInterface 接口
func (p PreConditionRule) GetRuleAction() RuleAction {
	return p.RuleAction
}

// GetConditions 实现 SequencingRuleInterface 接口
func (e ExitConditionRule) GetConditions() RuleConditions {
	return e.Conditions
}

// GetRuleAction 实现 SequencingRuleInterface 接口
func (e ExitConditionRule) GetRuleAction() RuleAction {
	return e.RuleAction
}

// GetConditions 实现 SequencingRuleInterface 接口
func (p PostConditionRule) GetConditions() RuleConditions {
	return p.Conditions
}

// GetRuleAction 实现 SequencingRuleInterface 接口
func (p PostConditionRule) GetRuleAction() RuleAction {
	return p.RuleAction
}

// RuleCondition 结构体表示 <imsss:ruleCondition> 元素
type RuleCondition struct {
	ReferencedObjective string  `xml:"referencedObjective,attr"` // 引用的目标ID
	MeasureThreshold    float64 `xml:"measureThreshold,attr"`    // 测量阈值
	Operator            string  `xml:"operator,attr"`            // 操作符 (noOp, not)
	Condition           string  `xml:"condition,attr"`           // 条件
}

// RuleAction 结构体表示 <imsss:ruleAction> 元素
type RuleAction struct {
	Action string `xml:"action,attr"` // 动作类型
}

// LimitConditions 结构体表示 <imsss:limitConditions> 元素
type LimitConditions struct {
	XMLName                      xml.Name `xml:"limitConditions"`                   // XML元素的名称，通常是"limitConditions"
	AttemptLimit                 *int     `xml:"attemptLimit,attr"`                 // 尝试次数限制
	AttemptAbsoluteDurationLimit *string  `xml:"attemptAbsoluteDurationLimit,attr"` // (Note, duration-based sequencing is currently not widely supported in SCORM 2004)
}

// Objectives 结构体表示 <imsss:objectives> 元素
type Objectives struct {
	XMLName          xml.Name         `xml:"http://www.imsglobal.org/xsd/imsss objectives"`       // XML元素的名称，通常是"objectives"
	PrimaryObjective PrimaryObjective `xml:"http://www.imsglobal.org/xsd/imsss primaryObjective"` // 主要目标
	Objectives       []Objective      `xml:"http://www.imsglobal.org/xsd/imsss objective"`        // 其他目标列表
}

// PrimaryObjective 结构体表示 <imsss:primaryObjective> 元素
type PrimaryObjective struct {
	XMLName              xml.Name             `xml:"http://www.imsglobal.org/xsd/imsss primaryObjective"` // 明确指定标签
	ObjectiveID          string               `xml:"objectiveID,attr"`                                    // 目标ID
	MinNormalizedMeasure MinNormalizedMeasure `xml:"minNormalizedMeasure,attr"`                           // 最小标准化测量值
	SatisfiedByMeasure   bool                 `xml:"satisfiedByMeasure,attr"`                             // 是否通过测量满足
	MeasureThreshold     float64              `xml:"measureThreshold,attr"`                               // 测量阈值
	MapInfo              []MapInfo            `xml:"mapInfo"`                                             // 映射信息列表
}

// Objective 结构体表示 <imsss:objective> 元素
type Objective struct {
	XMLName              xml.Name             `xml:"http://www.imsglobal.org/xsd/imsss objective"` // 明确指定标签
	ObjectiveID          string               `xml:"objectiveID,attr"`                             // 目标ID
	MinNormalizedMeasure MinNormalizedMeasure `xml:"minNormalizedMeasure,attr"`                    // 最小标准化测量值
	MapInfo              []MapInfo            `xml:"mapInfo"`                                      // 映射信息列表
}

// MinNormalizedMeasure 结构体表示 <imsss:minNormalizedMeasure> 元素
type MinNormalizedMeasure struct {
	XMLName xml.Name `xml:"minNormalizedMeasure"`
	Value   float64  `xml:",chardata"` // 实际的浮点数值
}

// MapInfo 结构体表示 <imsss:mapInfo> 元素
type MapInfo struct {
	XMLName                xml.Name `xml:"mapInfo"`                     // XML元素的名称，通常是"mapInfo"
	TargetObjectiveID      string   `xml:"targetObjectiveID,attr"`      // 目标目标ID
	ReadSatisfiedStatus    bool     `xml:"readSatisfiedStatus,attr"`    // 是否读取满足状态
	ReadNormalizedMeasure  bool     `xml:"readNormalizedMeasure,attr"`  // 是否读取标准化测量值
	WriteSatisfiedStatus   bool     `xml:"writeSatisfiedStatus,attr"`   // 是否写入满足状态
	WriteNormalizedMeasure bool     `xml:"writeNormalizedMeasure,attr"` // 是否写入标准化测量值
}

// Randomization 结构体表示 <imsss:randomization> 元素
type Randomization struct {
	XMLName             xml.Name `xml:"randomization"`            // XML元素的名称，通常是"randomization"
	SelectCount         int      `xml:"selectCount,attr"`         // 选择计数
	RandomizationTiming string   `xml:"randomizationTiming,attr"` // 随机化时机 (never, once, onEachNewAttempt)
	ReorderChildren     bool     `xml:"reorderChildren,attr"`     // 是否重新排序子元素
}

// DeliveryControls 结构体表示 <imsss:deliveryControls> 元素
type DeliveryControls struct {
	XMLName                xml.Name `xml:"deliveryControls"`            // XML元素的名称，通常是"deliveryControls"
	Tracked                bool     `xml:"tracked,attr"`                // 是否跟踪
	CompletionSetByContent bool     `xml:"completionSetByContent,attr"` // 完成状态是否由内容设置
	ObjectiveSetByContent  bool     `xml:"objectiveSetByContent,attr"`  // 目标状态是否由内容设置
}

// RollupRules 结构体表示 <imsss:rollupRules> 元素
type RollupRules struct {
	XMLName                  xml.Name     `xml:"rollupRules"` // XML元素的名称，通常是"rollupRules"
	RollupObjectiveSatisfied bool         `xml:"rollupObjectiveSatisfied,attr"`
	RollupProgressCompletion bool         `xml:"rollupProgressCompletion,attr"`
	ObjectiveMeasureWeight   float64      `xml:"objectiveMeasureWeight,attr"`
	RollupRule               []RollupRule `xml:"rollupRule"` // 汇总规则列表
}

// RollupRule 结构体表示 <imsss:rollupRule> 元素
type RollupRule struct {
	XMLName          xml.Name         `xml:"rollupRule"`            // XML元素的名称，通常是"rollupRule"
	ChildActivitySet string           `xml:"childActivitySet,attr"` // 子活动集 (all, any, none, atLeastCount)
	MinimumCount     *int             `xml:"minimumCount,attr"`     // 最小计数
	MinimumPercent   *float64         `xml:"minimumPercent,attr"`   // 最小百分比
	RollupConditions RollupConditions `xml:"rollupConditions"`      // 汇总条件
	RollupAction     RollupAction     `xml:"rollupAction"`          // 汇总动作
}

// RollupConditions 结构体表示 <imsss:rollupConditions> 元素
type RollupConditions struct {
	XMLName              xml.Name          `xml:"rollupConditions"`          // XML元素的名称，通常是"rollupConditions"
	ConditionCombination string            `xml:"conditionCombination,attr"` // 条件组合方式 (any 或 all)
	RollupCondition      []RollupCondition `xml:"rollupCondition"`           // 汇总条件列表
}

// RollupCondition 结构体表示 <imsss:rollupCondition> 元素
type RollupCondition struct {
	XMLName   xml.Name `xml:"rollupCondition"` // XML元素的名称，通常是"rollupCondition"
	Operator  string   `xml:"operator,attr"`   // 操作符 (noOp, not)
	Condition string   `xml:"condition,attr"`  // 条件
}

// RollupAction 结构体表示 <imsss:rollupAction> 元素
type RollupAction struct {
	XMLName xml.Name `xml:"rollupAction"` // XML元素的名称，通常是"rollupAction"
	Action  string   `xml:"action,attr"`  // 动作类型 (satisfied, notSatisfied, completed, notCompleted)
}

// RollupConsideration 结构体表示 <imsss:rollupConsideration> 元素
type RollupConsideration struct {
	XMLName                xml.Name `xml:"rollupConsideration"`         // XML元素的名称，通常是"rollupConsideration"
	RequiredForSatisfied   string   `xml:"requiredForSatisfied,attr"`   // 满足所需 (always, never, ifNotSuspended, ifNotSkipped)
	RequiredForUnsatisfied string   `xml:"requiredForUnsatisfied,attr"` // 不满足所需 (always, never, ifNotSuspended, ifNotSkipped)
	RequiredForCompleted   string   `xml:"requiredForCompleted,attr"`   // 完成所需 (always, never, ifNotSuspended, ifNotSkipped)
	RequiredForIncomplete  string   `xml:"requiredForIncomplete,attr"`  // 未完成所需 (always, never, ifNotSuspended, ifNotSkipped)
}

// RollupObjective 结构体表示 <imsss:rollupObjective> 元素
type RollupObjective struct {
	XMLName            xml.Name `xml:"rollupObjective"`         // XML元素的名称，通常是"rollupObjective"
	ObjectiveID        string   `xml:"objectiveID,attr"`        // 目标ID
	SatisfiedByMeasure bool     `xml:"satisfiedByMeasure,attr"` // 是否通过测量满足
	MeasureWeight      float64  `xml:"measureWeight,attr"`      // 测量权重
}

// RollupProgress 结构体表示 <imsss:rollupProgress> 元素
type RollupProgress struct {
	XMLName        xml.Name `xml:"rollupProgress"`      // XML元素的名称，通常是"rollupProgress"
	ProgressWeight float64  `xml:"progressWeight,attr"` // 进度权重
}

// ConstrainedChoiceConsiderations 结构体表示 <adlseq:constrainedChoiceConsiderations> 元素
type ConstrainedChoiceConsiderations struct {
	XMLName           xml.Name `xml:"http://www.adlnet.org/xsd/adlseq_v1p3 constrainedChoiceConsiderations"`
	PreventActivation bool     `xml:"preventActivitation,attr"` // 阻止激活
	ConstrainChoice   bool     `xml:"constrainChoice,attr"`     // 限制选择
}

// ADLSeqRollupConsiderations 结构体表示 <adlseq:rollupConsiderations> 元素
type ADLSeqRollupConsiderations struct {
	XMLName                     xml.Name `xml:"http://www.adlnet.org/xsd/adlseq_v1p3 rollupConsiderations"`
	RequiredForSatisfied        string   `xml:"requiredForSatisfied,attr"`        // 满足所需
	RequiredForNotSatisfied     string   `xml:"requiredForNotSatisfied,attr"`     // 不满足所需
	RequiredForCompleted        string   `xml:"requiredForCompleted,attr"`        // 完成所需
	RequiredForIncomplete       string   `xml:"requiredForIncomplete,attr"`       // 未完成所需
	MeasureSatisfactionIfActive bool     `xml:"measureSatisfactionIfActive,attr"` // 如果活动则测量满意度
}

// ADLSeqObjectives 结构体表示 <adlseq:objectives> 元素
type ADLSeqObjectives struct {
	XMLName    xml.Name          `xml:"http://www.adlnet.org/xsd/adlseq_v1p3 objectives"`
	Objectives []ADLSeqObjective `xml:"objective"` // ADL扩展目标列表
}

// ADLSeqObjective 结构体表示 <adlseq:objective> 元素
type ADLSeqObjective struct {
	XMLName     xml.Name        `xml:"http://www.adlnet.org/xsd/adlseq_v1p3 objective"`
	ObjectiveID string          `xml:"objectiveId,attr"` // 目标ID (必需)
	MapInfo     []ADLSeqMapInfo `xml:"mapInfo"`          // 映射信息列表
}

// ADLSeqMapInfo 结构体表示 <adlseq:mapInfo> 元素
type ADLSeqMapInfo struct {
	XMLName               xml.Name `xml:"http://www.adlnet.org/xsd/adlseq_v1p3 mapInfo"`
	TargetObjectiveID     string   `xml:"targetObjectiveId,attr"`     // 目标目标ID
	ReadRawScore          bool     `xml:"readRawScore,attr"`          // 读取原始分数
	ReadMinScore          bool     `xml:"readMinScore,attr"`          // 读取最小分数
	ReadMaxScore          bool     `xml:"readMaxScore,attr"`          // 读取最大分数
	ReadCompletionStatus  bool     `xml:"readCompletionStatus,attr"`  // 读取完成状态
	ReadProgressMeasure   bool     `xml:"readProgressMeasure,attr"`   // 读取进度测量
	WriteRawScore         bool     `xml:"writeRawScore,attr"`         // 写入原始分数
	WriteMinScore         bool     `xml:"writeMinScore,attr"`         // 写入最小分数
	WriteMaxScore         bool     `xml:"writeMaxScore,attr"`         // 写入最大分数
	WriteCompletionStatus bool     `xml:"writeCompletionStatus,attr"` // 写入完成状态
	WriteProgressMeasure  bool     `xml:"writeProgressMeasure,attr"`  // 写入进度测量
}

// ADLCPData 结构体表示 <adlcp:data> 元素
type ADLCPData struct {
	XMLName xml.Name   `xml:"http://www.adlnet.org/xsd/adlcp_v1p3 data"`
	Map     []ADLCPMap `xml:"map"` // 映射信息列表
}

// ADLCPMap 结构体表示 <adlcp:map> 元素
type ADLCPMap struct {
	XMLName         xml.Name `xml:"http://www.adlnet.org/xsd/adlcp_v1p3 map"`
	TargetID        string   `xml:"targetID,attr"`        // 目标ID
	ReadSharedData  bool     `xml:"readSharedData,attr"`  // 读取共享数据
	WriteSharedData bool     `xml:"writeSharedData,attr"` // 写入共享数据
}
