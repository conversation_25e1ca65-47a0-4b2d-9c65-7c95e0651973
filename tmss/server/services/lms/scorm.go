package lms

// scorm.go 文件实现了 SCORM 2004 标准的学习记录服务。
// 它负责处理学习者与 SCORM 课件的交互，包括初始化学习会话、
// 设置和获取学习数据（CMI数据模型）、以及结束学习会话。
// 同时，也包含了课件列表的获取和内容服务功能。

import (
	"context"
	"encoding/json"
	"encoding/xml"
	"errors"
	"fmt"
	"io/ioutil"
	"log/slog"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"sync"
	"time"
	"tms/model"
	"tms/pkg/db"
	"tms/utils"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"gorm.io/gorm"
)

type SessionContent struct {
	ScormSessions *model.ScormSessions
	LastError     string // 用于存储最近一次API调用的错误代码
}

// ScormService 结构体定义了 SCORM 服务。
// 它包含了处理 SCORM 运行时数据和逻辑所需的上下文信息。
type ScormService struct {
	Content map[uint]*SessionContent
	mu      sync.Mutex // mu 用于保护 Content 的并发访问
}

// NewScormService 是 ScormService 的构造函数。
// 它创建一个新的 ScormService 实例。
// 这个服务遵循 SCORM 2004 标准，用于管理学习者与 SCORM 课件的交互。
func NewScormService() *ScormService {
	return &ScormService{
		Content: make(map[uint]*SessionContent),
	}
}

// Initialize 初始化学习记录。
// 当用户开始学习一个 SCORM 课件时，此函数会被调用。
// 它会检查是否已有该用户和课件的学习记录，如果没有则创建新记录，
// 如果有则更新尝试次数和最后访问时间。
//
// 参数:
//
//	ctx: 请求上下文，用于传递请求范围的数据。
//	userID: 学习者的用户ID。
//	scoID: 课件的ID。
//
// 返回:
//
//	如果初始化成功，返回学习记录和 nil；否则返回 nil 和相应的错误。
func (s *ScormService) Initialize(ctx context.Context, userID int64, scoID uint) (*model.ScormSessions, error) {
	if userID == 0 || scoID == 0 {
		return nil, errors.New("用户ID或ScoID缺失")
	}

	var record model.ScormSessions
	// 查找现有记录：尝试从数据库中查找该用户和课件的学习记录。
	result := db.DB.WithContext(ctx).Where("learner_id = ? AND sco_id = ?", cast.ToString(userID), scoID).First(&record)

	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			// 第一次学习此课件，创建新记录：如果记录不存在，则创建一个新的学习记录。
			manifest, err := s.getScormMetadata(scoID)
			if err != nil {
				slog.Warn("获取课件元数据失败", "scoID", scoID, "error", err)
				return nil, errors.New("获取课件元数据失败") // Changed this line to return error
			}

			// Build the activity tree
			activityTree, err := s.BuildActivityTree(manifest, cast.ToString(userID), scoID)
			if err != nil {
				slog.Warn("构建活动树失败", "error", err)
				return nil, errors.New("构建活动树失败")
			}

			// Serialize the activity tree to JSON for storage in CMI field
			treeJSON, err := json.Marshal(activityTree)
			if err != nil {
				slog.Warn("序列化活动树失败", "error", err)
				return nil, errors.New("序列化活动树失败")
			}

			var courseware model.Courseware
			if err := db.DB.WithContext(ctx).Where("id = ?", cast.ToInt64(scoID)).First(&courseware).Error; err != nil {
				slog.Warn("获取课件信息失败", "error", err)
				return nil, errors.New("获取课件信息失败")
			}

			record = model.ScormSessions{
				CourseID:          courseware.CourseID,
				ChapterID:         courseware.ChapterID,
				LearnerID:         cast.ToString(userID),
				ScoID:             scoID,
				AttemptNumber:     1,                // 首次尝试，尝试次数设为1
				CompletionStatus:  "not attempted",  // 初始完成状态为“未尝试”
				SuccessStatus:     "unknown",        // 初始成功状态为“未知”
				LessonStatus:      "not attempted",  // 初始课程状态为“未尝试”
				CMI:               string(treeJSON), // Store the serialized activity tree
				InitialAccessTime: time.Now().Unix(),
				LastAccessTime:    time.Now().Unix(), // 记录最后访问时间
			}
			if err := db.DB.WithContext(ctx).Create(&record).Error; err != nil {
				slog.Error("创建学习记录失败", "error", err)
				return nil, errors.New("创建学习记录失败")
			}
		} else {
			// 查询学习记录失败：如果查询过程中发生其他错误，则返回错误。
			return nil, errors.New("查询学习记录失败")
		}
	} else {
		// 不是第一次学习，更新尝试次数和最后访问时间：如果记录已存在，则更新尝试次数和最后访问时间。
		record.AttemptNumber++
		record.LastAccessTime = time.Now().Unix()

		// Deserialize the activity tree from the CMI field
		var activityTree ActivityTree
		if err := json.Unmarshal([]byte(record.CMI), &activityTree); err != nil {
			slog.Error("反序列化活动树失败", "error", err)
			return nil, errors.New("反序列化活动树失败")
		}

		// TODO: Update the activity tree with any new session-specific data if needed.
		// For example, if the manifest has changed, you might need to re-sync the tree.

		// Re-serialize the activity tree back to CMI field after updates
		updatedTreeJSON, err := json.Marshal(activityTree)
		if err != nil {
			slog.Error("重新序列化活动树失败", "error", err)
			return nil, errors.New("重新序列化活动树失败")
		}
		record.CMI = string(updatedTreeJSON)

		if err := db.DB.WithContext(ctx).Save(&record).Error; err != nil {
			slog.Error("更新学习记录失败", "error", err)
			return nil, err
		}
	}

	s.mu.Lock()
	defer s.mu.Unlock()
	s.Content[record.ID] = &SessionContent{
		ScormSessions: &record,
		LastError:     "",
	}

	slog.Info("学习记录初始化成功")
	return &record, nil
}

var scormReadOnlyElements = map[string]bool{
	// SCORM 2004 4th Edition CMI 数据模型只读元素
	"cmi._version":                                true, // SCORM 版本号
	"cmi.comments_from_lms._children":             true, // LMS 发送给学习者的评论的子元素列表
	"cmi.comments_from_lms._count":                true, // LMS 发送给学习者的评论数量
	"cmi.completion_threshold":                    true, // 完成阈值，表示完成课程所需的最低分数或进度
	"cmi.credit":                                  true, // 学习者是否获得学分 (credit/no-credit)
	"cmi.entry":                                   true, // 学习者进入课件的方式 (ab-initio/resume/normal)
	"cmi.launch_data":                             true, // 启动课件时传递给课件的数据
	"cmi.learner_id":                              true, // 学习者的唯一标识符
	"cmi.learner_name":                            true, // 学习者的姓名
	"cmi.learner_preference._children":            true, // 学习者偏好的子元素列表
	"cmi.learner_preference.audio_level":          true, // 学习者的音频级别
	"cmi.learner_preference.language":             true, // 学习者的语言
	"cmi.learner_preference.delivery_speed":       true, // 学习者的交互速度
	"cmi.learner_preference.audio_captioning":     true, // 学习者的音频配音
	"cmi.max_time_allowed":                        true, // 允许学习者在课件中花费的最大时间
	"cmi.mode":                                    true, // 学习者进入课件的模式 (browse/normal/review)
	"cmi.scaled_passing_score":                    true, // 课件的通过分数（已缩放）
	"cmi.time_limit_action":                       true, // 达到时间限制时的行为 (continue,message/continue,no message/exit,message/no message)
	"cmi.total_time":                              true, // 学习者在课件中累积花费的总时间
	"cmi.objectives._children":                    true, // 目标的子元素列表
	"cmi.objectives._count":                       true, // 目标的数量
	"cmi.interactions._children":                  true, // 交互的子元素列表
	"cmi.interactions._count":                     true, // 交互的数量
	"cmi.interactions.n.objectives._count":        true, // 交互的目标数量
	"cmi.interactions.n.correct_responses._count": true, // 交互的正确响应数量
	"adl.data._count":                             true, // ADL 数据的数量
}

// scormVocabulary 定义了特定 CMI 元素合法词汇的集合 (SCORM 2004 4th Edition)。
var scormVocabulary = map[string]map[string]bool{
	"cmi.completion_status": { // 学习者完成课件的状态
		"completed":     true, // 已完成
		"incomplete":    true, // 未完成
		"not attempted": true, // 未尝试
		"unknown":       true, // 未知
	},
	"cmi.success_status": { // 学习者在课件中的成功状态
		"passed":  true, // 通过
		"failed":  true, // 未通过
		"unknown": true, // 未知
	},
	"cmi.exit": { // 学习者退出课件的方式
		"time-out": true, // 超时退出
		"suspend":  true, // 挂起，下次可恢复
		"logout":   true, // 登出
		"normal":   true, // 正常退出
		"":         true, // 空字符串也是一个合法的 normal 退出
	},
	// ... 更多词汇表可以根据需要添加
}

// SetValue 设置 SCORM 数据模型元素的值。
// 这是 SCORM 运行时 API (RTE) 的核心函数之一，允许课件向 LMS 提交学习数据。
//
// 参数:
//
//	scormSession: 当前的 SCORM 会话记录。
//	key: SCORM CMI (Content Aggregation Model) 数据元素的路径，例如 "cmi.completion_status"。
//	value: 要设置的数据值。
//
// 返回:
//
//	如果设置成功，返回 nil；否则返回相应的错误。
func (s *ScormService) SetValue(scormSession *model.ScormSessions, key, value string) error {
	s.mu.Lock()
	defer s.mu.Unlock()
	scormSession = s.Content[scormSession.ID].ScormSessions

	// 1. 检查是否为只读元素
	if scormReadOnlyElements[key] {
		s.Content[scormSession.ID].LastError = "404" // Data Model Element Is Read Only
		return errors.New("data model element is read only")
	}

	// 2. 验证特定词汇表的元素
	if vocab, ok := scormVocabulary[key]; ok {
		if !vocab[value] {
			s.Content[scormSession.ID].LastError = "407" // Data Model Element Value Out Of Range
			return fmt.Errorf("value '%s' is not a valid vocabulary for key '%s'", value, key)
		}
	}

	// 解析 CMI JSON 字符串：将存储在数据库中的 CMI JSON 字符串解析为 Go 的 map 结构，以便操作。
	var activityTree ActivityTree
	var cmiData map[string]interface{}
	if err := json.Unmarshal([]byte(scormSession.CMI), &activityTree); err != nil {
		slog.Error("CMI数据解析失败", "error", err)
		s.Content[scormSession.ID].LastError = "351" // General Set Failure
		return err
	}
	cmiData = activityTree.CMIData

	// 更新 CMI 数据：调用辅助函数 updateCMIValue 来更新 CMI 数据中的特定键值。
	// 简单的键值对更新，实际SCORM数据模型可能需要更复杂的路径解析和验证。
	updateCMIValue(cmiData, key, value)
	activityTree.CMIData = cmiData

	// 将更新后的 CMI 数据转回 JSON 字符串：将修改后的 CMI map 重新序列化为 JSON 字符串，以便存储。
	updatedCMI, err := json.Marshal(activityTree)
	if err != nil {
		slog.Error("CMI数据序列化失败", "error", err)
		s.Content[scormSession.ID].LastError = "351" // General Set Failure
		return err
	}
	scormSession.CMI = string(updatedCMI)

	// 根据 key 更新 ScormRecord 的特定字段：
	// SCORM CMI 数据模型中的一些常用字段直接映射到 ScormRecord 结构体的字段，
	// 这样可以方便地进行数据库操作和快速访问。
	switch key {
	case "cmi.completion_status": // cmi.completion_status: 学习者完成课件的状态 (completed/incomplete/not attempted/unknown)
		scormSession.CompletionStatus = value
	case "cmi.success_status": // cmi.success_status: 学习者在课件中的成功状态 (passed/failed/unknown)
		scormSession.SuccessStatus = value
	case "cmi.lesson_status": // cmi.lesson_status: 学习者在课件中的课程状态 (在 SCORM 2004 中已弃用，但为了兼容性仍处理)
		scormSession.LessonStatus = value
	case "cmi.location": // cmi.location: 学习者在课件中最后访问的位置
		// SCORM 2004 4th Edition 中 cmi.location 允许设置为空字符串
		// if value == "" {
		// 	s.Content[scormSession.ID].LastError = "406" // Data Model Element Type Mismatch
		// 	return errors.New("location value cannot be empty")
		// }

		scormSession.Location = value
	case "cmi.score.raw", "cmi.score.min", "cmi.score.max", "cmi.score.scaled": // cmi.score.*: 分数相关
		score, err := strconv.ParseFloat(value, 64)
		if err != nil {
			s.Content[scormSession.ID].LastError = "406" // Data Model Element Type Mismatch
			return fmt.Errorf("invalid score value '%s', must be a number", value)
		}
		// SCORM 2004 4th Ed. 要求 score.raw 在 0-100 之间 (scaled)，但这里我们允许更广范围
		if score < 0 { // 简单验证
			s.Content[scormSession.ID].LastError = "407" // Data Model Element Value Out Of Range
			return fmt.Errorf("score value '%f' cannot be negative", score)
		}
		switch key {
		case "cmi.score.raw":
			scormSession.ScoreRaw = int(score)
		case "cmi.score.min":
			scormSession.ScoreMin = int(score)
		case "cmi.score.max":
			scormSession.ScoreMax = int(score)
		case "cmi.score.scaled":
			scormSession.ScoreScaled = float64(score)
		}
	case "cmi.session_time":
		scormSession.SessionTime = value
	case "cmi.suspend_data": // cmi.suspend_data: 课件用于保存和恢复学习者状态的挂起数据
		if len(value) > 64000 { // SCORM 2004 4th Ed. 建议的最大长度
			s.Content[scormSession.ID].LastError = "407" // Data Model Element Value Out Of Range
			return errors.New("suspend_data exceeds the recommended maximum length of 64000 characters")
		}
		scormSession.SuspendData = value
	case "cmi.exit": // cmi.exit: 学习者退出课件的方式
		scormSession.Exit = value
	case "cmi.credit":
		scormSession.Credit = value
	case "cmi.mode":
		scormSession.Mode = value
	// ... 其他可写字段的处理
	default:
		// 对于其他未明确处理的字段，我们已经通过 updateCMIValue 更新了 CMI JSON。
		// 这里可以添加日志记录，以观察课件正在设置哪些不常见的字段。
		slog.Debug("Un-typed CMI element set", "key", key, "value", value)
	}

	// 更新最后访问时间：每次 SetValue 操作都更新学习记录的最后访问时间。
	scormSession.LastAccessTime = time.Now().Unix()

	// 将更新后的学习记录保存到数据库。
	if err := db.DB.Save(scormSession).Error; err != nil {
		slog.Error("更新学习记录失败", "error", err)
		s.Content[scormSession.ID].LastError = "351" // General Set Failure
		return err
	}

	slog.Info("SetValue 成功", "key", key, "value", value)
	s.Content[scormSession.ID].LastError = "0" // No Error

	s.Content[scormSession.ID].ScormSessions = scormSession
	return nil
}

// updateCMIValue 递归更新 CMI 数据（map[string]interface{} 类型）中的值。
// 这个函数支持通过点分隔的路径（例如 "cmi.score.raw"）来访问和更新嵌套的 CMI 数据。
//
// 参数:
//
//	data: 存储 CMI 数据的 map。
//	keyPath: 要更新的 CMI 数据元素的路径，使用点分隔。
//	value: 要设置的新值。
func updateCMIValue(data map[string]interface{}, keyPath string, value string) {
	// 将键路径分割成多个部分，例如 "cmi.score.raw" 分割为 ["cmi", "score", "raw"]。
	parts := splitKeyPath(keyPath)
	if len(parts) == 0 {
		return // 如果路径为空，则直接返回
	}

	current := data // 从根数据 map 开始
	for i, part := range parts {
		if i == len(parts)-1 {
			// 如果是路径的最后一个部分，则直接设置值。
			current[part] = value
			return
		}
		// 如果当前部分在 map 中不存在，则创建一个新的 map。
		if _, ok := current[part]; !ok {
			current[part] = make(map[string]interface{})
		}
		// 尝试将当前部分的值转换为 map[string]interface{} 类型，以便继续遍历嵌套结构。
		if next, ok := current[part].(map[string]interface{}); ok {
			current = next // 移动到下一个嵌套的 map
		} else {
			// 如果路径中间的某个部分不是 map 类型，则无法继续更新，直接返回。
			return
		}
	}
}

// splitKeyPath 将 "cmi.score.raw" 这样的点分隔路径字符串分割成字符串切片，
// 例如 "cmi.score.raw" 会被分割成 ["cmi", "score", "raw"]。
// 这个函数是辅助函数，用于解析 SCORM CMI 数据元素的路径。
//
// 参数:
//
//	keyPath: 点分隔的键路径字符串。
//
// 返回:
//
//	一个字符串切片，包含路径的各个部分。
func splitKeyPath(keyPath string) []string {
	return strings.Split(keyPath, ".")
}

var scormWriteOnlyElements = map[string]bool{
	// SCORM 2004 4th Edition CMI 数据模型只写元素
	"cmi.comments_from_learner.n.comment":   true, // 学习者评论内容
	"cmi.comments_from_learner.n.location":  true, // 学习者评论位置
	"cmi.comments_from_learner.n.timestamp": true, // 学习者评论时间戳
	"cmi.exit":                              true, // 学习者退出课件的方式
	"cmi.interactions.n.id":                 true, // 交互的唯一标识符
	"cmi.interactions.n.learner_response":   true, // 学习者对交互的响应
	"cmi.interactions.n.result":             true, // 交互的结果
	"cmi.interactions.n.type":               true, // 交互的类型
	"cmi.interactions.n.weighting":          true, // 交互的权重
	"cmi.interactions.n.latency":            true, // 交互的延迟时间
	"cmi.interactions.n.description":        true, // 交互的描述
	"cmi.interactions.n.timestamp":          true, // 交互的时间戳
	"cmi.interactions.n.objectives.m.id":    true, // 目标的唯一标识符
	"cmi.objectives.n.id":                   true, // 目标的唯一标识符
	"cmi.session_time":                      true, // 本次学习会话持续时间
	"cmi.suspend_data":                      true, // 课件用于保存和恢复学习者状态的挂起数据
	"adl.data.n.store":                      true, // ADL 数据的存储路径
}

// GetValue 获取 SCORM 数据模型元素的值。
// 这是 SCORM 运行时 API (RTE) 的核心函数之一，允许课件从 LMS 请求学习数据。
//
// 参数:
//
//	scormSession: 当前的 SCORM 会话记录。
//	key: SCORM CMI 数据元素的路径，例如 "cmi.completion_status"。
//
// 返回:
//
//	如果成功获取到值，返回数据值的字符串表示和 nil 错误；否则返回空字符串和相应的错误。
func (s *ScormService) GetValue(scormSession *model.ScormSessions, key string) (string, error) {
	s.mu.Lock()
	defer s.mu.Unlock()
	scormSession = s.Content[scormSession.ID].ScormSessions

	if key == "" {
		s.Content[scormSession.ID].LastError = "301" // General Get Failure
		return "", errors.New("key is missing")
	}

	// 1. 检查是否为只写元素
	if scormWriteOnlyElements[key] {
		s.Content[scormSession.ID].LastError = "405" // Data Model Element Is Write Only
		slog.Warn("尝试从只写元素获取值", "key", key)
		return "", nil
	}

	// 2. 动态处理 _count
	if strings.HasSuffix(key, "._count") {
		var cmiData map[string]interface{}
		var activityTree ActivityTree
		if err := json.Unmarshal([]byte(scormSession.CMI), &activityTree); err != nil {
			s.Content[scormSession.ID].LastError = "301"
			return "", err
		}
		cmiData = activityTree.CMIData

		// 获取父路径，例如 "cmi.objectives"
		parentKey := strings.TrimSuffix(key, "._count")
		if data, ok := getCMIValue(cmiData, parentKey).(map[string]interface{}); ok {
			count := len(data)
			s.Content[scormSession.ID].LastError = "0"
			return strconv.Itoa(count), nil
		}
		// 如果父路径不存在，则数量为0
		s.Content[scormSession.ID].LastError = "0"
		return "0", nil
	}

	// 3. 根据 key 返回 ScormRecord 的特定字段
	// 对于一些常用的 SCORM CMI 字段，直接从 ScormRecord 结构体中获取，以提高效率。
	switch key {
	case "cmi.completion_status":
		s.Content[scormSession.ID].LastError = "0"
		return scormSession.CompletionStatus, nil
	case "cmi.success_status":
		s.Content[scormSession.ID].LastError = "0"
		return scormSession.SuccessStatus, nil
	case "cmi.location":
		if scormSession.Location == "" {
			// s.Content[scormSession.ID].LastError = "403" // Not Initialized
			s.Content[scormSession.ID].LastError = "0"
			return "", nil
		}
		s.Content[scormSession.ID].LastError = "0"
		return scormSession.Location, nil
	case "cmi.score.raw":
		s.Content[scormSession.ID].LastError = "0"
		return strconv.Itoa(scormSession.ScoreRaw), nil
	case "cmi.score.min":
		s.Content[scormSession.ID].LastError = "0"
		return strconv.Itoa(scormSession.ScoreMin), nil
	case "cmi.score.max":
		s.Content[scormSession.ID].LastError = "0"
		return strconv.Itoa(scormSession.ScoreMax), nil
	case "cmi.score.scaled":
		s.Content[scormSession.ID].LastError = "0"
		return cast.ToString(scormSession.ScoreScaled), nil
	case "cmi.total_time":
		s.Content[scormSession.ID].LastError = "0"
		return scormSession.TotalTime, nil
	case "cmi.learner_id":
		s.Content[scormSession.ID].LastError = "0"
		return scormSession.LearnerID, nil
	case "cmi.learner_name":
		s.Content[scormSession.ID].LastError = "0"
		return scormSession.LearnerName, nil
	case "cmi.mode":
		s.Content[scormSession.ID].LastError = "0"
		return scormSession.Mode, nil
	case "cmi.credit":
		s.Content[scormSession.ID].LastError = "0"
		return scormSession.Credit, nil
	case "cmi._version":
		s.Content[scormSession.ID].LastError = "0"
		return "1.0", nil // SCORM 2004 4th Edition version
	// ... 其他频繁访问的只读字段
	default:
		// 对于其他字段，尝试从 CMI JSON 数据中获取
		var cmiData map[string]interface{}
		var activityTree ActivityTree
		if err := json.Unmarshal([]byte(scormSession.CMI), &activityTree); err != nil {
			slog.Error("解析 CMI JSON 数据失败", "error", err)
			s.Content[scormSession.ID].LastError = "301" // General Get Failure
			return "", err
		}
		cmiData = activityTree.CMIData
		value := getCMIValue(cmiData, key)
		if value != nil {
			s.Content[scormSession.ID].LastError = "0" // 无错误
			return fmt.Sprintf("%v", value), nil
		}

		// 如果在 CMI JSON 中也找不到，则该元素未定义
		s.Content[scormSession.ID].LastError = "401" // Undefined Data Model Element
		slog.Warn("未找到对应的数据", "key", key)
		return "", errors.New("undefined data model element")
	}
}

// getCMIValueAsString 从 CMI JSON 数据中获取值并转换为字符串。
func getCMIValueAsString(cmiJSON string, keyPath string) string {
	var cmiData map[string]interface{}
	var activityTree ActivityTree
	if err := json.Unmarshal([]byte(cmiJSON), &activityTree); err != nil {
		slog.Error("解析 CMI JSON 数据失败", "error", err)
		return ""
	}
	cmiData = activityTree.CMIData
	value := getCMIValue(cmiData, keyPath)
	if value != nil {
		return fmt.Sprintf("%v", value)
	}
	return ""
}

// Terminate 结束学习会话。
// 当学习者退出 SCORM 课件时，此函数会被调用。
// 它负责保存最终的学习记录数据，并清理相关的上下文信息。
//
// 返回:
//
//	如果会话结束成功，返回 nil；否则返回相应的错误。
func (s *ScormService) Terminate(scormSession *model.ScormSessions) error {
	s.mu.Lock()
	defer s.mu.Unlock()
	scormSession = s.Content[scormSession.ID].ScormSessions

	// Capture the LastAccessTime *before* updating it for the current termination.
	// This represents the start of the current session's duration.
	initialAccessTime := scormSession.InitialAccessTime

	// 更新最后访问时间：在会话结束时，更新学习记录的最后访问时间。
	now := time.Now()
	scormSession.LastAccessTime = now.Unix() // Update LastAccessTime for the termination event

	// 解析 CMI JSON 数据以便进行读写
	var cmiData map[string]interface{}
	var activityTree ActivityTree
	if err := json.Unmarshal([]byte(scormSession.CMI), &activityTree); err != nil {
		slog.Error("Terminate: CMI数据解析失败", "error", err)
		return err
	}
	cmiData = activityTree.CMIData

	// 1. 计算 cmi.session_time
	// SCORM 2004 要求 session_time 是本次会话的持续时间
	var sessionSeconds int64
	// Use the captured previousLastAccessTime for calculation
	sessionSeconds = now.Unix() - initialAccessTime
	slog.Info("会话持续时间", "now", now.Unix(), "initialAccessTime", initialAccessTime, "sessionSeconds", sessionSeconds)

	sessionTimeStr := utils.FormatISODuration(sessionSeconds)
	updateCMIValue(cmiData, "cmi.session_time", sessionTimeStr)

	// 2. 更新 cmi.total_time
	// total_time 是所有 session_time 的累积
	totalTimeStr := getCMIValueAsString(scormSession.CMI, "cmi.total_time")
	totalSeconds, _ := utils.ParseISODuration(totalTimeStr)
	totalSeconds += sessionSeconds
	scormSession.TotalTime = utils.FormatISODuration(totalSeconds) // 更新主结构字段
	slog.Info("总持续时间", "totalSeconds", totalSeconds)
	updateCMIValue(cmiData, "cmi.total_time", scormSession.TotalTime)

	// 3. 根据 cmi.exit 的值更新学习状态 (suspend, normal, logout 等)
	// SCORM 规范定义了根据 cmi.exit 的值来处理学习状态的逻辑，
	// 例如，"suspend" 表示学习者暂停学习，下次可以从上次离开的地方继续。
	exitStatus := getCMIValueAsString(scormSession.CMI, "cmi.exit")
	scormSession.Exit = exitStatus // 更新主结构字段

	switch exitStatus {
	case "suspend":
		// 如果是挂起，下次进入时应为 "resume"
		scormSession.Entry = "resume"
	case "normal", "":
		// 正常退出，下次进入时为新的开始
		scormSession.Entry = ""
	case "logout":
		// 登出，行为通常与 normal 类似，但 LMS 层面可以有额外处理
		scormSession.Entry = ""
		slog.Info("学员请求登出", "UserID", scormSession.LearnerID)
	case "time-out":
		// 超时退出
		scormSession.Entry = ""
	default:
		// 其他情况，默认为正常退出
		scormSession.Entry = ""
	}
	updateCMIValue(cmiData, "cmi.entry", scormSession.Entry)

	activityTree.CMIData = cmiData
	// 将更新后的 CMI map 重新序列化为 JSON 字符串
	updatedCMI, err := json.Marshal(activityTree)
	if err != nil {
		slog.Error("Terminate: CMI数据序列化失败", "error", err)
		return err
	}
	scormSession.CMI = string(updatedCMI)

	// 将更新后的学习记录保存到数据库。
	if err := db.DB.Save(scormSession).Error; err != nil {
		slog.Error("更新学习记录失败", "error", err)
		return err
	}

	s.Content[scormSession.ID].ScormSessions = scormSession

	slog.Info("Terminate 成功")
	return nil
}

// Commit 指示 LMS 持久化所有数据。
// 这是一个可选的函数，用于在会话结束前强制 LMS 保存所有已报告的数据。
//
// 返回:
//
//	如果提交成功，返回 nil；否则返回相应的错误。
func (s *ScormService) Commit(scormSession *model.ScormSessions) error {
	s.mu.Lock()
	defer s.mu.Unlock()
	if scormSession == nil {
		s.Content[scormSession.ID].LastError = "142" // Commit Before Initialization
		return errors.New("Commit Before Initialization")
	}

	// 更新最后访问时间：在提交时，更新学习记录的最后访问时间。
	scormSession.LastAccessTime = time.Now().Unix()

	if err := db.DB.Save(&scormSession).Error; err != nil {
		slog.Error("提交学习记录失败", "err", err)
		s.Content[scormSession.ID].LastError = "391" // General Commit Failure
		return err
	}

	slog.Info("Commit 成功")
	s.Content[scormSession.ID].LastError = "0" // No Error
	return nil
}

// GetLastError 返回上次 API 调用产生的错误代码。
//
// 返回:
//
//	上次 API 调用产生的错误代码的字符串表示。
func (s *ScormService) GetLastError(sessionID uint) string {
	s.mu.Lock()
	defer s.mu.Unlock()
	if content, ok := s.Content[sessionID]; ok {
		return content.LastError
	}
	return "" // 如果 sessionID 不存在，返回空字符串
}

// GetErrorString 返回指定错误代码的简短描述字符串。
//
// 参数:
//
//	errorCode: 要查询的错误代码。
//
// 返回:
//
//	错误代码的简短描述字符串。
func (s *ScormService) GetErrorString(errorCode string) string {
	switch errorCode {
	case "0":
		return "No Error"
	case "101":
		return "General Exception"
	case "102":
		return "General Initialization Failure"
	case "103":
		return "Already Initialized"
	case "104":
		return "Content Instance Terminated"
	case "111":
		return "General Termination Failure"
	case "112":
		return "Termination Before Initialization"
	case "113":
		return "Termination After Termination"
	case "122":
		return "Retrieve Data Before Initialization"
	case "123":
		return "Retrieve Data After Termination"
	case "132":
		return "Store Data Before Initialization"
	case "133":
		return "Store Data After Termination"
	case "142":
		return "Commit Before Initialization"
	case "143":
		return "Commit After Termination"
	case "201":
		return "General Argument Error"
	case "301":
		return "General Get Failure"
	case "351":
		return "General Set Failure"
	case "391":
		return "General Commit Failure"
	case "401":
		return "Undefined Data Model Element"
	case "402":
		return "Unimplemented Data Model Element"
	case "403":
		return "Data Model Element Value Not Initialized"
	case "404":
		return "Data Model Element Is Read Only"
	case "405":
		return "Data Model Element Is Write Only"
	case "406":
		return "Data Model Element Type Mismatch"
	case "407":
		return "Data Model Element Value Out Of Range"
	case "408":
		return "Data Model Dependency Not Established"
	default:
		return "Unknown Error"
	}
}

// GetDiagnostic 返回指定错误代码的详细诊断信息。
//
// 参数:
//
//	errorCode: 要查询的错误代码。
//
// 返回:
//
//	错误代码的详细诊断信息字符串。
func (s *ScormService) GetDiagnostic(errorCode string) string {
	// 这里可以根据 errorCode 返回更详细的诊断信息，目前简单返回与 GetErrorString 相同的内容
	return s.GetErrorString(errorCode)
}

// getScormMetadata 根据 scoID 获取 SCORM 课件的元数据
func (s *ScormService) getScormMetadata(scoID uint) (*Manifest, error) {
	coursewareDir := filepath.Join("data", "scorm")
	coursewareID := cast.ToInt64(scoID)
	var courseware model.Courseware
	if err := db.DB.Where("id = ?", coursewareID).First(&courseware).Error; err != nil {
		return nil, fmt.Errorf("failed to find courseware for scoID %d: %w", scoID, err)
	}
	manifestPath := filepath.Join(coursewareDir, cast.ToString(courseware.ChapterID), cast.ToString(courseware.ID), "imsmanifest.xml")

	if _, err := os.Stat(manifestPath); os.IsNotExist(err) {
		return nil, fmt.Errorf("manifest for scoID %d not found, path: %s", scoID, manifestPath)
	}

	manifestContent, err := ioutil.ReadFile(manifestPath)
	if err != nil {
		return nil, fmt.Errorf("failed to read manifest for scoID %d: %w", scoID, err)
	}

	var manifest Manifest
	if err := xml.Unmarshal(manifestContent, &manifest); err != nil {
		return nil, fmt.Errorf("failed to unmarshal manifest for scoID %d: %w", scoID, err)
	}

	return &manifest, nil
}

// getCMIValue 递归获取 CMI 数据（map[string]interface{} 类型）中的值。
// 这个函数支持通过点分隔的路径（例如 "cmi.score.raw"）来访问和获取嵌套的 CMI 数据。
//
// 参数:
//
//	data: 存储 CMI 数据的 map。
//	keyPath: 要获取的 CMI 数据元素的路径，使用点分隔。
//
// 返回:
//
//	如果找到对应的值，返回该值（interface{} 类型）；否则返回 nil。
func getCMIValue(data map[string]interface{}, keyPath string) interface{} {
	// 将键路径分割成多个部分。
	parts := splitKeyPath(keyPath)
	if len(parts) == 0 {
		return nil // 如果路径为空，则直接返回 nil
	}

	current := data // 从根数据 map 开始
	for i, part := range parts {
		if i == len(parts)-1 {
			// 如果是路径的最后一个部分，则返回对应的值。
			return current[part]
		}
		// 尝试将当前部分的值转换为 map[string]interface{} 类型，以便继续遍历嵌套结构。
		if next, ok := current[part].(map[string]interface{}); ok {
			current = next // 移动到下一个嵌套的 map
		} else {
			// 如果路径中间的某个部分不是 map 类型，则无法继续，返回 nil。
			return nil
		}
	}
	return nil // 理论上不会执行到这里，除非 keyPath 为空或处理逻辑有误
}

func (s *ScormService) GetCourseContent(c *gin.Context) {
	//把 data / course / $(courseId) / res 下的所有文件和文件夹设置为fs => 301 error
}

// containsDotDot 检查路径中是否包含 ".."
func containsDotDot(path string) bool {
	parts := filepath.SplitList(path)
	for _, part := range parts {
		if part == ".." {
			return true
		}
	}
	return false
}
