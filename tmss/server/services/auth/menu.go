package auth

import (
	"sort"
	"tms/model"
)

type MenuTree struct {
	*model.Menus
	Children []*MenuTree `json:"children"`
}

func BuildMenuTree(menus []*model.Menus) []*MenuTree {
	// 创建菜单ID到菜单树节点的映射
	menuMap := make(map[int64]*MenuTree)

	// 第一次遍历：初始化所有节点
	for _, menu := range menus {
		menuMap[menu.ID] = &MenuTree{
			Menus:    menu,
			Children: make([]*MenuTree, 0),
		}
	}

	// 第二次遍历：构建树结构
	var rootNodes []*MenuTree
	for _, menu := range menus {
		node := menuMap[menu.ID]

		if menu.ParentID == 0 {
			// 根节点
			rootNodes = append(rootNodes, node)
		} else {
			// 查找父节点
			if parent, ok := menuMap[menu.ParentID]; ok {
				parent.Children = append(parent.Children, node)
			}
		}
	}

	// 排序函数 - 按 OrderNum 升序排列
	sortMenu := func(menuSlice []*MenuTree) {
		sort.Slice(menuSlice, func(i, j int) bool {
			return menuSlice[i].OrderNum < menuSlice[j].OrderNum
		})
	}

	// 对根节点排序
	sortMenu(rootNodes)

	// 对所有子节点排序
	for _, node := range menuMap {
		if len(node.Children) > 0 {
			sortMenu(node.Children)
		}
	}

	return rootNodes
}
