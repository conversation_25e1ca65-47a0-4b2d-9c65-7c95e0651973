package auth

import (
	"errors"
	"fmt"
	"log"
	"time"
	"tms/model"
	"tms/pkg/cache"
	"tms/pkg/db"
	"tms/utils"

	"github.com/gin-gonic/gin"
)

// LoginResult 登录结果结构体
type LoginResult struct {
	User            model.Users             `json:"user"`
	Menus           []*MenuTree             `json:"menus"`
	Token           string                  `json:"token"`
	Roles           []model.Roles           `json:"roles"`
	UserCode        string                  `json:"user_code"`
	SysCode         string                  `json:"sys_code"`
	RolePermissions []model.RolePermissions `json:"role_permissions,omitempty"`
	Major           *model.Majors           `json:"major,omitempty"`
	Class           *model.Class            `json:"class,omitempty"`
	UserRef         *model.StudentMap       `json:"user_ref,omitempty"`
}

// Login 用于处理用户登录请求
//
// 参数：
// account - 用户账号
// password - 用户密码
// c - gin.Context 上下文
//
// 返回值：
// *LoginResult - 登录结果
// error - 错误信息
func Login(account, password, userCode string, c *gin.Context) (*LoginResult, error) {
	var user model.Users
	if err := db.DB.Where("account = ?", account).First(&user).Error; err != nil {
		log.Printf("Failed to get user: %v", err)
		return nil, errors.New("用户不存在")
	}
	if utils.HashPassword(password, user.Salt) != user.Password {
		return nil, errors.New("密码错误")
	}

	// 获取用户角色
	var userRoleCodes []string
	if err := db.DB.Model(&model.UserRoles{}).Where("user_id = ?", user.ID).Pluck("role_code", &userRoleCodes).Error; err != nil {
		log.Printf("Failed to get user roles: %v", err)
		return nil, errors.New("获取用户角色失败")
	}
	if len(userRoleCodes) == 0 {
		return nil, errors.New("用户未分配角色")
	}
	if userCode == "" || !utils.HasContainsStrings(userRoleCodes, userCode) {
		userCode = userRoleCodes[0]
	}
	var roles []model.Roles
	queryRoles := db.DB.Where("role_code in (?)", userRoleCodes)
	if c.Request.Header.Get("X-App-Key") == "" {
		queryRoles = queryRoles.Select("id", "role_code", "role_name", "sys_code")
	}
	if err := queryRoles.Find(&roles).Error; err != nil {
		log.Printf("Failed to get user roles: %v", err)
		return nil, errors.New("获取用户角色失败")
	}
	var sysCode string
	for _, role := range roles {
		if role.RoleCode == userCode {
			sysCode = role.SysCode
			break
		}
	}
	// userRoleIDS := make([]int64, len(roles))
	// for i, role := range roles {
	// 	userRoleIDS[i] = role.ID
	// }
	token, err := GetToken(user.ID, userRoleCodes, userCode)
	if err != nil {
		log.Printf("Failed to get token: %v", err)
		return nil, errors.New("获取令牌失败")
	}

	// 获取菜单
	// 检查 userRoleCodes 是否包含 "all"
	hasAllRole := false
	for _, roleCode := range userRoleCodes {
		if roleCode == "all" {
			hasAllRole = true
			break
		}
	}

	var menuIDS []int64
	if hasAllRole {
		// 获取所有菜单 ID
		if err := db.DB.Model(&model.Menus{}).Pluck("id", &menuIDS).Error; err != nil {
			log.Printf("Failed to get all menu IDs: %v", err)
			return nil, errors.New("获取菜单失败")
		}
	} else {
		// 否则只获取用户角色对应的菜单
		if err := db.DB.Model(&model.RoleMenu{}).Where("role_code = ?", userCode).Pluck("menu_id", &menuIDS).Error; err != nil {
			log.Printf("Failed to get menu: %v", err)
			return nil, errors.New("获取菜单失败")
		}
	}

	menus := make([]*model.Menus, 0)
	if len(menuIDS) > 0 {
		if err := db.DB.Where("id in (?)", menuIDS).Find(&menus).Error; err != nil {
			log.Printf("Failed to get menu: %v", err)
			return nil, errors.New("获取菜单失败")
		}
	}

	loginResult := &LoginResult{
		User:     user,
		Menus:    BuildMenuTree(menus),
		Token:    token,
		Roles:    roles,
		UserCode: userCode,
		SysCode:  sysCode,
	}

	if c.Request.Header.Get("X-App-Key") != "" {
		// 获取用户权限
		var rolePermissions []model.RolePermissions
		if err := db.DB.Where("role_code in (?)", userRoleCodes).Find(&rolePermissions).Error; err != nil {
			log.Printf("Failed to get role permissions: %v", err)
			return nil, errors.New("获取角色权限失败")
		}

		loginResult.RolePermissions = rolePermissions

		// 获取专业和所属班级
		var userRef model.StudentMap
		if err := db.DB.Where("user_id = ?", user.ID).First(&userRef).Error; err == nil {
			var major model.Majors
			if err := db.DB.Where("id = ?", userRef.MajorID).First(&major).Error; err != nil {
				log.Printf("Failed to get major: %v", err)
				return nil, errors.New("获取专业失败")
			}
			var class model.Class
			if err := db.DB.Where("id = ?", userRef.ClassID).First(&class).Error; err != nil {
				log.Printf("Failed to get class: %v", err)
				return nil, errors.New("获取班级失败")
			}
			loginResult.Major = &major
			loginResult.Class = &class
			loginResult.UserRef = &userRef
		}
	}

	loginResult.User.Salt = ""
	loginResult.User.Password = ""

	return loginResult, nil
}
func GetToken(userId int64, roleCode []string, userCode string) (string, error) {
	has := false
	if len(roleCode) == 0 {
		return "", fmt.Errorf("角色不存在")
	}
	if userCode == "" {
		return "", fmt.Errorf("用户角色不存在")
	}
	if userId == 0 {
		return "", fmt.Errorf("用户不存在")
	}
	for _, v := range roleCode {
		if v == userCode {
			has = true
			break
		}
	}
	if !has {
		return "", fmt.Errorf("角色不存在")
	}
	claims := utils.UserClaims{
		ID:        userId,
		RoleCodes: roleCode, // 保留原有角色列表
		UserCode:  userCode, // 更新当前角色
	}
	newToken, err := utils.GenerateToken(&claims)
	if err != nil {
		log.Printf("Failed to generate token: %v", err)
		return "", fmt.Errorf("生成token失败")
	}

	// 更新缓存中的token
	key := fmt.Sprintf("%d-token", userId)
	cache.CacheStorage.Set(key, newToken, 24*time.Hour)
	return newToken, nil
}
func ChangeRoles(userCode string) ([]*MenuTree, error) {
	var menuIDS []int64
	if err := db.DB.Model(&model.RoleMenu{}).Where("role_code = ?", userCode).Pluck("menu_id", &menuIDS).Error; err != nil {
		log.Printf("Failed to get menu: %v", err)
		return nil, errors.New("获取菜单失败")
	}
	menus := make([]*model.Menus, 0)
	if len(menuIDS) > 0 {
		if err := db.DB.Where("id in (?)", menuIDS).Find(&menus).Error; err != nil {
			log.Printf("Failed to get menu: %v", err)
			return nil, errors.New("获取菜单失败")
		}
	}
	return BuildMenuTree(menus), nil
}

// Logout 处理用户登出的业务逻辑
func Logout(c *gin.Context) error {
	// session := sessions.Default(c)
	// session.Delete("userId")
	// if err := session.Save(); err != nil {
	// 	log.Printf("Failed to save session: %v", err)
	// 	return errors.New("保存会话失败")
	// }
	userId := c.GetInt64("userId")
	key := fmt.Sprintf("%d-token", userId)
	cache.CacheStorage.Delete(key)
	return nil
}
