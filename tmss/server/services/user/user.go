package user

import (
	"errors"
	"fmt"
	"log"
	"time"
	"tms/model"
	"tms/pkg/db"
	"tms/utils"

	"gorm.io/gorm"
)

type UserList struct {
	User  model.Users   `json:"user_info"`
	Roles []model.Roles `json:"roles" gorm:"many2many:user_roles;"`
	Class model.Class   `json:"class_info"`
	Major model.Majors  `json:"major_info"`
}
type UserListRes struct {
	Users []UserList    `json:"users"`
	Roles []model.Roles `json:"roles"`
}
type UpdateUser struct {
	User      model.Users `json:"user_info"`
	RoleCodes []string    `json:"role_codes"`
}

// 获取所有用户
func (s *UserService) GetAllUsers() []model.Users {
	var users []model.Users
	if err := db.DB.Find(&users).Error; err != nil {
		log.Printf("Failed to get all users: %v", err)
		return nil
	}

	return users
}
func CheckUserExists(userID int64) (bool, error) {
	var user model.Users
	err := db.DB.Where("id = ?", userID).First(&user).Error
	return err == nil, err

}
func GetUsersByIDs(userIDs []int64) ([]model.Users, error) {
	var users []model.Users
	if err := db.DB.Where("id IN (?)", userIDs).Find(&users).Error; err != nil {
		return nil, fmt.Errorf("查询用户失败: %v", err)
	}
	return users, nil
}
func GetUserByID(userID int64) (model.Users, error) {
	var user model.Users
	if err := db.DB.Where("id = ?", userID).First(&user).Error; err != nil {
		return model.Users{}, fmt.Errorf("查询用户失败: %v", err)
	}
	return user, nil
}
func GetRoleByCode(roleCodes []string, roles []model.Roles) []model.Roles {
	var res []model.Roles
	for _, role := range roles {
		for _, code := range roleCodes {
			if role.RoleCode == code {
				res = append(res, role)
			}
		}
	}
	return res
}
func GetUserRoleCode(userId int64, userRoles []model.UserRoles) []string {
	var roleCodes []string
	for _, userRole := range userRoles {
		if userRole.UserID == userId {
			roleCodes = append(roleCodes, userRole.RoleCode)
		}
	}
	return roleCodes
}
func (s *UserService) GetUserList(list []model.Users) UserListRes {
	var res UserListRes
	var userIds []int64
	for _, user := range list {
		userIds = append(userIds, user.ID)
	}
	var userRoles []model.UserRoles
	if err := db.DB.Model(&model.UserRoles{}).Where("user_id in (?)", userIds).Find(&userRoles).Error; err != nil {
		log.Printf("Failed to get user roles: %v", err)
		return res
	}
	var roles []model.Roles
	if err := db.DB.Model(&model.Roles{}).Find(&roles).Error; err != nil {
		log.Printf("Failed to get roles: %v", err)
		return res
	}
	var userMaps []model.StudentMap
	if err := db.DB.Model(&model.StudentMap{}).Where("user_id in (?)", userIds).Find(&userMaps).Error; err != nil {
		log.Printf("Failed to get user maps: %v", err)
		return res
	}
	//构建 userID → StudentMap 映射
	userMap := make(map[int64]model.StudentMap)
	var classIds []int64
	var majorIds []int64
	for _, um := range userMaps {
		userMap[um.UserID] = um
		classIds = append(classIds, um.ClassID)
		majorIds = append(majorIds, um.MajorID)
	}
	//构建 classID → Class 映射
	var classes []model.Class
	if err := db.DB.Model(&model.Class{}).
		Where("id IN (?)", classIds).
		Find(&classes).Error; err != nil {
		log.Printf("Failed to get classes: %v", err)
		return res
	}
	classMap := make(map[int64]model.Class)
	for _, c := range classes {
		classMap[c.ID] = c
	}
	//构建 majorID → Majors 映射
	var majors []model.Majors
	if err := db.DB.Model(&model.Majors{}).
		Where("id IN (?)", majorIds).
		Find(&majors).Error; err != nil {
		log.Printf("Failed to get majors: %v", err)
		return res
	}
	majorMap := make(map[int64]model.Majors)
	for _, m := range majors {
		majorMap[m.ID] = m
	}
	var userList []UserList
	for _, user := range list {
		u := UserList{User: user}
		roleCodes := GetUserRoleCode(user.ID, userRoles)
		roles := GetRoleByCode(roleCodes, roles)
		u.Roles = roles
		// 获取学生映射关系
		if sm, ok := userMap[user.ID]; ok {
			// 获取班级和专业信息
			if cls, ok := classMap[sm.ClassID]; ok {
				u.Class = cls
			}
			if maj, ok := majorMap[sm.MajorID]; ok {
				u.Major = maj
			}
		}

		userList = append(userList, u)
	}
	return UserListRes{Roles: roles, Users: userList}
}
func (s *UserService) GetUserIdsByRoleName(roleName string) ([]int64, error) {
	var roleCodes []string
	err := db.DB.Model(&model.Roles{}).Where("role_name LIKE ?", "%"+roleName+"%").Pluck("role_code", &roleCodes).Error
	if err != nil {
		log.Printf("Failed to get role codes: %v", err)
		return nil, errors.New("获取角色失败")
	}
	var userIds []int64
	err = db.DB.Model(&model.UserRoles{}).Where("role_code IN ?", roleCodes).Pluck("user_id", &userIds).Error
	if err != nil {
		log.Printf("Failed to get user IDs: %v", err)
		return nil, errors.New("获取用户失败")
	}
	return userIds, nil
}
func (s *UserService) GetUsersByClassName(className string) ([]int64, error) {
	var classIds []int64
	err := db.DB.Model(&model.Class{}).Where("name LIKE ?", "%"+className+"%").Pluck("id", &classIds).Error
	if err != nil {
		log.Printf("Failed to get classes: %v", err)
		return nil, errors.New("获取班级失败")
	}
	log.Printf("classIds: %v", classIds)
	return s.GetUsersByClassIDs(classIds)
}
func (s *UserService) GetUsersByMajorName(majorName string) ([]int64, error) {
	var majorIds []int64
	err := db.DB.Model(&model.Majors{}).Where("name LIKE ?", "%"+majorName+"%").Pluck("id", &majorIds).Error
	if err != nil {
		log.Printf("Failed to get majors: %v", err)
		return nil, errors.New("获取专业失败")
	}
	return s.GetUsersByMajorIDs(majorIds)
}
func (s *UserService) GetUsersByClassIDs(classIds []int64) ([]int64, error) {
	var userIds []int64
	err := db.DB.Model(&model.StudentMap{}).Where("class_id IN ?", classIds).Pluck("user_id", &userIds).Error
	if err != nil {
		log.Printf("Failed to get users by classes: %v", err)
		return nil, errors.New("获取用户失败")
	}
	return userIds, nil
}
func (s *UserService) GetUsersByMajorIDs(majorIds []int64) ([]int64, error) {
	var userIds []int64
	err := db.DB.Model(&model.StudentMap{}).Where("major_id IN ?", majorIds).Pluck("user_id", &userIds).Error
	if err != nil {
		log.Printf("Failed to get users by majors: %v", err)
		return nil, errors.New("获取用户失败")
	}
	return userIds, nil
}

// 按条件获取用户
func (s *UserService) GetUsersByCondition(scopes func(db *gorm.DB) *gorm.DB) []model.Users {
	var users []model.Users
	if err := db.DB.Scopes(scopes).Order("id desc").Find(&users).Error; err != nil {
		log.Printf("Failed to get users by condition: %v", err)
		return nil
	}

	return users
}

// 根据ID获取用户
func (s *UserService) GetUserByID(id int64) *UserList {
	var user model.Users
	if err := db.DB.First(&user, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil // 用户不存在
		}
		log.Printf("Failed to get user by ID %d: %v", id, err)
		return nil
	}
	var roleCodes []string
	err := db.DB.Model(&model.UserRoles{}).Where("user_id = ?", id).Pluck("role_code", &roleCodes).Error
	if err != nil {
		log.Printf("Failed to get user roles: %v", err)
		return nil
	}
	var roles []model.Roles
	if err := db.DB.Model(&model.Roles{}).Where("role_code in (?)", roleCodes).Find(&roles).Error; err != nil {
		log.Printf("Failed to get roles: %v", err)
		return nil
	}
	u := UserList{User: user, Roles: roles}
	return &u
}

// 创建新用户
func (s *UserService) CreateUser(user *UpdateUser) error {
	// 检验用户
	log.Printf("user info %v", user.User)
	if user.User.Username == "" || user.User.Password == "" || user.User.Account == "" {
		return gorm.ErrInvalidData
	}

	// 检查用户是否存在
	var existingUser *model.Users
	err := db.DB.Model(&model.Users{}).Where("account = ?", user.User.Account).First(&existingUser).Error
	if err == nil {
		log.Printf("Account %s already exists", user.User.Account)
		return fmt.Errorf("用户账户已存在")
	}
	if err != gorm.ErrRecordNotFound {
		log.Printf("Failed to check existing user: %v", err)
		return err
	}
	salt, err := utils.GenerateSalt(6)
	if err != nil {
		log.Printf("Failed to generate salt: %v", err)
		return err
	}
	user.User.Password = utils.HashPassword(user.User.Password, salt)
	user.User.Salt = salt
	// 创建用户
	if err := db.DB.Create(&user.User).Error; err != nil {
		log.Printf("Failed to create user: %v", err)
		return err
	}
	//创建用户关联角色，一个用户可以关联多个角色
	for _, roleCode := range user.RoleCodes {
		if err := db.DB.Create(&model.UserRoles{UserID: user.User.ID, RoleCode: roleCode}).Error; err != nil {
			return err
		}
	}
	err = s.SaveUserCodes(user.User.ID, user.RoleCodes)
	if err != nil {
		return err
	}
	return nil
}
func (s *UserService) GetUserSysCodes(roleCodes []string) ([]string, error) {
	var sysCodes []string
	err := db.DB.Model(&model.Roles{}).Where("role_code IN ?", roleCodes).Pluck("sys_code", &sysCodes).Error
	if err != nil {
		log.Printf("Failed to get system codes: %v", err)
		return nil, errors.New("获取系统代码失败")
	}
	// 手动去重
	unique := make(map[string]bool)
	var result []string
	for _, code := range sysCodes {
		if !unique[code] {
			unique[code] = true
			result = append(result, code)
		}
	}

	return result, nil
}
func (s *UserService) SaveUserCodes(userId int64, roleCodes []string) error {
	sysCodes, err := s.GetUserSysCodes(roleCodes)
	if err != nil {
		return err
	}
	err = db.DB.Model(&model.UserCodes{}).Where("user_id = ?", userId).Delete(&model.UserCodes{}).Error
	if err != nil {
		log.Printf("Failed to delete user codes: %v", err)
	}
	var saveData []model.UserCodes
	for _, code := range sysCodes {
		saveData = append(saveData, model.UserCodes{UserID: userId, SysCode: code})
	}
	if err := db.DB.Create(&saveData).Error; err != nil {
		return errors.New("更新用户权限失败")
	}
	return nil
}

// UpdateUser 更新用户
func (s *UserService) UpdateUser(user *UpdateUser) error {
	// 检查用户是否存在
	existingUser := s.GetUserByID(user.User.ID)
	if existingUser == nil {
		return gorm.ErrRecordNotFound // 用户不存在
	}

	// 检查账户、手机号或邮箱是否已存在（排除当前用户）
	if db.DB.Where("account = ? AND id != ?", user.User.Account, user.User.ID).First(&model.Users{}).Error == nil {
		return gorm.ErrDuplicatedKey // 账户已存在
	}
	if user.User.Phone != "" && db.DB.Where("phone = ? AND id != ?", user.User.Phone, user.User.ID).First(&model.Users{}).Error == nil {
		return gorm.ErrDuplicatedKey // 手机号已存在
	}
	if user.User.Email != "" && db.DB.Where("email = ? AND id != ?", user.User.Email, user.User.ID).First(&model.Users{}).Error == nil {
		return gorm.ErrDuplicatedKey // 邮箱已存在
	}
	updates := map[string]interface{}{
		"account":    user.User.Account,
		"username":   user.User.Username,
		"updated_at": time.Now().Unix(),
	}
	if user.User.Phone != "" {
		updates["phone"] = user.User.Phone
	}
	if user.User.Email != "" {
		updates["email"] = user.User.Email
	}
	if user.User.Password != "" {
		salt, err := utils.GenerateSalt(6)
		if err != nil {
			log.Printf("Failed to generate salt: %v", err)
			return err
		}
		updates["salt"] = salt
		updates["password"] = utils.HashPassword(user.User.Password, salt)
		user.User.Salt = salt
	}

	// 更新用户
	if err := db.DB.Model(&model.Users{}).
		Where("id = ?", user.User.ID).
		Updates(updates).Error; err != nil {
		log.Printf("Failed to update user: %v", err)
		return err
	}

	if len(user.RoleCodes) == 0 {
		return nil
	}

	//更新用户关联角色，一个用户可以关联多个角色
	if err := db.DB.Where("user_id = ?", user.User.ID).Delete(&model.UserRoles{}).Error; err != nil {
		return err
	}
	var userRoles []model.UserRoles
	for _, roleID := range user.RoleCodes {
		userRoles = append(userRoles, model.UserRoles{UserID: user.User.ID, RoleCode: roleID})
	}

	if err := db.DB.Create(&userRoles).Error; err != nil {
		return err
	}
	err := s.SaveUserCodes(user.User.ID, user.RoleCodes)
	if err != nil {
		return err
	}
	return nil
}

// DeleteUser 删除用户
func (s *UserService) DeleteUser(id int64) error {
	// 检查用户是否存在
	existingUser := s.GetUserByID(id)
	if existingUser == nil {
		return gorm.ErrRecordNotFound // 用户不存在
	}

	if err := db.DB.Delete(&model.Users{}, id).Error; err != nil {
		log.Printf("Failed to delete user by ID %d: %v", id, err)
		return err
	}
	// 删除用户关联角色
	if err := db.DB.Where("user_id = ?", id).Delete(&model.UserRoles{}).Error; err != nil {
		return err
	}
	// 删除用户关联的系统权限
	if err := db.DB.Where("user_id = ?", id).Delete(&model.UserCodes{}).Error; err != nil {
		return err
	}

	return nil
}

// 在 userService 中添加批量删除方法
func (s *UserService) BatchDeleteUsers(ids []int64) error {
	if len(ids) == 0 {
		return nil
	}

	result := db.DB.Where("id IN ?", ids).Delete(&model.Users{})
	if result.Error != nil {
		return result.Error
	}
	if err := db.DB.Where("user_id IN ?", ids).Delete(&model.UserRoles{}).Error; err != nil {
		return err
	}
	if err := db.DB.Where("user_id IN ?", ids).Delete(&model.UserCodes{}).Error; err != nil {
		return err
	}
	return nil
}

// 判断是否为user role
func (s *UserService) IsUserRole(id int64) bool {
	var exits model.UserCodes
	if db.DB.Where("user_id = ? AND sys_code = ?", id, model.AuthUser).First(&exits).Error == nil {
		return true
	} else {
		return false
	}
}

func (s *UserService) GetUsersBySearch(userQuery model.ReqUsersSearch) ([]model.UserEntity, int64, error) {
	// 构建查询范围
	scopes := func(db *gorm.DB) *gorm.DB {
		if userQuery.Username != "" {
			db = db.Where("username LIKE ?", "%"+userQuery.Username+"%")
		}

		return db
	}

	var count int64
	if err := db.DB.Model(&model.Users{}).Scopes(scopes).Count(&count).Error; err != nil {
		return nil, 0, err
	}

	var users []model.Users
	userListQuery := db.DB.Scopes(scopes).Order("id desc")
	if userQuery.Page > 0 && userQuery.PageSize > 0 {
		offset := (userQuery.Page - 1) * userQuery.PageSize
		userListQuery = userListQuery.Offset(offset).Limit(userQuery.PageSize)
	}
	if err := userListQuery.Omit("password", "salt").Find(&users).Error; err != nil {
		return nil, 0, err
	}

	if len(users) == 0 {
		return nil, count, nil
	}

	userIDs := make([]int64, len(users))
	for i, user := range users {
		userIDs[i] = user.ID
	}

	var userRoles []struct {
		UserID   int64       `gorm:"column:user_id"`
		RoleCode string      `gorm:"column:role_code"`
		Role     model.Roles `gorm:"embedded"`
	}

	if err := db.DB.Model(&model.UserRoles{}).
		Select("user_roles.user_id, user_roles.role_code, roles.*").
		Joins("left join roles on user_roles.role_code = roles.role_code").
		Where("user_roles.user_id IN (?)", userIDs).
		Find(&userRoles).Error; err != nil {
		return nil, 0, err
	}

	userRolesMap := make(map[int64][]model.Roles)
	for _, ur := range userRoles {
		if ur.Role.ID != 0 || ur.Role.RoleCode != "" {
			userRolesMap[ur.UserID] = append(userRolesMap[ur.UserID], ur.Role)
		}
	}

	var userEntities []model.UserEntity
	for _, user := range users {
		roles, ok := userRolesMap[user.ID]
		if !ok {
			roles = []model.Roles{}
		}
		user.Salt = ""
		userEntities = append(userEntities, model.UserEntity{
			Users: user,
			Roles: roles,
		})
	}

	return userEntities, count, nil
}
