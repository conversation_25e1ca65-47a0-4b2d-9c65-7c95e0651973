package user

import (
	"errors"
	"log"
	"tms/model"
	"tms/pkg/db"
)

// MenuNode 用于构建菜单树的结构体
type MenuNode struct {
	model.Menus
	Children []MenuNode `json:"children"`
	Level    int        `json:"level"` // 表示当前节点的层级
}
type ReqMenuRole struct {
	RoleCode string  `json:"role_code"`
	MenuIds  []int64 `json:"menu_ids" binding:"required"`
}

func (s *UserService) GetMenuList() ([]MenuNode, error) {
	var menus []model.Menus
	if err := db.DB.Model(&model.Menus{}).Order("order_num ASC").Find(&menus).Error; err != nil {
		log.Printf("Failed to get menu permissions: %v", err)
		return nil, errors.New("获取菜单权限失败")
	}
	menuTree := s.buildMenuTree(menus, 0)
	return menuTree, nil
}

// GetMenuPermissions 获取菜单权限
func (s *UserService) GetMenuRoles(sysCode, roleCode string) ([]MenuNode, []int64, error) {
	var menus []model.Menus
	var menuIds []int64
	if err := db.DB.Where("sys_code = ?", sysCode).Find(&menus).Error; err != nil {
		log.Printf("Failed to get menu permissions: %v", err)
		return nil, menuIds, errors.New("获取菜单权限失败")
	}
	menuTree := s.buildMenuTree(menus, 0)

	if roleCode != "" {
		if err := db.DB.Model(&model.RoleMenu{}).Where("role_code = ?", roleCode).Pluck("menu_id", &menuIds).Error; err != nil {
			log.Printf("Failed to get role menu permissions: %v", err)
		}
	}
	return menuTree, menuIds, nil
}
func (s *UserService) UpdateMenuRole(req ReqMenuRole) error {
	err := db.DB.Model(&model.RoleMenu{}).Where("role_code = ?", req.RoleCode).Delete(&model.RoleMenu{}).Error
	if err != nil {
		return errors.New("删除现有菜单失败")
	}
	if len(req.MenuIds) > 0 {
		var saveData []model.RoleMenu
		for _, id := range req.MenuIds {
			saveData = append(saveData, model.RoleMenu{
				RoleCode: req.RoleCode,
				MenuID:   id,
			})
		}
		if err := db.DB.Create(&saveData).Error; err != nil {
			return errors.New("更新菜单失败")
		}
	}
	return nil
}

// buildMenuTree 递归构建菜单树
// buildMenuTree 递归构建菜单树，并添加 level 字段
func (s *UserService) buildMenuTree(menus []model.Menus, parentID int64) []MenuNode {
	return s.buildMenuTreeWithLevel(menus, parentID, 0)
}

// buildMenuTreeWithLevel 递归构建菜单树并设置 level
func (s *UserService) buildMenuTreeWithLevel(menus []model.Menus, parentID int64, level int) []MenuNode {
	var tree []MenuNode
	for _, menu := range menus {
		if menu.ParentID == parentID {
			node := MenuNode{
				Menus:    menu,
				Children: []MenuNode{},
				Level:    level + 1, // 当前节点层级为父级+1
			}
			children := s.buildMenuTreeWithLevel(menus, menu.ID, node.Level)
			if len(children) > 0 {
				node.Children = children
			}
			tree = append(tree, node)
		}
	}
	return tree
}
