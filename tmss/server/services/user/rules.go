package user

import (
	"errors"
	"fmt"
	"log"
	"tms/common"
	"tms/middleware"
	"tms/model"
	"tms/pkg/db"
)

type ReqRules struct {
	RoleCode string                  `json:"role_code" binding:"required"`
	Data     []model.RolePermissions `json:"data" binding:"required"`
}

func (s *UserService) GetApiList(roleCode string, sysCode string) ([]common.TreeNode, []model.RolePermissions) {
	rootGroup := common.GetTreeRoutes()
	nodes := rootGroup.GetApiList(sysCode)
	var rolePermissions []model.RolePermissions
	if roleCode != "" {
		db.DB.Model(&model.RolePermissions{}).Where("role_code = ?", roleCode).Find(&rolePermissions)
	}
	return nodes, rolePermissions
}
func (s *UserService) SetApiList(req ReqRules) error {
	err := db.DB.Model(&model.RolePermissions{}).Where("role_code = ?", req.RoleCode).Delete(&model.RolePermissions{}).Error
	if err != nil {
		log.Printf("Failed to delete existing role permissions: %v", err)
		return errors.New("删除现有角色权限失败")
	}
	if len(req.Data) > 0 {
		err = db.DB.Create(&req.Data).Error
		if err != nil {
			return errors.New("创建角色权限失败" + err.Error())
		}
	}
	err = middleware.UpdateRolePermission(req.RoleCode, req.Data)
	if err != nil {
		return errors.New("更新角色权限失败")
	}
	return nil
}

func (s *UserService) SetDataConfig(roleCode string, ids []int64) error {
	// 1. 检查配置是否有效且无重复表代码
	tableCodeMap := make(map[string]bool)
	for _, id := range ids {
		config, err := GetConfigByID(id)
		if err != nil {
			return fmt.Errorf("配置ID %d 不存在", id)
		}

		// 检查表代码是否重复
		if tableCodeMap[config.TableCode] {
			return fmt.Errorf("表代码 '%s' 的配置已存在多个", config.TableCode)
		}
		tableCodeMap[config.TableCode] = true
	}

	// 2. 删除现有关联
	err := db.DB.Model(&model.RoleDataMap{}).Where("role_code = ?", roleCode).Delete(model.RoleDataConfig{}).Error
	if err != nil {
		return errors.New("删除现有角色数据权限失败")
	}

	// 3. 创建新关联
	saveData := make([]model.RoleDataMap, 0, len(ids))
	for _, id := range ids {
		saveData = append(saveData, model.RoleDataMap{ConfigID: id, RoleCode: roleCode})
	}

	if err = db.DB.Model(&model.RoleDataMap{}).Create(&saveData).Error; err != nil {
		return errors.New("创建角色数据权限失败")
	}
	// 更新角色配置缓存
	UpdateRoleConfigCache(roleCode, ids)
	return nil
}

// 新增辅助函数：根据ID获取配置
func GetConfigByID(id int64) (model.RoleDataConfig, error) {
	config, ok := GetConfigFromCache(id)
	if ok {
		return config, nil
	}
	return model.RoleDataConfig{}, fmt.Errorf("配置ID %d 不存在", id)
}

func (s *UserService) CreateDataConfig(data model.RoleDataConfig) error {
	data.ID = 0 // 重置ID，以便自动生成新ID
	existSame := &model.RoleDataConfig{}
	err := db.DB.Where("name = ?", data.Name).First(&existSame).Error
	if err == nil {
		return fmt.Errorf("配置已存在相同名称")
	}

	err = db.DB.Model(&model.RoleDataConfig{}).Create(&data).Error
	if err != nil {
		return err
	}

	// 更新缓存
	UpdateConfigCache(data)
	return nil
}

func (s *UserService) UpdateDataConfig(data model.RoleDataConfig) error {
	existSame := &model.RoleDataConfig{}
	err := db.DB.Where("name = ? AND id != ?", data.Name, data.ID).First(existSame).Error
	if err == nil {
		return fmt.Errorf("配置已存在相同名称或标志")
	}
	err = db.DB.Model(&model.RoleDataConfig{}).Where("id = ?", data.ID).Updates(&data).Error
	if err != nil {
		return err
	}

	// 更新缓存
	UpdateConfigCache(data)
	return nil
}

func (s *UserService) DeleteDataConfig(id int64) error {
	// 先删除关联
	err := db.DB.Model(&model.RoleDataMap{}).Where("config_id = ?", id).Delete(&model.RoleDataMap{}).Error
	if err != nil {
		return errors.New("删除角色关联失败")
	}

	// 再删除配置
	err = db.DB.Model(&model.RoleDataConfig{}).Where("id = ?", id).Delete(&model.RoleDataConfig{}).Error
	if err != nil {
		return errors.New("删除角色数据权限失败")
	}

	// 从缓存中删除
	DeleteConfigCache(id)
	return nil
}

// List 获取配置列表（支持分页和搜索）
func (s *UserService) DataConfigList(page, pageSize int, syscode string, search string) ([]model.RoleDataConfig, int64, error) {
	var configs []model.RoleDataConfig
	var total int64
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 || pageSize > 100 {
		pageSize = 15
	}
	query := db.DB.Model(&model.RoleDataConfig{}).Order("created_at desc")

	// 添加搜索条件
	if search != "" {
		query = query.Where("name LIKE ? OR table_code LIKE ?", "%"+search+"%", "%"+search+"%")
	}
	if syscode != "" {
		query = query.Where("sys_code = ?", syscode)
	}
	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * pageSize
	err := query.Offset(offset).Limit(pageSize).Find(&configs).Error

	return configs, total, err
}

// List 获取配置列表（支持分页和搜索）
// func (s *UserService) DataConfigList(page, pageSize int, search string) ([]model.RoleDataConfig, int64, error) {
// 	// 从缓存获取所有配置
// 	allConfigs := GetAllConfigsFromCache()
// 	sort.Slice(allConfigs, func(i, j int) bool {
// 		return allConfigs[i].CreatedAt > allConfigs[j].CreatedAt
// 	})
// 	// 应用搜索过滤
// 	var filtered []model.RoleDataConfig
// 	if search != "" {
// 		for _, config := range allConfigs {
// 			if strings.Contains(config.Name, search) ||
// 				strings.Contains(config.TableCode, search) ||
// 				strings.Contains(config.SysCode, search) {
// 				filtered = append(filtered, config)
// 			}
// 		}
// 	} else {
// 		filtered = allConfigs
// 	}

// 	total := int64(len(filtered))

// 	// 应用分页
// 	start := (page - 1) * pageSize
// 	if start > len(filtered) {
// 		return []model.RoleDataConfig{}, total, nil
// 	}
// 	end := start + pageSize
// 	if end > len(filtered) {
// 		end = len(filtered)
// 	}

// 	return filtered[start:end], total, nil
// }

func (s *UserService) GetDataConfig(roleCode, syscode string, page, pageSize int, search string) ([]int64, []model.RoleDataConfig, int64, error) {
	ids, ok := GetRoleConfigIDs(roleCode)
	if !ok {
		err := db.DB.Model(&model.RoleDataMap{}).Where("role_code = ?", roleCode).Pluck("config_id", &ids).Error
		if err != nil {
			return ids, nil, 0, err
		}
	}

	list, total, err := s.DataConfigList(page, pageSize, syscode, search)
	if err != nil {
		return ids, nil, 0, err
	}

	return ids, list, total, nil
}
func GetRoleDataConfigIds(roleCodes []string) ([]int64, error) {
	var ids []int64
	err := db.DB.Model(&model.RoleDataMap{}).Where("role_code IN ?", roleCodes).Pluck("config_id", &ids).Error
	return ids, err
}
