package user

import (
	"encoding/json"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"tms/model"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

const DataConfigCacheFile = "config/role_data_configs.json"
const InitDataConfigCacheFile = "config/role_data.json"

// 使用双层缓存结构
var (
	// 第一层: 配置ID -> 配置详情
	ConfigCache sync.Map
	// 第二层: 角色代码 -> 该角色的配置ID列表
	RoleConfigCache sync.Map
)

func InitDataConfigCache(db *gorm.DB) error {
	// 尝试从数据库加载配置
	var configs []model.RoleDataConfig
	if err := db.Find(&configs).Error; err != nil {
		return err
	}

	// 尝试从数据库加载映射关系
	var mappings []model.RoleDataMap
	if err := db.Find(&mappings).Error; err != nil {
		return err
	}

	// 如果数据库中没有数据，但缓存文件存在，则从缓存文件恢复数据到数据库
	if len(configs) == 0 && len(mappings) == 0 {
		if _, err := os.Stat(DataConfigCacheFile); err == nil {
			log.Println("数据库中没有数据，尝试从缓存文件恢复...")
			if err := restoreFromCacheFile(db); err != nil {
				log.Printf("从缓存文件恢复失败: %v", err)
			} else {
				log.Println("从缓存文件恢复成功")

				// 重新从数据库加载数据
				if err := db.Find(&configs).Error; err != nil {
					return err
				}
				if err := db.Find(&mappings).Error; err != nil {
					return err
				}
			}
		}
	}

	// 存储到配置缓存
	for _, config := range configs {
		ConfigCache.Store(config.ID, config)
	}

	// 构建角色配置缓存
	roleConfigMap := make(map[string][]int64)
	for _, mapping := range mappings {
		roleConfigMap[mapping.RoleCode] = append(roleConfigMap[mapping.RoleCode], mapping.ConfigID)
	}

	// 存储到角色缓存
	for roleCode, configIDs := range roleConfigMap {
		RoleConfigCache.Store(roleCode, configIDs)
	}

	// 保存到缓存文件
	SaveDataConfigCache()
	return nil
}

// 从缓存文件恢复数据到数据库
func restoreFromCacheFile(tx *gorm.DB) error {
	data, err := os.ReadFile(InitDataConfigCacheFile)
	if err != nil {
		return err
	}

	// 解析缓存文件
	var cacheData struct {
		Configs map[int64]model.RoleDataConfig `json:"configs"`
		Roles   map[string][]int64             `json:"roles"`
	}
	if err := json.Unmarshal(data, &cacheData); err != nil {
		return err
	}

	// 开始数据库事务

	// 批量插入配置数据
	var configsToInsert []model.RoleDataConfig
	for _, config := range cacheData.Configs {
		configsToInsert = append(configsToInsert, config)
	}

	if len(configsToInsert) > 0 {
		if err := tx.Create(configsToInsert).Error; err != nil {
			return fmt.Errorf("批量创建配置失败: %v", err)
		}
		// if err := model.FixSequence(db, "role_data_config"); err != nil {
		// 	tx.Rollback()
		// 	return fmt.Errorf("修复序列失败: %v", err)
		// }
	}

	// 批量插入角色-配置映射关系
	var mappingsToInsert []model.RoleDataMap
	for roleCode, configIDs := range cacheData.Roles {
		for _, configID := range configIDs {
			mappingsToInsert = append(mappingsToInsert, model.RoleDataMap{
				RoleCode: roleCode,
				ConfigID: configID,
			})
		}
	}

	if len(mappingsToInsert) > 0 {
		if err := tx.Create(mappingsToInsert).Error; err != nil {
			return fmt.Errorf("批量创建角色映射失败: %v", err)
		}
		// if err := model.FixSequence(db, "role_data_map"); err != nil {
		// 	tx.Rollback()
		// 	return fmt.Errorf("修复序列失败: %v", err)
		// }
	}

	// 提交事务
	return nil
}

func LoadDataConfigCache() {
	// 确保缓存目录存在
	if err := os.MkdirAll(filepath.Dir(DataConfigCacheFile), 0755); err != nil {
		log.Printf("创建缓存目录失败: %v", err)
		return
	}

	data, err := os.ReadFile(DataConfigCacheFile)
	if err != nil {
		if !os.IsNotExist(err) {
			log.Printf("读取缓存文件失败: %v", err)
		}
		return
	}

	// 解析缓存文件
	var cacheData struct {
		Configs map[int64]model.RoleDataConfig `json:"configs"`
		Roles   map[string][]int64             `json:"roles"`
	}
	if err := json.Unmarshal(data, &cacheData); err != nil {
		log.Printf("解析缓存文件失败: %v", err)
		return
	}

	// 加载到缓存
	for id, config := range cacheData.Configs {
		ConfigCache.Store(id, config)
	}
	for roleCode, configIDs := range cacheData.Roles {
		RoleConfigCache.Store(roleCode, configIDs)
	}
}

func SaveDataConfigCache() {
	// 构建配置缓存数据
	configMap := make(map[int64]model.RoleDataConfig)
	ConfigCache.Range(func(key, value interface{}) bool {
		id := key.(int64)
		config := value.(model.RoleDataConfig)
		configMap[id] = config
		return true
	})

	// 构建角色缓存数据
	roleMap := make(map[string][]int64)
	RoleConfigCache.Range(func(key, value interface{}) bool {
		roleCode := key.(string)
		configIDs := value.([]int64)
		roleMap[roleCode] = configIDs
		return true
	})

	// 组合缓存数据
	cacheData := struct {
		Configs map[int64]model.RoleDataConfig `json:"configs"`
		Roles   map[string][]int64             `json:"roles"`
	}{
		Configs: configMap,
		Roles:   roleMap,
	}

	data, err := json.MarshalIndent(cacheData, "", "  ")
	if err != nil {
		log.Printf("序列化缓存失败: %v", err)
		return
	}

	if err := os.WriteFile(DataConfigCacheFile, data, 0644); err != nil {
		log.Printf("写入缓存文件失败: %v", err)
	}
}

func GetConfigFromCache(id int64) (model.RoleDataConfig, bool) {
	value, ok := ConfigCache.Load(id)
	if !ok {
		return model.RoleDataConfig{}, false
	}
	config, ok := value.(model.RoleDataConfig)
	return config, ok
}

func UpdateConfigCache(config model.RoleDataConfig) {
	ConfigCache.Store(config.ID, config)
	SaveDataConfigCache()
}

func DeleteConfigCache(id int64) {
	ConfigCache.Delete(id)

	// 从所有角色的缓存中删除该配置ID
	RoleConfigCache.Range(func(key, value interface{}) bool {
		roleCode := key.(string)
		configIDs := value.([]int64)

		// 创建新列表，排除被删除的配置ID
		newIDs := make([]int64, 0, len(configIDs))
		for _, configID := range configIDs {
			if configID != id {
				newIDs = append(newIDs, configID)
			}
		}

		// 更新角色缓存
		RoleConfigCache.Store(roleCode, newIDs)
		return true
	})

	SaveDataConfigCache()
}

func GetAllConfigsFromCache() []model.RoleDataConfig {
	var configs []model.RoleDataConfig
	ConfigCache.Range(func(_, value interface{}) bool {
		configs = append(configs, value.(model.RoleDataConfig))
		return true
	})
	return configs
}

// 获取角色的配置ID列表
func GetRoleConfigIDs(roleCode string) ([]int64, bool) {
	value, ok := RoleConfigCache.Load(roleCode)
	if !ok {
		return nil, false
	}
	configIDs, ok := value.([]int64)
	return configIDs, ok
}

// 更新角色的配置缓存
func UpdateRoleConfigCache(roleCode string, configIDs []int64) {
	RoleConfigCache.Store(roleCode, configIDs)
	SaveDataConfigCache()
}

// 删除角色的配置缓存
func DeleteRoleConfigCache(roleCode string) {
	RoleConfigCache.Delete(roleCode)
	SaveDataConfigCache()
}
func ApplyDataRules(c *gin.Context, db *gorm.DB, tableCode string) (*gorm.DB, bool) {
	// 获取该表的所有配置ID
	config, has := GetRoleConfigByCode(c, tableCode)
	if !has {
		return db, false
	}

	// 应用所有符合条件的规则
	switch config.Type {
	case "condition":
		db = db.Where(config.Field+" = ?", config.Value)
	case "in":
		values := strings.Split(config.Value, ",")
		db = db.Where(config.Field+" IN (?)", values)
	case "like":
		db = db.Where(config.Field+" LIKE ?", "%"+config.Value+"%")
	case "self":
		userId := c.GetInt64("userId")
		if userId == 0 {
			return db, false
		}
		db = db.Where(config.Field+" = ?", userId)
	case "all":
		// 无限制，继续处理其他规则
	}
	return db, true
}

// 根据角色代码列表和表代码获取配置
func GetRoleConfigByCode(c *gin.Context, tableCode string) (model.RoleDataConfig, bool) {
	userCode := c.GetString("userCode")
	if userCode == "" {
		return model.RoleDataConfig{}, false
	}
	// 步骤1: 收集所有相关配置ID
	configIDs, ok := GetRoleConfigIDs(userCode)
	if !ok {
		return model.RoleDataConfig{}, false
	}

	// 步骤2: 从缓存获取所有配置
	allConfigs := GetAllConfigsFromCache()

	// 步骤3: 优先查找直接匹配的配置
	for _, config := range allConfigs {
		// 检查表代码匹配且ID在相关配置列表中
		if config.TableCode == tableCode {
			for _, id := range configIDs {
				if config.ID == id {
					return config, true
				}
			}
		}
	}

	// 步骤4: 查找默认配置
	for _, config := range allConfigs {
		// 检查是否为该表的默认配置
		if config.IsDefault && config.TableCode == tableCode {
			return config, true
		}
	}

	// 步骤5: 都没有找到，返回错误
	return model.RoleDataConfig{}, false
}

// func GetRoleConfigByIds(ids []int64, tableCode string) (model.RoleDataConfig, error) {
// 	// 创建ID映射用于快速查找
// 	idMap := make(map[int64]bool)
// 	for _, id := range ids {
// 		idMap[id] = true
// 	}

// 	// 从缓存获取所有配置
// 	allConfigs := GetAllConfigsFromCache()

// 	// 查找匹配的配置
// 	for _, config := range allConfigs {
// 		// 检查表代码匹配且ID在给定列表中
// 		if config.TableCode == tableCode && idMap[config.ID] {
// 			return config, nil
// 		}
// 	}
// 	// 如果没有找到直接匹配的配置，尝试查找默认配置
// 	for _, config := range allConfigs {
// 		// 检查是否为该表的默认配置
// 		if config.IsDefault && config.TableCode == tableCode {
// 			return config, nil
// 		}
// 	}
// 	// 最后，尝试查找任何匹配的配置（即使不是角色关联或默认）
// 	for _, config := range allConfigs {
// 		if config.TableCode == tableCode {
// 			return config, nil
// 		}
// 	}

// 	return model.RoleDataConfig{}, errors.New("未找到匹配的数据权限配置")
// }
