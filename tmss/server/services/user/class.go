package user

import (
	"errors"
	"tms/model"
	"tms/pkg/db"
)

func (s *UserService) SetStudentClassID(req model.ReqSetStudentClassID) error {
	if len(req.Ids) == 0 {
		return errors.New("用户ID列表不能为空")
	}
	// if req.ClassID == 0 {
	// 	return errors.New("班级ID不能为空")
	// }
	// if req.MajorID == 0 {
	// 	return errors.New("专业ID不能为空")
	// }
	if req.ClassID == 0 && req.MajorID == 0 {
		err := db.DB.Model(&model.StudentMap{}).Where("user_id in ?", req.Ids).Delete(&model.StudentMap{}).Error
		if err != nil {
			return errors.New("清空用户班级关系失败")
		}
	}
	if req.ClassID != 0 && req.MajorID != 0 {
		//先清空之前的班级信息
		err := db.DB.Model(&model.StudentMap{}).Where("user_id in ?", req.Ids).Delete(&model.StudentMap{}).Error
		if err != nil {
			return errors.New("清空用户班级关系失败")
		}
		var studentMaps []model.StudentMap
		for _, id := range req.Ids {
			studentMaps = append(studentMaps, model.StudentMap{UserID: id, ClassID: req.ClassID, MajorID: req.MajorID})
		}
		if err := db.DB.Create(&studentMaps).Error; err != nil {
			return errors.New("设置用户班级关系失败")
		}
	}

	return nil
}
