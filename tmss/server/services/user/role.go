package user

import (
	"errors"
	"log"
	"tms/middleware"
	"tms/model"
	"tms/pkg/db"
)

// GetRoles 获取所有角色
func (s *UserService) GetRoles() ([]model.Roles, error) {
	var roles []model.Roles
	if err := db.DB.Find(&roles).Error; err != nil {
		log.Printf("Failed to get roles: %v", err)
		return nil, errors.New("获取角色失败")
	}
	return roles, nil
}

// CreateRole 创建新角色
func (s *UserService) CreateRole(req *model.Roles) (*model.Roles, error) {
	// if !req.ValidRoleCode() {
	// 	return nil, errors.New("角色代码错误")
	// }

	// 检查角色名称或角色代码是否已存在
	if err := db.DB.Where("role_name = ? OR role_code = ?", req.RoleName, req.RoleCode).First(&model.Roles{}).Error; err == nil {
		return nil, errors.New("角色名称或角色代码已存在")
	}

	if err := db.DB.Create(&req).Error; err != nil {
		log.Printf("Failed to create role: %v", err)
		return nil, errors.New("创建角色失败")
	}

	return req, nil
}

// UpdateRole 更新角色
func (s *UserService) UpdateRole(roleID int64, req *model.Roles) (*model.Roles, error) {
	var existingRole model.Roles
	if err := db.DB.Where("id = ?", roleID).First(&existingRole).Error; err != nil {
		log.Printf("Failed to get existing role: %v", err)
		return nil, errors.New("角色不存在")
	}

	// 检查角色名称或角色代码是否已存在，但排除当前角色
	if err := db.DB.Where("(role_name = ? OR role_code = ?) AND id != ?", req.RoleName, req.RoleCode, roleID).First(&model.Roles{}).Error; err == nil {
		return nil, errors.New("角色名称或角色代码已存在")
	}

	if err := db.DB.Model(&existingRole).Updates(req).Error; err != nil {
		log.Printf("Failed to update role: %v", err)
		return nil, errors.New("更新角色失败")
	}

	return &existingRole, nil
}

// DeleteRole 删除角色
func (s *UserService) DeleteRole(roleID int64) error {
	var role model.Roles
	if err := db.DB.Where("id = ?", roleID).First(&role).Error; err != nil {
		log.Printf("Failed to get role for deletion: %v", err)
		return errors.New("角色不存在")
	}

	// 无法删除超级管理员角色
	if role.RoleCode == model.AuthAdmin && role.ID != 1 {
		return errors.New("无法删除超级管理员角色")
	}
	if role.IsSystem { // 系统角色不允许删除
		return errors.New("系统角色不允许删除")
	}

	// 检查是否有用户关联到该角色，如果有则不允许删除
	var count int64
	db.DB.Model(&model.UserRoles{}).Where("role_code = ?", role.RoleCode).Count(&count)
	if count > 0 {
		return errors.New("该角色下有用户关联，无法删除")
	}

	if err := db.DB.Delete(&role).Error; err != nil {
		log.Printf("Failed to delete role: %v", err)
		return errors.New("删除角色失败")
	}
	//删除菜单关联
	err := db.DB.Where("role_code = ?", role.RoleCode).Delete(&model.RoleMenu{}).Error
	if err != nil {
		log.Printf("Failed to delete role menu: %v", err)
	}
	err = middleware.DeleteRolePermission(role.RoleCode)
	if err != nil {
		return errors.New("删除角色权限失败")
	}
	return nil
}
