package exam

import (
	"errors"
	"log/slog"
	"time"

	"tms/model"
	"tms/pkg/db"
	"tms/services/user"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type ExamService struct{}

func NewExamService() *ExamService {
	return &ExamService{}
}

// CreateExamDraft 创建考试草稿，并关联班级、老师、试卷等信息
func (api *ExamService) CreateExamDraft(req model.ReqCreateExam, userID int64) error {
	if userID == 0 {
		return errors.New("无效的用户ID")
	}

	req.Exams.UserID = userID
	req.Exams.Status = model.ExamStatusDraft
	var paper model.Papers
	if err := db.DB.Where("id = ?", req.Exams.PaperID).First(&paper).Error; err != nil {
		return errors.New("无效的试卷ID")
	}
	req.Exams.TotalMinutes = paper.TotalMinutes
	req.Exams.CreatedAt = time.Now().Unix()
	if paper.TotalMinutes > 0 && req.StartAtStr != "" {
		startAt, err := time.ParseInLocation("2006-01-02 15:04", req.StartAtStr, time.Local)
		if err != nil {
			slog.Error("开始时间格式错误，应为 YYYY-MM-DD HH:mm", "err", err)
			return errors.New("开始时间格式错误，应为 YYYY-MM-DD HH:mm")
		}
		req.Exams.StartAt = startAt.Unix()
		req.Exams.EndAt = req.Exams.StartAt + paper.TotalMinutes*60
	}
	if req.Exams.StartAt == 0 || req.Exams.EndAt == 0 {
		return errors.New("开始时间不能为空")
	}

	tx := db.DB.Begin()
	defer tx.Rollback()

	// 创建考试
	if err := tx.Model(&model.Exams{}).Create(&req.Exams).Error; err != nil {
		tx.Rollback()
		return errors.New("创建失败: " + err.Error())
	}

	err := api.AddExts(req, tx)
	if err != nil {
		tx.Rollback()
		return err
	}

	tx.Commit()
	return nil
}
func (api *ExamService) AddExts(req model.ReqCreateExam, tx *gorm.DB) error {
	var exts []model.ExamsExt
	for _, classID := range req.ClassIDs {
		exts = append(exts, model.ExamsExt{
			ExamID:   req.Exams.ID,
			ExtKey:   "class",
			ExtValue: classID,
		})
	}
	for _, majorID := range req.MajorIDs {
		exts = append(exts, model.ExamsExt{
			ExamID:   req.Exams.ID,
			ExtKey:   "major",
			ExtValue: majorID,
		})
	}
	var students []int64
	dbQuery := tx.Model(&model.StudentMap{})
	if len(req.ClassIDs) > 0 {
		dbQuery = dbQuery.Where("class_id IN (?)", req.ClassIDs)
	}
	if len(req.MajorIDs) > 0 {
		dbQuery = dbQuery.Where("major_id IN (?)", req.MajorIDs)
	}

	err := dbQuery.Pluck("user_id", &students).Error
	if err != nil {
		return errors.New("获取学生失败: " + err.Error())
	}
	for _, studentID := range students {
		exts = append(exts, model.ExamsExt{
			ExamID:   req.Exams.ID,
			ExtKey:   "student",
			ExtValue: studentID,
		})
	}
	// 添加阅卷老师
	for _, teacherID := range req.TeacherIDs {
		exts = append(exts, model.ExamsExt{
			ExamID:   req.Exams.ID,
			ExtKey:   "teacher",
			ExtValue: teacherID,
		})
	}

	// 添加监考老师
	for _, supervisorID := range req.SupervisorIDs {
		exts = append(exts, model.ExamsExt{
			ExamID:   req.Exams.ID,
			ExtKey:   "supervisor",
			ExtValue: supervisorID,
		})
	}
	if err := tx.Model(&model.ExamsExt{}).Create(&exts).Error; err != nil {
		return errors.New("创建学生关联失败: " + err.Error())
	}
	return nil

}

// UpdateExamDraft 更新考试草稿及其关联信息
func (api *ExamService) UpdateExamDraft(req model.ReqCreateExam) error {
	if req.Exams.ID <= 0 {
		return errors.New("无效的考试ID")
	}

	var exam model.Exams
	if err := db.DB.Model(&model.Exams{}).First(&exam, req.Exams.ID).Error; err != nil {
		return errors.New("未找到对应考试")
	}

	if exam.Status != model.ExamStatusDraft && req.Exams.Status != model.ExamStatusRejected {
		return errors.New("只能编辑草稿")
	}
	var paper model.Papers
	if err := db.DB.Where("id = ?", req.Exams.PaperID).First(&paper).Error; err != nil {
		return errors.New("无效的试卷ID")
	}
	req.Exams.TotalMinutes = paper.TotalMinutes
	if req.Exams.TotalMinutes > 0 && req.StartAtStr != "" {
		startAt, err := time.ParseInLocation("2006-01-02 15:04", req.StartAtStr, time.Local)
		if err != nil {
			return errors.New("开始时间格式错误，应为 YYYY-MM-DD HH:mm")
		}
		req.Exams.StartAt = startAt.Unix()
		req.Exams.EndAt = req.Exams.StartAt + req.Exams.TotalMinutes*60
	}
	now := time.Now().Unix()
	req.Exams.UpdatedAt = now

	tx := db.DB.Begin()
	defer tx.Rollback()

	// 更新基础信息
	updates := map[string]interface{}{
		"name":           req.Exams.Name,
		"paper_id":       req.Exams.PaperID,
		"exam_type":      req.Exams.ExamType,
		"total_minutes":  req.Exams.TotalMinutes,
		"location":       req.Exams.Location,
		"is_certificate": req.Exams.Iscertificate,
		"start_at":       req.Exams.StartAt,
		"end_at":         req.Exams.EndAt,
		"updated_at":     now,
	}
	if err := tx.Model(&exam).Updates(updates).Error; err != nil {
		return errors.New("更新失败: " + err.Error())
	}

	// 删除旧的关联关系
	if err := tx.Where("exam_id = ?", req.Exams.ID).Delete(&model.ExamsExt{}).Error; err != nil {
		tx.Rollback()
		return errors.New("删除旧关联失败: " + err.Error())
	}

	err := api.AddExts(req, tx)
	if err != nil {
		tx.Rollback()
		return err
	}
	tx.Commit()
	return nil
}

// DeleteExam 删除考试（仅限草稿）
func (api *ExamService) DeleteExam(id int64) error {
	if id == 0 {
		return errors.New("无效的考试ID")
	}

	var exam model.Exams
	if err := db.DB.First(&exam, id).Error; err != nil {
		return errors.New("未找到对应考试")
	}

	if exam.Status != model.ExamStatusDraft {
		return errors.New("只能删除草稿")
	}

	tx := db.DB.Begin()
	defer tx.Rollback()

	if err := tx.Delete(&exam).Error; err != nil {
		return errors.New("删除考试失败: " + err.Error())
	}

	if err := tx.Where("exam_id = ?", id).Delete(&model.ExamsExt{}).Error; err != nil {
		return errors.New("删除关联失败: " + err.Error())
	}

	tx.Commit()
	return nil
}

// GetExamList 获取考试列表
func (api *ExamService) GetExamList(c *gin.Context, req model.ReqExamSearch) (model.RespExamList, error) {
	var res model.RespExamList
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 || req.PageSize > 100 {
		req.PageSize = 10
	}

	dbQuery := db.DB.Model(&model.Exams{})
	if req.Name != "" {
		dbQuery = dbQuery.Where("name ILIKE ?", "%"+req.Name+"%")
	}
	if req.Status != "" {
		dbQuery = dbQuery.Where("status = ?", req.Status)
	}
	// if req.UserID > 0 {
	// 	dbQuery = dbQuery.Where("user_id = ?", req.UserID)
	// }
	dbQuery, has := user.ApplyDataRules(c, dbQuery, model.ModuleSyllabus)
	if !has && req.UserID > 0 {
		dbQuery = dbQuery.Where("user_id = ?", req.UserID)
	}

	if err := dbQuery.Count(&res.Total).Error; err != nil {
		return res, errors.New("查询总数失败")
	}

	offset := (req.Page - 1) * req.PageSize
	if err := dbQuery.Offset(offset).Limit(req.PageSize).Order("created_at DESC").Find(&res.List).Error; err != nil {
		return res, errors.New("查询数据失败: " + err.Error())
	}

	return res, nil
}

// GetExamDetail 获取考试详情
func (api *ExamService) GetExamDetail(id int64) (*model.RespExamDetail, error) {
	if id == 0 {
		return nil, errors.New("无效的考试ID")
	}

	var exam model.Exams
	if err := db.DB.First(&exam, id).Error; err != nil {
		return nil, errors.New("未找到对应考试")
	}
	var exts []model.ExamsExt
	if err := db.DB.Where("exam_id = ?", id).Find(&exts).Error; err != nil {
		return nil, errors.New("未找到对应考试扩展信息")
	}
	var classIds []int64
	var majorIds []int64
	var studentIds []int64
	var TeacherIds []int64
	var supervisorIds []int64
	var userIds []int64
	for _, ext := range exts {
		userIds = append(userIds, ext.ExtValue)
		if ext.ExtKey == "class" {
			classIds = append(classIds, ext.ExtValue)
		}
		if ext.ExtKey == "major" {
			majorIds = append(majorIds, ext.ExtValue)
		}
		if ext.ExtKey == "student" {
			studentIds = append(studentIds, ext.ExtValue)
		}
		if ext.ExtKey == "teacher" {
			TeacherIds = append(TeacherIds, ext.ExtValue)
		}
		if ext.ExtKey == "supervisor" {
			supervisorIds = append(supervisorIds, ext.ExtValue)
		}
	}
	var classes []model.Class
	if err := db.DB.Model(&model.Class{}).
		Where("id IN (?)", classIds).
		Find(&classes).Error; err != nil {
		return nil, errors.New("获取班级信息失败")
	}
	var majors []model.Majors
	if err := db.DB.Model(&model.Majors{}).
		Where("id IN (?)", majorIds).
		Find(&majors).Error; err != nil {
		return nil, errors.New("获取专业信息失败")
	}
	var users []model.Users
	if err := db.DB.Where("id IN ?", userIds).Omit("password", "salt").Find(&users).Error; err != nil {
		return nil, errors.New("获取用户信息失败")
	}
	var teachers []model.Users
	var students []model.Users
	var supervisors []model.Users
	for _, user := range users {
		for _, teacher := range TeacherIds {
			if user.ID == teacher {
				teachers = append(teachers, user)
			}
		}
		for _, student := range studentIds {
			if user.ID == student {
				students = append(students, user)
			}
		}
		for _, supervisor := range supervisorIds {
			if user.ID == supervisor {
				supervisors = append(supervisors, user)
			}
		}
	}
	var paper model.Papers
	if err := db.DB.First(&paper, exam.PaperID).Error; err != nil {
		return nil, errors.New("获取试卷失败: " + err.Error())
	}

	resp := &model.RespExamDetail{
		Exams:       exam,
		Classes:     classes,
		Majors:      majors,
		Students:    students,
		Teachers:    teachers,
		Supervisors: supervisors,
		Paper:       paper,
	}

	return resp, nil
}
func (api *ExamService) GetUserList(examID int64, ext_key string, page, pageSize int) ([]model.Users, int64, error) {
	var ids []int64
	if err := db.DB.Model(&model.ExamsExt{}).Where("exam_id = ? AND ext_key = ?", examID, ext_key).Pluck("ext_value", &ids).Error; err != nil {
		return nil, 0, errors.New("获取学生失败: " + err.Error())
	}
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 || pageSize > 100 {
		pageSize = 10
	}

	dbQuery := db.DB.Model(&model.Users{}).Omit("password", "salt").Where("id IN (?)", ids)

	var total int64
	if err := dbQuery.Count(&total).Error; err != nil {
		return nil, 0, errors.New("查询总数失败")
	}
	var list []model.Users
	offset := (page - 1) * pageSize
	if err := dbQuery.Offset(offset).Limit(pageSize).Order("created_at DESC").Find(&list).Error; err != nil {
		return nil, 0, errors.New("查询数据失败: " + err.Error())
	}

	return list, total, nil

}

// GetExamsList 获取考试列表，支持按类型检索
func (s *ExamService) GetExamsList(c *gin.Context, req model.ReqGetExams) ([]model.StudentExamEntry, int64, error) {
	if req.UserID == 0 {
		return nil, 0, errors.New("无效的学生ID")
	}
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 || req.PageSize > 100 {
		req.PageSize = 10
	}
	if req.StartAt == 0 {
		req.StartAt = time.Now().Unix()
	}
	if req.Status == "" {
		req.Status = model.ExamStatusPublished
	}
	if req.Role == "" {
		req.Role = "student"
	}
	dbQuery := db.DB.Model(&model.Exams{}).
		Joins("JOIN exams_ext ON exams.id = exams_ext.exam_id").
		Joins("LEFT JOIN exam_progress ON exams.id = exam_progress.exam_id AND exam_progress.user_id = ?", req.UserID).
		Where("exams_ext.ext_value = ?", req.UserID).
		Where("exams.status = ?", req.Status).
		Where("exams_ext.ext_key = ?", req.Role)

	if req.StartAt > 0 && req.EndAt > 0 {
		// 查询时间范围内的考试（考试时间与查询时间范围有重叠）
		dbQuery = dbQuery.Where("exams.start_at <= ? AND exams.end_at >= ?", req.EndAt, req.StartAt)
	} else if req.StartAt > 0 {
		// 只指定开始时间，查找在该时间点有效的考试和未来的考试
		dbQuery = dbQuery.Where("((exams.start_at <= ? AND exams.end_at >= ?) OR (exams.start_at > ?))", req.StartAt, req.StartAt, req.StartAt)
	} else if req.EndAt > 0 {
		// 只指定结束时间，查找在该时间点之前开始的考试
		dbQuery = dbQuery.Where("exams.start_at <= ?", req.EndAt)
	}

	// 设置默认值并构建查询条件

	if req.ExamType != "" {
		dbQuery = dbQuery.Where("exams.exam_type = ?", req.ExamType)
	}

	var total int64
	if err := dbQuery.Count(&total).Error; err != nil {
		return nil, 0, errors.New("统计总数失败：" + err.Error())
	}

	page := req.Page
	pageSize := req.PageSize
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 || pageSize > 100 {
		pageSize = 10
	}

	offset := (page - 1) * pageSize

	var exams []model.StudentExamEntry
	if err := dbQuery.Offset(offset).
		Limit(pageSize).
		Order("exams.created_at DESC").
		Find(&exams).Error; err != nil {

		return nil, 0, errors.New("查询数据失败：" + err.Error())
	}

	return exams, total, nil
}
