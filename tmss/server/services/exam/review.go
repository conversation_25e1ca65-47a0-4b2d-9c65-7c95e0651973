package exam

import (
	"errors"
	"fmt"
	"time"

	"tms/model"
	"tms/pkg/db"
)

type ExamReviewService struct{}

func NewExamReviewService() *ExamReviewService {
	return &ExamReviewService{}
}

// GetTeacherReviewExams 获取老师需要阅卷的试卷列表
func (api *ExamReviewService) GetTeacherReviewExams(teacherID int64, pageNum, pageSize int) ([]model.Exams, int64, error) {
	if teacherID == 0 {
		return nil, 0, errors.New("无效的教师ID")
	}

	// 设置分页默认值
	if pageNum <= 0 {
		pageNum = 1
	}
	if pageSize <= 0 || pageSize > 100 {
		pageSize = 10
	}

	// 查询老师关联的考试ID
	var examIDs []int64
	if err := db.DB.Model(&model.ExamsExt{}).
		Where("ext_key = 'teacher' AND ext_value = ?", teacherID).
		Pluck("exam_id", &examIDs).Error; err != nil {
		return nil, 0, err
	}

	if len(examIDs) == 0 {
		return []model.Exams{}, 0, nil
	}

	// 构建查询
	dbQuery := db.DB.Model(&model.Exams{}).
		Where("id IN (?) AND status = ?", examIDs, model.ExamStatusFinished)

	// 获取总数
	var total int64
	if err := dbQuery.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	var exams []model.Exams
	offset := (pageNum - 1) * pageSize
	if err := dbQuery.Order("end_at DESC").
		Offset(offset).Limit(pageSize).
		Find(&exams).Error; err != nil {
		return nil, 0, err
	}

	return exams, total, nil
}

// GetReviewList 获取老师的阅卷任务列表
func (api *ExamReviewService) GetReviewList(teacherID int64, pageNum, pageSize int) ([]model.ExamReview, int64, error) {
	if teacherID == 0 {
		return nil, 0, errors.New("无效的教师ID")
	}

	// 设置分页默认值
	if pageNum <= 0 {
		pageNum = 1
	}
	if pageSize <= 0 || pageSize > 100 {
		pageSize = 10
	}

	// 构建查询
	dbQuery := db.DB.Model(&model.ExamReview{}).
		Where("reviewer_id = ? AND review_status = ?", teacherID, model.ReviewStatusPending)

	// 获取总数
	var total int64
	if err := dbQuery.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	var reviews []model.ExamReview
	offset := (pageNum - 1) * pageSize
	if err := dbQuery.Order("created_at DESC").
		Offset(offset).Limit(pageSize).
		Find(&reviews).Error; err != nil {
		return nil, 0, err
	}

	return reviews, total, nil
}

// GetStudentExamProgress 获取考试下所有学生的考试情况
// GetStudentExamProgress 获取考试下所有学生的考试情况（支持分页）
func (api *ExamReviewService) GetStudentExamProgress(examID int64, page, pageSize int) (model.ExamProgressList, error) {
	var res model.ExamProgressList
	if examID == 0 {
		return res, errors.New("无效的考试ID")
	}

	// 设置分页默认值
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 || pageSize > 100 {
		pageSize = 10
	}

	// 构建查询
	dbQuery := db.DB.Model(&model.ExamProgress{}).
		Where("exam_id = ? AND status = ?", examID, model.ExamProgressStatusFinished)

	// 获取总数
	var total int64
	if err := dbQuery.Count(&total).Error; err != nil {
		return res, err
	}

	// 分页查询
	var progresses []model.ExamProgress
	offset := (page - 1) * pageSize
	if err := dbQuery.Order("created_at DESC").
		Offset(offset).Limit(pageSize).
		Find(&progresses).Error; err != nil {
		return res, err
	}

	// 预加载学生信息
	if len(progresses) > 0 {
		var studentIDs []int64
		for _, progress := range progresses {
			studentIDs = append(studentIDs, progress.UserID)
		}

		var students []model.Users
		if err := db.DB.Model(&model.Users{}).
			Where("id IN (?)", studentIDs).
			Find(&students).Error; err != nil {
			return res, err
		}

		// 创建学生ID到学生信息的映射
		studentMap := make(map[int64]string)
		for _, student := range students {
			studentMap[student.ID] = student.Username
		}
		var teacherIDs []int64
		for _, progress := range progresses {
			teacherIDs = append(teacherIDs, progress.ReviewerID)
		}
		var teachers []model.Users
		if err := db.DB.Model(&model.Users{}).
			Where("id IN (?)", teacherIDs).
			Find(&teachers).Error; err != nil {
			return res, err
		}
		teacherMap := make(map[int64]string)
		for _, teacher := range teachers {
			teacherMap[teacher.ID] = teacher.Username
		}

		// 关联学生信息
		for i := range progresses {
			d := model.ExamProgressDetail{ExamProgress: progresses[i]}
			d.StudentName = studentMap[progresses[i].UserID]
			d.TeacherName = teacherMap[progresses[i].UserID]
			res.List = append(res.List, d)

		}
	}

	return res, nil
}
func (api *ExamReviewService) ChangeReviewer(progressIDs []int64, teacherID int64) error {
	if len(progressIDs) == 0 || teacherID == 0 {
		return errors.New("无效的参数")
	}

	// 检查这些progressIDs是否在ExamReview中存在
	var existingReviews []model.ExamReview
	if err := db.DB.Model(&model.ExamReview{}).
		Where("exam_progress_id IN (?)", progressIDs).
		Find(&existingReviews).Error; err != nil {
		return err
	}

	// 创建存在的progressID映射
	existingProgressIDs := make(map[int64]bool)
	for _, review := range existingReviews {
		existingProgressIDs[review.ExamProgressID] = true
	}

	// 只更新在ExamReview中存在的progressID
	for _, progressID := range progressIDs {
		// 检查progressID是否存在于ExamReview中
		if !existingProgressIDs[progressID] {
			return fmt.Errorf("进度ID %d 在阅卷记录中不存在", progressID)
		}

		if err := db.DB.Model(&model.ExamProgress{}).Where("id = ?", progressID).
			Update("reviewer_id", teacherID).Error; err != nil {
			return err
		}
		if err := db.DB.Model(&model.ExamReview{}).Where("exam_progress_id = ?", progressID).
			Update("reviewer_id", teacherID).Error; err != nil {
			return err
		}
	}
	return nil
}

// InitReviewRecord 进入学生试卷并生成阅卷记录
func (api *ExamReviewService) InitReviewRecord(examID int64, progressIDs []int64, teacherID int64) ([]model.ExamReview, error) {
	if examID == 0 || len(progressIDs) == 0 || teacherID == 0 {
		return nil, errors.New("无效的参数")
	}

	// 检查是否已有阅卷记录
	var existingRecords []model.ExamReview
	if err := db.DB.Where("exam_progress_id IN (?)", progressIDs).
		Find(&existingRecords).Error; err != nil {
		return nil, err
	}

	if len(existingRecords) > 0 {
		return existingRecords, nil
	}

	// 获取需要阅卷的主观题答案
	var answers []model.ExamAnswer
	if err := db.DB.Where("exam_progress_id IN (?) AND question_type IN (?)",
		progressIDs, model.QuestionTypeManual).
		Find(&answers).Error; err != nil {
		return nil, err
	}

	if len(answers) == 0 {
		return nil, errors.New("该试卷没有需要阅卷的主观题")
	}

	// 创建阅卷记录
	tx := db.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	reviewRecords := make([]model.ExamReview, 0, len(answers))
	for _, answer := range answers {
		record := model.ExamReview{
			ExamID:         examID,
			ExamProgressID: answer.ExamProgressID,
			QuestionID:     answer.QuestionID,
			ReviewerID:     teacherID,
			OriginalScore:  answer.Score, // 系统自动评分
			ReviewStatus:   model.ReviewStatusPending,
			CreatedAt:      time.Now().Unix(),
			UpdatedAt:      time.Now().Unix(),
		}

		reviewRecords = append(reviewRecords, record)
	}
	if err := tx.Create(&reviewRecords).Error; err != nil {
		tx.Rollback()
		return nil, err
	}
	if err := tx.Model(&model.ExamProgress{}).Where("id IN (?)", progressIDs).Update("reviewer_id", teacherID).Error; err != nil {
		tx.Rollback()
		return nil, err
	}

	if err := tx.Commit().Error; err != nil {
		return nil, err
	}

	return reviewRecords, nil
}

// IsExamGraded 判断考试是否已完成评分
func (api *ExamReviewService) IsExamGraded(examID int64) (bool, error) {
	if examID == 0 {
		return false, errors.New("无效的考试ID")
	}

	// 查询是否还有未评分的学生试卷
	var pendingCount int64
	if err := db.DB.Model(&model.ExamProgress{}).
		Where("exam_id = ? AND status != ?", examID, model.ExamProgressStatusReviewed).
		Count(&pendingCount).Error; err != nil {
		return false, err
	}

	// 如果 pendingCount == 0，表示所有学生都已完成评分
	return pendingCount == 0, nil
}

// AutoGradeExamIfNoQuestions 如果考试无主观题，且未完成阅卷，则自动完成阅卷并更新状态
func (api *ExamReviewService) AutoGradeExamIfNoQuestions(examID int64) error {
	if examID == 0 {
		return errors.New("无效的考试ID")
	}

	// 检查是否已完成评分
	isGraded, err := api.IsExamGraded(examID)
	if err != nil {
		return err
	}
	if isGraded {
		return errors.New("该考试已全部完成阅卷，无需重复操作")
	}

	tx := db.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 检查是否存在主观题
	var answerCount int64
	if err := tx.Model(&model.ExamAnswer{}).
		Joins("JOIN exam_progress ON exam_progress.id = exam_answers.exam_progress_id").
		Where("exam_progress.exam_id = ? AND exam_answers.question_type IN (?)", examID, model.QuestionTypeManual).
		Count(&answerCount).Error; err != nil {
		tx.Rollback()
		return err
	}

	if answerCount == 0 {
		// 获取所有已完成考试的学生进度
		var progresses []model.ExamProgress
		if err := tx.Where("exam_id = ? AND status = ?", examID, model.ExamProgressStatusFinished).
			Find(&progresses).Error; err != nil {
			tx.Rollback()
			return err
		}

		for _, progress := range progresses {
			// 更新每个进度为已阅卷
			if err := tx.Model(&model.ExamProgress{}).
				Where("id = ?", progress.ID).
				Updates(map[string]interface{}{
					"status":      model.ExamProgressStatusReviewed,
					"reviewed_at": time.Now().Unix(),
					"updated_at":  time.Now().Unix(),
				}).Error; err != nil {
				tx.Rollback()
				return err
			}

			// 获取客观题总分
			var totalScore int
			if err := tx.Model(&model.ExamAnswer{}).
				Where("exam_progress_id = ?", progress.ID).
				Select("COALESCE(SUM(score), 0)").
				Scan(&totalScore).Error; err != nil {
				tx.Rollback()
				return err
			}

			// 更新分数
			if err := tx.Model(&model.ExamProgress{}).
				Where("id = ?", progress.ID).
				Update("score", totalScore).Error; err != nil {
				tx.Rollback()
				return err
			}
		}

		// 更新考试状态为已评分
		if err := tx.Model(&model.Exams{}).
			Where("id = ?", examID).
			Update("status", model.ExamStatusGraded).Error; err != nil {
			tx.Rollback()
			return err
		}
	} else {
		return errors.New("该考试包含主观题，请完成人工阅卷后再提交")
	}

	if err := tx.Commit().Error; err != nil {
		return err
	}

	return nil
}

// SubmitReview 批量提交阅卷信息
func (api *ExamReviewService) SubmitReview(reviews []model.ExamReview) error {
	if len(reviews) == 0 {
		return errors.New("无阅卷数据")
	}

	// 获取第一个记录的进度ID和考试ID
	progressID := reviews[0].ExamProgressID
	examID := reviews[0].ExamID

	tx := db.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 更新阅卷记录
	for _, review := range reviews {
		if review.ID == 0 || review.ExamProgressID != progressID {
			tx.Rollback()
			return errors.New("无效的阅卷记录ID")
		}

		if review.ReviewedScore < 0 {
			tx.Rollback()
			return errors.New("分数不能为负数")
		}

		// 更新阅卷记录
		review.ReviewStatus = model.ReviewStatusReviewed
		review.ReviewedAt = time.Now().Unix()
		review.UpdatedAt = time.Now().Unix()

		if err := tx.Save(&review).Error; err != nil {
			tx.Rollback()
			return err
		}

		// 更新答题记录中的分数
		if err := tx.Model(&model.ExamAnswer{}).
			Where("exam_progress_id = ? AND question_id = ?", progressID, review.QuestionID).
			Update("score", review.ReviewedScore).Error; err != nil {
			tx.Rollback()
			return err
		}
	}

	// 更新考试进度总分
	var totalScore int
	if err := tx.Model(&model.ExamAnswer{}).
		Select("COALESCE(SUM(score), 0)").
		Where("exam_progress_id = ?", progressID).
		Scan(&totalScore).Error; err != nil {
		tx.Rollback()
		return err
	}

	if err := tx.Model(&model.ExamProgress{}).
		Where("id = ?", progressID).
		Updates(map[string]interface{}{
			"score":       totalScore,
			"status":      model.ExamProgressStatusReviewed,
			"updated_at":  time.Now().Unix(),
			"reviewed_at": time.Now().Unix(),
		}).Error; err != nil {
		tx.Rollback()
		return err
	}

	// 检查是否所有学生都已完成阅卷
	var pendingCount int64
	if err := tx.Model(&model.ExamReview{}).
		Where("exam_id = ? AND review_status = ?", examID, model.ReviewStatusPending).
		Count(&pendingCount).Error; err != nil {
		tx.Rollback()
		return err
	}

	// 如果所有学生都已完成阅卷，更新考试状态
	if pendingCount == 0 {
		if err := tx.Model(&model.Exams{}).
			Where("id = ?", examID).
			Update("status", model.ExamStatusGraded).Error; err != nil {
			tx.Rollback()
			return err
		}
	}

	if err := tx.Commit().Error; err != nil {
		return err
	}

	return nil
}

// GenerateExamScoreRecords 手动生成考试成绩记录
func (api *ExamReviewService) GenerateExamScoreRecords(examID int64) error {
	if examID == 0 {
		return errors.New("无效的考试ID")
	}

	// 检查考试是否已完成阅卷
	var exam model.Exams
	if err := db.DB.First(&exam, examID).Error; err != nil {
		return err
	}

	// if exam.Status != model.ExamStatusGraded {
	// 	return errors.New("考试尚未完成阅卷，无法生成成绩记录")
	// }

	// 获取所有学生的考试成绩
	var progresses []model.ExamProgress
	if err := db.DB.Where("exam_id = ? AND status = ?", examID, model.ExamProgressStatusReviewed).
		Find(&progresses).Error; err != nil {
		return err
	}

	tx := db.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 为每个学生创建成绩记录
	var records []model.ExamScore
	for _, progress := range progresses {
		// 检查是否已存在成绩记录
		var existingRecord model.ExamScore
		if err := tx.Where("exam_id = ? AND student_id = ?", examID, progress.UserID).
			First(&existingRecord).Error; err == nil {
			continue // 已存在记录，跳过
		}

		record := model.ExamScore{
			ExamID:    examID,
			StudentID: progress.UserID,
			Score:     progress.Score,
			Status:    model.ScoreStatusDraft, // 初始状态为草稿
			CreatedAt: time.Now().Unix(),
			UpdatedAt: time.Now().Unix(),
		}
		records = append(records, record)
	}
	if err := tx.Create(&records).Error; err != nil {
		tx.Rollback()
		return err
	}
	//更改考试状态为已归档
	updates := map[string]interface{}{
		"status":     model.ExamStatusArchived,
		"updated_at": time.Now().Unix(),
	}
	if err := tx.Model(&model.Exams{}).Where("id = ?", examID).Updates(updates).Error; err != nil {
		tx.Rollback()
		return err
	}
	if err := tx.Commit().Error; err != nil {
		return err
	}

	return nil
}

// GenerateCertificates 手动生成证书记录（需要审核）
func (api *ExamReviewService) GenerateCertificates(req model.ReqCertificateCreate) error {
	if req.ExamID == 0 {
		return errors.New("无效的考试ID")
	}

	// 检查考试是否已完成阅卷
	var exam model.Exams
	if err := db.DB.First(&exam, req.ExamID).Error; err != nil {
		return err
	}

	// if exam.Status != model.ExamStatusGraded {
	// 	return errors.New("考试尚未完成阅卷，无法颁发证书")
	// }

	// 获取所有合格的考试成绩（分数大于60分）
	var scores []model.ExamScore
	if err := db.DB.Where("exam_id = ? AND score >= ? AND status = ?", req.ExamID, req.Score, model.ScoreStatusApproved).Find(&scores).Error; err != nil {
		return err
	}

	if len(scores) == 0 {
		return errors.New("没有合格的学生成绩")
	}

	tx := db.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 为每个合格学生创建证书记录
	for _, score := range scores {
		// 检查是否已存在证书记录
		var existingCert model.Certificates
		if err := tx.Where("exam_id = ? AND student_id = ?", req.ExamID, score.StudentID).
			First(&existingCert).Error; err == nil {
			continue // 已存在记录，跳过
		}

		cert := model.Certificates{
			StudentID:  score.StudentID,
			ExamID:     req.ExamID,
			ScoreID:    score.ID,
			Score:      score.Score,
			TemplateID: req.TemplateID,
			UserID:     req.UserID,
			Status:     model.CertificatesStatusDraft, // 初始状态为草稿
			CreatedAt:  time.Now().Unix(),
			UpdatedAt:  time.Now().Unix(),
		}
		cert.GenerateCertNumber()
		if err := tx.Create(&cert).Error; err != nil {
			tx.Rollback()
			return fmt.Errorf("创建证书记录失败: %w", err)
		}

		// 更新成绩记录状态为证书已颁发（待审核）
		if err := tx.Model(&model.ExamScore{}).
			Where("id = ?", score.ID).
			Update("status", model.ScoreStatusCertified).Error; err != nil {
			tx.Rollback()
			return fmt.Errorf("更新成绩状态失败: %w", err)
		}
	}
	//更改考试状态为已颁发证书
	updates := map[string]interface{}{
		"status":     model.ExamStatusCertified,
		"updated_at": time.Now().Unix(),
	}
	if err := tx.Model(&model.Exams{}).Where("id = ?", req.ExamID).Updates(updates).Error; err != nil {
		tx.Rollback()
		return err
	}
	if err := tx.Commit().Error; err != nil {
		return err
	}

	return nil
}

// GetExamProgressDetail 获取单个试卷的详细信息
func (api *ExamReviewService) GetExamProgressDetail(progressID int64) (*model.ExamProgressDetailResponse, error) {
	if progressID == 0 {
		return nil, errors.New("无效的试卷进度ID")
	}

	// 获取考试进度基本信息
	var progress model.ExamProgress
	if err := db.DB.Where("id = ?", progressID).First(&progress).Error; err != nil {
		return nil, fmt.Errorf("获取试卷进度信息失败: %w", err)
	}

	// 获取考试基本信息
	var exam model.Exams
	if err := db.DB.Where("id = ?", progress.ExamID).First(&exam).Error; err != nil {
		return nil, fmt.Errorf("获取考试信息失败: %w", err)
	}

	// 获取学生信息
	var student model.Users
	if err := db.DB.Where("id = ?", progress.UserID).First(&student).Error; err != nil {
		return nil, fmt.Errorf("获取学生信息失败: %w", err)
	}

	// 获取阅卷老师信息（如果已分配）
	var reviewer model.Users
	if progress.ReviewerID != 0 {
		if err := db.DB.Where("id = ?", progress.ReviewerID).First(&reviewer).Error; err != nil {
			return nil, fmt.Errorf("获取阅卷老师信息失败: %w", err)
		}
	}

	// 获取该试卷的所有题目和答案
	var answers []model.ExamAnswer
	if err := db.DB.Where("exam_progress_id = ?", progressID).Find(&answers).Error; err != nil {
		return nil, fmt.Errorf("获取试卷答案失败: %w", err)
	}

	// 收集题目ID
	var questionIDs []int64
	answerMap := make(map[int64]model.ExamAnswer)
	for _, answer := range answers {
		questionIDs = append(questionIDs, answer.QuestionID)
		answerMap[answer.QuestionID] = answer
	}

	// 获取题目详情
	var questions []model.Questions
	if len(questionIDs) > 0 {
		if err := db.DB.Where("id IN (?)", questionIDs).Find(&questions).Error; err != nil {
			return nil, fmt.Errorf("获取题目信息失败: %w", err)
		}
	}

	// 构建题目详情列表
	questionDetails := make([]model.QuestionDetail, 0, len(questions))
	for _, question := range questions {
		detail := model.QuestionDetail{
			Questions: question,
			Answer:    answerMap[question.ID],
		}

		// 如果是主观题，获取阅卷记录
		var review model.ExamReview
		if err := db.DB.Where("exam_progress_id = ? AND question_id = ?", progressID, question.ID).
			First(&review).Error; err == nil {
			detail.Review = &review
		}

		questionDetails = append(questionDetails, detail)
	}

	// 组装返回结果
	response := &model.ExamProgressDetailResponse{
		Progress:       progress,
		Exam:           exam,
		Student:        student,
		Reviewer:       reviewer,
		QuestionDetail: questionDetails,
	}

	return response, nil
}
