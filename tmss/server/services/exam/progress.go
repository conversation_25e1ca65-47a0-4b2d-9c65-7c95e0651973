package exam

import (
	"errors"
	"sort"
	"time"

	"tms/model"
	"tms/pkg/db"
)

type ExamProgressService struct{}

func NewExamProgressService() *ExamProgressService {
	return &ExamProgressService{}
}

// CreateExamProgress 创建用户的考试进度
func (s *ExamProgressService) CreateExamProgress(userID, examID int64) (int64, error) {
	if userID == 0 || examID == 0 {
		return 0, errors.New("无效的用户ID或考试ID")
	}

	var exam model.Exams
	if err := db.DB.First(&exam, examID).Error; err != nil {
		return 0, errors.New("未找到对应考试")
	}

	var progress model.ExamProgress
	db.DB.Where("user_id = ? and exam_id = ?", userID, examID).First(&progress)

	if progress.ID > 0 {
		if progress.Status == model.ExamProgressStatusFinished || progress.Status == model.ExamProgressStatusTimeout {
			return 0, errors.New("考试已完成")
		}
	}

	// 未到考试时间
	if exam.StartAt > time.Now().Unix() {
		return 0, errors.New("考试未开始")
	}

	if exam.EndAt < time.Now().Unix() {
		// 更新考试状态为已结束
		if progress.ID > 0 {
			db.DB.Model(&model.ExamProgress{}).
				Where("id = ?", progress.ID).
				Update("status", model.ExamProgressStatusTimeout)
		}
		return 0, errors.New("考试已结束")
	}
	var paper model.Papers
	if err := db.DB.First(&paper, exam.PaperID).Error; err != nil {
		return 0, errors.New("未找到对应试卷")
	}

	// 中途退出的情况
	if progress.ID > 0 {
		return progress.ID, nil
	}

	progress = model.ExamProgress{
		UserID:     userID,
		ExamID:     examID,
		PaperID:    paper.ID,
		StartTime:  time.Now().Unix(),
		Status:     model.ExamProgressStatusNotStarted,
		TotalScore: paper.TotalScore,
		CreatedAt:  time.Now().Unix(),
		UpdatedAt:  time.Now().Unix(),
		IsTimeout:  false,
	}

	if err := db.DB.Create(&progress).Error; err != nil {
		return 0, errors.New("创建考试进度失败: " + err.Error())
	}

	return progress.ID, nil
}

// StartExam 开始考试
func (api *ExamProgressService) StartExam(progressID int64) error {
	if progressID == 0 {
		return errors.New("无效的考试进度ID")
	}

	var progress model.ExamProgress
	if err := db.DB.First(&progress, progressID).Error; err != nil {
		return errors.New("未找到对应考试进度")
	}

	if progress.Status != model.ExamProgressStatusNotStarted {
		return errors.New("考试已开始或已完成")
	}

	now := time.Now().Unix()
	progress.Status = model.ExamProgressStatusOngoing
	progress.StartTime = now
	progress.UpdatedAt = now

	if err := db.DB.Save(&progress).Error; err != nil {
		return errors.New("更新考试进度失败: " + err.Error())
	}

	return nil
}

// SubmitExam 提交试卷
func (api *ExamProgressService) SubmitExam(progressID int64, answers []model.ExamAnswer, submitStatus string) (model.ExamProgress, error) {
	if progressID == 0 {
		return model.ExamProgress{}, errors.New("无效的考试进度ID")
	}
	if submitStatus != model.AnswerSubmitCommitStatus && submitStatus != model.AnswerSubmitTerminateStatus {
		return model.ExamProgress{}, errors.New("无效的提交状态")
	}

	var progress model.ExamProgress
	if err := db.DB.First(&progress, progressID).Error; err != nil {
		return model.ExamProgress{}, errors.New("未找到对应考试进度")
	}

	if progress.Status == model.ExamProgressStatusFinished {
		return model.ExamProgress{}, errors.New("考试已完成，无法再次提交")
	}

	tx := db.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 获取总题数
	var totalQuestions int64
	if err := tx.Model(&model.PaperQuestions{}).
		Where("paper_id = ?", progress.PaperID).
		Count(&totalQuestions).Error; err != nil {

		tx.Rollback()
		return model.ExamProgress{}, errors.New("无法获取试卷题目总数")
	}

	// 获取已答过的题目数量
	var answeredCount int64
	if err := tx.Model(&model.ExamAnswer{}).
		Where("exam_progress_id = ?", progressID).
		Count(&answeredCount).Error; err != nil {

		tx.Rollback()
		return model.ExamProgress{}, errors.New("无法获取已答题数")
	}

	totalScore := progress.Score
	completedCount := int(answeredCount)

	for _, answer := range answers {
		answer.ExamProgressID = progressID
		answer.CreatedAt = time.Now().Unix()
		answer.UpdatedAt = time.Now().Unix()

		var question model.PaperQuestions
		if err := tx.Where("question_id = ?", answer.QuestionID).First(&question).Error; err != nil {
			tx.Rollback()
			return model.ExamProgress{}, errors.New("未找到对应题目")
		}

		// 避免重复提交同一道题
		if err := tx.Where("exam_progress_id = ? AND question_id = ?", progressID, answer.QuestionID).Delete(&model.ExamAnswer{}).Error; err != nil {
			tx.Rollback()
			return model.ExamProgress{}, errors.New("删除旧答案失败: " + err.Error())
		}

		// var existingAnswer model.ExamAnswer
		// if err := tx.Where("exam_progress_id = ? AND question_id = ?", progressID, answer.QuestionID).
		// 	First(&existingAnswer).Error; err == nil {

		// 	continue
		// }

		// 判分逻辑
		if question.QuestionCategory == model.QuestionCategoryTheoryCourseware ||
			question.QuestionCategory == model.QuestionCategoryVirtualCourseware {

			totalScore += answer.Score
		} else {
			if answer.UserAnswer == question.Answer {
				answer.Score = question.Score
				totalScore += question.Score
			} else {
				answer.Score = 0
			}
		}

		completedCount++

		if err := tx.Create(&answer).Error; err != nil {
			tx.Rollback()
			return model.ExamProgress{}, errors.New("保存答题详情失败: " + err.Error())
		}
	}

	// 更新考试进度状态
	now := time.Now().Unix()
	progress.UpdatedAt = now
	progress.Score = totalScore
	progress.CompletedCount = completedCount

	// if int64(completedCount) >= totalQuestions {
	// 	progress.Status = model.ExamProgressStatusFinished
	// 	progress.EndTime = now
	// } else {
	// 	progress.Status = model.ExamProgressStatusOngoing
	// }

	progress.Status = model.ExamProgressStatusOngoing
	if submitStatus == model.AnswerSubmitTerminateStatus {
		progress.Status = model.ExamProgressStatusFinished
		progress.EndTime = now
	}

	if err := tx.Save(&progress).Error; err != nil {
		tx.Rollback()
		return model.ExamProgress{}, errors.New("更新考试进度失败: " + err.Error())
	}

	tx.Commit()
	// 提交后检查考试是否应该结束
	// go api.CheckAndFinishExam(progress.ExamID)
	return progress, nil
}

// GetExamProgress 获取用户的考试进度
func (api *ExamProgressService) GetExamProgress(progressID int64) (*model.ExamProgress, error) {
	if progressID == 0 {
		return nil, errors.New("无效的考试进度ID")
	}

	var progress model.ExamProgress
	if err := db.DB.Preload("Answers").First(&progress, progressID).Error; err != nil {
		return nil, errors.New("未找到对应考试进度")
	}

	return &progress, nil
}
func (api *ExamProgressService) CheckAndFinishExam(examID int64) {
	// 获取考试信息
	var exam model.Exams
	if err := db.DB.First(&exam, examID).Error; err != nil {
		return
	}

	// 如果考试结束时间已到，立即结束考试
	if exam.EndAt < time.Now().Unix() {
		api.FinishExam(examID)
		return
	}

	// 检查所有学生是否都已完成考试
	var totalStudents int64
	var completedStudents int64

	// 获取考试关联的学生总数
	if err := db.DB.Model(&model.ExamsExt{}).
		Where("exam_id = ? AND ext_key = 'student'", examID).
		Count(&totalStudents).Error; err != nil {
		return
	}

	// 获取已完成考试的学生数
	if err := db.DB.Model(&model.ExamProgress{}).
		Where("exam_id = ? AND status IN (?)",
			examID,
			[]string{model.ExamProgressStatusFinished, model.ExamProgressStatusTimeout}).
		Count(&completedStudents).Error; err != nil {
		return
	}

	// 如果所有学生都已完成考试，结束考试
	if totalStudents > 0 && completedStudents == totalStudents {
		api.FinishExam(examID)
	}
}
func (api *ExamProgressService) FinishExam(examID int64) {
	// 更新考试状态为已结束
	db.DB.Model(&model.Exams{}).
		Where("id = ?", examID).
		Update("status", model.ExamStatusFinished)

	// 将所有未完成的考试进度标记为超时
	db.DB.Model(&model.ExamProgress{}).
		Where("exam_id = ? AND status NOT IN (?)",
			examID,
			[]string{model.ExamProgressStatusFinished, model.ExamProgressStatusTimeout}).
		Updates(map[string]interface{}{
			"status":     model.ExamProgressStatusTimeout,
			"is_timeout": true,
			"end_time":   time.Now().Unix(),
		})
}

func (s *ExamProgressService) QueryExamProgressAndAnswers(examProgressID int64) (model.UserExamEntry, error) {
	var progress model.ExamProgress
	if err := db.DB.Where("id = ?", examProgressID).First(&progress).Error; err != nil {
		return model.UserExamEntry{}, errors.New("未找到对应考试进度")
	}

	var exam model.Exams
	if err := db.DB.First(&exam, progress.ExamID).Error; err != nil {
		return model.UserExamEntry{}, errors.New("未找到对应考试")
	}

	var answers []model.ExamAnswer
	if err := db.DB.Where("exam_progress_id = ?", progress.ID).Find(&answers).Error; err != nil {
		return model.UserExamEntry{}, errors.New("查询答题详情失败: " + err.Error())
	}

	return model.UserExamEntry{
		Exam:         &exam,
		ExamProgress: &progress,
		ExamAnswers:  answers,
	}, nil
}

// 考试记录
func (s *ExamProgressService) GetExamHistory(userID int64) ([]model.ExamHistory, error) {
	var progresses []model.ExamProgress
	if err := db.DB.Where("status NOT IN (?) AND user_id = ?", []string{model.ExamProgressStatusNotStarted, model.ExamProgressStatusOngoing}, userID).Find(&progresses).Error; err != nil {
		return nil, err
	}
	var examIds []int64
	for _, p := range progresses {
		examIds = append(examIds, p.ExamID)
	}
	if len(examIds) == 0 {
		return []model.ExamHistory{}, nil
	}

	var joinExams []model.Exams
	if err := db.DB.Where("id IN (?)", examIds).Find(&joinExams).Error; err != nil {
		return nil, err
	}
	joinExamsMap := make(map[int64]model.Exams)
	examFinishMap := make(map[int64]struct{})
	for _, e := range joinExams {
		joinExamsMap[e.ID] = e
		if e.Status == model.ExamStatusGraded || e.Status == model.ExamStatusArchived || e.Status == model.ExamStatusCertified {
			examFinishMap[e.ID] = struct{}{}
		}
	}

	ranks, err := CalculateRanks(progresses, userID)
	if err != nil {
		return nil, err
	}
	rankMap := make(map[int64]int)
	for _, rank := range ranks {
		rankMap[rank.ExamID] = rank.Rank
	}

	var results []model.ExamHistory
	for _, p := range progresses {
		r := model.ExamHistory{
			UserID:         p.UserID,
			ExamName:       joinExamsMap[p.ExamID].Name,
			ExamID:         p.ExamID,
			ProgressdID:    p.ID,
			ProgressStatus: p.Status,
			ExamStatus:     joinExamsMap[p.ExamID].Status,
			SubmitAt:       p.EndTime,
			CostTime:       p.EndTime - p.StartTime,
		}
		if _, ok := examFinishMap[r.ExamID]; ok {
			r.Score = p.Score
			r.Rank = rankMap[r.ExamID]
		}
		results = append(results, r)
	}

	return results, nil
}

func CalculateRanks(progresses []model.ExamProgress, userID int64) ([]model.ExamRank, error) {
	// 1. 按 exam_id 分组
	grouped := make(map[int64][]model.ExamProgress)
	for _, p := range progresses {
		grouped[p.ExamID] = append(grouped[p.ExamID], p)
	}

	// 2. 排序 + 排名
	var results []model.ExamRank
	for examID, group := range grouped {
		// 排序（按 score 降序）
		sort.Slice(group, func(i, j int) bool {
			return group[i].Score > group[j].Score
		})

		// 排名计算（支持并列）
		rank := 1
		for i := 0; i < len(group); i++ {
			if i > 0 && group[i].Score < group[i-1].Score {
				rank = i + 1
			}
			results = append(results, model.ExamRank{
				UserID: group[i].UserID,
				ExamID: examID,
				Score:  group[i].Score,
				Rank:   rank,
			})
		}
	}

	return results, nil
}

// 获取回答详情
func (s *ExamProgressService) GetAnswerHistoryDetail(progressID, questionID int64) (model.UserPaperAnswerEntry, error) {
	if progressID == 0 {
		return model.UserPaperAnswerEntry{}, errors.New("无效的试卷进度ID")
	}
	if questionID == 0 {
		return model.UserPaperAnswerEntry{}, errors.New("无效的题目ID")
	}
	var progress model.ExamProgress
	if err := db.DB.Where("id = ?", progressID).First(&progress).Error; err != nil {
		return model.UserPaperAnswerEntry{}, errors.New("未找到对应考试进度")
	}

	// 获取答案
	var answer model.ExamAnswer
	if err := db.DB.Where("exam_progress_id = ? AND question_id = ?", progressID, questionID).First(&answer).Error; err != nil {
		return model.UserPaperAnswerEntry{}, errors.New("未找到对应答案")
	}

	// 获取题目
	var question model.PaperQuestions
	if err := db.DB.Where("question_id = ? AND paper_id = ?", questionID, progress.PaperID).First(&question).Error; err != nil {
		return model.UserPaperAnswerEntry{}, errors.New("未找到对应题目")
	}

	return model.UserPaperAnswerEntry{
		PaperQuestion: question,
		ExamAnswer:    answer,
	}, nil
}
