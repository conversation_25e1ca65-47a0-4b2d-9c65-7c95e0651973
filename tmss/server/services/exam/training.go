package exam

import (
	"errors"
	"strconv"
	"time"

	"tms/model"
	"tms/pkg/db"
)

type TrainingService struct{}

func NewTrainingService() *TrainingService {
	return &TrainingService{}
}

// StartTraining 开始训练
func (s *TrainingService) StartTraining(userID, coursewareID int64) error {
	if userID == 0 || coursewareID == 0 {
		return errors.New("无效的用户ID或课件ID")
	}

	var exists model.QuestionProgress
	err := db.DB.Where("user_id = ? AND courseware_id = ? AND status = ?", userID, coursewareID, model.QuestionProgressStatusStarted).First(&exists).Error
	if err == nil {
		return errors.New("该课件训练已开始")
	}

	now := time.Now().Unix()
	progress := model.QuestionProgress{
		UserID:       userID,
		CoursewareID: coursewareID,
		Status:       model.QuestionProgressStatusStarted,
		StartTime:    now,
		CreatedAt:    now,
		UpdatedAt:    now,
	}

	if err := db.DB.Create(&progress).Error; err != nil {
		return errors.New("创建训练进度失败: " + err.Error())
	}

	return nil
}

// SubmitTraining 提交训练答案（支持多次提交）
func (s *TrainingService) SubmitTraining(progressID int64, answers []model.QuestionAnswer) error {
	if progressID == 0 {
		return errors.New("无效的训练进度ID")
	}

	var progress model.QuestionProgress
	if err := db.DB.First(&progress, progressID).Error; err != nil {
		return errors.New("未找到对应训练进度")
	}

	if progress.Status == model.QuestionProgressStatusFinished {
		return errors.New("训练已完成，无法再次提交")
	}

	tx := db.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 查询该训练对应的总题数（可以从试卷/课件中获取）
	var totalQuestions int64
	if err := tx.Model(&model.PaperQuestions{}).
		Where("courseware_id = ?", progress.CoursewareID).
		Count(&totalQuestions).Error; err != nil {

		tx.Rollback()
		return errors.New("无法获取题目总数")
	}

	// 获取已答过的题目数量
	var answeredCount int64
	if err := tx.Model(&model.QuestionAnswer{}).
		Where("question_progress_id = ?", progressID).
		Count(&answeredCount).Error; err != nil {

		tx.Rollback()
		return errors.New("无法获取已答题数")
	}

	totalScore := progress.Score // 继续累加分数
	completedCount := int(answeredCount)

	// 处理新提交的答案
	for _, ans := range answers {
		ans.QuestionProgressID = progressID
		ans.UserID = progress.UserID
		ans.AnsweredAt = time.Now().Unix()
		ans.CreatedAt = ans.AnsweredAt
		ans.UpdatedAt = ans.AnsweredAt

		// 查询题目信息
		var question model.PaperQuestions
		if err := tx.First(&question, ans.QuestionID).Error; err != nil {
			tx.Rollback()
			return errors.New("未找到对应题目: " + strconv.Itoa(int(ans.QuestionID)))
		}

		// 判断是否已经答过这道题，避免重复提交
		var existingAnswer model.QuestionAnswer
		if err := tx.Where("question_progress_id = ? AND question_id = ?", progressID, ans.QuestionID).
			First(&existingAnswer).Error; err == nil {

			continue // 已经答过，跳过
		}

		// 判分逻辑
		if question.QuestionCategory == model.QuestionCategoryVirtualCourseware ||
			question.QuestionCategory == model.QuestionCategoryTheoryCourseware {

			totalScore += ans.Score
		} else {
			if ans.UserAnswer == question.Answer {
				ans.Score = question.Score
				totalScore += question.Score
			} else {
				ans.Score = 0
			}
		}

		completedCount++

		if err := tx.Create(&ans).Error; err != nil {
			tx.Rollback()
			return errors.New("保存答题记录失败: " + err.Error())
		}
	}

	// 更新训练进度状态
	now := time.Now().Unix()
	progress.UpdatedAt = now
	progress.Score = totalScore
	progress.CompletedCount = completedCount

	if int64(completedCount) >= totalQuestions {
		progress.Status = model.QuestionProgressStatusFinished
		progress.EndTime = now
	} else {
		progress.Status = model.QuestionProgressStatusOngoing
	}

	if err := tx.Save(&progress).Error; err != nil {
		tx.Rollback()
		return errors.New("更新训练进度失败: " + err.Error())
	}

	tx.Commit()
	return nil
}

// GetTrainingProgress 获取训练进度详情
func (s *TrainingService) GetTrainingProgress(progressID int64) (*model.QuestionProgress, error) {
	if progressID == 0 {
		return nil, errors.New("无效的训练进度ID")
	}

	var progress model.QuestionProgress
	if err := db.DB.Preload("Answers").First(&progress, progressID).Error; err != nil {
		return nil, errors.New("未找到对应训练进度")
	}

	return &progress, nil
}
