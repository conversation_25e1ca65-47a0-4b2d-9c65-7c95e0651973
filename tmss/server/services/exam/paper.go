package exam

import (
	"errors"
	"time"
	"tms/model"
	"tms/pkg/db"
	"tms/services/user"

	"github.com/gin-gonic/gin"
)

type PaperService struct{}

// 定义 ReqPaperSearch 结构体，用于试卷搜索请求
func NewPaperService() *PaperService {
	return &PaperService{}
}

// CreatePaperDraft 创建试卷草稿
func (api *PaperService) CreatePaperDraft(req model.ReqPaperUpdate, userId int64) error {
	if userId == 0 {
		return errors.New("无效的用户ID")
	}
	if req.Papers.Title == "" {
		return errors.New("请填写试卷名称")
	}
	if req.Papers.TotalScore <= 0 {
		return errors.New("请填写试卷总分")
	}
	if req.Papers.TotalMinutes <= 0 {
		return errors.New("请填写试卷时长")
	}
	// if req.Papers.CourseID <= 0 {
	// 	return errors.New("请选择课程")
	// }
	// if req.Papers.ChapterID <= 0 {
	// 	return errors.New("请选择章节")
	// }
	if len(req.PaperQuestions) == 0 {
		return errors.New("请添加试卷题目")
	}
	req.Papers.UserID = userId
	req.Papers.Status = model.PaperStatusDraft
	req.Papers.CreatedAt = time.Now().Unix()
	// if req.Papers.TotalMinutes > 0 && req.StartAtStr != "" {
	// 	startAt, err := time.Parse("2006-01-02 15:04", req.StartAtStr)
	// 	if err != nil {
	// 		return errors.New("开始时间格式错误，应为 YYYY-MM-DD HH:mm")
	// 	}
	// 	req.Papers.StartAt = startAt.Unix()
	// 	req.Papers.EndAt = req.Papers.StartAt + req.Papers.TotalMinutes*60

	// }

	// 开启事务
	tx := db.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 创建试卷
	if err := tx.Create(&req.Papers).Error; err != nil {
		tx.Rollback()
		return errors.New("创建失败: " + err.Error())
	}

	// 创建试卷与题目的关联关系
	if len(req.PaperQuestions) > 0 {
		var paperQuestions []model.PaperQuestions
		var TotalScore int
		for _, question := range req.PaperQuestions {
			question.PaperID = req.Papers.ID
			paperQuestions = append(paperQuestions, question)
			TotalScore += question.Score
		}
		if TotalScore != req.Papers.TotalScore {
			tx.Rollback()
			return errors.New("总分与题目得分不一致")
		}
		if err := tx.Create(&paperQuestions).Error; err != nil {
			tx.Rollback()
			return errors.New("创建失败: " + err.Error())
		}
	}

	tx.Commit()
	return nil
}

// UpdatePaperDraft 更新试卷草稿
func (api *PaperService) UpdatePaperDraft(req model.ReqPaperUpdate) error {
	if req.Papers.ID <= 0 {
		return errors.New("无效的试卷ID")
	}
	if req.Papers.Title == "" {
		return errors.New("请填写试卷名称")
	}
	if req.Papers.TotalScore <= 0 {
		return errors.New("请填写试卷总分")
	}
	if req.Papers.TotalMinutes <= 0 {
		return errors.New("请填写试卷时长")
	}
	// if req.Papers.CourseID <= 0 {
	// 	return errors.New("请选择课程")
	// }
	// if req.Papers.ChapterID <= 0 {
	// 	return errors.New("请选择章节")
	// }
	if len(req.PaperQuestions) == 0 {
		return errors.New("请添加试卷题目")
	}
	var paper model.Papers
	if err := db.DB.First(&paper, req.Papers.ID).Error; err != nil {
		return errors.New("未找到对应试卷")
	}

	if paper.Status != model.PaperStatusDraft && paper.Status != model.PaperStatusRejected {
		return errors.New("只能编辑草稿")
	}
	// if req.Papers.TotalMinutes > 0 && req.StartAtStr != "" {
	// 	startAt, err := time.Parse("2006-01-02 15:04", req.StartAtStr)
	// 	if err != nil {
	// 		return errors.New("开始时间格式错误，应为 YYYY-MM-DD HH:mm")
	// 	}
	// 	req.Papers.StartAt = startAt.Unix()
	// 	req.Papers.EndAt = req.Papers.StartAt + req.Papers.TotalMinutes*60

	// }
	now := time.Now().Unix()
	req.Papers.UpdatedAt = now

	// 开启事务
	tx := db.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 更新试卷基本信息
	updates := map[string]interface{}{
		"title":      req.Papers.Title,
		"course_id":  req.Papers.CourseID,
		"chapter_id": req.Papers.ChapterID,
		//"exam_type":   req.Papers.ExamType,
		"total_minutes": req.Papers.TotalMinutes,
		"total_score":   req.Papers.TotalScore,
		"updated_at":    now,
	}
	if err := tx.Model(&paper).Updates(updates).Error; err != nil {
		tx.Rollback()
		return errors.New("更新失败: " + err.Error())
	}
	if len(req.PaperQuestions) > 0 {
		// 删除旧的题目关联关系
		if err := tx.Where("paper_id = ?", req.Papers.ID).Delete(&model.PaperQuestions{}).Error; err != nil {
			tx.Rollback()
			return errors.New("删除旧关联失败: " + err.Error())
		}

		// 创建新的题目关联关系
		var paperQuestions []model.PaperQuestions
		var TotalScore int
		for _, question := range req.PaperQuestions {
			question.PaperID = req.Papers.ID
			paperQuestions = append(paperQuestions, question)
			TotalScore += question.Score
		}
		if TotalScore != req.Papers.TotalScore {
			tx.Rollback()
			return errors.New("总分与题目得分不一致")
		}
		if err := tx.Create(&paperQuestions).Error; err != nil {
			tx.Rollback()
			return errors.New("创建失败: " + err.Error())
		}
	}

	tx.Commit()
	return nil
}

// DeletePaper 删除试卷
func (api *PaperService) DeletePaper(id int64) error {
	if id == 0 {
		return errors.New("无效的试卷ID")
	}

	var paper model.Papers
	if err := db.DB.First(&paper, id).Error; err != nil {
		return errors.New("未找到对应试卷")
	}

	if paper.Status != model.PaperStatusDraft && paper.Status != model.PaperStatusRejected {
		return errors.New("只能删除草稿")
	}

	if err := db.DB.Delete(&paper).Error; err != nil {
		return errors.New("删除失败: " + err.Error())
	}
	if err := db.DB.Where("paper_id = ?", paper.ID).Delete(&model.PaperQuestions{}).Error; err != nil {
		return errors.New("删除关联失败: " + err.Error())
	}

	return nil
}

// SubmitForReview 提交试卷审核
func (api *PaperService) SubmitForReview(id int64) error {
	if id == 0 {
		return errors.New("无效的试卷ID")
	}

	var paper model.Papers
	if err := db.DB.First(&paper, id).Error; err != nil {
		return errors.New("未找到对应试卷")
	}

	if paper.Status != model.PaperStatusDraft && paper.Status != model.PaperStatusRejected {
		return errors.New("只能提交草稿")
	}

	now := time.Now().Unix()
	paper.Status = model.PaperStatusReviewing
	paper.SubmitAt = now

	if err := db.DB.Save(&paper).Error; err != nil {
		return errors.New("提交失败: " + err.Error())
	}

	return nil
}

// ApprovePaper 审核试卷
func (api *PaperService) ApprovePaper(id int64) error {
	if id == 0 {
		return errors.New("无效的试卷ID")
	}

	var paper model.Papers
	if err := db.DB.First(&paper, id).Error; err != nil {
		return errors.New("未找到对应试卷")
	}

	if paper.Status != model.PaperStatusReviewing {
		return errors.New("只有审核中的试卷才能通过")
	}

	now := time.Now().Unix()
	paper.Status = model.PaperStatusPublished
	paper.PublishedAt = now

	if err := db.DB.Save(&paper).Error; err != nil {
		return errors.New("审核失败: " + err.Error())
	}

	return nil
}
func (api *PaperService) RejectPaper(id int64) error {
	if id == 0 {
		return errors.New("无效的试卷ID")
	}
	var paper model.Papers
	if err := db.DB.First(&paper, id).Error; err != nil {
		return errors.New("未找到对应试卷")
	}
	if paper.Status != model.PaperStatusReviewing {
		return errors.New("只有审核中的试卷才能被拒绝")
	}
	now := time.Now().Unix()
	paper.Status = model.PaperStatusRejected
	paper.UpdatedAt = now
	if err := db.DB.Save(&paper).Error; err != nil {
		return errors.New("拒绝失败: " + err.Error())
	}
	return nil
}

// GetPaperList 获取试卷列表
func (api *PaperService) GetPaperList(c *gin.Context, req model.ReqPaperSearch) (model.RespPaperList, error) {
	var res model.RespPaperList
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 || req.PageSize > 100 {
		req.PageSize = 10
	}

	dbQuery := db.DB.Model(&model.Papers{})

	if req.Name != "" {
		dbQuery = dbQuery.Where("title ILIKE ?", "%"+req.Name+"%")
	}
	if req.Status != "" {
		dbQuery = dbQuery.Where("status = ?", req.Status)
	}
	dbQuery, has := user.ApplyDataRules(c, dbQuery, model.ModuleSyllabus)
	if !has && req.UserID > 0 {
		dbQuery = dbQuery.Where("user_id = ?", req.UserID)
	}
	// if req.UserID > 0 {
	// 	dbQuery = dbQuery.Where("user_id = ?", req.UserID)
	// }

	if err := dbQuery.Count(&res.Total).Error; err != nil {
		return res, errors.New("查询总数失败")
	}

	offset := (req.Page - 1) * req.PageSize
	if err := dbQuery.Offset(offset).Limit(req.PageSize).Order("created_at DESC").Find(&res.List).Error; err != nil {
		return res, errors.New("查询数据失败: " + err.Error())
	}

	return res, nil
}

// GetReviewingPaperList 获取待审核试卷列表
func (api *PaperService) GetReviewingPaperList(c *gin.Context, req model.ReqPaperSearch) (model.RespPaperList, error) {
	req.Status = model.PaperStatusReviewing

	return api.GetPaperList(c, req)
}

// GetPaperDetail 根据ID获取试卷详情
func (api *PaperService) GetPaperDetail(id int64) (model.RespPaperDetail, error) {
	if id == 0 {
		return model.RespPaperDetail{}, errors.New("无效的试卷ID")
	}
	var resp model.RespPaperDetail

	// 1. 获取试卷基本信息
	var paper model.Papers
	if err := db.DB.First(&paper, id).Error; err != nil {
		return resp, errors.New("未找到对应试卷")
	}
	resp.Papers = paper

	// 2. 获取试卷关联的题目列表
	var paperQuestions []model.PaperQuestions
	err := db.DB.Where("paper_id = ?", id).Find(&paperQuestions).Error

	if err != nil {
		return resp, errors.New("获取题目列表失败: " + err.Error())
	}
	resp.PaperQuestions = paperQuestions

	// 3. 获取课程信息
	if paper.CourseID > 0 {
		err := db.DB.Where("id = ?", paper.CourseID).First(&resp.Courses).Error
		if err != nil {
			return resp, errors.New("获取课程信息失败: " + err.Error())
		}
	}

	return resp, nil
}
