package workflow

import (
	"encoding/json"
	"errors"
	"fmt"
	"os"
	"sort"
	"sync"
	"tms/libs"
	"tms/model"
)

// 全局配置服务实例
var (
	moduleConfigService *ModuleConfigService
	configPath          = "config/workflow.json"
	initOnce            sync.Once
)

// ModuleConfigService 模块配置服务
type ModuleConfigService struct {
	configs map[string]model.ModuleConfig
	mu      sync.RWMutex
}

// InitWorkflowModuleConfigService 初始化模块配置服务
func InitWorkflowModuleConfigService() error {
	var initErr error
	initOnce.Do(func() {
		service := &ModuleConfigService{
			configs: make(map[string]model.ModuleConfig),
		}

		// 初始化配置
		if err := service.initConfig(); err != nil {
			initErr = err
			return
		}

		moduleConfigService = service
	})

	return initErr
}

// GetModuleConfigService 获取全局配置服务实例
func GetModuleConfigService() *ModuleConfigService {
	return moduleConfigService
}

// initConfig 初始化配置
func (s *ModuleConfigService) initConfig() error {
	s.mu.Lock()
	defer s.mu.Unlock()

	// 检查文件是否存在，不存在则创建默认配置
	if !libs.PathExists(configPath) {
		s.initDefaultConfigs()
		return s.saveConfigs()
	}

	// 文件存在，加载配置
	file, err := os.Open(configPath)
	if err != nil {
		return fmt.Errorf("failed to open config file: %w", err)
	}
	defer file.Close()

	decoder := json.NewDecoder(file)
	if err := decoder.Decode(&s.configs); err != nil {
		return fmt.Errorf("failed to decode config JSON: %w", err)
	}

	// 确保所有模块都有配置
	s.ensureAllModulesConfigured()

	return nil
}

// initDefaultConfigs 初始化默认配置
func (s *ModuleConfigService) initDefaultConfigs() {
	// 获取所有模块
	moduleList := model.GetModuleList()

	for _, module := range moduleList {
		moduleKey := module.Value
		// 创建默认配置
		s.configs[moduleKey] = model.ModuleConfig{
			ID:          module.ID,
			ModuleName:  module.Name,
			ModuleKey:   moduleKey,
			Enable:      false,
			AutoReview:  false,
			WorkflowIDS: []int64{},
		}
	}
}

// ensureAllModulesConfigured 确保所有模块都有配置
func (s *ModuleConfigService) ensureAllModulesConfigured() {
	// 获取所有模块
	moduleList := model.GetModuleList()

	for _, module := range moduleList {
		moduleKey := module.Value
		if _, exists := s.configs[moduleKey]; !exists {
			// 创建默认配置
			s.configs[moduleKey] = model.ModuleConfig{
				ID:          module.ID,
				ModuleName:  module.Name,
				ModuleKey:   moduleKey,
				Enable:      false,
				AutoReview:  false,
				WorkflowIDS: []int64{},
			}
		}
	}
}

// saveConfigs 保存配置到文件
func (s *ModuleConfigService) saveConfigs() error {
	file, err := os.Create(configPath)
	if err != nil {
		return fmt.Errorf("failed to create config file: %w", err)
	}
	defer file.Close()

	encoder := json.NewEncoder(file)
	encoder.SetIndent("", "  ")
	if err := encoder.Encode(s.configs); err != nil {
		return fmt.Errorf("failed to encode config JSON: %w", err)
	}

	return nil
}

// GetConfig 获取模块配置
func (s *ModuleConfigService) GetConfig(moduleKey string) (model.ModuleConfig, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()

	config, exists := s.configs[moduleKey]
	if !exists {
		return model.ModuleConfig{}, fmt.Errorf("module %s not found", moduleKey)
	}

	return config, nil
}

// GetAllConfigs 获取所有模块配置，并返回有序的数组列表
func (s *ModuleConfigService) GetAllConfigs() []model.ModuleConfig {
	s.mu.RLock()
	defer s.mu.RUnlock()

	// 将 map 转换为切片
	var result []model.ModuleConfig
	for _, v := range s.configs {
		result = append(result, v)
	}

	// 按照 ModuleKey 排序
	sort.Slice(result, func(i, j int) bool {
		return result[i].ID < result[j].ID
	})

	return result
}

// UpdateConfig 更新模块配置
func (s *ModuleConfigService) UpdateConfig(config model.ModuleConfig) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	// 验证模块键
	if !model.IsValidModuleKey(config.ModuleKey) {
		return errors.New("invalid module key")
	}
	if config.Enable && len(config.WorkflowIDS) == 0 {
		return errors.New("workflow ID is required when module is enabled")
	}
	// 验证工作流ID（如果启用了自动审核）
	// if config.AutoReview && len(config.WorkflowIDS) != 1 {
	// 	return errors.New("workflow ID is required when auto review is enabled")
	// }

	// 更新配置
	s.configs[config.ModuleKey] = config

	// 保存到文件
	if err := s.saveConfigs(); err != nil {
		return fmt.Errorf("failed to save config: %w", err)
	}

	return nil
}

// ToggleModule 切换模块启用状态
func (s *ModuleConfigService) ToggleModule(moduleKey string, enable bool) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	config, exists := s.configs[moduleKey]
	if !exists {
		return fmt.Errorf("module %s not found", moduleKey)
	}

	config.Enable = enable

	// 如果禁用模块，同时禁用自动审核
	if !enable {
		config.AutoReview = false
	}

	s.configs[moduleKey] = config

	// 保存到文件
	if err := s.saveConfigs(); err != nil {
		return fmt.Errorf("failed to save config: %w", err)
	}

	return nil
}
