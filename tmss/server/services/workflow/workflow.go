package workflow

import (
	"errors"
	"time"

	"tms/model"
	"tms/pkg/db"

	"gorm.io/gorm"
)

type WorkflowService struct{}

func NewWorkflowService() *WorkflowService {
	return &WorkflowService{}
}

// CreateWorkflow 创建流程
func (s *WorkflowService) CreateWorkflow(workflow *model.Workflow, nodes []model.WorkflowNodeUsers) error {
	if workflow == nil || len(nodes) == 0 {
		return errors.New("参数错误")
	}
	now := time.Now().Unix()
	workflow.CreatedAt = now
	workflow.UpdatedAt = now

	tx := db.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	if err := tx.Create(workflow).Error; err != nil {
		tx.Rollback()
		return err
	}
	err := s.AddNodes(tx, workflow, nodes)
	if err != nil {
		tx.Rollback()
		return err
	}

	return tx.Commit().Error
}
func (s *WorkflowService) AddNodes(tx *gorm.DB, workflow *model.Workflow, nodes []model.WorkflowNodeUsers) error {
	var userIds []int64
	for _, node := range nodes {
		userIds = append(userIds, node.UserID)
	}
	var users []model.Users
	err := db.DB.Where("id in ?", userIds).Find(&users).Error
	if err != nil {
		return err
	}
	userIdMap := make(map[int64]string)
	for _, user := range users {
		userIdMap[user.ID] = user.Username
	}
	var workflowNodeUsers []model.WorkflowNodeUsers
	now := time.Now().Unix()
	for i, node := range nodes {
		if _, ok := userIdMap[node.UserID]; !ok {
			return errors.New("无效的用户ID")
		}
		workflowNodeUsers = append(workflowNodeUsers, model.WorkflowNodeUsers{
			Name:       node.Name,
			WorkflowID: workflow.ID,
			CreatedAt:  now,
			UserName:   userIdMap[node.UserID],
			StepNum:    i + 1,
			UserID:     node.UserID,
		})
	}

	if err := tx.Model(&model.WorkflowNodeUsers{}).Create(&workflowNodeUsers).Error; err != nil {
		return err
	}
	return nil
}

// UpdateWorkflow 更新流程及节点
func (s *WorkflowService) UpdateWorkflow(workflow *model.Workflow, nodes []model.WorkflowNodeUsers) error {
	if workflow == nil || workflow.ID <= 0 {
		return errors.New("无效的流程ID")
	}

	workflow.UpdatedAt = time.Now().Unix()

	tx := db.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 更新主表
	if err := tx.Model(&model.Workflow{}).
		Where("id = ?", workflow.ID).
		Updates(map[string]interface{}{
			"name":        workflow.Name,
			"description": workflow.Description,
			"module_key":  workflow.ModuleKey,
			"node_len":    len(nodes),
			"updated_at":  workflow.UpdatedAt,
		}).Error; err != nil {
		tx.Rollback()
		return err
	}

	// 删除旧节点
	if err := tx.Where("workflow_id = ?", workflow.ID).
		Delete(&model.WorkflowNodeUsers{}).Error; err != nil {
		tx.Rollback()
		return err
	}

	// 插入新节点
	err := s.AddNodes(tx, workflow, nodes)
	if err != nil {
		tx.Rollback()
		return err
	}
	return tx.Commit().Error
}

// DeleteWorkflow 删除流程
func (s *WorkflowService) DeleteWorkflow(id int64) error {
	if id <= 0 {
		return errors.New("无效的流程ID")
	}

	tx := db.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 删除节点
	if err := tx.Where("workflow_id = ?", id).
		Delete(&model.WorkflowNodeUsers{}).Error; err != nil {
		tx.Rollback()
		return err
	}

	// 删除主表
	if err := tx.Delete(&model.Workflow{}, id).Error; err != nil {
		tx.Rollback()
		return err
	}

	return tx.Commit().Error
}

// GetWorkflowList 查询流程列表（支持分页和过滤）
func (s *WorkflowService) GetWorkflowList(name, status, modulekey string, pageNum, pageSize int) ([]model.Workflow, int64, error) {
	var workflows []model.Workflow
	var total int64
	if pageNum <= 0 {
		pageNum = 1
	}
	if pageSize <= 0 || pageSize > 100 {
		pageSize = 10
	}
	dbQuery := db.DB.Model(&model.Workflow{})
	if name != "" {
		dbQuery = dbQuery.Where("name ILIKE ?", "%"+name+"%")
	}
	if status != "" {
		dbQuery = dbQuery.Where("status = ?", status)
	}
	if modulekey != "" {
		dbQuery = dbQuery.Where("module_key = ?", modulekey)
	}
	if err := dbQuery.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	offset := (pageNum - 1) * pageSize
	if err := dbQuery.Offset(offset).Limit(pageSize).Order("created_at DESC").Find(&workflows).Error; err != nil {
		return nil, 0, err
	}

	return workflows, total, nil
}

// GetWorkflowByID 获取流程详情（包含节点信息）
func (s *WorkflowService) GetWorkflowByID(id int64) (*model.Workflow, []model.WorkflowNodeUsers, error) {
	var workflow model.Workflow
	var nodes []model.WorkflowNodeUsers

	if id <= 0 {
		return nil, nil, errors.New("无效的流程ID")
	}

	if err := db.DB.Where("id = ?", id).First(&workflow).Error; err != nil {
		return nil, nil, err
	}

	if err := db.DB.Where("workflow_id = ?", id).Find(&nodes).Error; err != nil {
		return nil, nil, err
	}

	return &workflow, nodes, nil
}

// ChangeWorkflowStatus 修改流程状态（启用/停用）
func (s *WorkflowService) ChangeWorkflowStatus(id int64, status string) error {
	if id <= 0 {
		return errors.New("无效的流程ID")
	}

	return db.DB.Model(&model.Workflow{}).
		Where("id = ?", id).
		Update("status", status).Error
}
