package hooks

import (
	"gorm.io/gorm"
)

func updateScheduleStatusToPublished(tx *gorm.DB, moduleKey string, dataID int64) error {

	// var schedule model.ClassSchedule
	// err := tx.Where("id = ?", dataID).First(&schedule).Error
	// if err != nil {
	// 	return err
	// }
	// teachingClass := model.TeachingClass{
	// 	ClassScheduleID: schedule.ID,
	// 	CourseID:        schedule.CourseID,
	// 	TeachingPlanID:  schedule.TeachingPlanID,
	// 	ClassroomID:     schedule.ClassroomID,
	// 	LessonPlanID:    schedule.TeachingPlanID,
	// 	Title:           schedule.Title,
	// 	Status:          model.ClassScheduleStatusPublished,
	// 	UserID:          schedule.TeacherID,
	// 	ChapterID:       schedule.TeachingPlanID,
	// 	StartAt:         schedule.StartAt,
	// 	EndAt:           schedule.EndAt,
	// 	ClassDate:       schedule.StartAt,
	// }
	// err = tx.Create(&teachingClass).Error
	// if err != nil {
	// 	return err
	// }

	return updateModuleDataStatusToPublished(tx, moduleKey, dataID)
}
