package hooks

import (
	"errors"
	"tms/model"
	"tms/utils"

	"gorm.io/gorm"
)

func updateCoursewareStatusToPublished(tx *gorm.DB, moduleKey string, dataID int64) error {
	var courseware model.Courseware
	if err := tx.Where("id = ?", dataID).First(&courseware).Error; err == nil {
		// if courseware.Status != model.CoursewareStatusReviewing {
		// 	return errors.New("只有审核中的课件才能通过")
		// }

		// tigger unzip
		updateFunc := func(coursewareID int64, unzipPath string) error {
			if err := tx.Model(&model.Courseware{}).Where("id = ?", coursewareID).Update("unzip_path", unzipPath).Error; err != nil {
				return errors.New("更新数据失败: " + err.Error())
			}
			return nil
		}

		_, err := utils.DownloadAndUnzipCourseware(courseware.ID, courseware.ChapterID, courseware.FilePath, updateFunc)
		if err != nil {
			return err
		}
	} else {
		return err
	}

	return updateModuleDataStatusToPublished(tx, moduleKey, dataID)
}
