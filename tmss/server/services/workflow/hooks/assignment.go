package hooks

import (
	"errors"
	"time"
	"tms/model"

	"gorm.io/gorm"
)

func updateAssignmentStatusToPublished(tx *gorm.DB, moduleKey string, dataID int64) error {
	var assignment model.Assignment
	if err := tx.Where("id = ?", dataID).First(&assignment).Error; err != nil {
		return err
	}
	var actions []model.AssignmentActions
	if err := tx.Where("assignment_id = ?", dataID).Find(&actions).Error; err != nil {
		return err
	}
	var studentIds []int64
	if err := tx.Model(&model.AssignmentExt{}).Where("ext_key = 'student' AND assignment_id = ?", dataID).Pluck("ext_value", &studentIds).Error; err != nil {
		return err
	}
	var studentMap []model.StudentMap
	if err := tx.Model(&model.StudentMap{}).Where("user_id IN (?)", studentIds).Find(&studentMap).Error; err != nil {
		return err
	}
	classMap := make(map[int64]int64)
	for _, sm := range studentMap {
		classMap[sm.UserID] = sm.ClassID
	}
	now := time.Now().Unix()
	var learningProgresses []model.LearningProgress
	for _, studentID := range studentIds {
		progressSummary := model.LearningProgressSummary{
			AssignmentID:          dataID,
			StudentID:             studentID,
			ClassID:               classMap[studentID],
			CourseID:              assignment.CourseID,
			ChapterID:             assignment.ChapterID,
			AssignmentName:        assignment.Name,        // 作业名称
			AssignmentDescription: assignment.Description, // 作业描述
			StartAt:               assignment.StartAt,
			EndAt:                 assignment.EndAt,
			CreatedAt:             now,
			UpdatedAt:             now,
		}
		if err := tx.Create(&progressSummary).Error; err != nil {
			return err
		}

		for _, action := range actions {
			learningProgresses = append(learningProgresses, model.LearningProgress{
				SummaryID:    progressSummary.ID,
				AssignmentID: dataID,
				StudentID:    studentID,
				CourseID:     assignment.CourseID,
				ChapterID:    assignment.ChapterID,
				ActionType:   action.ActionType,
				RefID:        action.RefID,
				Content:      action.Content,
				StartAt:      assignment.StartAt,
				EndAt:        assignment.EndAt,
				CompleteTime: 0,
				StartTime:    0,
				Required:     action.Required,
				OrderNum:     action.OrderNum,
				UseTime:      0,
				GetScore:     0,
				Minutes:      action.Minutes,
				Score:        action.Score,
				Status:       model.LearningProgressStatusNotStarted,
				CreatedAt:    now,
				UpdatedAt:    now,
			})
		}
	}

	// 批量插入学习进度数据
	if len(learningProgresses) > 0 {
		if err := tx.Create(&learningProgresses).Error; err != nil {
			return errors.New("批量插入学习进度失败: " + err.Error())
		}
	}
	return updateModuleDataStatusToPublished(tx, moduleKey, dataID)
}
