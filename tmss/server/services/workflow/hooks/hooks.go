package hooks

import (
	"encoding/json"
	"fmt"
	"log"
	"reflect"
	"time"
	"tms/model"
	"tms/utils"

	"gorm.io/gorm"
)

// 定义函数类型 转到已通过
type UpdateStatusToPublished func(tx *gorm.DB, moduleKey string, dataID int64) error

// 定义函数类型 转到草稿
type UpdateStatusToDraft func(tx *gorm.DB, moduleKey string, dataID int64) error

// 定义函数类型 转到审核中
type UpdateStatusToReviewing func(tx *gorm.DB, moduleKey string, dataID int64) error

var UpdateToPublishedMap = map[string]UpdateStatusToPublished{
	model.ModuleSyllabus: updateSyllabusStatusToPublished,
	// model.ModuleTeachingPlan:    updateModuleDataStatusToPublished,
	// model.ModuleCourses:         updateModuleDataStatusToPublished,
	model.ModuleCourseware: updateCoursewareStatusToPublished,
	// model.ModuleResources:       updateModuleDataStatusToPublished,
	// model.ModuleQuestions:       updateModuleDataStatusToPublished,
	// model.ModulePapers:          updateModuleDataStatusToPublished,
	// model.ModuleExams:           updateModuleDataStatusToPublished,
	// model.ModuleScore:           updateModuleDataStatusToPublished,
	// model.ModuleCertificates:    updateModuleDataStatusToPublished,
	model.ModuleSchedule: updateScheduleStatusToPublished,
	// model.ModuleDocuments:       updateModuleDataStatusToPublished,
	// model.ModuleKnowledgePoints: updateModuleDataStatusToPublished,
	model.ModuleAssignment: updateAssignmentStatusToPublished,
}

var UpdateToDraftMap = map[string]UpdateStatusToDraft{
	// model.ModuleSyllabus:        updateModuleDataStatusToDraft,
	// model.ModuleTeachingPlan:    updateModuleDataStatusToDraft,
	// model.ModuleCourses:         updateModuleDataStatusToDraft,
	// model.ModuleCourseware:      updateModuleDataStatusToDraft,
	// model.ModuleResources:       updateModuleDataStatusToDraft,
	// model.ModuleQuestions:       updateModuleDataStatusToDraft,
	// model.ModulePapers:          updateModuleDataStatusToDraft,
	// model.ModuleExams:           updateModuleDataStatusToDraft,
	// model.ModuleScore:           updateModuleDataStatusToDraft,
	// model.ModuleCertificates:    updateModuleDataStatusToDraft,
	// model.ModuleSchedule:        updateModuleDataStatusToDraft,
	// model.ModuleDocuments:       updateModuleDataStatusToDraft,
	// model.ModuleKnowledgePoints: updateModuleDataStatusToDraft,
	// model.ModuleAssignment:      updateModuleDataStatusToDraft,
}

var UpdateToReviewingMap = map[string]UpdateStatusToReviewing{
	// model.ModuleSyllabus:        updateModuleDataStatusToReviewing,
	// model.ModuleTeachingPlan:    updateModuleDataStatusToReviewing,
	// model.ModuleCourses:         updateModuleDataStatusToReviewing,
	// model.ModuleCourseware:      updateModuleDataStatusToReviewing,
	// model.ModuleResources:       updateModuleDataStatusToReviewing,
	// model.ModuleQuestions:       updateModuleDataStatusToReviewing,
	// model.ModulePapers:          updateModuleDataStatusToReviewing,
	// model.ModuleExams:           updateModuleDataStatusToReviewing,
	// model.ModuleScore:           updateModuleDataStatusToReviewing,
	// model.ModuleCertificates:    updateModuleDataStatusToReviewing,
	// model.ModuleSchedule:        updateModuleDataStatusToReviewing,
	// model.ModuleDocuments:       updateModuleDataStatusToReviewing,
	// model.ModuleKnowledgePoints: updateModuleDataStatusToReviewing,
	// model.ModuleAssignment:      updateModuleDataStatusToReviewing,
}

func UpdateToPublished(tx *gorm.DB, moduleKey string, dataID int64) error {
	handler, ok := UpdateToPublishedMap[moduleKey]
	if !ok {
		log.Printf("unsupported moduleKey type: %s", moduleKey)
		return updateModuleDataStatusToPublished(tx, moduleKey, dataID)
	}
	return handler(tx, moduleKey, dataID)
}
func UpdateToDraft(tx *gorm.DB, moduleKey string, dataID int64) error {
	handler, ok := UpdateToDraftMap[moduleKey]
	if !ok {
		//return fmt.Errorf("unsupported moduleKey type: %s", moduleKey)
		return updateModuleDataStatusToDraft(tx, moduleKey, dataID)
	}
	return handler(tx, moduleKey, dataID)
}
func UpdateToReviewing(tx *gorm.DB, moduleKey string, dataID int64) error {
	handler, ok := UpdateToReviewingMap[moduleKey]
	if !ok {
		//return fmt.Errorf("unsupported moduleKey type: %s", moduleKey)
		return updateModuleDataStatusToReviewing(tx, moduleKey, dataID)
	}
	return handler(tx, moduleKey, dataID)
}
func updateModuleDataStatusToPublished(tx *gorm.DB, moduleKey string, dataID int64) error {
	tableName := moduleKey // 根据 moduleKey 获取对应的业务表名
	updateNew := map[string]interface{}{
		"status":       model.WorkflowApproveStatusPublished,
		"updated_at":   time.Now().Unix(),
		"published_at": time.Now().Unix(),
	}

	//如果存在修订记录，则将原始数据状态设置为已废弃，修订记录状态设置为已通过
	var revision model.RevisionRecord
	err := tx.Where("new_id = ? AND module_key = ?", dataID, moduleKey).First(&revision).Error
	if err == nil {
		moduleData, err := model.GetModelByModuleKey(moduleKey)
		if err != nil {
			return err
		}
		contentBytes := []byte(revision.Content)
		//log.Printf("contentBytes: %s", contentBytes)
		if err := json.Unmarshal(contentBytes, moduleData); err == nil {
			//log.Printf("moduleData: %v", moduleData)
			reflectValue := reflect.ValueOf(moduleData)
			if reflectValue.Kind() == reflect.Ptr {
				reflectValue = reflectValue.Elem()
			}

			if reflectValue.Kind() == reflect.Struct {
				versionField := reflectValue.FieldByName("Version")
				if versionField.IsValid() && versionField.Kind() == reflect.String {
					version := versionField.Interface().(string)
					if v, err := utils.IncrementVersion(version); err == nil {
						updateNew["version"] = v
					}
				}
			}
		}
		updates := map[string]interface{}{
			"status":     model.WorkflowApproveStatusDeleted,
			"updated_at": time.Now().Unix(),
		}

		if err := tx.Table(tableName).Where("id = ?", revision.OriginalID).Updates(updates).Error; err != nil {
			return err
		}
		updates = map[string]interface{}{
			"status":     model.RevisionStatusPublished,
			"updated_at": time.Now().Unix(),
		}
		if err := tx.Model(&model.RevisionRecord{}).Where("id = ?", revision.ID).Updates(updates).Error; err != nil {
			return err
		}
	}
	dbConn := tx.Table(tableName).Where("id = ?", dataID).Updates(updateNew)
	if dbConn.Error != nil {
		return dbConn.Error
	}
	if dbConn.RowsAffected == 0 {
		return fmt.Errorf("no rows were updated for table %s with id %d", tableName, dataID)
	}
	return nil
}
func updateModuleDataStatusToDraft(tx *gorm.DB, moduleKey string, dataID int64) error {
	tableName := moduleKey // 根据 moduleKey 获取对应的业务表名
	var revision model.RevisionRecord
	err := tx.Where("new_id = ? AND module_key = ?", dataID, moduleKey).First(&revision).Error
	if err == nil {
		updates := map[string]interface{}{
			"status":     model.RevisionStatusRejected,
			"updated_at": time.Now().Unix(),
		}
		if err := tx.Model(&model.RevisionRecord{}).Where("id = ?", revision.ID).Updates(updates).Error; err != nil {
			return err
		}
		//删除新的提交记录
		if err := tx.Table(tableName).Where("id = ?", dataID).Delete(map[string]interface{}{}).Error; err != nil {
			return err
		}
		return nil
	}

	updates := map[string]interface{}{
		"status":     model.WorkflowApproveStatusRejected,
		"updated_at": time.Now().Unix(),
	}
	dbConn := tx.Table(tableName).Where("id = ?", dataID).Updates(updates)
	if dbConn.Error != nil {
		return dbConn.Error
	}
	if dbConn.RowsAffected == 0 {
		return fmt.Errorf("no rows were updated for table %s with id %d", tableName, dataID)
	}
	return nil
}
func updateModuleDataStatusToReviewing(tx *gorm.DB, moduleKey string, dataID int64) error {
	tableName := moduleKey // 根据 moduleKey 获取对应的业务表名
	updates := map[string]interface{}{
		"status":     model.WorkflowApproveStatusReviewing,
		"updated_at": time.Now().Unix(),
		"submit_at":  time.Now().Unix(),
	}
	dbConn := tx.Table(tableName).Where("id = ?", dataID).Updates(updates)
	if dbConn.Error != nil {
		return dbConn.Error
	}
	if dbConn.RowsAffected == 0 {
		return fmt.Errorf("no rows were updated for table %s with id %d", tableName, dataID)
	}
	return nil
}
