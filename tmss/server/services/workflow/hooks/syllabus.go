package hooks

import (
	"time"
	"tms/model"

	"gorm.io/gorm"
)

func updateSyllabusStatusToPublished(tx *gorm.DB, moduleKey string, dataID int64) error {

	var revision model.RevisionRecord
	err := tx.Where("new_id = ? AND module_key = ?", dataID, moduleKey).First(&revision).Error
	if err == nil {
		updates := map[string]interface{}{
			"syllabus_id": revision.NewID,
			"updated_at":  time.Now().Unix(),
		}
		//更新所有教学计划
		if err := tx.Model(&model.TeachingPlan{}).Where("syllabus_id = ?", revision.OriginalID).Updates(updates).Error; err != nil {
			return err
		}
	}
	return updateModuleDataStatusToPublished(tx, moduleKey, dataID)
}
