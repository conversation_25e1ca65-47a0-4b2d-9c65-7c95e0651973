package workflow

import (
	"errors"
	"fmt"
	"log"
	"time"
	"tms/model"
	"tms/pkg/db"
	"tms/services/user"
	"tms/services/workflow/hooks"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

func GetApproveConfig(moduleKey string) (model.ModuleConfig, error) {
	// 获取全局配置服务实例
	configService := GetModuleConfigService()
	if configService == nil {
		return model.ModuleConfig{}, fmt.Errorf("module config service not initialized")
	}
	return configService.GetConfig(moduleKey)
}

// GetApproveStartStatus 检查模块是否启用并返回状态
func GetApproveStartStatus(moduleKey string) (string, error) {
	// 获取全局配置服务实例
	config, err := GetApproveConfig(moduleKey)
	if err != nil {
		return "", err
	}

	// 判断模块是否启用
	if config.Enable {
		return "reviewing", nil
	}
	return "published", nil
}
func GetWorkFlowList(moduleKey string) ([]model.Workflow, error) {
	config, error := GetApproveConfig(moduleKey)
	if error != nil {
		return nil, error
	}
	if !config.Enable {
		return nil, fmt.Errorf("module %s not enabled", moduleKey)
	}
	flowIds := config.WorkflowIDS
	var workflows []model.Workflow
	err := db.DB.Where("id IN ?", flowIds).Find(&workflows).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get workflows: %w", err)
	}

	return workflows, nil
}

// CreateWorkflowApprove 创建 workflow_approve 审核记录
func CreateWorkflowApprove(req model.ReqWorkflowApprove) (*model.WorkflowApprove, error) {
	// 获取全局配置服务实例
	configService := GetModuleConfigService()
	if configService == nil {
		return nil, fmt.Errorf("module config service not initialized")
	}
	config, err := configService.GetConfig(req.ModuleKey)
	if err != nil {
		return nil, err
	}

	tx := db.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()
	approve, err := CreateWorkflow(tx, config, req)
	if err != nil {
		tx.Rollback()
		return nil, err
	}
	tx.Commit()
	return approve, nil
}
func BatchCreateWorkflowApprove(req []model.ReqWorkflowApprove) ([]model.WorkflowApprove, error) {
	// 获取全局配置服务实例
	configService := GetModuleConfigService()
	if configService == nil {
		return nil, fmt.Errorf("module config service not initialized")
	}
	config, err := configService.GetConfig(req[0].ModuleKey)
	if err != nil {
		return nil, err
	}
	tx := db.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()
	var approves []model.WorkflowApprove
	for _, req := range req {
		approve, err := CreateWorkflow(tx, config, req)
		if err != nil {
			tx.Rollback()
			return nil, err
		}
		approves = append(approves, *approve)
	}
	tx.Commit()
	return approves, nil
}
func CreateWorkflow(tx *gorm.DB, config model.ModuleConfig, req model.ReqWorkflowApprove) (*model.WorkflowApprove, error) {
	if config.Enable && req.WorkflowID == 0 {
		return nil, fmt.Errorf("workflow id is required")
	}
	var hasBeenApprovedData string
	err := tx.Table(req.ModuleKey).Select("status").Where("id = ?", req.DataID).Pluck("status", &hasBeenApprovedData).Error
	if err != nil {
		return nil, err
	}
	if hasBeenApprovedData == model.WorkflowApproveStatusPublished || hasBeenApprovedData == model.WorkflowApproveStatusReviewing {
		log.Printf("Data has been approved, skip")
		return nil, nil
	}
	if !config.Enable && req.WorkflowID == 0 {
		err := hooks.UpdateToPublished(tx, req.ModuleKey, req.DataID)
		if err != nil {
			return nil, fmt.Errorf("failed to update module data status: %w", err)
		}
		return nil, nil
	}

	// 查询是否存在当前数据的审核记录
	var existingApprove model.WorkflowApprove
	err = tx.Where("module_key = ? AND data_id = ? AND status = ?", req.ModuleKey, req.DataID, model.WorkflowApproveStatusReviewing).First(&existingApprove).Error

	var stepID int
	if err != nil {
		// 不存在审核记录，设置为第一步
		stepID = 1
	} else {
		// 存在审核记录
		return nil, fmt.Errorf("data has been approved")
	}

	// 获取当前步骤的审核人信息
	checkerID, checkerName, err := GetCheckerInfoByWorkflowIDAndStepNum(req.WorkflowID, stepID)
	if err != nil {
		return nil, fmt.Errorf("failed to get checker info: %w", err)
	}

	// 构造新的审核记录
	approve := &model.WorkflowApprove{
		UserID:     req.UserID,
		CheckerID:  checkerID,
		StepID:     stepID,
		UserName:   checkerName,
		ModuleKey:  req.ModuleKey,
		WorkflowID: req.WorkflowID,
		DataID:     req.DataID,
		DataTitle:  req.Title,
		Status:     model.WorkflowApproveStatusReviewing, // 默认状态为待审批
	}

	// 插入数据库
	if err := tx.Create(approve).Error; err != nil {
		return nil, fmt.Errorf("failed to create workflow approve record: %w", err)
	}
	err = hooks.UpdateToReviewing(tx, req.ModuleKey, req.DataID)
	if err != nil {
		return nil, fmt.Errorf("failed to update module data status: %w", err)
	}
	return approve, nil
}

// GetCheckerInfoByWorkflowIDAndStepNum 根据 workflowID 和 stepNum 查询审核人信息
func GetCheckerInfoByWorkflowIDAndStepNum(workflowID int64, stepNum int) (userID int64, username string, err error) {
	var nodeUser model.WorkflowNodeUsers
	err = db.DB.Where("workflow_id = ? AND step_num = ?", workflowID, stepNum).First(&nodeUser).Error
	if err != nil {
		return 0, "", fmt.Errorf("failed to find node user: %w", err)
	}
	return nodeUser.UserID, nodeUser.UserName, nil
}
func GetApproveIDIsReviewing(moduleKey string, dataID int64) (int64, error) {
	var approve model.WorkflowApprove
	err := db.DB.Where("module_key = ? AND data_id = ? AND status = ?", moduleKey, dataID, model.WorkflowApproveStatusReviewing).First(&approve).Error
	if err != nil {
		return 0, fmt.Errorf("failed to find approve record: %w", err)
	}
	return approve.ID, nil
}
func GetApproveIDIsRejected(moduleKey string, dataID int64) (int64, error) {
	var approve model.WorkflowApprove
	err := db.DB.Where("module_key = ? AND data_id = ? AND status = ?", moduleKey, dataID, model.WorkflowApproveStatusRejected).First(&approve).Error
	if err != nil {
		return 0, fmt.Errorf("failed to find approve record: %w", err)
	}
	return approve.ID, nil
}

func CreateApproveRemark(db *gorm.DB, req model.WorkflowRemark) error {
	return db.Model(&model.WorkflowRemark{}).Create(&req).Error
}

// UpdateModuleDataStatus 动态更新业务表的 status 字段为 published

// ApproveData 审核通过数据并推进流程
func ApproveData(approveID int64, remarker string, operatorID int64) error {
	tx := db.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()
	err := ApproveDataOne(tx, approveID, remarker, operatorID)
	if err != nil {
		tx.Rollback()
		return err
	}

	tx.Commit()
	return nil
}

// BatchApproveData 批量审核通过数据
func BatchApproveData(approveIDs []int64, remark string, operatorID int64) error {
	tx := db.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	for _, approveID := range approveIDs {
		err := ApproveDataOne(tx, approveID, remark, operatorID)
		if err != nil {
			tx.Rollback()
			return err
		}
	}
	tx.Commit()
	return nil
}

// ApproveData 审核通过数据并推进流程
func ApproveDataOne(tx *gorm.DB, approveID int64, remarker string, operatorID int64) error {
	if operatorID == 0 {
		return errors.New("无效的操作人ID")
	}
	if approveID == 0 {
		return errors.New("无效的审核ID")
	}
	// 获取当前审核记录
	var currentApprove model.WorkflowApprove
	err := tx.Model(&model.WorkflowApprove{}).Where("id = ?", approveID).First(&currentApprove).Error
	if err != nil {
		return fmt.Errorf("failed to find approve record: %w", err)
	}
	// 更新当前审核记录为通过状态
	now := time.Now().Unix()
	currentApprove.Status = model.WorkflowApproveStatusPublished
	currentApprove.ApprovedAt = now
	currentApprove.Remark = remarker
	currentApprove.OperatorID = operatorID

	if err := tx.Save(&currentApprove).Error; err != nil {
		return fmt.Errorf("failed to update current approve status: %w", err)
	}
	remarkData := model.WorkflowRemark{
		ApproveID:  currentApprove.ID,
		Remark:     remarker,
		DataID:     currentApprove.DataID,
		UserID:     operatorID,
		StepID:     currentApprove.StepID,
		RemarkType: "remark",
		CreatedAt:  time.Now().Unix(),
	}
	// 下一步审核人 step = currentStep + 1
	nextStep := currentApprove.StepID + 1

	// 查询是否存在下一个审核人
	checkerID, checkerName, err := GetCheckerInfoByWorkflowIDAndStepNum(currentApprove.WorkflowID, nextStep)
	if err != nil {
		// 没有下一个审核人，流程结束，更新业务表状态为 published
		if err := hooks.UpdateToPublished(tx, currentApprove.ModuleKey, currentApprove.DataID); err != nil {
			return fmt.Errorf("failed to publish module data: %w", err)
		}
		if err := CreateApproveRemark(tx, remarkData); err != nil {
			return fmt.Errorf("failed to create approve remark: %w", err)
		}
		return nil
	}

	// 存在下一个审核人，插入新的 workflow_approve 记录
	newApprove := &model.WorkflowApprove{
		UserID:     currentApprove.UserID,
		CheckerID:  checkerID,
		StepID:     nextStep,
		UserName:   checkerName,
		ModuleKey:  currentApprove.ModuleKey,
		WorkflowID: currentApprove.WorkflowID,
		DataID:     currentApprove.DataID,
		DataTitle:  currentApprove.DataTitle,
		Status:     model.WorkflowApproveStatusReviewing,
		CreatedAt:  time.Now().Unix(),
	}

	if err = tx.Create(newApprove).Error; err != nil {
		return fmt.Errorf("failed to create next approve record: %w", err)
	}
	remarkData.NextID = newApprove.ID
	if err := CreateApproveRemark(tx, remarkData); err != nil {
		return fmt.Errorf("failed to create approve remark: %w", err)
	}
	return nil
}

// RejectData 打回数据，退回至上一步或直接驳回流程
func RejectData(approveID int64, rejectReason, remarker string, operatorID int64) error {
	tx := db.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()
	err := RejectDataOne(tx, approveID, rejectReason, remarker, operatorID)
	if err != nil {
		tx.Rollback()
		return err
	}
	tx.Commit()

	return nil
}

// BatchRejectData 批量驳回数据
func BatchRejectData(approveIDs []int64, rejectReason, remarker string, operatorID int64) error {
	tx := db.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()
	for _, approveID := range approveIDs {
		err := RejectDataOne(tx, approveID, rejectReason, remarker, operatorID)
		if err != nil {
			tx.Rollback()
			return err
		}

	}
	tx.Commit()
	return nil
}
func RejectDataOne(tx *gorm.DB, approveID int64, rejectReason, remarker string, operatorID int64) error {
	if operatorID == 0 {
		return errors.New("无效的操作人ID")
	}
	if approveID == 0 {
		return errors.New("无效的审核ID")
	}
	// 获取当前审核记录
	var currentApprove model.WorkflowApprove
	err := tx.Model(&model.WorkflowApprove{}).Where("id = ?", approveID).First(&currentApprove).Error
	if err != nil {
		return fmt.Errorf("failed to find approve record: %w", err)
	}
	now := time.Now().Unix()
	// 设置当前审核记录为已拒绝
	currentApprove.Status = model.WorkflowApproveStatusRejected
	currentApprove.RejectAt = now
	currentApprove.Remark = remarker
	currentApprove.OperatorID = operatorID // 记录操作人

	if err := tx.Save(&currentApprove).Error; err != nil {
		return fmt.Errorf("failed to update current approve status to rejected: %w", err)
	}
	remarkData := model.WorkflowRemark{
		ApproveID:    currentApprove.ID,
		Remark:       remarker,
		RejectReason: rejectReason,
		DataID:       currentApprove.DataID,
		UserID:       operatorID,
		StepID:       currentApprove.StepID,
		RemarkType:   "rejected",
		CreatedAt:    time.Now().Unix(),
	}
	// 判断是否存在上一步（StepID > 1）
	if currentApprove.StepID > 1 {
		prevStep := currentApprove.StepID - 1
		// 查询是否已存在上一步的审核记录
		var prevApprove model.WorkflowApprove
		err = tx.Where("module_key = ? AND data_id = ? AND step_id = ? AND workflow_id = ?",
			currentApprove.ModuleKey, currentApprove.DataID, prevStep, currentApprove.WorkflowID).First(&prevApprove).Error

		if err == nil {
			// 上一步记录存在，将其状态重置为 WorkflowApproveStatusBackedUp，并记录拒绝原因
			prevApprove.Status = model.WorkflowApproveStatusBackedUp
			prevApprove.RejectAt = now
			prevApprove.RejectReason = rejectReason
			if err := tx.Save(&prevApprove).Error; err != nil {
				return fmt.Errorf("failed to reset previous approve status: %w", err)
			}
			remarkData.NextID = prevApprove.ID
		} else {
			return fmt.Errorf("failed to find previous approve record: %w", err)
		}
	} else {
		// 当前是第一步，无上一步，直接将业务表数据状态改为 draft
		if err := hooks.UpdateToDraft(tx, currentApprove.ModuleKey, currentApprove.DataID); err != nil {
			return fmt.Errorf("failed to revert module data to draft: %w", err)
		}
	}
	if err := CreateApproveRemark(tx, remarkData); err != nil {
		return fmt.Errorf("failed to create approve remark: %w", err)
	}

	return nil
}

// ListWorkflowApproves 查询审核记录列表，支持分页和多条件过滤（使用 model.WorkflowApprove 作为查询条件）
func ListWorkflowApproves(c *gin.Context, req model.ReqWorkflowList) ([]*model.ReqWorkflowApproveWithUser, int64, error) {
	var approves []*model.WorkflowApprove
	var total int64

	dbQuery := db.DB.Model(&model.WorkflowApprove{})

	// 按 moduleKey 过滤
	if req.ModuleKey != "" {
		dbQuery = dbQuery.Where("module_key = ?", req.ModuleKey)
	}

	// 按 status 过滤
	if req.Status != "" {
		dbQuery = dbQuery.Where("status = ?", req.Status)
	}
	dbQuery, has := user.ApplyDataRules(c, dbQuery, model.ModuleApprove)
	if !has && req.CheckerID > 0 {
		dbQuery = dbQuery.Where("checker_id = ?", req.CheckerID)
	}
	if req.UserID > 0 {
		dbQuery = dbQuery.Where("user_id = ?", req.UserID)
	}

	if req.StartAt != "" && req.EndAt != "" {
		// 获取本地时区
		loc, err := time.LoadLocation("Local")
		if err != nil {
			return nil, 0, fmt.Errorf("failed to load local timezone: %w", err)
		}

		// 解析时间字符串为本地时间
		startAt, err := time.ParseInLocation(time.RFC3339, req.StartAt, loc)
		if err != nil {
			return nil, 0, fmt.Errorf("invalid start_at format: %w", err)
		}
		endAt, err := time.ParseInLocation(time.RFC3339, req.EndAt, loc)
		if err != nil {
			return nil, 0, fmt.Errorf("invalid end_at format: %w", err)
		}

		// 检查时间范围是否有效
		if startAt.After(endAt) {
			return nil, 0, fmt.Errorf("start_at cannot be after end_at")
		}

		// 将时间范围应用到查询条件中
		dbQuery = dbQuery.Where("created_at BETWEEN ? AND ?", startAt.Unix(), endAt.Unix())
	}
	if req.Page < 1 {
		req.Page = 1
	}
	if req.PageSize <= 0 || req.PageSize > 100 {
		req.PageSize = 10
	}
	log.Printf("page: %v,pagesize:%v", req.Page, req.PageSize)
	// 获取总记录数
	if err := dbQuery.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count workflow approves: %w", err)
	}

	// 分页查询
	offset := (req.Page - 1) * req.PageSize
	if err := dbQuery.Offset(offset).Limit(req.PageSize).Order("id desc").Find(&approves).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to list workflow approves: %w", err)
	}
	var userIds []int64
	for _, remark := range approves {
		userIds = append(userIds, remark.UserID)
	}
	var users []model.Users
	if err := db.DB.Model(&model.Users{}).Where("id in (?)", userIds).Find(&users).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to get users: %w", err)
	}
	userIdMap := make(map[int64]string)
	for _, user := range users {
		userIdMap[user.ID] = user.Username
	}
	result := make([]*model.ReqWorkflowApproveWithUser, 0, len(approves))
	for _, approve := range approves {
		username := userIdMap[approve.UserID]
		result = append(result, &model.ReqWorkflowApproveWithUser{
			WorkflowApprove: approve,
			UserName:        username,
			Created:         time.Unix(approve.CreatedAt, 0).Format("2006-01-02 15:04"),
		})
	}

	return result, total, nil
}

// ListWorkflowRemarks 查询审核备注列表，支持分页和多条件过滤
func ListWorkflowRemarks(dataID int64) ([]*model.WorkflowRemarkWithUser, error) {
	var remarks []*model.WorkflowRemark
	err := db.DB.Model(&model.WorkflowRemark{}).Where("data_id = ?", dataID).Order("created_at desc").Find(&remarks).Error
	if err != nil {
		return nil, err
	}
	var userIds []int64
	for _, remark := range remarks {
		userIds = append(userIds, remark.UserID)
	}
	var users []model.Users
	if err := db.DB.Model(&model.Users{}).Where("id in (?)", userIds).Find(&users).Error; err != nil {
		return nil, fmt.Errorf("failed to get users: %w", err)
	}
	userIdMap := make(map[int64]string)
	for _, user := range users {
		userIdMap[user.ID] = user.Username
	}
	result := make([]*model.WorkflowRemarkWithUser, 0, len(remarks))
	for _, remark := range remarks {
		username := userIdMap[remark.UserID]
		result = append(result, &model.WorkflowRemarkWithUser{
			WorkflowRemark: remark,
			UserName:       username,
			Created:        time.Unix(remark.CreatedAt, 0).Format("2006-01-02 15:04"),
		})
	}

	return result, nil
}
