package libs

import (
	"strconv"

	"github.com/gin-gonic/gin"
)

// PageParam 定义分页参数结构
type PageParam struct {
	Page     int `json:"page" form:"page"`          // 当前页码
	PageSize int `json:"pageSize" form:"page_size"` // 每页大小
}

// PageResult 定义分页结果结构
type PageResult struct {
	List       interface{} `json:"list"`        // 数据列表
	Total      int64       `json:"total"`       // 总数
	Page       int         `json:"page"`        // 当前页码
	PageSize   int         `json:"page_size"`   // 每页大小
	TotalPages int64       `json:"total_pages"` // 总页数
}

// GetPageParam 从 Gin 上下文中获取分页参数
func GetPageParam(c *gin.Context) PageParam {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "10"))

	if page < 1 {
		page = 1
	}
	if pageSize < 1 {
		pageSize = 10 // 默认每页大小
	}

	return PageParam{
		Page:     page,
		PageSize: pageSize,
	}
}

// GetOffset 根据分页参数计算数据库查询的偏移量
func (p *PageParam) GetOffset() int {
	return (p.Page - 1) * p.PageSize
}

// GeneratePageResult 生成分页结果
// 此函数仅用于封装分页数据，不负责计算偏移量或执行数据库查询。
// 数据库查询时请使用 PageParam.GetOffset() 来计算偏移量。
func GeneratePageResult(list interface{}, total int64, pageParam PageParam) PageResult {
	totalPages := (total + int64(pageParam.PageSize) - 1) / int64(pageParam.PageSize)
	return PageResult{
		List:       list,
		Total:      total,
		Page:       pageParam.Page,
		PageSize:   pageParam.PageSize,
		TotalPages: totalPages,
	}
}
