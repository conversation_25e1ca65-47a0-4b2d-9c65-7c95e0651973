package model

const (
	TeachingDocumentStatusDraft     = "draft"     // 草稿
	TeachingDocumentStatusPublished = "published" // 已发布
	TeachingDocumentStatusReviewing = "reviewing" // 审核中
	TeachingDocumentStatusRejected  = "rejected"  // 审核拒绝
)

// TeachingDocument 教案表
type TeachingDocument struct {
	ID              int64  `gorm:"primary_key;AUTO_INCREMENT;column:id;type:bigint;" json:"id"`
	ClassScheduleID int64  `gorm:"column:class_schedule_id;type:bigint;index;" json:"class_schedule_id"` // 关联课时计划ID
	Title           string `gorm:"column:title;type:varchar(200);" json:"title"`                         // 教案标题
	Content         string `gorm:"column:content;type:text;" json:"content"`                             // 教案内容（富文本）
	UserID          int64  `gorm:"column:user_id;type:bigint;" json:"user_id"`                           // 授课教师ID
	CoursewareID    int64  `gorm:"column:courseware_id;type:bigint;" json:"courseware_id"`               // 关联课件ID
	Status          string `gorm:"column:status;type:varchar(20);" json:"status"`                        // 状态：draft, published
	CreatedAt       int64  `gorm:"column:created_at;autoCreateTime" json:"created_at"`
	UpdatedAt       int64  `gorm:"column:updated_at;autoUpdateTime" json:"updated_at"`
	SubmitAt        int64  `gorm:"column:submit_at;" json:"submit_at"`                           // 提交时间（审核提交时间）
	PublishedAt     int64  `gorm:"column:published_at;" json:"published_at"`                     // 发布时间
	Version         string `gorm:"column:version;type:varchar(20);default:v1.0;" json:"version"` // 版本号
}

// TableName 设置表名
func (t *TeachingDocument) TableName() string {
	return "teaching_document"
}

// 教案搜索请求
type ReqTeachingDocumentSearch struct {
	Page            int    `form:"page,default=1" json:"page"`
	PageSize        int    `form:"page_size,default=10" json:"page_size"`
	ClassScheduleID int64  `form:"class_schedule_id" json:"class_schedule_id"` // 课时计划ID
	CoursewareID    int64  `form:"courseware_id" json:"courseware_id"`         // 课件ID
	UserID          int64  `form:"user_id" json:"user_id"`                     // 教师ID
	Title           string `form:"title" json:"title"`                         // 教案标题
	Status          string `form:"status" json:"status"`                       // 状态
}

// 教案详情返回结构
type RespTeachingDocumentDetail struct {
	TeachingDocument
	ClassScheduleTitle string `json:"class_schedule_title"`       // 课时标题
	CoursewareTitle    string `json:"courseware_title,omitempty"` // 关联的课件信息
	TeacherName        string `json:"teacher_name"`               // 教师姓名

}
type RespTeachingDocumentDetailWithRev struct {
	TeachingDocument
	Rev *RevisionRecord `json:"rev,omitempty" gorm:"-"` // 可能为空

}
type RespTeachingDocumentList struct {
	List  []RespTeachingDocumentDetailWithRev `json:"list"`
	Total int64                               `json:"total"`
}
