package model

type StudentMap struct {
	ID      int64 `gorm:"primary_key;AUTO_INCREMENT;column:id;type:bigint;" json:"id"`
	UserID  int64 `gorm:"column:user_id;type:bigint;" json:"user_id"`
	ClassID int64 `gorm:"column:class_id;type:bigint;" json:"class_id"`
	MajorID int64 `gorm:"column:major_id;type:bigint;" json:"major_id"`
}

func (StudentMap) TableName() string {
	return "student_map"
}

type ReqSetStudentClassID struct {
	Ids     []int64 `json:"ids"`
	ClassID int64   `json:"class_id"`
	MajorID int64   `json:"major_id"`
}
