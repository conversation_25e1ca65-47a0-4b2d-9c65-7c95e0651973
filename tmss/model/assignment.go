package model

const (
	AssignmentActionTypeQuestion   = "questions"  // 做试题 关联Questions表
	AssignmentActionTypeCourseware = "courseware" // 看课件 关联Courseware表
	AssignmentActionTypeResources  = "resources"  // 看资料 关联Resources表
)

// 状态定义
const (
	AssignmentStatusDraft     = "draft"     //草稿
	AssignmentStatusPublished = "published" //已发布
	AssignmentStatusReviewing = "reviewing" //审核中
	AssignmentStatusRejected  = "rejected"  //驳回
)

// 下发作业管理
type Assignment struct {
	ID          int64  `gorm:"primary_key;AUTO_INCREMENT;column:id;type:bigint;" json:"id"`
	CourseID    int64  `gorm:"column:course_id;type:bigint;" json:"course_id"`              // 课程ID
	ChapterID   int64  `gorm:"column:chapter_id;type:bigint;" json:"chapter_id"`            // 章节ID
	Name        string `gorm:"column:name;type:varchar;size:255;" json:"name"`              // 作业名称
	ActionType  string `gorm:"column:action_type;type:varchar;size:20;" json:"action_type"` // 动作类型
	StartAt     int64  `gorm:"column:start_at;" json:"start_at"`                            // 开始时间（开始时间）
	EndAt       int64  `gorm:"column:end_at;" json:"end_at"`                                // 结束时间（截止时间）
	UserID      int64  `gorm:"column:user_id;type:bigint;" json:"user_id"`                  // 下发教师ID
	Description string `gorm:"column:description;type:text;" json:"description"`            // 描述
	Status      string `gorm:"column:status;varchar;size:50;" json:"status"`                // 状态
	CreatedAt   int64  `gorm:"column:created_at;autoCreateTime" json:"created_at"`
	UpdatedAt   int64  `gorm:"column:updated_at;autoUpdateTime" json:"updated_at"`
	SubmitAt    int64  `gorm:"column:submit_at;" json:"submit_at"`       // 提交时间（审核提交时间）
	PublishedAt int64  `gorm:"column:published_at;" json:"published_at"` // 发布时间（审核通过时间）
}

// TableName sets the insert table name for this struct type
func (a *Assignment) TableName() string {
	return "assignment"
}

type AssignmentUpdate struct {
	Assignment Assignment          `json:"assignment"`  // 关联主表
	ClassIDs   []int64             `json:"class_ids"`   //关联班级 写入AssignmentExt
	MajorIDs   []int64             `json:"major_ids"`   //关联专业 写入AssignmentExt
	TeacherIDs []int64             `json:"teacher_ids"` //关联批阅老师 写入AssignmentExt
	Actions    []AssignmentActions `json:"actions"`     //关联动作

}
type AssignmentDetail struct {
	Assignment Assignment          `json:"assignment"` // 关联主表
	Classes    []Class             `json:"classes"`    //关联班级
	Majors     []Majors            `json:"majors"`     //关联专业
	Teachers   []Users             `json:"teachers"`   //关联批阅老师
	Actions    []AssignmentActions `json:"actions"`    //关联动作
}
type AssignmentListDetail struct {
	Assignment
	CourseName  string `json:"course_name"`
	ChapterName string `json:"chapter_name"`
	//ActionName  string `json:"action_name"`
	Rev *RevisionRecord `json:"rev,omitempty" gorm:"-"` // 可能为空
}
type AssignmentList struct {
	List  []AssignmentListDetail `json:"list"`
	Total int64                  `json:"total"`
}

func GetAssignmentActionName(actionType string) string {
	switch actionType {
	case AssignmentActionTypeQuestion:
		return "做试题"
	case AssignmentActionTypeCourseware:
		return "看课件"
	case AssignmentActionTypeResources:
		return "看资料"
	default:
		return ""
	}
}
