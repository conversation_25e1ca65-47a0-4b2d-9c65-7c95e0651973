package model

import (
	"time"
)

// RequestLog 表示 HTTP 请求日志
type RequestLog struct {
	ID        int64     `gorm:"primary_key;AUTO_INCREMENT;column:id;type:bigint;" json:"id"` // 班级ID
	Method    string    `gorm:"size:10" json:"method"`                                       // 请求方法 (GET, POST 等)
	UserId    int64     `json:"user_id"`                                                     // 用户ID
	Data      string    `gorm:"type:text" json:"data"`
	Path      string    `gorm:"size:255" json:"path"`       // 请求路径
	IP        string    `gorm:"size:15" json:"ip"`          // 请求IP地址
	UserAgent string    `gorm:"size:255" json:"user_agent"` // 用户代理信息
	Status    int       `json:"status"`                     // 响应状态码
	Duration  int64     `json:"duration"`                   // 请求耗时（纳秒）
	CreatedAt time.Time `json:"created_at"`                 // 请求完成时间
}

// TableName 设置表名
func (RequestLog) TableName() string {
	return "request_logs"
}
