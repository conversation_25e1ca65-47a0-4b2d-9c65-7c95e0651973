package model

const (
	AnswerSubmitCommitStatus    = "commit"    // 提交
	AnswerSubmitTerminateStatus = "terminate" // 终止
)

// ExamAnswer struct is a row record of the exam_answers table in the tms database
type ExamAnswer struct {
	ID               int64  `gorm:"primary_key;AUTO_INCREMENT;column:id" json:"id"`                     // 主键ID
	ExamProgressID   int64  `gorm:"column:exam_progress_id;type:bigint;" json:"exam_progress_id"`       // 关联的考试进度ID
	QuestionID       int64  `gorm:"column:question_id;type:bigint;" json:"question_id"`                 // 题目ID
	QuestionType     string `gorm:"column:question_type;varchar;size:50;" json:"question_type"`         // 题目类型
	QuestionCategory string `gorm:"column:question_category;varchar;size:50;" json:"question_category"` // 题目分类
	UserAnswer       string `gorm:"column:user_answer;type:text;" json:"user_answer"`                   // 用户答案
	Score            int    `gorm:"column:score;type:int;" json:"score"`                                // 得分
	AnsweredAt       int64  `gorm:"column:answered_at;type:bigint;" json:"answered_at"`                 // 作答时间（Unix 时间戳）
	CreatedAt        int64  `gorm:"column:created_at;autoCreateTime" json:"created_at"`                 // 创建时间
	UpdatedAt        int64  `gorm:"column:updated_at;autoUpdateTime" json:"updated_at"`                 // 更新时间
}

// TableName sets the insert table name for this struct type
func (e *ExamAnswer) TableName() string {
	return "exam_answers"
}

// 如果是课件类型
// 1. scorm: 做完后，需要 get value, cmi.completion_status 判断是否完成, 获取分数 cmi.score.raw
// 2. 虚拟课件：调用课件的历史详情接口，判断是否完成, 获取分数
