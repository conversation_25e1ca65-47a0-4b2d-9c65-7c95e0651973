package model

const (
	WorkflowMsgStatusHasRead = "has_read" // 已读
	WorkflowMsgStatusUnRead  = "un_read"  // 未读
)

type WorkflowMsg struct {
	ID        int64  `gorm:"primary_key;AUTO_INCREMENT;column:id;type:bigint;" json:"id"`
	ApproveID int64  `gorm:"column:approve_id;type:bigint;" json:"approve_id"`   // 审核ID
	DataID    int64  `gorm:"column:data_id;type:bigint;" json:"data_id"`         // 审核数据ID
	SenderID  int64  `gorm:"column:sender_id;type:bigint;" json:"sender_id"`     // 发送人ID
	ResiverID int64  `gorm:"column:resiver_id;type:bigint;" json:"resiver_id"`   // 接收人ID
	Title     string `gorm:"column:title;type:varchar;size:255;" json:"title"`   // 数据标题（唯一）
	Content   string `gorm:"column:content;type:text;" json:"content"`           // 内容描述
	Status    string `gorm:"column:status;type:varchar;size:50;" json:"status"`  // 审核状态，例如：已读、未读等
	CreatedAt int64  `gorm:"column:created_at;autoCreateTime" json:"created_at"` // 创建时间（秒级时间戳）
	ReadAt    int64  `gorm:"column:read_at;autoCreateTime" json:"read_at"`       // 阅读时间（秒级时间戳）
	Remark    string `gorm:"column:remark;type:varchar;size:255;" json:"remark"` // 备注

}

// TableName sets the insert table name for this struct type
func (w *WorkflowMsg) TableName() string {
	return "workflow_msg"
}
