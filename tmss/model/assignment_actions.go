package model

type AssignmentActions struct {
	ID           int64  `gorm:"primary_key;AUTO_INCREMENT;column:id;type:bigint;" json:"id"`
	AssignmentID int64  `gorm:"column:assignment_id;type:bigint;" json:"assignment_id"`      // 关联作业ID
	ActionType   string `gorm:"column:action_type;type:varchar;size:20;" json:"action_type"` // 动作类型
	RefID        int64  `gorm:"column:ref_id;type:bigint;" json:"ref_id"`                    // 关联ID（试题ID、课件ID、资料ID等）
	Content      string `gorm:"column:content;type:text;" json:"content"`                    // 内容描述
	Required     bool   `gorm:"column:required;type:boolean;" json:"required"`               // 是否必做
	Status       string `gorm:"column:status;varchar;size:50;" json:"status"`                // 状态
	OrderNum     int    `gorm:"column:order_num;type:int;" json:"order_num"`                 // 排序序号
	Minutes      int    `gorm:"column:minutes;type:int;" json:"minutes"`                     // 学习时长（分钟）
	Score        int    `gorm:"column:score;type:int;" json:"score"`                         // 最低分数
	CreatedAt    int64  `gorm:"column:created_at;autoCreateTime" json:"created_at"`
}

func (a *AssignmentActions) TableName() string {
	return "assignment_actions"
}
