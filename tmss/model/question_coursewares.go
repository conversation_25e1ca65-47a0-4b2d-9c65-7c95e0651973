package model

// QuestionCourseware struct is a row record of the question_coursewares table in the tms database
type QuestionCourseware struct {
	ID           int64 `gorm:"primary_key;AUTO_INCREMENT;column:id;type:bigint;" json:"id"`     // 主键
	QuestionID   int64 `gorm:"column:question_id;type:bigint;not null;" json:"question_id"`     // 外键：试题ID
	CoursewareID int64 `gorm:"column:courseware_id;type:bigint;not null;" json:"courseware_id"` // 外键：课件ID
	CourseID     int64 `gorm:"column:course_id;type:bigint;" json:"course_id"`                  // 外键：课程ID
	ChapterID    int64 `gorm:"column:chapter_id;type:bigint;" json:"chapter_id"`                // 外键：章节ID
}

// TableName sets the insert table name for this struct type
func (qc *QuestionCourseware) TableName() string {
	return "question_coursewares"
}
