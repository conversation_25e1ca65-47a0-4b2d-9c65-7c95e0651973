package model

// 学习进度总汇总表
type LearningSummary struct {
	ID                    int64   `gorm:"primary_key;AUTO_INCREMENT;column:id;type:bigint;" json:"id"`
	StudentID             int64   `gorm:"column:student_id;type:bigint;" json:"student_id"`                                         // 学生ID
	ClassID               int64   `gorm:"column:class_id;type:bigint;" json:"class_id"`                                             // 班级ID
	MajorsID              int64   `gorm:"column:majors_id;type:bigint;" json:"majors_id"`                                           // 专业ID
	CourseID              int64   `gorm:"column:course_id;type:bigint;" json:"course_id"`                                           // 课程ID
	ChapterCompletionRate float64 `gorm:"column:chapter_completion_rate;type:decimal;default:0.00;" json:"chapter_completion_rate"` // 章节完成率（%）
	TotalStudyTime        int     `gorm:"column:total_study_time;type:int;default:0;" json:"total_study_time"`                      // 总学习时长（秒）
	CreatedAt             int64   `gorm:"column:created_at;autoCreateTime" json:"created_at"`
	UpdatedAt             int64   `gorm:"column:updated_at;autoUpdateTime" json:"updated_at"`
}

// TableName sets the insert table name for this struct type
func (l *LearningSummary) TableName() string {
	return "learning_summary"
}
