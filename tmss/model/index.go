package model

import (
	"fmt"
	"log"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

//	func CreateIndexIfNotExists(Db *gorm.DB, model interface{}, field, indexName string) {
//		if db.AppConfig.DBType != "mongodb" {
//			return
//		}
//		err := Db.Migrator().CreateIndex(model, indexName)
//		if err != nil {
//			log.Printf("Failed to create index %s for field %s: %v", indexName, field, err)
//		}
//	}
func FixSequence(db *gorm.DB, table string) error {
	var maxID int64
	db.Table(table).Select("MAX(id)").Scan(&maxID)

	seqName := fmt.Sprintf("%s_id_seq", table)
	err := db.Exec(fmt.Sprintf("SELECT setval('%s', %d)", seqName, maxID+1)).Error
	if err != nil {
		log.Printf("Failed to fix sequence for %s: %v", table, err)
		return err
	}
	log.Printf("Fixed sequence for %s. New value: %d", table, maxID+1)
	return nil
}

type ReqPage struct {
	Page     int    `json:"page" form:"page" binding:"gte=1"` // 页码
	PageSize int    `json:"page_size" form:"page_size"`       // 每页大小
	Name     string `json:"name" form:"name"`                 // 查询条件
	Status   string `json:"status" form:"status"`             // 查询条件

}

func GetPage(c *gin.Context) ReqPage {
	var req ReqPage
	if err := c.ShouldBindQuery(&req); err != nil {
		return ReqPage{}
	}
	if req.Page == 0 {
		req.Page = 1
	}
	if req.PageSize == 0 || req.PageSize > 500 {
		req.PageSize = 10
	}
	return req
}
