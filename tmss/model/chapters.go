package model

// Chapter struct is a row record of the chapters table in the tms database
type Chapter struct {
	ID          int64  `gorm:"primary_key;AUTO_INCREMENT;column:id;type:bigint;" json:"id"`
	UserID      int64  `gorm:"column:user_id;type:bigint;" json:"user_id"`               // 创建人ID
	Name        string `gorm:"column:name;type:varchar;size:255;" json:"name"`           // 章节名称
	ParentID    int64  `gorm:"column:parent_id;type:bigint;default:0;" json:"parent_id"` // 上级章节ID（默认为0表示顶级章节）
	Description string `gorm:"column:description;type:text;" json:"description"`         // 章节描述
	CreatedAt   int64  `gorm:"column:created_at;autoCreateTime" json:"created_at"`       // 创建时间（Unix 时间戳）
	UpdatedAt   int64  `gorm:"column:updated_at;autoUpdateTime" json:"updated_at"`       // 更新时间（Unix 时间戳）

	AttachedAttributeValue string `gorm:"-" json:"attached_attribute_value,omitempty"` // 表示该节点挂载的属性值
}

// TableName sets the insert table name for this struct type
func (c *Chapter) TableName() string {
	return "chapters"
}

type ReqChapterSearch struct {
	Name     string `form:"name" json:"name"`           // 按名称模糊搜索
	ParentID int64  `form:"parent_id" json:"parent_id"` // 上级章节ID
}

// ChapterTreeNode 树形结构节点
type ChapterTreeNode struct {
	ID                     int64              `json:"id"`
	Name                   string             `json:"name"`
	Description            string             `json:"description"`
	ParentID               int64              `json:"parent_id"`
	AttachedAttributeValue string             `json:"attached_attribute_value"` // 表示该节点挂载的属性值
	Children               []*ChapterTreeNode `json:"children"`                 // 移除 omitempty
}

type AttachedAttribute struct {
	Key   string `json:"key"`
	Value string `json:"value"`
}
