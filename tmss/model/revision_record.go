package model

const (
	RevisionStatusDraft     = "draft"     // 草稿
	RevisionStatusSubmitted = "submitted" // 已提交
	RevisionStatusPublished = "published" // 已发布
	RevisionStatusRejected  = "rejected"  // 已打回
)

// RevisionRecord 通用修订记录表
type RevisionRecord struct {
	ID         int64  `gorm:"primaryKey;autoIncrement;column:id" json:"id"`
	ModuleKey  string `gorm:"column:module_key;type:varchar(100);not null;index" json:"module_key"` // 修订的表名
	OriginalID int64  `gorm:"column:original_id;type:bigint;not null" json:"original_id"`           // 原记录ID
	NewID      int64  `gorm:"column:new_id;type:bigint;default:0" json:"new_id"`                    // 新记录ID（0表示未生成新记录）
	UserID     int64  `gorm:"column:user_id;type:bigint;not null" json:"user_id"`                   // 修订人ID
	Notes      string `gorm:"column:notes;type:text" json:"notes"`                                  // 修订说明
	Content    string `gorm:"column:content;type:text;not null" json:"content"`                     // 原记录完整内容（JSON格式）
	Changes    string `gorm:"column:changes;type:text" json:"changes"`                              // 变更内容（可选）
	Status     string `gorm:"column:status;type:varchar(20);not null;index" json:"status"`          // 修订状态
	CreatedAt  int64  `gorm:"column:created_at;autoCreateTime" json:"created_at"`                   // 创建时间
	UpdatedAt  int64  `gorm:"column:updated_at;autoUpdateTime" json:"updated_at"`                   // 更新时间
}

func (r *RevisionRecord) TableName() string {
	return "revision_record"
}

// 查询参数结构体
type ReqRevisionSearch struct {
	ModuleKey string `form:"module_key" json:"module_key"`
	Status    string `form:"status" json:"status"`
	Page      int    `form:"page" json:"page,omitempty"`
	PageSize  int    `form:"page_size" json:"page_size,omitempty"`
}

// 响应结构体
type RespRevisionRecord struct {
	RevisionRecord
	UserName string `json:"user_name,omitempty"` // 修订人姓名
}
