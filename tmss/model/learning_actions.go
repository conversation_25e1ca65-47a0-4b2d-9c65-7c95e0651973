package model

import (
	"database/sql"
	"time"
)

// LearningActions struct is a row record of the learning_actions table in the tms database
type LearningActions struct {
	//[ 0] action_id                                      bigint               null: false  primary: true   isArray: false  auto: true   col: bigint          len: -1      default: []
	ActionID int64 `gorm:"primary_key;AUTO_INCREMENT;column:action_id;type:bigint;" json:"action_id"`
	//[ 1] action_code                                    varchar(50)          null: false  primary: false  isArray: false  auto: false  col: varchar         len: 50      default: []
	ActionCode string `gorm:"column:action_code;type:varchar;size:50;" json:"action_code"` // 行为编码
	//[ 2] action_name                                    varchar(100)         null: false  primary: false  isArray: false  auto: false  col: varchar         len: 100     default: []
	ActionName string `gorm:"column:action_name;type:varchar;size:100;" json:"action_name"` // 行为名称
	//[ 3] description                                    text(65535)          null: true   primary: false  isArray: false  auto: false  col: text            len: 65535   default: []
	Description sql.NullString `gorm:"column:description;type:text;size:65535;" json:"description"` // 描述
	//[ 4] created_at                                     datetime             null: false  primary: false  isArray: false  auto: false  col: datetime        len: -1      default: [CURRENT_TIMESTAMP]
	CreatedAt time.Time `gorm:"column:created_at;type:datetime;default:CURRENT_TIMESTAMP;" json:"created_at"` // 创建时间

}

// TableName sets the insert table name for this struct type
func (l *LearningActions) TableName() string {
	return "learning_actions"
}
