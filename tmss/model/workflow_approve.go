package model

const (
	WorkflowApproveStatusReviewing = "reviewing"
	WorkflowApproveStatusPublished = "published"
	WorkflowApproveStatusRejected  = "rejected"
	WorkflowApproveStatusRevision  = "revision"  //修订记录
	WorkflowApproveStatusBackedUp  = "backed_up" //上级打回
	WorkflowApproveStatusDeleted   = "deleted"   // 已废弃
)

type WorkflowApprove struct {
	ID           int64  `gorm:"primary_key;AUTO_INCREMENT;column:id;type:bigint;" json:"id"`
	UserID       int64  `gorm:"column:userId;type:bigint;" json:"userId"`                         // 被审核人ID
	CheckerID    int64  `gorm:"column:checker_id;type:bigint;" json:"checker_id"`                 // 审核人ID
	StepID       int    `gorm:"column:step_id;type:bigint;" json:"step_id"`                       // 步骤序号
	UserName     string `gorm:"column:username;type:varchar;size:255;" json:"username"`           // 审核人用户名
	OperatorID   int64  `gorm:"column:operator_id;type:bigint;" json:"operator_id"`               // 操作人ID
	ModuleKey    string `gorm:"column:module_key;type:varchar;size:50;" json:"module_key"`        // 模块键值
	WorkflowID   int64  `gorm:"column:workflow_id;type:bigint;" json:"workflow_id"`               // 流程ID（唯一）
	DataID       int64  `gorm:"column:data_id;type:bigint;" json:"data_id"`                       // 数据ID（唯一）
	DataTitle    string `gorm:"column:data_title;type:varchar;size:255;" json:"data_title"`       // 数据标题（唯一）
	Status       string `gorm:"column:status;type:varchar;size:50;" json:"status"`                // 审核状态，例如：待审批、已通过、未通过等
	CreatedAt    int64  `gorm:"column:created_at;autoCreateTime" json:"created_at"`               // 创建时间（秒级时间戳）
	ApprovedAt   int64  `gorm:"column:approved_at;autoCreateTime" json:"approved_at"`             // 审核通过时间（秒级时间戳）
	RejectAt     int64  `gorm:"column:reject_at;autoCreateTime" json:"reject_at"`                 // 审核拒绝时间（秒级时间戳）
	RejectReason string `gorm:"column:reject_reason;type:varchar;size:255;" json:"reject_reason"` // 审核拒绝原因
	Remark       string `gorm:"column:remark;type:varchar;size:255;" json:"remark"`               // 备注

}

// TableName sets the insert table name for this struct type
func (w *WorkflowApprove) TableName() string {
	return "workflow_approve"
}

type ReqWorkflowApproveWithUser struct {
	*WorkflowApprove
	UserName string `json:"username"` // 用户名
	Created  string `json:"created"`
}
type ReqWorkflowApprove struct {
	UserID       int64  `json:"user_id"`
	DataID       int64  `json:"data_id"`
	Title        string `json:"title"`
	ApproveID    int64  `json:"approve_id"`
	WorkflowID   int64  `json:"workflow_id"`
	ModuleKey    string `json:"module_key"`
	RejectReason string `json:"reject_reason"`
	Remark       string `json:"remark"`
}
type ReqBatchWorkflowApprove struct {
	ApproveIDs   []int64 `json:"approve_ids"`   // 审核记录ID列表
	Remark       string  `json:"remark"`        // 备注/驳回原因
	RejectReason string  `json:"reject_reason"` // 审核拒绝原因
}
type ReqWorkflowList struct {
	Page      int    `json:"page" form:"page" binding:"gte=1"`           // 页码
	PageSize  int    `json:"page_size" form:"page_size" binding:"gte=1"` // 每页大小
	Status    string `json:"status" form:"status"`
	UserID    int64  `json:"user_id" form:"user_id"`
	CheckerID int64  `form:"checker_id" json:"checker_id"`
	ModuleKey string `json:"module_key" form:"module_key"`
	StartAt   string `json:"start_at" form:"start_at"`
	EndAt     string `json:"end_at" form:"end_at"`
}
