package model

// ExamReview 阅卷记录表
type ExamReview struct {
	ID             int64  `gorm:"primary_key;AUTO_INCREMENT;column:id;type:bigint;" json:"id"`
	ExamID         int64  `gorm:"column:exam_id;type:bigint;" json:"exam_id"`                      // 考试ID
	ExamProgressID int64  `gorm:"column:exam_progress_id;type:bigint;" json:"exam_progress_id"`    // 考试进度ID
	QuestionID     int64  `gorm:"column:question_id;type:bigint;" json:"question_id"`              // 题目ID
	ReviewerID     int64  `gorm:"column:reviewer_id;type:bigint;" json:"reviewer_id"`              // 阅卷老师ID
	OriginalScore  int    `gorm:"column:original_score;type:int;" json:"original_score"`           // 原始得分
	ReviewedScore  int    `gorm:"column:reviewed_score;type:int;" json:"reviewed_score"`           // 阅卷后得分
	ReviewStatus   string `gorm:"column:review_status;type:varchar;size:20;" json:"review_status"` // 阅卷状态
	ReviewComments string `gorm:"column:review_comments;type:text;" json:"review_comments"`        // 阅卷评语
	ReviewedAt     int64  `gorm:"column:reviewed_at;type:bigint;" json:"reviewed_at"`              // 阅卷时间
	CreatedAt      int64  `gorm:"column:created_at;autoCreateTime" json:"created_at"`              // 创建时间
	UpdatedAt      int64  `gorm:"column:updated_at;autoUpdateTime" json:"updated_at"`              // 更新时间
}

// TableName 设置表名
func (ExamReview) TableName() string {
	return "exam_review"
}

// 阅卷状态常量
const (
	ReviewStatusPending  = "pending"  // 待阅卷
	ReviewStatusReviewed = "reviewed" // 已阅卷
)
