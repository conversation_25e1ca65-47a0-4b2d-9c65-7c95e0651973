package model

// RoleDataConfig 数据权限配置表
type RoleDataConfig struct {
	ID          int64  `gorm:"primary_key;AUTO_INCREMENT;column:id;" json:"id"`
	Name        string `gorm:"column:name;type:varchar(100);" json:"name"`             // 配置名称
	Description string `gorm:"column:description;type:text;" json:"description"`       // 配置描述
	TableCode   string `gorm:"column:table_code;type:varchar(100);" json:"table_code"` // 配置标志
	SysCode     string `gorm:"column:sys_code;type:varchar(100);" json:"sys_code"`     // 系统编码
	IsSystem    bool   `gorm:"column:is_system;type:boolean;" json:"is_system"`        // 是否系统内置
	Type        string `gorm:"column:type;type:varchar(20);" json:"type"`              // 规则类型: 'condition', 'in', 'all'
	Field       string `gorm:"column:field;type:varchar(100);" json:"field"`           // 条件字段名
	Value       string `gorm:"column:value;type:text;" json:"value"`                   // 条件值
	IsDefault   bool   `gorm:"column:is_default;type:boolean;" json:"is_default"`      // 是否为默认配置
	CreatedAt   int64  `gorm:"column:created_at;autoCreateTime" json:"created_at"`
}

func (d *RoleDataConfig) TableName() string {
	return "role_data_config"
}

var RoleDataCodeList = []ModuleListDefine{
	{ID: 1, Name: "大纲", Value: ModuleSyllabus},
	{ID: 2, Name: "计划", Value: ModuleTeachingPlan},
	{ID: 3, Name: "课程", Value: ModuleCourses},
	{ID: 4, Name: "课件", Value: ModuleCourseware},
	{ID: 5, Name: "资料", Value: ModuleResources},
	{ID: 6, Name: "题库", Value: ModuleQuestions},
	{ID: 7, Name: "组卷", Value: ModulePapers},
	{ID: 8, Name: "考试", Value: ModuleExams},
	{ID: 9, Name: "成绩", Value: ModuleScore},
	{ID: 10, Name: "课表", Value: ModuleSchedule},
	{ID: 11, Name: "教案", Value: ModuleDocuments},
	{ID: 12, Name: "证书", Value: ModuleCertificates},
	{ID: 13, Name: "知识点", Value: ModuleKnowledgePoints},
	{ID: 14, Name: "作业", Value: ModuleAssignment},
	{ID: 30, Name: "审核", Value: ModuleApprove},
}
