package model

type ExamsExt struct {
	ID       int64  `gorm:"primary_key;AUTO_INCREMENT;column:id;type:bigint;" json:"id"`
	ExtKey   string `gorm:"column:ext_key;type:varchar;" json:"ext_key"` // 扩展键，例如：'teacher', 'student','supervisor' 等
	ExtValue int64  `gorm:"column:ext_value;type:bigint;" json:"ext_value"`
	ExamID   int64  `gorm:"column:exam_id;type:bigint;" json:"exam_id"`
}

func (ExamsExt) TableName() string {
	return "exams_ext"
}
