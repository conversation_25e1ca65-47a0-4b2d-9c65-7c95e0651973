package model

type WorkflowRemark struct {
	ID           int64  `gorm:"primary_key;AUTO_INCREMENT;column:id;type:bigint;" json:"id"`
	ApproveID    int64  `gorm:"column:approve_id;type:bigint;" json:"approve_id"`                 // 审核ID
	NextID       int64  `gorm:"column:next_id;type:bigint;" json:"next_id"`                       // 下一个审核ID
	DataID       int64  `gorm:"column:data_id;type:bigint;" json:"data_id"`                       // 审核数据ID
	StepID       int    `gorm:"column:step_id;type:bigint;" json:"step_id"`                       // 步骤序号
	UserID       int64  `gorm:"column:sender_id;type:bigint;" json:"sender_id"`                   // 发送人ID
	RejectReason string `gorm:"column:reject_reason;type:varchar;size:500;" json:"reject_reason"` // 审核拒绝原因
	Remark       string `gorm:"column:remark;type:varchar;size:500;" json:"remark"`               // 备注
	RemarkType   string `gorm:"column:remark_type;type:varchar;size:50;" json:"remark_type"`      // 审核状态，例如：已读、未读等
	CreatedAt    int64  `gorm:"column:created_at;autoCreateTime" json:"created_at"`               // 创建时间（秒级时间戳）

}

// TableName sets the insert table name for this struct type
func (w *WorkflowRemark) TableName() string {
	return "workflow_remark"
}

//	type ReqWorkflowRemarkSearch struct {
//		ApproveID int64 `json:"approve_id" from:"approve_id"` // 审核记录ID
//		DataID    int64 `json:"data_id" from:"data_id"`       // 数据ID
//		Page      int   `json:"page" from:"page"`             // 页码
//		PageSize  int   `json:"page_size" from:"page_size"`   // 每页数量
//	}
type WorkflowRemarkWithUser struct {
	*WorkflowRemark
	UserName string `json:"username"` // 发送人用户名
	Created  string `json:"created"`
}
