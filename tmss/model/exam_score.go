package model

// ExamScore 学生考试成绩记录表（需要审核）
type ExamScore struct {
	ID          int64  `gorm:"primary_key;AUTO_INCREMENT;column:id;type:bigint;" json:"id"`
	ExamID      int64  `gorm:"column:exam_id;type:bigint;" json:"exam_id"`         // 考试ID
	StudentID   int64  `gorm:"column:student_id;type:bigint;" json:"student_id"`   // 学生ID
	Score       int    `gorm:"column:score;type:int;" json:"score"`                // 总分数
	Status      string `gorm:"column:status;type:varchar;size:20;" json:"status"`  // 审核状态
	UserID      int64  `gorm:"column:user_id;type:bigint;" json:"user_id"`         // 提交人ID
	Remark      string `gorm:"column:remark;type:text;" json:"remark"`             // 审核备注
	CreatedAt   int64  `gorm:"column:created_at;autoCreateTime" json:"created_at"` // 创建时间
	UpdatedAt   int64  `gorm:"column:updated_at;autoUpdateTime" json:"updated_at"` // 更新时间
	SubmitAt    int64  `gorm:"column:submit_at;" json:"submit_at"`                 // 提交时间（审核提交时间）
	PublishedAt int64  `gorm:"column:published_at;" json:"published_at"`           // 发布时间
}

// TableName 设置表名
func (ExamScore) TableName() string {
	return "exam_score"
}

// 审核状态常量
const (
	ScoreStatusDraft     = "draft"     // 草稿
	ScoreStatusPending   = "pending"   // 待审核
	ScoreStatusApproved  = "approved"  // 已通过
	ScoreStatusRejected  = "rejected"  // 已驳回
	ScoreStatusCertified = "certified" // 已颁发证书

)
