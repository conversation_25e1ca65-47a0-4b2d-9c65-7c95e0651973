package model

// QuestionAnswer struct is a row record of the exam_answers table in the tms database
type QuestionAnswer struct {
	ID                 int64  `gorm:"primary_key;AUTO_INCREMENT;column:id;type:bigint;" json:"id"`          // 主键ID
	UserID             int64  `gorm:"column:user_id;type:bigint;" json:"user_id"`                           // 用户ID
	QuestionProgressID int64  `gorm:"column:question_progress_id;type:bigint;" json:"question_progress_id"` // 答题进程ID
	QuestionID         int64  `gorm:"column:question_id;type:bigint;" json:"question_id"`                   // 题目ID
	QuestionType       string `gorm:"column:question_type;varchar;size:50;" json:"question_type"`           // 题目类型
	UserAnswer         string `gorm:"column:user_answer;type:text;" json:"user_answer"`                     // 用户答案
	Score              int    `gorm:"column:score;type:int;" json:"score"`                                  // 得分
	AnsweredAt         int64  `gorm:"column:answered_at;type:bigint;" json:"answered_at"`                   // 作答时间（Unix 时间戳）
	CreatedAt          int64  `gorm:"column:created_at;autoCreateTime" json:"created_at"`                   // 创建时间
	UpdatedAt          int64  `gorm:"column:updated_at;autoUpdateTime" json:"updated_at"`                   // 更新时间
}

// TableName sets the insert table name for this struct type
func (e *QuestionAnswer) TableName() string {
	return "question_answers"
}
