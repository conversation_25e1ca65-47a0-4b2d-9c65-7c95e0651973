package model

import (
	"reflect"
	"strings"
	"time"
)

// FieldInfo 用于返回字段信息
type FieldInfo struct {
	Field string `json:"field"` // 字段名
	Label string `json:"label"` // 标签/注释
	Value string `json:"value"` // 值（字符串形式）
}

// GetStructFieldsWithComments 通过反射获取结构体字段和注释，并根据字段名后缀 _at 格式化时间
func GetStructFieldsWithComments(obj interface{}) []FieldInfo {
	var res []FieldInfo

	typ := reflect.TypeOf(obj)
	val := reflect.ValueOf(obj)

	// 如果是指针，获取实际类型
	if typ.Kind() == reflect.Ptr {
		typ = typ.Elem()
		val = val.Elem()
	}

	for i := 0; i < typ.NumField(); i++ {
		field := typ.Field(i)
		fv := val.Field(i)

		tag := field.Tag
		jsonTag := tag.Get("json")
		commentTag := tag.Get("comment")

		if jsonTag == "" {
			continue
		}

		// 提取 json tag 中字段名（忽略 omitempty）
		fieldName := strings.Split(jsonTag, ",")[0]

		// 判断字段是否是时间字段（以 _at 结尾）
		var value string
		if strings.HasSuffix(fieldName, "_at") {
			// 期望是 int64 类型的时间戳
			if fv.Kind() == reflect.Int64 {
				value = FormatUnixTime(fv.Int())
			} else {
				value = fv.String() // 或者报错/跳过
			}
		} else {
			// 非时间字段按原逻辑处理
			switch fv.Kind() {
			case reflect.String:
				value = fv.String()
			default:
				value = fv.String()
			}
		}

		res = append(res, FieldInfo{
			Field: fieldName,
			Label: commentTag,
			Value: value,
		})
	}

	return res
}

// FormatUnixTime 固定格式，可扩展布局参数
func FormatUnixTime(timestamp int64) string {
	if timestamp <= 0 {
		return ""
	}
	t := time.Unix(timestamp, 0)
	return t.Format("2006-01-02 15:04") // 可提取为常量或参数
}
