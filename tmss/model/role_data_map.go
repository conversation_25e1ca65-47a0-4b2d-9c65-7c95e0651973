package model

// RoleDataMap 角色与数据权限配置的关联表
type RoleDataMap struct {
	ID       int64  `gorm:"primary_key;AUTO_INCREMENT;column:id;" json:"id"`
	RoleCode string `gorm:"column:role_code;type:varchar;size:50;index:idx_role_code;" json:"role_code"`
	ConfigID int64  `gorm:"column:config_id;type:bigint;index:idx_config_id;" json:"config_id"` // 关联配置ID
}

func (r *RoleDataMap) TableName() string {
	return "role_data_map"
}
