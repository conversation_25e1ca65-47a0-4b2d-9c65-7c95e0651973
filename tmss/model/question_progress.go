package model

const (
	QuestionProgressStatusStarted  = "started"  // 刚开始
	QuestionProgressStatusOngoing  = "ongoing"  // 进行中
	QuestionProgressStatusFinished = "finished" // 已完成
)

type QuestionProgress struct {
	ID             int64            `gorm:"primary_key;AUTO_INCREMENT;column:id;type:bigint;" json:"id"` // 主键ID
	UserID         int64            `gorm:"column:user_id;type:bigint;" json:"user_id"`                  // 用户ID
	CoursewareID   int64            `gorm:"column:courseware_id;type:bigint;" json:"courseware_id"`      // 课件ID
	ChapterID      int64            `gorm:"column:chapter_id;type:bigint;" json:"chapter_id"`            // 章节ID
	Status         string           `gorm:"column:status;type:varchar;size:50;" json:"status"`           // 进度状态 "开始", "结束"
	StartTime      int64            `gorm:"column:start_time;type:bigint;" json:"start_time"`            // 开始时间（Unix 时间戳）
	EndTime        int64            `gorm:"column:end_time;type:bigint;" json:"end_time"`                // 结束时间（Unix 时间戳）
	CompletedCount int              `gorm:"column:completed_count;type:int;" json:"completed_count"`     // 已完成题目数量
	TotalScore     int              `gorm:"column:total_score;type:int;" json:"total_score"`             // 总分
	Score          int              `gorm:"column:score;type:int;" json:"score"`                         // 用户得分
	CreatedAt      int64            `gorm:"column:created_at;autoCreateTime" json:"created_at"`          // 创建时间
	UpdatedAt      int64            `gorm:"column:updated_at;autoUpdateTime" json:"updated_at"`          // 更新时间
	Answers        []QuestionAnswer `gorm:"foreignKey:QuestionProgressID" json:"answers"`                // 关联的课件

}

// TableName sets the insert table name for this struct type
func (e *QuestionProgress) TableName() string {
	return "question_progress"
}
