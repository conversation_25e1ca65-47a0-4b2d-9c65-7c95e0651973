package model

const (
	SyllabusStatusDraft     = "draft"     // 草稿
	SyllabusStatusReviewing = "reviewing" // 审查中
	SyllabusStatusRejected  = "rejected"  // 驳回
	SyllabusStatusPublished = "published" // 已发布
	SyllabusStatusRevision  = "revision"  // 修订中
	SyllabusStatusDeleted   = "deleted"   // 已废弃
)

// Syllabus struct is a row record of the syllabus table in the tms database
type Syllabus struct {
	ID            int64  `gorm:"primary_key;AUTO_INCREMENT;column:id;type:bigint;" json:"id" comment:"主键ID"`
	UserID        int64  `gorm:"column:user_id;type:bigint;" json:"user_id" comment:"用户ID"`
	Name          string `gorm:"column:name;type:varchar(100);not null" json:"name" comment:"课程名称"`
	Goals         string `gorm:"column:goals;type:text;" json:"goals" comment:"教学目标"`                   // 教学目标
	Description   string `gorm:"column:description;type:text;" json:"description" comment:"大纲描述"`       // 大纲描述
	Reference     string `gorm:"column:reference;type:text;" json:"reference" comment:"参考文献"`           // 参考文献
	RevisionNotes string `gorm:"column:revision_notes;type:text;" json:"revision_notes" comment:"修订说明"` // 修订说明
	Status        string `gorm:"column:status;type:varchar(20);not null" json:"status" comment:"课程状态"`  // 课程状态
	CreatedAt     int64  `gorm:"column:created_at;autoCreateTime" json:"created_at" comment:"创建时间"`
	UpdatedAt     int64  `gorm:"column:updated_at;autoUpdateTime" json:"updated_at" comment:"更新时间"`
	SubmitAt      int64  `gorm:"column:submit_at;" json:"submit_at" comment:"提交时间"`                          // 提交时间
	PublishedAt   int64  `gorm:"column:published_at;" json:"published_at" comment:"发布时间"`                    // 发布时间
	EffectiveDate int64  `gorm:"column:effective_date;" json:"effective_date" comment:"生效时间"`                // 生效时间
	Version       string `gorm:"column:version;type:varchar(30);default:v1.0;" json:"version" comment:"版本号"` // 版本号
}

// TableName sets the insert table name for this struct type
func (s *Syllabus) TableName() string {
	return "syllabus"
}

type RespSyllabusDetail struct {
	Syllabus  Syllabus    `json:"syllabus"`  // 大纲基本信息
	Textbooks []Textbook  `json:"textbooks"` // 关联的教材列表
	Resources []Resources `json:"resources"`
	// FromRevision SyllabusRevision `json:"from_revisions"` // 来源修订
	// ToRevision   SyllabusRevision `json:"to_revisions"`   // 目标修订

}
type ReqSyllabusSearch struct {
	Page     int    `form:"page,default=1" json:"page"`
	PageSize int    `form:"page_size,default=10" json:"page_size"`
	Name     string `form:"name" json:"name"`     // 按名称模糊搜索
	Status   string `form:"status" json:"status"` // 状态筛选 draft, published, reviewing, revision
	UserID   int64  `form:"user_id" json:"user_id"`
	UserName string `form:"user_name" json:"user_name"`
}

type ReqSyllabusCreate struct {
	Syllabus     Syllabus `json:"syllabus"`
	TextbookIDs  []int64  `json:"textbook_ids"`  // 新增字段用于接收多个教材ID
	ResourcesIDs []int64  `json:"resources_ids"` // 新增字段用于接收多个资源ID
}

// type ReqSyllabusRevision struct {
// 	SyllabusRevision SyllabusRevision `json:"syllabus_revision"`
// 	TextbookIDs      []int64          `json:"textbook_ids"` // 接收多个教材ID
// }

// RespSyllabusList 分页返回结果
type RespSyllabusList struct {
	List  []RespSyllabusWithLatestRevision `json:"list"`
	Total int64                            `json:"total"`
}

// RespSyllabusWithLatestRevision 分页返回大纲+最新修订记录
type RespSyllabusWithLatestRevision struct {
	Syllabus Syllabus        `json:"syllabus"`
	Rev      *RevisionRecord `json:"rev,omitempty"` // 可能为空
}
