package model

import (
	"time"

	"gorm.io/gorm"
)

type WorkflowNodeUsers struct {
	ID         int64  `gorm:"primary_key;AUTO_INCREMENT;column:id;type:bigint;" json:"id"`
	Name       string `gorm:"column:name;type:varchar;size:100;" json:"name"`         // 流程名称（唯一）
	UserID     int64  `gorm:"column:userId;type:bigint;" json:"userId"`               // 节点用户ID
	StepNum    int    `gorm:"column:step_num;type:bigint;" json:"step_num"`           // 步骤序号
	UserName   string `gorm:"column:username;type:varchar;size:255;" json:"username"` // 用户名
	WorkflowID int64  `gorm:"column:workflow_id;type:bigint;" json:"workflow_id"`     // 流程ID（唯一）
	CreatedAt  int64  `gorm:"column:created_at;autoCreateTime" json:"created_at"`     // 创建时间（秒级时间戳）
}

// TableName sets the insert table name for this struct type
func (w *WorkflowNodeUsers) TableName() string {
	return "workflow_nodeusers"
}
func InitWorkflowNodeUsersData(db *gorm.DB) error {
	now := time.Now().Unix()
	nodeUsers := []WorkflowNodeUsers{
		{Name: "节点1", UserID: 2, StepNum: 1, UserName: "teacher", WorkflowID: 1, CreatedAt: now},
		{Name: "节点1", UserID: 2, StepNum: 1, UserName: "teacher", WorkflowID: 2, CreatedAt: now},
		{Name: "节点1", UserID: 2, StepNum: 1, UserName: "teacher", WorkflowID: 3, CreatedAt: now},
		{Name: "节点1", UserID: 2, StepNum: 1, UserName: "teacher", WorkflowID: 4, CreatedAt: now},
		{Name: "节点1", UserID: 2, StepNum: 1, UserName: "teacher", WorkflowID: 5, CreatedAt: now},
		{Name: "节点1", UserID: 2, StepNum: 1, UserName: "teacher", WorkflowID: 6, CreatedAt: now},
		{Name: "节点1", UserID: 2, StepNum: 1, UserName: "teacher", WorkflowID: 7, CreatedAt: now},
		{Name: "节点1", UserID: 2, StepNum: 1, UserName: "teacher", WorkflowID: 8, CreatedAt: now},
		{Name: "节点1", UserID: 2, StepNum: 1, UserName: "teacher", WorkflowID: 9, CreatedAt: now},
		{Name: "节点1", UserID: 2, StepNum: 1, UserName: "teacher", WorkflowID: 10, CreatedAt: now},
		{Name: "节点1", UserID: 2, StepNum: 1, UserName: "teacher", WorkflowID: 11, CreatedAt: now},
		{Name: "节点1", UserID: 2, StepNum: 1, UserName: "teacher", WorkflowID: 12, CreatedAt: now},
	}

	if err := db.Create(&nodeUsers).Error; err != nil {
		return err
	}
	return nil
}
