package model

type VirtualCoursewareCompletionStatus string

const (
	VirtualCoursewareStatusIncomplete VirtualCoursewareCompletionStatus = "incomplete"
	VirtualCoursewareStatusCompleted  VirtualCoursewareCompletionStatus = "completed"
)

func (s VirtualCoursewareCompletionStatus) String() string {
	return string(s)
}

type VirtualCoursewareSubmitHistory struct {
	ID           int64  `gorm:"primary_key;AUTO_INCREMENT;column:id;" json:"id"`
	UserID       int64  `gorm:"column:user_id;type:bigint;" json:"user_id"`
	CoursewareID int64  `gorm:"column:courseware_id;type:bigint;" json:"courseware_id"`
	Subtype      int    `gorm:"column:subtype;" json:"subtype"` // 0：训练； 1：考核； 2: 引导
	Step         uint32 `gorm:"column:step;type:int;" json:"step"`
	Score        uint32 `gorm:"column:score;type:int;" json:"score"`
	Status       string `gorm:"column:status;type:varchar;size:50;" json:"status"` // incomplete, completed
	CompletedAt  int64  `gorm:"column:completed_at;" json:"completed_at"`          // 完成时间
	TotalTime    int64  `gorm:"column:total_time;" json:"total_time"`              // 总学习时长
	CreatedAt    int64  `gorm:"column:created_at;autoCreateTime" json:"created_at"`
	UpdatedAt    int64  `gorm:"column:updated_at;autoUpdateTime" json:"updated_at"`
}

func (VirtualCoursewareSubmitHistory) TableName() string {
	return "virtual_courseware_submit_history"
}
