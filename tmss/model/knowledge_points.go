package model

const (
	KnowledgePointsStatusDraft     = "draft"     // 草稿
	KnowledgePointsStatusPublished = "published" // 已发布
	KnowledgePointsStatusReviewing = "reviewing" // 审核中
	KnowledgePointsStatusRejected  = "rejected"  // 审核拒绝
)

// KnowledgePoints 知识点表
type KnowledgePoints struct {
	ID           int64  `gorm:"primary_key;AUTO_INCREMENT;column:id;type:bigint;" json:"id"`
	Title        string `gorm:"column:title;type:varchar(200);" json:"title"`           // 知识点标题
	Content      string `gorm:"column:content;type:text;" json:"content"`               // 知识点内容（富文本）
	UserID       int64  `gorm:"column:user_id;type:bigint;" json:"user_id"`             // 授课教师ID
	CoursewareID int64  `gorm:"column:courseware_id;type:bigint;" json:"courseware_id"` // 关联课件ID
	ChapterID    int64  `gorm:"column:chapter_id;type:bigint;" json:"chapter_id"`       // 关联章节ID
	QuestionID   int64  `gorm:"column:question_id;type:bigint;" json:"question_id"`     // 关联题库ID
	Status       string `gorm:"column:status;type:varchar(20);" json:"status"`          // 状态：draft, published
	CreatedAt    int64  `gorm:"column:created_at;autoCreateTime" json:"created_at"`
	UpdatedAt    int64  `gorm:"column:updated_at;autoUpdateTime" json:"updated_at"`
	SubmitAt     int64  `gorm:"column:submit_at;" json:"submit_at"`                           // 提交时间（审核提交时间）
	PublishedAt  int64  `gorm:"column:published_at;" json:"published_at"`                     // 发布时间
	Version      string `gorm:"column:version;type:varchar(20);default:v1.0;" json:"version"` // 版本号
}

// TableName 设置表名
func (t *KnowledgePoints) TableName() string {
	return "knowledge_points"
}

// 搜索请求
type ReqKnowledgePointsSearch struct {
	Page         int    `form:"page,default=1" json:"page"`
	PageSize     int    `form:"page_size,default=10" json:"page_size"`
	ChapterID    int64  `form:"chapter_id" json:"chapter_id"`       // 关联章节ID
	CoursewareID int64  `form:"courseware_id" json:"courseware_id"` // 课件ID
	QuestionID   int64  `form:"question_id" json:"question_id"`     // 题库ID
	UserID       int64  `form:"user_id" json:"user_id"`             // 教师ID
	Title        string `form:"title" json:"title"`                 // 教案标题
	Status       string `form:"status" json:"status"`               // 状态
}

// 详情返回结构
type RespKnowledgePointsDetail struct {
	KnowledgePoints
	QuestionTitle   string          `json:"question_title"`             // 题库标题
	ChapterTitle    string          `json:"chapter_title"`              // 章节标题
	CoursewareTitle string          `json:"courseware_title,omitempty"` // 关联的课件信息
	TeacherName     string          `json:"teacher_name"`               // 教师姓名
	Rev             *RevisionRecord `json:"rev,omitempty" gorm:"-"`     // 可能为空
}
type RespKnowledgePointsList struct {
	List  []RespKnowledgePointsDetail `json:"list"`
	Total int64                       `json:"total"`
}
