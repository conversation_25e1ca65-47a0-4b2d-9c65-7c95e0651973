package model

// PaperStatus 定义试卷状态常量
const (
	PaperStatusDraft     = "draft"     // 未发布
	PaperStatusReviewing = "reviewing" // 审核中
	PaperStatusPublished = "published" // 已发布
	PaperStatusRejected  = "rejected"  // 审核拒绝
	// PaperStatusOngoing   = "ongoing"   // 考试中
	// PaperStatusFinished  = "finished"  // 已考完
	// PaperStatusGraded    = "graded"    // 已打分
	// PaperStatusArchived  = "archived"  // 已归档
)

type ReqPaperUpdate struct {
	Papers         Papers           `json:"papers"`
	PaperQuestions []PaperQuestions `json:"paper_questions"`
	//StartAtStr     string           `json:"start_at"` // 开始时间
}
type ReqPaperSearch struct {
	Page     int    `form:"page" binding:"omitempty,min=1"`              // 当前页码，默认为1
	PageSize int    `form:"page_size" binding:"omitempty,min=1,max=100"` // 每页大小，默认为10，最大为100
	Name     string `form:"name" binding:"omitempty"`                    // 试卷名称（模糊搜索）
	Status   string `form:"status" binding:"omitempty"`                  // 试卷状态
	UserID   int64  `form:"user_id" binding:"omitempty,min=1"`           // 用户ID
}
type RespPaperList struct {
	List  []Papers `json:"list"`
	Total int64    `json:"total"`
}

// 试卷详情响应结构
type RespPaperDetail struct {
	Papers         Papers           `json:"papers"`          // 试卷基本信息
	PaperQuestions []PaperQuestions `json:"paper_questions"` // 题目详细信息
	Courses        Courses          `json:"courses"`         // 创建者姓名
}

// Papers struct is a row record of the papers table in the tms database
type Papers struct {
	ID        int64  `gorm:"primary_key;AUTO_INCREMENT;column:id;type:bigint;" json:"id"`
	Title     string `gorm:"size:255;not null" json:"title"`                   // 试卷标题
	CourseID  int64  `gorm:"column:course_id;type:bigint;" json:"course_id"`   // 课程ID
	ChapterID int64  `gorm:"column:chapter_id;type:bigint;" json:"chapter_id"` // 章节ID
	UserID    int64  `gorm:"column:user_id;type:bigint;" json:"user_id"`       // 用户ID

	QuestionCount int    `gorm:"column:question_count;type:int;" json:"question_count"` // 题目数量
	TotalScore    int    `gorm:"column:total_score;type:int;" json:"total_score"`       // 总分
	TotalMinutes  int64  `gorm:"column:total_minutes;type:int;" json:"total_minutes"`   // 总时数（单位：分钟）
	Status        string `gorm:"column:status;varchar;size:50;" json:"status"`          // 状态
	CreatedAt     int64  `gorm:"column:created_at;autoCreateTime" json:"created_at"`
	UpdatedAt     int64  `gorm:"column:updated_at;autoUpdateTime" json:"updated_at"`
	// StartAt       int64  `gorm:"column:start_at;" json:"start_at"`                                 // 开始时间（考试开始时间）
	// EndAt         int64  `gorm:"column:end_at;" json:"end_at"`                                     // 结束时间（考试截止时间）
	SubmitAt    int64  `gorm:"column:submit_at;" json:"submit_at"`                               // 提交时间（审核提交时间）
	PublishedAt int64  `gorm:"column:published_at;" json:"published_at"`                         // 发布时间
	Version     string `gorm:"column:version;type:varchar;size:20;default:v1.0;" json:"version"` // 版本号

}

// TableName sets the insert table name for this struct type
func (p *Papers) TableName() string {
	return "papers"
}
