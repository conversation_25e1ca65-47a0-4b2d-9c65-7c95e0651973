package model

import (
	"gorm.io/gorm"
)

// Menus struct is a row record of the menus table in the tms database
type Menus struct {
	ID        int64  `gorm:"primary_key;AUTO_INCREMENT;column:id;type:bigint;" json:"id"`
	ParentID  int64  `gorm:"column:parent_id;type:bigint;default:0;index;" json:"parent_id"` // 父菜单ID
	MenuName  string `gorm:"column:menu_name;type:varchar;size:50;" json:"menu_name"`        // 菜单名称
	Path      string `gorm:"column:path;type:varchar;size:255;index;" json:"path"`           // 路由路径
	Icon      string `gorm:"column:icon;type:varchar;size:150;" json:"icon"`                 // 图标名称
	OrderNum  int64  `gorm:"column:order_num;type:int;" json:"order_num"`                    // 排序字段，用于前端菜单的顺序排列
	SysCode   string `gorm:"column:sys_code;type:varchar;size:50;" json:"sys_code"`          // 角色编码，用于权限控制，只做系统标记不做强关联，方便后续扩展
	CreatedAt int64  `gorm:"column:created_at" json:"created_at"`                            // 自动创建时间
	UpdatedAt int64  `gorm:"column:updated_at" json:"updated_at"`                            // 自动更新时间
}

// TableName sets the insert table name for this struct type
func (m *Menus) TableName() string {
	return "menus"
}

func (m Menus) Columns() []string {
	return []string{"id", "parent_id", "menu_name", "path", "icon", "sys_code", "order_num", "created_at", "updated_at"}
}

func (m Menus) UpdateColumns(mode string) []string {
	if mode == "exclude_primary_key" {
		return []string{"parent_id", "menu_name", "path", "icon", "sys_code", "order_num", "updated_at"}
	}
	return m.Columns()
}

func InitMenuData(db *gorm.DB) error {
	// 先删除旧数据
	if err := db.Exec("DELETE FROM role_menu").Error; err != nil {
		return err
	}
	if err := db.Exec("DELETE FROM menus").Error; err != nil {
		return err
	}

	// 定义菜单数据（无ID）
	menuItems := []Menus{
		// 一级菜单: 用户角色权限
		{ParentID: 0, MenuName: "用户管理", Path: "/role", Icon: "User", SysCode: "admin", OrderNum: 1},
		{ParentID: 0, MenuName: "系统设置", Path: "/system", Icon: "Key", SysCode: "admin", OrderNum: 2},
		{ParentID: 0, MenuName: "审批管理", Path: "/workflow", Icon: "User", SysCode: "admin", OrderNum: 3},
		{ParentID: 0, MenuName: "播放器管理", Path: "/player", Icon: "VideoPlay", SysCode: "admin", OrderNum: 4},
		{ParentID: 0, MenuName: "考核管理", Path: "/exam", Icon: "Clock", SysCode: "teacher", OrderNum: 5},
		{ParentID: 0, MenuName: "教学管理", Path: "/teaching", Icon: "Document", SysCode: "teacher", OrderNum: 6},
		{ParentID: 0, MenuName: "教学配置", Path: "/teach-setting", Icon: "Setting", SysCode: "teacher", OrderNum: 6},
		{ParentID: 0, MenuName: "资料管理", Path: "/material", Icon: "Folder", SysCode: "teacher", OrderNum: 7},
		{ParentID: 0, MenuName: "证书管理", Path: "/certificate", Icon: "TrophyBase", SysCode: "teacher", OrderNum: 8},
		{ParentID: 0, MenuName: "评估分析", Path: "/analysis", Icon: "Histogram", SysCode: "teacher", OrderNum: 9},
		{ParentID: 0, MenuName: "审核管理", Path: "/approve", Icon: "Suitcase", SysCode: "teacher", OrderNum: 10},
		{ParentID: 0, MenuName: "自主学习", Path: "/selfstu", Icon: "Reading", SysCode: "user", OrderNum: 11},
		{ParentID: 0, MenuName: "考核评估", Path: "/evaluate", Icon: "SuitcaseLine", SysCode: "user", OrderNum: 12},
		{ParentID: 0, MenuName: "科目考核", Path: "/subject-examine", Icon: "Suitcase", SysCode: "user", OrderNum: 13},
		{ParentID: 0, MenuName: "资料查看", Path: "/materials", Icon: "Document", SysCode: "user", OrderNum: 14},
		{ParentID: 0, MenuName: "用户切换", Path: "/user", Icon: "User", SysCode: "user", OrderNum: 15},
		{ParentID: 0, MenuName: "学习历史", Path: "/study-history", Icon: "Clock", SysCode: "user", OrderNum: 16},
		{ParentID: 0, MenuName: "我的证书", Path: "/certificate", Icon: "Medal", SysCode: "user", OrderNum: 17},
	}

	// 批量插入一级菜单
	if err := db.Create(&menuItems).Error; err != nil {
		return err
	}

	// 构建父菜单映射表
	parentMap := make(map[string]int64)
	for _, menu := range menuItems {
		parentMap[menu.MenuName] = menu.ID
	}

	// 定义二级菜单
	childMenus := []Menus{
		// 用户管理子菜单
		{ParentID: parentMap["用户管理"], MenuName: "用户列表", Path: "/role/user", Icon: "", SysCode: "admin", OrderNum: 101},
		{ParentID: parentMap["用户管理"], MenuName: "角色管理", Path: "/role/permission", Icon: "", SysCode: "admin", OrderNum: 102},
		{ParentID: parentMap["用户管理"], MenuName: "菜单管理", Path: "/role/menu", Icon: "", SysCode: "admin", OrderNum: 103},
		{ParentID: parentMap["用户管理"], MenuName: "数据权限", Path: "/role/data", Icon: "", SysCode: "admin", OrderNum: 104},

		// 系统设置子菜单
		{ParentID: parentMap["系统设置"], MenuName: "核心设置", Path: "/system", Icon: "", SysCode: "admin"},
		{ParentID: parentMap["系统设置"], MenuName: "系统日志", Path: "/logs", Icon: "", SysCode: "admin"},
		{ParentID: parentMap["系统设置"], MenuName: "评估规则", Path: "/rule", Icon: "DocumentChecked", SysCode: "admin"},
		{ParentID: parentMap["系统设置"], MenuName: "字典管理", Path: "/dict", Icon: "", SysCode: "admin"},

		// 审批管理子菜单
		{ParentID: parentMap["审批管理"], MenuName: "流程管理", Path: "/approve", Icon: "", SysCode: "admin"},
		{ParentID: parentMap["审批管理"], MenuName: "审批配置", Path: "/approve-template", Icon: "", SysCode: "admin"},

		// 播放器管理子菜单
		{ParentID: parentMap["播放器管理"], MenuName: "解密设置", Path: "/player/decrypt", Icon: "", SysCode: "admin"},
		{ParentID: parentMap["播放器管理"], MenuName: "加密策略", Path: "/player/strategy", Icon: "", SysCode: "admin"},
		{ParentID: parentMap["播放器管理"], MenuName: "用户密钥", Path: "/player/key", Icon: "", SysCode: "admin"},

		// 考核管理子菜单
		{ParentID: parentMap["考核管理"], MenuName: "考试管理", Path: "/exam/exams", Icon: "", SysCode: "teacher"},
		{ParentID: parentMap["考核管理"], MenuName: "评估管理", Path: "/exam/plan", Icon: "", SysCode: "teacher"},
		{ParentID: parentMap["考核管理"], MenuName: "成绩管理", Path: "/exam/score", Icon: "", SysCode: "teacher"},
		{ParentID: parentMap["考核管理"], MenuName: "阅卷管理", Path: "/exam/grade", Icon: "", SysCode: "teacher"},
		{ParentID: parentMap["考核管理"], MenuName: "组卷管理", Path: "/exam/paper", Icon: "", SysCode: "teacher"},

		// 教学管理子菜单
		{ParentID: parentMap["教学管理"], MenuName: "教学大纲", Path: "/teaching/syllabus", Icon: "Document", SysCode: "teacher", OrderNum: 601},
		{ParentID: parentMap["教学管理"], MenuName: "教学计划", Path: "/teaching/teaching-plan", Icon: "Tickets", SysCode: "teacher", OrderNum: 602},
		{ParentID: parentMap["教学管理"], MenuName: "课表管理", Path: "/teaching/schedule", Icon: "", SysCode: "teacher", OrderNum: 603},
		{ParentID: parentMap["教学管理"], MenuName: "课程管理", Path: "/teaching/course", Icon: "Notebook", SysCode: "teacher", OrderNum: 604},
		{ParentID: parentMap["教学管理"], MenuName: "课件管理", Path: "/teaching/courseware", Icon: "FolderOpened", SysCode: "teacher", OrderNum: 605},
		{ParentID: parentMap["教学管理"], MenuName: "理论试题", Path: "/teaching/question", Icon: "", SysCode: "teacher", OrderNum: 606},

		{ParentID: parentMap["教学管理"], MenuName: "教案管理", Path: "/teaching/document", Icon: "", SysCode: "teacher", OrderNum: 612},
		{ParentID: parentMap["教学管理"], MenuName: "知识点管理", Path: "/teaching/points", Icon: "", SysCode: "teacher", OrderNum: 613},
		{ParentID: parentMap["教学管理"], MenuName: "作业管理", Path: "/teaching/assignment", Icon: "", SysCode: "teacher", OrderNum: 614},
		{ParentID: parentMap["教学管理"], MenuName: "学习进度", Path: "/teaching/learning", Icon: "", SysCode: "teacher", OrderNum: 615},

		{ParentID: parentMap["教学配置"], MenuName: "章节管理", Path: "/teaching/chapter", Icon: "", SysCode: "teacher", OrderNum: 607},
		{ParentID: parentMap["教学配置"], MenuName: "班级管理", Path: "/teaching/class", Icon: "", SysCode: "teacher", OrderNum: 608},
		{ParentID: parentMap["教学配置"], MenuName: "专业管理", Path: "/teaching/major", Icon: "", SysCode: "teacher", OrderNum: 609},
		{ParentID: parentMap["教学配置"], MenuName: "教室管理", Path: "/teaching/classroom", Icon: "", SysCode: "teacher", OrderNum: 609},
		{ParentID: parentMap["教学配置"], MenuName: "教材管理", Path: "/teaching/textbook", Icon: "", SysCode: "teacher", OrderNum: 610},

		// 资料管理子菜单
		{ParentID: parentMap["资料管理"], MenuName: "上传资料", Path: "/material/upload", Icon: "", SysCode: "teacher"},
		{ParentID: parentMap["资料管理"], MenuName: "资料分类", Path: "/material/classify", Icon: "", SysCode: "teacher"},

		// 证书管理子菜单
		{ParentID: parentMap["证书管理"], MenuName: "证书模板", Path: "/certificate/template", Icon: "", SysCode: "teacher"},
		{ParentID: parentMap["证书管理"], MenuName: "证书生成", Path: "/certificate/generate", Icon: "", SysCode: "teacher"},
		{ParentID: parentMap["证书管理"], MenuName: "证书审核", Path: "/certificate/audit", Icon: "", SysCode: "teacher"},

		// 评估分析子菜单
		{ParentID: parentMap["评估分析"], MenuName: "考试评估", Path: "/analysis/exam", Icon: "", SysCode: "teacher"},
		{ParentID: parentMap["评估分析"], MenuName: "学习进度评估", Path: "/analysis/learning", Icon: "", SysCode: "teacher"},

		// 审核管理子菜单
		{ParentID: parentMap["审核管理"], MenuName: "大纲审核", Path: "/approve/syllabus", Icon: "", SysCode: "teacher", OrderNum: 801},
		{ParentID: parentMap["审核管理"], MenuName: "计划审核", Path: "/approve/teaching_plan", Icon: "", SysCode: "teacher", OrderNum: 802},
		{ParentID: parentMap["审核管理"], MenuName: "课程审核", Path: "/approve/courses", Icon: "", SysCode: "teacher", OrderNum: 803},
		{ParentID: parentMap["审核管理"], MenuName: "课件审核", Path: "/approve/courseware", Icon: "", SysCode: "teacher", OrderNum: 804},
		{ParentID: parentMap["审核管理"], MenuName: "资料审核", Path: "/approve/resources", Icon: "", SysCode: "teacher", OrderNum: 805},
		{ParentID: parentMap["审核管理"], MenuName: "题库审核", Path: "/approve/questions", Icon: "", SysCode: "teacher", OrderNum: 806},
		{ParentID: parentMap["审核管理"], MenuName: "组卷审核", Path: "/approve/papers", Icon: "", SysCode: "teacher", OrderNum: 807},
		{ParentID: parentMap["审核管理"], MenuName: "考试审核", Path: "/approve/exams", Icon: "", SysCode: "teacher", OrderNum: 808},
		{ParentID: parentMap["审核管理"], MenuName: "成绩审核", Path: "/approve/exam_score", Icon: "", SysCode: "teacher", OrderNum: 809},
		{ParentID: parentMap["审核管理"], MenuName: "证书审核", Path: "/approve/certificates", Icon: "", SysCode: "teacher", OrderNum: 810},
		{ParentID: parentMap["审核管理"], MenuName: "教案审核", Path: "/approve/teaching_document", Icon: "", SysCode: "teacher", OrderNum: 811},
		{ParentID: parentMap["审核管理"], MenuName: "课表审核", Path: "/approve/class_schedule", Icon: "", SysCode: "teacher", OrderNum: 812},
		{ParentID: parentMap["审核管理"], MenuName: "作业审核", Path: "/approve/assignment", Icon: "", SysCode: "teacher", OrderNum: 813},

		// 自主学习子菜单
		{ParentID: parentMap["自主学习"], MenuName: "课程学习", Path: "/lesson", Icon: "", SysCode: "user"},
		{ParentID: parentMap["自主学习"], MenuName: "查看学习进度", Path: "/personal-progress", Icon: "", SysCode: "user"},
		{ParentID: parentMap["自主学习"], MenuName: "自测评估", Path: "/self-test", Icon: "", SysCode: "user"},

		// 考核评估子菜单
		{ParentID: parentMap["考核评估"], MenuName: "学员考核评估", Path: "/examine-evaluate", Icon: "", SysCode: "user"},
		{ParentID: parentMap["考核评估"], MenuName: "评估分析查看", Path: "/evaluate-analysis", Icon: "", SysCode: "user"},
	}

	// 批量插入二级菜单
	if err := db.Create(&childMenus).Error; err != nil {
		return err
	}

	// 构建所有菜单的映射表
	allMenus := append(menuItems, childMenus...)
	// menuIDMap := make(map[string]int64)
	// for _, menu := range allMenus {
	// 	menuIDMap[menu.MenuName] = menu.ID
	// }

	// 构建 role_menu 列表
	var roleMenus []RoleMenu
	for _, menu := range allMenus {
		if menu.SysCode != "" {
			roleMenus = append(roleMenus, RoleMenu{
				RoleCode: menu.SysCode,
				MenuID:   menu.ID,
			})
		}
	}

	// 批量插入到 role_menu 表
	if err := db.Create(&roleMenus).Error; err != nil {
		return err
	}
	return nil
}
