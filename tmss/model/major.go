package model

import "gorm.io/gorm"

// Majors 表示专业模型
type Majors struct {
	ID          int64  `gorm:"primary_key;AUTO_INCREMENT;column:id;type:bigint;" json:"id"`      // 专业ID
	ParentID    int64  `gorm:"column:parent_id;type:bigint;" json:"parent_id"`                   // 父专业ID
	UserID      int64  `gorm:"column:user_id;type:bigint;" json:"user_id"`                       // 用户ID
	MajorID     string `gorm:"column:major_id;type:varchar(50);unique;not null" json:"major_id"` // 专业编号（唯一）
	Name        string `gorm:"column:name;type:varchar(100);not null" json:"name"`               // 专业名称
	Description string `gorm:"column:description;type:text" json:"description"`                  // 专业描述
	CreatedAt   int64  `gorm:"column:created_at;autoCreateTime" json:"created_at"`               // 创建时间（秒级时间戳）
	UpdatedAt   int64  `gorm:"column:updated_at;autoUpdateTime" json:"updated_at"`               // 更新时间（秒级时间戳）
}

// TableName 设置数据库表名
func (Majors) TableName() string {
	return "majors"
}

func (m Majors) Columns() []string {
	return []string{"id", "parent_id", "user_id", "major_id", "name", "description", "created_at", "updated_at"}
}

func (m Majors) UpdateColumns(mode string) []string {
	if mode == "exclude_primary_key" {
		return []string{"parent_id", "user_id", "major_id", "name", "description", "updated_at"}
	}
	return m.Columns()
}

type MajorTreeNode struct {
	ID          int64            `json:"id"`
	MajorID     string           `json:"major_id"`
	Name        string           `json:"name"`
	Description string           `json:"description"`
	ParentID    int64            `json:"parent_id"`
	Children    []*MajorTreeNode `json:"children"` // 移除 omitempty
}
type ReqMajorSearch struct {
	Name    string `form:"name" json:"name"` // 按名称模糊搜索
	MajorID string `form:"major_id" json:"major_id"`
}

func InitMajorData(db *gorm.DB) error {
	// 先删除旧数据
	if err := db.Exec("DELETE FROM majors").Error; err != nil {
		return err
	}

	// 定义一级专业
	topLevelMajors := []Majors{
		{ParentID: 0, UserID: 1, MajorID: "WAR_MAJOR_001", Name: "战勤专业", Description: "战勤相关专业"},
		{ParentID: 0, UserID: 1, MajorID: "GROUND_MAJOR_001", Name: "地勤专业", Description: "地勤相关专业"},
	}

	// 插入一级专业
	if err := db.Create(&topLevelMajors).Error; err != nil {
		return err
	}

	// 构建父专业映射表
	parentMap := make(map[string]int64)
	for _, major := range topLevelMajors {
		parentMap[major.MajorID] = major.ID
	}

	// 定义二级专业
	childMajors := []Majors{
		// 战勤相关专业子类
		{ParentID: parentMap["WAR_MAJOR_001"], UserID: 1, MajorID: "WAR_SUB_001", Name: "飞行指挥", Description: "负责飞行任务的指挥与协调，确保飞行安全"},
		{ParentID: parentMap["WAR_MAJOR_001"], UserID: 1, MajorID: "WAR_SUB_002", Name: "空中作战", Description: "执行空中作战任务，包括战术飞行和武器操作"},
		{ParentID: parentMap["WAR_MAJOR_001"], UserID: 1, MajorID: "WAR_SUB_003", Name: "情报分析", Description: "收集分析军事情报，为作战决策提供支持"},

		// 地勤相关专业子类
		{ParentID: parentMap["GROUND_MAJOR_001"], UserID: 1, MajorID: "GROUND_SUB_001", Name: "机务维修", Description: "负责飞机日常维护和故障检修，保障飞机适航性"},
		{ParentID: parentMap["GROUND_MAJOR_001"], UserID: 1, MajorID: "GROUND_SUB_002", Name: "航材管理", Description: "管理航空器材的采购、存储和调配"},
		{ParentID: parentMap["GROUND_MAJOR_001"], UserID: 1, MajorID: "GROUND_SUB_003", Name: "场站保障", Description: "负责机场设施维护和地面保障服务"},
	}

	// 插入二级专业
	if err := db.Create(&childMajors).Error; err != nil {
		return err
	}

	return nil
}
