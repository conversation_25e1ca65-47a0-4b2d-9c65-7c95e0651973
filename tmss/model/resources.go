package model

// 资料状态常量定义
const (
	ResourceStatusDraft     = "draft"     // 草稿状态
	ResourceStatusReviewing = "reviewing" // 审核中状态
	ResourceStatusPublished = "published" // 已发布状态
	ResourceStatusArchived  = "archived"  // 已归档状态
)

var ResourceStatusText = map[string]string{
	ResourceStatusDraft:     "草稿",
	ResourceStatusReviewing: "审核中",
	ResourceStatusPublished: "已发布",
	ResourceStatusArchived:  "已归档",
}

type Resources struct {
	ID          int64  `gorm:"primary_key;AUTO_INCREMENT;column:id;type:bigint;" json:"id"`
	UserID      int64  `gorm:"column:user_id;type:bigint;" json:"user_id"`                       // 创建人ID
	Name        string `gorm:"column:name;type:varchar;size:255;" json:"name"`                   // 资料名称
	Path        string `gorm:"column:path;type:varchar;size:255;" json:"path"`                   // 资源路径
	Size        int64  `gorm:"column:size;type:bigint;" json:"size"`                             // 资源大小
	Ext         string `gorm:"column:ext;type:varchar;size:255;" json:"ext"`                     // 扩展名
	CategoryID  int64  `gorm:"column:category_id;type:bigint;" json:"category_id"`               // 分类ID
	ChapterID   int64  `gorm:"column:chapter_id;type:bigint;" json:"chapter_id"`                 // 章节ID
	Minutes     int    `gorm:"column:minutes;type:int;" json:"minutes"`                          // 阅读时间（分钟）
	Status      string `gorm:"column:status;type:varchar;size:50;" json:"status"`                // 状态（draft, reviewing, published）
	Description string `gorm:"column:description;type:text;" json:"description"`                 // 描述信息
	CreatedAt   int64  `gorm:"column:created_at;autoCreateTime" json:"created_at"`               // 创建时间
	UpdatedAt   int64  `gorm:"column:updated_at;autoUpdateTime" json:"updated_at"`               // 更新时间
	SubmitAt    int64  `gorm:"column:submit_at;" json:"submit_at"`                               // 提交审核时间
	PublishedAt int64  `gorm:"column:published_at;" json:"published_at"`                         // 发布时间
	Version     string `gorm:"column:version;type:varchar;size:20;default:v1.0;" json:"version"` // 版本号
}

func (r *Resources) TableName() string {
	return "resources"
}

type ReqResourceUpdate struct {
	ID          int64  `json:"id" binding:"required,min=1"`
	Name        string `json:"name"`
	Description string `json:"description"`
	CategoryID  int64  `json:"category_id"`
	Minutes     int    `json:"minutes"`
	Path        string `json:"path"`
	Ext         string `json:"ext"`
	Size        int64  `json:"size"`
}

type ReqResourceSearch struct {
	Page       int    `form:"page" binding:"omitempty,min=1"`
	PageSize   int    `form:"page_size" binding:"omitempty,min=1,max=100"`
	Name       string `form:"name" binding:"omitempty"`
	Status     string `form:"status" binding:"omitempty"`
	UserID     int64  `form:"user_id" binding:"omitempty,min=1"`
	CategoryID int64  `form:"category_id" binding:"omitempty"`
	ChapterID  int64  `form:"chapter_id" binding:"omitempty"`
}
type RespResourceData struct {
	Resources
	CategoryName string          `json:"category_name"`
	ChapterName  string          `json:"chapter_name"`
	DownloadURL  string          `json:"download_url"`
	Rev          *RevisionRecord `json:"rev,omitempty" gorm:"-"`
}
type RespResourceList struct {
	List  []*RespResourceData `json:"list"`
	Total int64               `json:"total"`
}
