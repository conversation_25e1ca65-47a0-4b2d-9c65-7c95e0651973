package model

// CoursewareExt struct is a row record of the courseware_ext table in the tms database
type CoursewareExt struct {
	//[ 0] ext_id                                         bigint               null: false  primary: true   isArray: false  auto: true   col: bigint          len: -1      default: []
	ExtID int64 `gorm:"primary_key;AUTO_INCREMENT;column:ext_id;type:bigint;" json:"ext_id"`
	//[ 1] courseware_id                                  bigint               null: false  primary: false  isArray: false  auto: false  col: bigint          len: -1      default: []
	CoursewareID int64 `gorm:"column:courseware_id;type:bigint;" json:"courseware_id"` // 课件ID
	//[ 2] ext_key                                        varchar(100)         null: false  primary: false  isArray: false  auto: false  col: varchar         len: 100     default: []
	ExtKey string `gorm:"column:ext_key;type:varchar;size:100;" json:"ext_key"` // 扩展字段键
	//[ 3] ext_value                                      text(65535)          null: false  primary: false  isArray: false  auto: false  col: text            len: 65535   default: []
	ExtValue string `gorm:"column:ext_value;" json:"ext_value"` // 扩展字段值

}

// TableName sets the insert table name for this struct type
func (c *CoursewareExt) TableName() string {
	return "courseware_ext"
}
