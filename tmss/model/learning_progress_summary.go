package model

const (
	LearningProgressStatusNotStarted = "not_started"
	LearningProgressStatusInProgress = "in_progress"
	LearningProgressStatusCompleted  = "completed"
	LearningProgressStatusReviewed   = "reviewed" //已批阅
)

// LearningProgressSummary struct is a row record of the learning_progress_summary table in the tms database
type LearningProgressSummary struct {
	ID                    int64  `gorm:"primary_key;AUTO_INCREMENT;column:id;type:bigint;" json:"id"`
	StudentID             int64  `gorm:"column:student_id;type:bigint;" json:"student_id"`       // 学生ID
	ClassID               int64  `gorm:"column:class_id;type:bigint;" json:"class_id"`           // 班级ID
	MajorsID              int64  `gorm:"column:majors_id;type:bigint;" json:"majors_id"`         // 专业ID
	CourseID              int64  `gorm:"column:course_id;type:bigint;" json:"course_id"`         // 课程ID
	ChapterID             int64  `gorm:"column:chapter_id;type:bigint;" json:"chapter_id"`       // 章节ID
	TeacherID             int64  `gorm:"column:teacher_id;type:bigint;" json:"teacher_id"`       // 批阅教师ID
	AssignmentID          int64  `gorm:"column:assignment_id;type:bigint;" json:"assignment_id"` // 作业ID
	Status                string `gorm:"column:status;type:varchar;size:20;default:not_started;" json:"status"`
	AssignmentName        string `gorm:"column:assignment_name;type:varchar;size:255;" json:"assignment_name"`   // 作业名称
	AssignmentDescription string `gorm:"column:assignment_description;type:text;" json:"assignment_description"` // 作业描述
	StartAt               int64  `gorm:"column:start_at;" json:"start_at"`                                       // 开始时间（开始时间）
	EndAt                 int64  `gorm:"column:end_at;" json:"end_at"`                                           // 结束时间（截止时间）
	CreatedAt             int64  `gorm:"column:created_at;autoCreateTime" json:"created_at"`
	UpdatedAt             int64  `gorm:"column:updated_at;autoUpdateTime" json:"updated_at"`
	ReviewAt              int64  `gorm:"column:review_at;" json:"review_at"`                     // 批阅时间
	ReviewComment         string `gorm:"column:review_comment;type:text;" json:"review_comment"` //批语
}

// TableName sets the insert table name for this struct type
func (l *LearningProgressSummary) TableName() string {
	return "learning_progress_summary"
}
