package model

type ClassScheduleExt struct {
	ID              int64  `gorm:"primary_key;AUTO_INCREMENT;column:id;type:bigint;" json:"id"`
	ExtKey          string `gorm:"column:ext_key;type:varchar;" json:"ext_key"` // 扩展键，例如：'teacher', 'student','supervisor' 等
	ExtValue        int64  `gorm:"column:ext_value;type:bigint;" json:"ext_value"`
	ClassScheduleID int64  `gorm:"column:class_schedule_id;type:bigint;index;" json:"class_schedule_id"` // 关联课表ID
}

func (ClassScheduleExt) TableName() string {
	return "class_schedule_ext"
}
