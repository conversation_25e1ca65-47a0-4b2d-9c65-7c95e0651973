package model

import "gorm.io/gorm"

// RolePermissions struct is a row record of the role_permissions table in the tms database
type RolePermissions struct {
	RoleCode string `gorm:"primary_key;column:role_code;varchar;size:50;" json:"role_code"`
	Path     string `gorm:"primary_key;column:path;type:varchar;size:250;" json:"path"`
	Method   string `gorm:"primary_key;column:method;type:varchar;size:50;" json:"method"`
	ID       string `gorm:"column:id;type:varchar;size:50;" json:"id"`
	Label    string `gorm:"column:label;type:varchar;size:250;" json:"label"`
}

// TableName sets the insert table name for this struct type
func (r *RolePermissions) TableName() string {
	return "role_permissions"
}
func GetCustomRolePaths(db *gorm.DB) (map[string][]RolePermissions, error) {
	customRoles, err := GetCustomRoles(db)
	if err != nil {
		return nil, err
	}
	res := make(map[string][]RolePermissions)
	for _, role := range customRoles {
		paths, err := GetPermissionIDsByRoleCode(db, role.RoleCode)
		if err != nil {
			return nil, err
		}
		res[role.RoleCode] = paths
	}
	return res, nil
}

// GetPermissionIDsByRoleIDs 查询多个角色拥有的权限ID，并去重
func GetPermissionIDsByRoleCode(db *gorm.DB, roleCode string) ([]RolePermissions, error) {
	var paths []RolePermissions

	// 查询所有对应的角色权限，并使用 DISTINCT 去重
	err := db.Model(&RolePermissions{}).
		Where("role_code = ?", roleCode).
		Find(&paths).Error

	if err != nil {
		return nil, err
	}

	return paths, nil
}
