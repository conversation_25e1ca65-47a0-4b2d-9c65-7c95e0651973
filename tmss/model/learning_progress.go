package model

// 学习进度管理
type LearningProgress struct {
	ID            int64  `gorm:"primary_key;AUTO_INCREMENT;column:id;type:bigint;" json:"id"`
	AssignmentID  int64  `gorm:"column:assignment_id;type:bigint;" json:"assignment_id"` // 关联作业ID
	SummaryID     int64  `gorm:"column:summary_id;type:bigint;" json:"summary_id"`       // 关联作业进度汇总ID
	StudentID     int64  `gorm:"column:student_id;type:bigint;" json:"student_id"`       // 学生ID
	TeacherID     int64  `gorm:"column:teacher_id;type:bigint;" json:"teacher_id"`       // 批阅教师ID
	CourseID      int64  `gorm:"column:course_id;type:bigint;" json:"course_id"`
	ChapterID     int64  `gorm:"column:chapter_id;type:bigint;" json:"chapter_id"`
	ActionType    string `gorm:"column:action_type;type:varchar;size:20;" json:"action_type"` // 动作类型（courseware、questions、resources）
	Status        string `gorm:"column:status;type:varchar;size:20;default:not_started;" json:"status"`
	RefID         int64  `gorm:"column:ref_id;type:bigint;" json:"ref_id"`                                   // 关联ID（试题ID、课件ID、资料ID等）
	Content       string `gorm:"column:content;type:text;" json:"content"`                                   // 内容描述
	Answer        string `gorm:"column:answer;type:text;" json:"answer"`                                     // 答案
	CompleteTime  int64  `gorm:"column:complete_time;type:bigint;" json:"complete_time"`                     // 完成时间
	StartTime     int64  `gorm:"column:start_time;type:bigint;" json:"start_time"`                           // 开始时间（开始时间）
	StartAt       int64  `gorm:"column:start_at;" json:"start_at"`                                           // 开始时间（开始时间）
	EndAt         int64  `gorm:"column:end_at;" json:"end_at"`                                               // 结束时间（截止时间）
	Required      bool   `gorm:"column:required;type:boolean;" json:"required"`                              // 是否必做
	OrderNum      int    `gorm:"column:order_num;type:int;" json:"order_num"`                                // 排序序号
	Minutes       int    `gorm:"column:minutes;type:int;" json:"minutes"`                                    // 学习时长（分钟）
	UseTime       int    `gorm:"column:use_time;type:int;" json:"use_time"`                                  // 使用时长（分）
	GetScore      int    `gorm:"column:get_score;type:int;" json:"get_score"`                                //获得分数
	Score         int    `gorm:"column:score;type:int;" json:"score"`                                        // 参考分数
	RecordedBy    string `gorm:"column:recorded_by;type:varchar;size:20;default:system;" json:"recorded_by"` // 记录来源（system/manual）
	CreatedAt     int64  `gorm:"column:created_at;autoCreateTime" json:"created_at"`
	UpdatedAt     int64  `gorm:"column:updated_at;autoUpdateTime" json:"updated_at"`
	ReviewAt      int64  `gorm:"column:review_at;" json:"review_at"`                     // 批阅时间
	ReviewComment string `gorm:"column:review_comment;type:text;" json:"review_comment"` //批语
}

// TableName sets the insert table name for this struct type
func (l *LearningProgress) TableName() string {
	return "learning_progress"
}
