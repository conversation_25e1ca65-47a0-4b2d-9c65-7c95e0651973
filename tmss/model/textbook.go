package model

// Textbook struct is a row record of the textbook table in the tms database
type Textbook struct {
	ID        int64  `gorm:"primary_key;AUTO_INCREMENT;column:id;type:bigint;" json:"id"`
	Title     string `gorm:"column:title;type:varchar;size:255;" json:"title"`         // 教材名称
	UserID    int64  `gorm:"column:user_id;type:bigint;" json:"user_id"`               // 用户ID
	Author    string `gorm:"column:author;type:varchar;size:100;" json:"author"`       // 作者
	Isbn      string `gorm:"column:isbn;type:varchar;size:50;" json:"isbn"`            // ISBN编号
	Publisher string `gorm:"column:publisher;type:varchar;size:100;" json:"publisher"` // 出版社
	Edition   string `gorm:"column:edition;type:varchar;size:50;" json:"edition"`      // 版本号
	FileName  string `gorm:"column:file_name;type:varchar;size:255;" json:"file_name"` // 文件名
	FileType  string `gorm:"column:file_type;type:varchar;size:50;" json:"file_type"`  // 文件类型
	FilePath  string `gorm:"column:file_path;type:varchar;size:255;" json:"file_path"` // 文件路径
	CreatedAt int64  `gorm:"column:created_at;autoCreateTime" json:"created_at"`       // 创建时间（秒级时间戳）
	UpdatedAt int64  `gorm:"column:updated_at;autoUpdateTime" json:"updated_at"`       // 更新时间（秒级时间戳）
}

// TableName sets the insert table name for this struct type
func (t *Textbook) TableName() string {
	return "textbook"
}

type ReqTextbookSearch struct {
	Page     int    `form:"page,default=1" json:"page"`
	PageSize int    `form:"page_size,default=10" json:"page_size"`
	Title    string `form:"title" json:"title"`       // 按标题模糊搜索
	Author   string `form:"author" json:"author"`     // 按作者搜索
	UserID   int64  `form:"user_id" json:"user_id"`   // 用户ID
	Username string `form:"username" json:"username"` // 用户名
}

// RespTextbookList 分页返回结果
type RespTextbookList struct {
	List  []Textbook `json:"list"`
	Total int64      `json:"total"`
}
