package model

type AssignmentExt struct {
	ID           int64  `gorm:"primary_key;AUTO_INCREMENT;column:id;type:bigint;" json:"id"`
	ExtKey       string `gorm:"column:ext_key;type:varchar;" json:"ext_key"` // 扩展键，例如：'class','teacher' 等
	ExtValue     int64  `gorm:"column:ext_value;type:bigint;" json:"ext_value"`
	AssignmentID int64  `gorm:"column:assignment_id;type:bigint;" json:"assignment_id"` // 关联作业ID
}

func (AssignmentExt) TableName() string {
	return "assignment_ext"
}
