package model

import "github.com/spf13/cast"

// Permissions struct is a row record of the permissions table in the tms database
type Permissions struct {
	//[1] id                                             text                 null: false  primary: true   isArray: false  auto: false  col: text            len: -1      default: []
	ID string `gorm:"primary_key;column:id;type:text;" json:"id"`
	//[2] parent_id
	ParentID string `gorm:"column:parent_id;type:text;default:0;" json:"parent_id"` // 父权限
	//[3] name                                           varchar(100)         null: false  primary: false  isArray: false  auto: false  col: varchar         len: 100     default: []
	Name string `gorm:"column:name;type:varchar;size:100;" json:"name"`
	//[4] type                                           varchar(20)          null: false  primary: false  isArray: false  auto: false  col: varchar         len: 20      default: [route]
	Type string `gorm:"column:type;type:varchar;size:20;default:'route';" json:"type"` // 权限类型：group 或 route
	//[5] method                                         varchar(50)          null: false  primary: false  isArray: false  auto: false  col: varchar         len: 50      default: []
	Method string `gorm:"column:method;type:varchar;size:50;" json:"method"` // 请求方法
	//[4] path                                           varchar(255)         null: false  primary: false  isArray: false  auto: false  col: varchar         len: 255     default: []
	Path string `gorm:"column:path;type:varchar;size:255;" json:"path"` // 请求路径
	//[5] module                                         varchar(50)          null: false  primary: false  isArray: false  auto: false  col: varchar         len: 50      default: []
	Module string `gorm:"column:module;type:varchar;size:50;" json:"module"`
	//[6] api_version                                     varchar(50)          null: false  primary: false  isArray: false  auto: false  col: varchar         len: 50      default: []
	APIVersion string `gorm:"column:api_version;type:varchar;size:50;" json:"api_version"`
}

// TableName sets the insert table name for this struct type
func (p *Permissions) TableName() string {
	return "permissions"
}

// GeneratePermissionID generates a stable ID for a permission based on its method and path.
// This helps maintain consistent IDs across application restarts and route changes.
func GeneratePermissionID(method, path string) string {
	// A simple hash function for demonstration.
	// In a real application, consider a more robust hashing algorithm (e.g., FNV-1a, SHA-1)
	// and handle potential collisions or ensure uniqueness.
	hash := 0
	for _, char := range method + path {
		hash = (hash*31 + int(char)) % 1000000007 // Using a large prime to reduce collisions
	}
	return cast.ToString(hash)
}
