package model

// ExamProgressStatus 定义用户考试进度的状态常量
const (
	ExamProgressStatusNotStarted = "not_started" // 未开始
	ExamProgressStatusOngoing    = "ongoing"     // 进行中
	ExamProgressStatusFinished   = "finished"    // 已完成
	ExamProgressStatusTimeout    = "timeout"     // 超时
	ExamProgressStatusReviewed   = "reviewed"    // 已阅卷
)

// 🔄 状态流转逻辑

// 未开始 (not_started)：
// 用户尚未进入考试页面。
// exam_progress 和 exam_answers 表无记录。

// 进行中 (ongoing)：
// 用户开始答题时，在 exam_answers 表中插入记录。
// 实时更新 exam_progress 表的 completed_count 和 score。

// 已完成 (finished)：
// 用户提交试卷后，更新 exam_answers 表的得分字段。
// 计算总分并更新到 exam_progress 表。

// 超时 (timeout)：
// 考试时间超过规定时长。
// 设置 exam_progress.is_timeout 为 true。
type ExamProgress struct {
	ID             int64        `gorm:"primary_key;AUTO_INCREMENT;column:id" json:"id"`          // 主键ID
	UserID         int64        `gorm:"column:user_id;type:bigint;" json:"user_id"`              // 用户ID
	ReviewerID     int64        `gorm:"column:reviewer_id;type:bigint;" json:"reviewer_id"`      // 阅卷人ID
	ExamID         int64        `gorm:"column:exam_id;type:bigint;" json:"exam_id"`              // 考试ID
	PaperID        int64        `gorm:"column:paper_id;type:bigint;" json:"paper_id"`            // 试卷ID
	Status         string       `gorm:"column:status;type:varchar;size:50;" json:"status"`       // 考试进度状态
	StartTime      int64        `gorm:"column:start_time;type:bigint;" json:"start_time"`        // 考试开始时间（Unix 时间戳）
	EndTime        int64        `gorm:"column:end_time;type:bigint;" json:"end_time"`            // 考试结束时间（Unix 时间戳）
	CompletedCount int          `gorm:"column:completed_count;type:int;" json:"completed_count"` // 已完成题目数量
	TotalScore     int          `gorm:"column:total_score;type:int;" json:"total_score"`         // 试卷总分
	Score          int          `gorm:"column:score;type:int;" json:"score"`                     // 用户得分
	IsTimeout      bool         `gorm:"column:is_timeout;type:bool;" json:"is_timeout"`          // 是否超时
	CreatedAt      int64        `gorm:"column:created_at;autoCreateTime" json:"created_at"`      // 创建时间
	UpdatedAt      int64        `gorm:"column:updated_at;autoUpdateTime" json:"updated_at"`      // 更新时间
	ReviewedAt     int64        `gorm:"column:reviewed_at;type:bigint;" json:"reviewed_at"`      // 阅卷时间（Unix 时间戳）
	Answers        []ExamAnswer `gorm:"foreignKey:ExamProgressID" json:"answers"`                // 关联的答题详情
	//AnswerDetails  string `gorm:"column:answer_details;type:text;" json:"answer_details"`      // 答题详情（JSON 格式存储）

}

// TableName sets the insert table name for this struct type
func (e *ExamProgress) TableName() string {
	return "exam_progress"
}

type ExamRank struct {
	UserID int64 `json:"user_id"`
	ExamID int64 `json:"exam_id"`
	Score  int   `json:"score"`
	Rank   int   `json:"rank"`
}

type ExamProgressDetail struct {
	ExamProgress
	StudentName string `json:"student_name"`
	TeacherName string `json:"teacher_name"`
}

type ExamProgressList struct {
	List  []ExamProgressDetail `json:"list"`
	Total int64                `json:"total"`
}

type ExamHistory struct {
	UserID         int64  `json:"user_id"`
	ExamID         int64  `json:"exam_id"`
	ExamName       string `json:"exam_name"`
	ProgressdID    int64  `json:"progress_id"`
	ProgressStatus string `json:"progress_status"`
	ExamStatus     string `json:"exam_status"`
	Score          int    `json:"score"`
	SubmitAt       int64  `json:"submit_at"`
	CostTime       int64  `json:"cost_time"`
	Rank           int    `json:"rank"`
}

type UserExamEntry struct {
	Exam         *Exams        `json:"exam"`
	ExamProgress *ExamProgress `json:"exam_progress"`
	ExamAnswers  []ExamAnswer  `json:"exam_answers"`
}

// 在model包中添加以下结构体定义

type QuestionDetail struct {
	Questions Questions   `json:"question"`
	Answer    ExamAnswer  `json:"answer"`
	Review    *ExamReview `json:"review,omitempty"` // 仅主观题有阅卷记录
}

type ExamProgressDetailResponse struct {
	Progress       ExamProgress     `json:"progress"`  // 考试进度信息
	Exam           Exams            `json:"exam"`      // 考试信息
	Student        Users            `json:"student"`   // 学生信息
	Reviewer       Users            `json:"reviewer"`  // 阅卷老师信息
	QuestionDetail []QuestionDetail `json:"questions"` // 题目详情列表
}

type UserPaperAnswerEntry struct {
	PaperQuestion PaperQuestions `json:"paper_question"`
	ExamAnswer    ExamAnswer     `json:"exam_answer"`
}
