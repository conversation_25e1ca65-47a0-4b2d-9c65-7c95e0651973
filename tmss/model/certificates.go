package model

import (
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"strconv"
	"time"
)

const (
	CertificatesStatusDraft     = "draft"     // 未发布
	CertificatesStatusReviewing = "reviewing" // 审核中
	CertificatesStatusPublished = "published" // 已发布
	CertificatesStatusRejected  = "rejected"  // 审核拒绝
)

type ReqCertificateCreate struct {
	TemplateID int64 `json:"template_id"`
	UserID     int64 `json:"user_id"`
	ExamID     int64 `json:"exam_id"`
	Score      int   `json:"score"`
}

// Certificates struct is a row record of the certificates table in the tms database
type Certificates struct {
	ID             int64  `gorm:"primary_key;AUTO_INCREMENT;column:id;type:bigint;" json:"id"`
	StudentID      int64  `gorm:"column:student_id;type:bigint;" json:"student_id"`
	ExamID         int64  `gorm:"column:exam_id;type:bigint;" json:"exam_id"`
	ScoreID        int64  `gorm:"column:score_id;type:bigint;" json:"score_id"`
	Score          int    `gorm:"column:score;type:int;" json:"score"`                                     // 总分数
	UserID         int64  `gorm:"column:user_id;type:bigint;" json:"user_id"`                              // 提交人ID
	CertNumber     string `gorm:"column:cert_number;uniqueIndex;type:varchar;size:50;" json:"cert_number"` // 证书编号
	Signature      string `gorm:"column:signature;type:text;" json:"signature"`                            // 数字签名（SM2算法）
	QrCode         string `gorm:"column:qr_code;type:text;" json:"qr_code"`                                // 二维码内容
	TemplateID     int64  `gorm:"column:template_id;type:bigint;" json:"template_id"`                      // 印刷模板ID
	Address        string `gorm:"column:address;type:varchar;size:255;" json:"address"`                    // 邮寄地址
	TrackingNumber string `gorm:"column:tracking_number;type:varchar;size:100;" json:"tracking_number"`    // 物流单号
	Status         string `gorm:"column:status;type:varchar;size:50;" json:"status"`
	CreatedAt      int64  `gorm:"column:created_at;autoCreateTime" json:"created_at"`   // 创建时间
	UpdatedAt      int64  `gorm:"column:updated_at;autoUpdateTime" json:"updated_at"`   // 更新时间
	SubmitAt       int64  `gorm:"column:submit_at;" json:"submit_at"`                   // 提交时间（审核提交时间）
	PublishedAt    int64  `gorm:"column:published_at;" json:"published_at"`             // 发布时间
	RevokedAt      int64  `gorm:"column:revoked_at;" json:"revoked_at"`                 // 撤销时间
	RevokeReason   string `gorm:"column:revoke_reason;type:text;" json:"revoke_reason"` // 撤销原因
}

// TableName sets the insert table name for this struct type
func (c *Certificates) TableName() string {
	return "certificates"
}

// 生成唯一的证书编号
func (c *Certificates) GenerateCertNumber() {
	// 使用考试ID、学生ID和分数创建基础字符串
	base := fmt.Sprintf("%d-%d-%d", c.ExamID, c.StudentID, c.Score)

	// 添加当前时间戳增加随机性
	timestamp := strconv.FormatInt(time.Now().UnixNano(), 10)

	// 创建哈希值确保唯一性
	hash := sha256.Sum256([]byte(base + timestamp))
	hashStr := hex.EncodeToString(hash[:])

	// 取前16位作为证书编号（纯数字）
	numStr := ""
	for _, char := range hashStr {
		if char >= '0' && char <= '9' {
			numStr += string(char)
			if len(numStr) >= 16 {
				break
			}
		}
	}
	// 如果数字长度不足，用0补齐
	for len(numStr) < 16 {
		numStr += "0"
	}

	c.CertNumber = numStr
}

// // BeforeUpdate Gorm钩子 - 在更新前检查状态变化
// func (c *Certificates) BeforeUpdate(tx *gorm.DB) (err error) {
// 	// 检查状态是否更新为"published"
// 	if c.Status == CertificatesStatusPublished {
// 		// 从数据库中获取原始记录
// 		var original Certificates
// 		if err := tx.First(&original, c.ID).Error; err != nil {
// 			return err
// 		}

// 		// 如果状态从非published变为published
// 		if original.Status != CertificatesStatusPublished {
// 			// 从配置加载私钥
// 			privateKey, err := config.LoadPrivateKey(config.Config.Secret.CertificatesKey)
// 			if err != nil {
// 				return fmt.Errorf("加载私钥失败: %w", err)
// 			}

// 			// 生成签名和二维码
// 			if err := c.GenerateSignatureAndQR(privateKey); err != nil {
// 				return fmt.Errorf("生成签名和二维码失败: %w", err)
// 			}

// 			// 设置发布时间戳
// 			if c.PublishedAt == 0 {
// 				c.PublishedAt = time.Now().Unix()
// 			}
// 		}
// 	}
// 	return nil
// }

// // 生成证书签名和二维码
// func (c *Certificates) GenerateSignatureAndQR(privateKey *sm2.PrivateKey) error {
// 	// 1. 生成签名
// 	signature, err := c.generateSignature(privateKey)
// 	if err != nil {
// 		return err
// 	}
// 	c.Signature = signature

// 	// 2. 生成二维码
// 	qrCode, err := c.generateQRCode()
// 	if err != nil {
// 		return err
// 	}
// 	c.QrCode = qrCode

// 	return nil
// }

// // 生成数字签名（使用SM2算法）
// func (c *Certificates) generateSignature(privateKey *sm2.PrivateKey) (string, error) {
// 	// 签名数据：证书编号 + 学生ID + 考试ID + 分数
// 	data := fmt.Sprintf("%s-%d-%d-%d", c.CertNumber, c.StudentID, c.ExamID, c.Score)

// 	// 使用SM2私钥签名
// 	sign, err := privateKey.Sign(rand.Reader, []byte(data), nil)
// 	if err != nil {
// 		return "", fmt.Errorf("签名生成失败: %w", err)
// 	}

// 	// 将签名转换为Base64字符串
// 	return base64.StdEncoding.EncodeToString(sign), nil
// }

// // 生成二维码内容
// func (c *Certificates) generateQRCode() (string, error) {
// 	// 二维码内容：证书基本信息
// 	content := fmt.Sprintf("证书编号: %s\n学生ID: %d\n考试ID: %d\n分数: %d\n",
// 		c.CertNumber, c.StudentID, c.ExamID, c.Score)

// 	// 生成二维码图片
// 	qr, err := qrcode.Encode(content, qrcode.Medium, 256)
// 	if err != nil {
// 		return "", fmt.Errorf("二维码生成失败: %w", err)
// 	}

// 	// 转换为Base64字符串
// 	return base64.StdEncoding.EncodeToString(qr), nil
// }
