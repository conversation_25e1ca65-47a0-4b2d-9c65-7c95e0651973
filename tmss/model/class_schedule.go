package model

const (
	ClassScheduleStatusDraft     = "draft"     // 草稿
	ClassScheduleStatusPublished = "published" // 已发布
	ClassScheduleStatusReviewing = "reviewing" // 审核中
	ClassScheduleStatusRejected  = "rejected"  // 审核拒绝
	ClassScheduleStatusCompleted = "completed" // 已完成
	ClassScheduleStatusAdjusting = "adjusting" // 已调整
	ClassScheduleStatusOngoing   = "ongoing"   // 进行中
)

// ClassSchedule 课表
type ClassSchedule struct {
	ID             int64  `gorm:"primary_key;AUTO_INCREMENT;column:id;type:bigint;" json:"id"`
	TeachingPlanID int64  `gorm:"column:teaching_plan_id;type:bigint;index;" json:"teaching_plan_id"` // 所属教学计划ID
	CourseID       int64  `gorm:"column:course_id;type:bigint;" json:"course_id"`                     // 课程ID
	Title          string `gorm:"column:title;type:varchar(200);" json:"title"`                       // 课时标题
	UserID         int64  `gorm:"column:user_id;type:bigint;" json:"user_id"`                         // 添加人
	Content        string `gorm:"column:content;type:text;" json:"content"`                           // 课时内容
	ClassroomID    int64  `gorm:"column:classroom_id;type:bigint;" json:"classroom_id"`               // 教室ID
	TeacherID      int64  `gorm:"column:teacher_id;type:bigint;" json:"teacher_id"`                   // 授课教师ID
	StartAt        int64  `gorm:"column:start_at;" json:"start_at"`                                   // 开始时间（时间戳）
	EndAt          int64  `gorm:"column:end_at;" json:"end_at"`                                       // 结束时间（时间戳）
	Weekday        int    `gorm:"column:weekday;type:int;" json:"weekday"`                            // 星期几（0=周日,1=周一,...,6=周六）
	PreviousID     int64  `gorm:"column:previous_id;type:bigint;index;" json:"previous_id"`           // 前置课时ID（0表示无前置）
	Status         string `gorm:"column:status;type:varchar(30);" json:"status"`                      // 课时状态
	CreatedAt      int64  `gorm:"column:created_at;autoCreateTime" json:"created_at"`
	UpdatedAt      int64  `gorm:"column:updated_at;autoUpdateTime" json:"updated_at"`
	SubmitAt       int64  `gorm:"column:submit_at;" json:"submit_at"`                 // 提交时间（审核提交时间）
	PublishedAt    int64  `gorm:"column:published_at;" json:"published_at"`           // 发布时间（审核通过时间）
	ChangedInfo    string `gorm:"column:changed_info;type:text;" json:"changed_info"` // 更改信息（审核时填写）
	Remarks        string `gorm:"column:remarks;type:text;" json:"remarks"`           // 备注信息
}

// TableName 设置表名
func (c *ClassSchedule) TableName() string {
	return "class_schedule"
}

// 课表生成请求
type ReqGenerateClassSchedule struct {
	TeachingPlanID int64  `json:"teaching_plan_id"`           // 教学计划ID
	StartDate      string `json:"start_date"`                 // 开始日期（YYYY-MM-DD）
	LessonNum      int    `json:"lesson_num"`                 // 课时数
	ClassHours     int    `json:"class_hours"`                // 每个课时的时长（分钟）
	ClassTime      string `json:"class_time"`                 // 每天上课时间（HH:MM）
	Weekdays       []int  `json:"weekdays"`                   // 每周上课日（0=周日,1=周一,...,6=周六）
	TeacherID      int64  `json:"teacher_id"`                 // 教师ID
	CourseID       int64  `form:"course_id" json:"course_id"` // 课程ID
	ClassroomID    int64  `json:"classroom_id"`               // 教室ID
	Title          string `json:"title"`                      // 课时标题（可选，如果为空则自动生成）
	Content        string `json:"content"`                    // 课时内容
	PreviousID     int64  `json:"previous_id"`                // 前置课时ID（0表示无前置）
}

// 课表创建请求
type ReqClassScheduleCreate struct {
	PreviousID int64           `json:"previous_id"` // 前置课时ID（0表示无前置）
	Schedules  []ClassSchedule `json:"schedules"`   // 批量创建课时
}

// 课表搜索请求
type ReqClassScheduleSearch struct {
	Page           int    `form:"page,default=1" json:"page"`
	PageSize       int    `form:"page_size,default=10" json:"page_size"`
	TeachingPlanID int64  `form:"teaching_plan_id" json:"teaching_plan_id"` // 教学计划ID
	CourseID       int64  `form:"course_id" json:"course_id"`               // 课程ID
	ClassroomID    int64  `form:"classroom_id" json:"classroom_id"`         // 教室ID
	TeacherID      int64  `form:"teacher_id" json:"teacher_id"`             // 教师ID
	Status         string `form:"status" json:"status"`                     // 课时状态
	DateRange      string `form:"date_range" json:"date_range"`             // 时间范围（如2023-01-01~2023-12-31）
}

// 课表返回结构（带教师名称）
type RespClassSchedule struct {
	ClassSchedule
	TeacherName      string          `json:"teacher_name"`           // 教师姓名（关联查询）
	TeachingPlanName string          `json:"teaching_plan_name"`     // 教学计划（关联查询）
	PreviousName     string          `json:"previous_name"`          // 前置课时名称（关联查询）
	CourseName       string          `json:"course_name"`            // 课程名称（关联查询）
	ClassroomName    string          `json:"classroom_name"`         // 教室名称（关联查询）
	Rev              *RevisionRecord `json:"rev,omitempty" gorm:"-"` // 可能为空
}
type RespClassScheduleList struct {
	List  []RespClassSchedule `json:"list"`
	Total int64               `json:"total"`
}
