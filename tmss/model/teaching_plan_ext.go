package model

// TeachingPlanExt struct is a row record of the teaching_plan_ext table in the tms database
type TeachingPlanExt struct {
	//[ 0] ext_id                                         bigint               null: false  primary: true   isArray: false  auto: true   col: bigint          len: -1      default: []
	ExtID int64 `gorm:"primary_key;AUTO_INCREMENT;column:ext_id;type:bigint;" json:"ext_id"`
	//[ 1] plan_id                                        bigint               null: false  primary: false  isArray: false  auto: false  col: bigint          len: -1      default: []
	PlanID int64 `gorm:"column:plan_id;type:bigint;" json:"plan_id"` // 关联教学计划ID
	//[ 2] ext_key                                        varchar(100)         null: false  primary: false  isArray: false  auto: false  col: varchar         len: 100     default: []
	ExtKey string `gorm:"column:ext_key;type:varchar;size:100;" json:"ext_key"` // 扩展字段键
	//[ 3] ext_value                                      text(65535)          null: false  primary: false  isArray: false  auto: false  col: text            len: 65535   default: []
	ExtValue int64 `gorm:"column:ext_value;type:bigint;" json:"ext_value"` // 扩展字段值

}

// TableName sets the insert table name for this struct type
func (t *TeachingPlanExt) TableName() string {
	return "teaching_plan_ext"
}
