package model

import (
	"crypto/sha1"
	"encoding/hex"
	"fmt"
	"time"
)

// sync_type 同步用户user_info 学习进度 progress 考试记录 exam_records 习记录 study_records
// SyncRecords 同步记录表结构体
type SyncRecords struct {
	ID         int64  `gorm:"primary_key;AUTO_INCREMENT;column:id;" json:"id"`               // 同步记录唯一标识符，主键，自增
	EventID    string `gorm:"column:event_id;type:varchar;uniqueIndex" json:"event_id"`      // 事件ID hash 生成唯一
	UserID     int64  `gorm:"column:user_id;type:bigint;" json:"user_id"`                    // 需要同步的用户ID
	SyncType   string `gorm:"column:sync_type;type:varchar;size:50;" json:"sync_type"`       // 同步类型（例如：登录信息、配置信息等）
	SyncObject string `gorm:"column:sync_object;type:varchar;size:50;" json:"sync_object"`   // 同步对象（例如：客户端client、服务端server）
	SyncData   string `gorm:"column:sync_data;type:text;" json:"sync_data"`                  // 同步的具体数据内容
	IsSynced   bool   `gorm:"column:is_synced;type:boolean;default:false;" json:"is_synced"` // 是否已经完成同步
	CreatedAt  int64  `gorm:"column:created_at;" json:"created_at"`                          // 添加时间，默认当前时间戳
	UpdatedAt  int64  `gorm:"column:updated_at;" json:"updated_at"`                          // 上次同步时间
}

// TableName 设置数据库表名
func (s *SyncRecords) TableName() string {
	return "sync_records"
}

func GenerateEventID(operator int64, eventType, syncObject, syncData string, createdAt time.Time) string {
	// 格式化时间（毫秒级时间戳）
	timestamp := createdAt.UnixMilli()
	raw := fmt.Sprintf("%d|%s|%s|%s|%d", operator, eventType, syncObject, syncData, timestamp)
	h := sha1.Sum([]byte(raw))
	return hex.EncodeToString(h[:])
}
