package model

import (
	"gorm.io/gorm"
)

// Endpoint
const (
	EndpointManager = "manager" // 后台
)
const (
	AuthNone    = "guest"   // 无需认证
	AuthUser    = "user"    // 普通用户认证
	AuthTeacher = "teacher" // 教员认证
	AuthHeader  = "header"  // 教导主任
	AuthAdmin   = "admin"   // 管理员认证
	AuthAll     = "all"     // 所有认证
)

// Role Code
// const (
// 	RoleAdmin    = "admin"
// 	RoleDirector = "director"
// 	RoleTeacher  = "teacher"
// 	RoleStudent  = "student"
// )

// Roles struct is a row record of the roles table in the tms database
type Roles struct {
	ID          int64  `gorm:"primary_key;AUTO_INCREMENT;column:id;" json:"id"`
	RoleName    string `gorm:"column:role_name;type:varchar;size:50;" json:"role_name"`
	Description string `gorm:"column:description;type:text;" json:"description"`
	IsSystem    bool   `gorm:"column:is_system;type:boolean;" json:"is_system"`       // 是否为系统内置角色，默认false
	SysCode     string `gorm:"column:sys_code;type:varchar;size:50;" json:"sys_code"` // 系统标识
	RoleCode    string `gorm:"column:role_code;type:varchar;size:50;index:idx_role_code;" json:"role_code"`
	CreatedAt   int64  `gorm:"column:created_at" json:"created_at"` // 记录创建时间，默认当前时间戳
}

// TableName sets the insert table name for this struct type
func (r *Roles) TableName() string {
	return "roles"
}

// vaidator role code
func (r Roles) ValidRoleCode() bool {
	return r.RoleCode == AuthUser || r.RoleCode == AuthTeacher || r.RoleCode == AuthHeader || r.RoleCode == AuthAdmin || r.RoleCode == AuthAll
}
func GetRoleCodes() []string {
	roles := []string{
		AuthAdmin,
		AuthUser,
		AuthTeacher,
		AuthHeader,
		AuthAll,
	}
	return roles
}
func GetCustomRoles(db *gorm.DB) ([]Roles, error) {
	var roles []Roles
	err := db.Where("is_system = ?", false).Find(&roles).Error
	if err != nil {
		return nil, err
	}

	return roles, nil
}

func (r Roles) Columns() []string {
	return []string{"id", "role_name", "description", "is_system", "sys_code", "role_code", "created_at"}
}

func (r Roles) UpdateColumns(mode string) []string {
	if mode == "exclude_primary_key" {
		return []string{"role_name", "description", "is_system", "sys_code", "role_code"}
	}
	return r.Columns()
}
