package model

// CoursewareType 定义课件类型常量
const (
	CoursewareTypeTheory  = "theory_courseware"  // 理论课件
	CoursewareTypeVirtual = "virtual_courseware" // 虚拟课件
)

// CoursewareStatus 定义课件状态常量
const (
	CoursewareStatusDraft     = "draft"     // 草稿
	CoursewareStatusReviewing = "reviewing" // 审核中
	CoursewareStatusPublished = "published" // 已发布
	CoursewareStatusRejected  = "rejected"  // 审核拒绝
)

// Courseware struct is a row record of the courseware table in the tms database
type Courseware struct {
	ID             int64  `gorm:"primary_key;AUTO_INCREMENT;column:id;type:bigint;" json:"id"`
	CourseID       int64  `gorm:"column:course_id;type:bigint;" json:"course_id"`                      // 关联课程ID
	ChapterID      int64  `gorm:"column:chapter_id;type:bigint;" json:"chapter_id"`                    // 关联章节ID
	UserID         int64  `gorm:"column:user_id;type:bigint;" json:"user_id"`                          // 创建人ID
	Title          string `gorm:"column:title;type:varchar;size:255;" json:"title"`                    // 课件标题
	FileName       string `gorm:"column:file_name;type:varchar;size:255;" json:"file_name"`            // 文件名
	FileType       string `gorm:"column:file_type;type:varchar;size:10;" json:"file_type"`             // 文件类型（pptx/pdf/mp4等）
	CoursewareType string `gorm:"column:courseware_type;type:varchar;size:20;" json:"courseware_type"` // 课件类型（theory/virtual）
	FilePath       string `gorm:"column:file_path;type:varchar;size:600;" json:"file_path"`            // 文件存储路径
	UnzipPath      string `gorm:"column:unzip_path;type:varchar;size:600;" json:"unzip_path"`          // 解压后文件存储路径
	Description    string `gorm:"column:description;type:text;" json:"description"`                    // 课件描述
	Target         string `gorm:"column:target;type:varchar;size:50;" json:"target"`                   // 适用对象
	Score          int    `gorm:"column:score;type:int;" json:"score"`                                 // 参考分数
	Minutes        int    `gorm:"column:minutes;type:int;" json:"minutes"`                             // 答题时间（分钟）
	TotalStep      int    `gorm:"column:total_step;type:int;" json:"total_step"`                       // 完成虚拟课件总步数
	Status         string `gorm:"column:status;type:varchar;size:50;" json:"status"`                   // 课程状态（draft, reviewing, published）
	CreatedAt      int64  `gorm:"column:created_at;autoCreateTime" json:"created_at"`                  // 创建时间（Unix 时间戳）
	UpdatedAt      int64  `gorm:"column:updated_at;autoUpdateTime" json:"updated_at"`                  // 更新时间（Unix 时间戳）
	SubmitAt       int64  `gorm:"column:submit_at;" json:"submit_at"`                                  // 提交时间（审核提交时间）
	PublishedAt    int64  `gorm:"column:published_at;" json:"published_at"`                            // 发布时间
	Version        string `gorm:"column:version;type:varchar(30);default:v1.0;" json:"version"`        // 版本号
}

// TableName sets the insert table name for this struct type
func (c *Courseware) TableName() string {
	return "courseware"
}

// ReqCoursewareSearch 搜索请求参数
type ReqCoursewareSearch struct {
	Page           int    `form:"page,default=1" json:"page"`
	PageSize       int    `form:"page_size,default=10" json:"page_size"`
	Name           string `form:"name" json:"name"`           // 按名称模糊搜索
	CourseID       int64  `form:"course_id" json:"course_id"` // 关联课程ID
	ChapterID      int64  `form:"chapter_id" json:"chapter_id"`
	UserID         int64  `form:"user_id" json:"user_id"`                 // 用户ID
	Status         string `form:"status" json:"status"`                   // 状态筛选 draft, reviewing, published
	CoursewareType string `form:"courseware_type" json:"courseware_type"` // 课件类型筛选 theory, virtual
}
type RespCoursewareDetail struct {
	Courseware
	CourseName  string `json:"course_name"`  // 课程名称
	ChapterName string `json:"chapter_name"` // 章节名称

}
type RespCoursewareList struct {
	List  []RespCoursewareWithLatestRevision `json:"list"`
	Total int64                              `json:"total"`
}

type UpSertCoursewareReq struct {
	Courseware
	TotalStep int `json:"total_step"` // 完成虚拟课件总步数
}

type CoursewareWithExt struct {
	Courseware Courseware      `json:"courseware"`
	Exts       []CoursewareExt `json:"exts"`
}

// type RespCoursewareList struct {
// 	List  []RespCoursewareWithLatestRevision `json:"list"`
// 	Total int64                           `json:"total"`
// }

// RespSyllabusWithLatestRevision 分页返回+最新修订记录
type RespCoursewareWithLatestRevision struct {
	Courseware RespCoursewareDetail `json:"courseware"`
	Rev        *RevisionRecord      `json:"rev,omitempty"` // 可能为空
}
