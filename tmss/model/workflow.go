package model

import (
	"errors"
	"sort"
	"time"

	"gorm.io/gorm"
)

const (
	WorkflowStatusEnable  = "enable"  // 开启
	WorkflowStatusDisable = "disable" // 禁用
)

// 定义模块常量
const (
	ModuleSyllabus        = "syllabus"
	ModuleTeachingPlan    = "teaching_plan"
	ModuleCourses         = "courses"
	ModuleCourseware      = "courseware"
	ModuleResources       = "resources"
	ModuleQuestions       = "questions"
	ModulePapers          = "papers"
	ModuleExams           = "exams"
	ModuleApprove         = "workflow_approve"
	ModuleScore           = "exam_score"
	ModuleCertificates    = "certificates"
	ModuleSchedule        = "class_schedule"
	ModuleDocuments       = "teaching_document"
	ModuleKnowledgePoints = "knowledge_points"
	ModuleAssignment      = "assignment"
)

// Workflow_ struct is a row record of the workflow table in the tms database
type Workflow struct {
	ID          int64  `gorm:"primary_key;AUTO_INCREMENT;column:id;type:bigint;" json:"id"`
	Name        string `gorm:"column:name;type:varchar;size:100;" json:"name"`               // 流程名称（唯一）
	UserID      int64  `gorm:"column:user_id;type:bigint;" json:"user_id"`                   // 用户ID（创建者）
	Description string `gorm:"column:description;type:varchar;size:255;" json:"description"` // 流程描述
	NodeLen     int64  `gorm:"column:node_len;type:bigint;" json:"node_len"`                 // 节点数量
	ModuleKey   string `gorm:"column:module_key;type:varchar;size:50;" json:"module_key"`    // 流程类型（课件审核/资源发布）
	Status      string `gorm:"column:status;varchar;size:50;" json:"status"`                 // 流程状态（1：启用，0：禁用）
	CreatedAt   int64  `gorm:"column:created_at;autoCreateTime" json:"created_at"`           // 创建时间（秒级时间戳）
	UpdatedAt   int64  `gorm:"column:updated_at;autoUpdateTime" json:"updated_at"`           // 更新时间（秒级时间戳）
}

// TableName sets the insert table name for this struct type
func (w *Workflow) TableName() string {
	return "workflow"
}

type ModuleListDefine struct {
	ID    int64  `json:"id"`
	Name  string `json:"name"`
	Value string `json:"value"`
}

// 配置JSON结构
type ModuleConfig struct {
	ID          int64   `json:"id"`           // 模块ID
	ModuleName  string  `json:"module_name"`  // 模块名称（课件审核/资源发布）
	ModuleKey   string  `json:"module_key"`   // 模块键值
	Enable      bool    `json:"enable"`       // 是否开启模块
	AutoReview  bool    `json:"auto_review"`  // 是否开启自动提交审核
	WorkflowIDS []int64 `json:"workflow_ids"` // 关联的工作流ID
}

// 模块列表
var ModuleList = []ModuleListDefine{
	{ID: 1, Name: "大纲", Value: ModuleSyllabus},
	{ID: 2, Name: "计划", Value: ModuleTeachingPlan},
	{ID: 3, Name: "课程", Value: ModuleCourses},
	{ID: 4, Name: "课件", Value: ModuleCourseware},
	{ID: 5, Name: "资料", Value: ModuleResources},
	{ID: 6, Name: "题库", Value: ModuleQuestions},
	{ID: 7, Name: "组卷", Value: ModulePapers},
	{ID: 8, Name: "考试", Value: ModuleExams},
	{ID: 9, Name: "成绩", Value: ModuleScore},
	{ID: 10, Name: "证书", Value: ModuleCertificates},
	{ID: 11, Name: "课时", Value: ModuleSchedule},
	{ID: 12, Name: "教案", Value: ModuleDocuments},
	{ID: 13, Name: "知识点", Value: ModuleKnowledgePoints},
	{ID: 14, Name: "作业", Value: ModuleAssignment},
}

func GetModelByModuleKey(moduleKey string) (interface{}, error) {
	if moduleKey == "" {
		return nil, errors.New("模块键不能为空")
	}
	switch moduleKey {
	case ModuleSyllabus:
		return &Syllabus{}, nil
	case ModuleTeachingPlan:
		return &TeachingPlan{}, nil
	case ModuleCourses:
		return &Courses{}, nil
	case ModuleCourseware:
		return &Courseware{}, nil
	case ModuleQuestions:
		return &Questions{}, nil
	case ModulePapers:
		return &Papers{}, nil
	case ModuleExams:
		return &Exams{}, nil
	case ModuleScore:
		return &ExamScore{}, nil
	case ModuleCertificates:
		return &Certificates{}, nil
	case ModuleSchedule:
		return &ClassSchedule{}, nil
	case ModuleDocuments:
		return &TeachingDocument{}, nil
	case ModuleKnowledgePoints:
		return &KnowledgePoints{}, nil
	case ModuleAssignment:
		return &Assignment{}, nil
	case ModuleResources:
		return &Resources{}, nil
	default:
		return nil, errors.New("不支持的模块类型" + moduleKey)
	}
}

// IsValidModuleKey 验证模块键是否有效
func IsValidModuleKey(moduleKey string) bool {
	if moduleKey == "" {
		return false
	}
	for _, m := range ModuleList {
		if m.Value == moduleKey {
			return true
		}
	}
	return false
}
func GetModuleName(moduleKey string) string {
	for _, v := range ModuleList {
		if v.Value == moduleKey {
			return v.Name
		}
	}
	return ""
}
func GetModuleList() []ModuleListDefine {
	sort.Slice(ModuleList, func(i, j int) bool {
		return ModuleList[i].ID < ModuleList[j].ID
	})
	return ModuleList
}
func InitWorkflowData(db *gorm.DB) error {
	now := time.Now().Unix()
	workflowItems := []Workflow{
		{Name: "大纲审核", UserID: 2, Description: "", NodeLen: 1, ModuleKey: "syllabus", Status: "disable", CreatedAt: now, UpdatedAt: now},
		{Name: "计划审核", UserID: 2, Description: "", NodeLen: 1, ModuleKey: "teaching_plan", Status: "disable", CreatedAt: now, UpdatedAt: now},
		{Name: "课程审核", UserID: 2, Description: "", NodeLen: 1, ModuleKey: "courses", Status: "disable", CreatedAt: now, UpdatedAt: now},
		{Name: "课件流程", UserID: 2, Description: "", NodeLen: 1, ModuleKey: "courseware", Status: "disable", CreatedAt: now, UpdatedAt: now},
		{Name: "资料审核", UserID: 2, Description: "", NodeLen: 1, ModuleKey: "resources", Status: "disable", CreatedAt: now, UpdatedAt: now},
		{Name: "题库审核", UserID: 2, Description: "", NodeLen: 1, ModuleKey: "questions", Status: "disable", CreatedAt: now, UpdatedAt: now},
		{Name: "组卷审核", UserID: 2, Description: "", NodeLen: 1, ModuleKey: "papers", Status: "disable", CreatedAt: now, UpdatedAt: now},
		{Name: "考试审核", UserID: 2, Description: "", NodeLen: 1, ModuleKey: "exams", Status: "disable", CreatedAt: now, UpdatedAt: now},
		{Name: "成绩审核", UserID: 2, Description: "", NodeLen: 1, ModuleKey: "exam_score", Status: "disable", CreatedAt: now, UpdatedAt: now},
		{Name: "证书审核", UserID: 2, Description: "", NodeLen: 1, ModuleKey: "certificates", Status: "disable", CreatedAt: now, UpdatedAt: now},
		{Name: "课时审核", UserID: 2, Description: "", NodeLen: 1, ModuleKey: "class_schedule", Status: "disable", CreatedAt: now, UpdatedAt: now},
		{Name: "教案审核", UserID: 2, Description: "", NodeLen: 1, ModuleKey: "teaching_document", Status: "disable", CreatedAt: now, UpdatedAt: now},
		{Name: "知识点审核", UserID: 2, Description: "", NodeLen: 1, ModuleKey: "knowledge_points", Status: "disable", CreatedAt: now, UpdatedAt: now},
		{Name: "作业审核", UserID: 2, Description: "", NodeLen: 1, ModuleKey: "assignment", Status: "disable", CreatedAt: now, UpdatedAt: now},
	}

	if err := db.Create(&workflowItems).Error; err != nil {
		return err
	}
	return nil
}
