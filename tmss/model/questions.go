package model

// QuestionType 定义试题类型常量
const (
	QuestionTypeSingle        = "single"        // 单选题
	QuestionTypeMultiple      = "multiple"      // 多选题
	QuestionTypeJudge         = "judge"         // 判断题
	QuestionTypeFill          = "fill"          // 填空题
	QuestionTypeShort         = "short"         // 简答题
	QuestionTypeDiscuss       = "discuss"       // 论述题
	QuestionTypeAnalyze       = "analyze"       // 分析题
	QuestionTypeComprehensive = "comprehensive" // 综合题
	QuestionTypeSelf          = "self"          // 自拟题
)

// 需要手工阅卷的类型
var QuestionTypeManual = []string{QuestionTypeFill, QuestionTypeShort, QuestionTypeDiscuss, QuestionTypeAnalyze, QuestionTypeComprehensive, QuestionTypeSelf}

// QuestionStatus 定义试题状态常量
const (
	QuestionStatusDraft     = "draft"     // 草稿状态
	QuestionStatusReviewing = "reviewing" // 审核中状态
	QuestionStatusPublished = "published" // 已发布状态
	QuestionStatusRejected  = "rejected"  // 审核拒绝
)

// Question struct is a row record of the questions table in the tms database
type Questions struct {
	ID     int64 `gorm:"primary_key;AUTO_INCREMENT;column:id;type:bigint;" json:"id"` // 主键
	UserID int64 `gorm:"column:user_id;type:bigint;" json:"user_id"`                  // 创建人ID
	// CourseID     int64  `gorm:"column:course_id;type:bigint;" json:"course_id"`                   // 课程ID
	// CoursewareID int64  `gorm:"column:courseware_id;type:bigint;" json:"courseware_id"`           // 课件ID
	Title        string `gorm:"column:title;type:varchar(255);not null;" json:"title"`                // 试题名称
	QuestionType string `gorm:"column:question_type;type:varchar(20);not null;" json:"question_type"` // 试题类型（single, multiple, judge, fill, short）
	Content      string `gorm:"column:content;type:text;not null;" json:"content"`                    // 试题内容
	Answer       string `gorm:"column:answer;type:text;not null;" json:"answer"`                      // 试题答案
	ScorePoints  string `gorm:"column:score_points;type:text;" json:"score_points"`                   // 评分标准（仅适用于手工阅卷的类型）
	Score        int    `gorm:"column:score;type:int;" json:"score"`                                  // 参考分数
	Minutes      int    `gorm:"column:minutes;type:int;" json:"minutes"`                              // 答题时间（分钟）
	Options      string `gorm:"column:options;type:text;" json:"options"`                             // 选项（JSON数组存储，仅适用于单选和多选题）
	Status       string `gorm:"column:status;type:varchar;size:50;" json:"status"`                    // 课程状态（draft, reviewing, published）
	CreatedAt    int64  `gorm:"column:created_at;autoCreateTime" json:"created_at"`                   // 创建时间（Unix 时间戳）
	UpdatedAt    int64  `gorm:"column:updated_at;autoUpdateTime" json:"updated_at"`                   // 更新时间（Unix 时间戳）
	SubmitAt     int64  `gorm:"column:submit_at;" json:"submit_at"`                                   // 提交时间（审核提交时间）
	PublishedAt  int64  `gorm:"column:published_at;" json:"published_at"`                             // 发布时间
	Version      string `gorm:"column:version;type:varchar;size:20;default:v1.0;" json:"version"`     // 版本号
}

// TableName sets the insert table name for this struct type
func (q *Questions) TableName() string {
	return "questions"
}

type ReqQuestionSearch struct {
	Page         int    `form:"page,default=1" json:"page"`
	PageSize     int    `form:"page_size,default=10" json:"page_size"`
	Name         string `form:"name" json:"name"`                   // 按名称模糊搜索
	Status       string `form:"status" json:"status"`               // 状态筛选 draft, reviewing, published
	UserID       int64  `form:"user_id" json:"user_id"`             // 用户ID
	CourseID     int64  `form:"course_id" json:"course_id"`         // 课程ID
	CoursewareID int64  `form:"courseware_id" json:"courseware_id"` // 课件ID
	ChapterID    int64  `form:"chapter_id" json:"chapter_id"`       // 章节ID
}
type ReqQuestionUpdate struct {
	Questions     Questions `json:"questions"`
	CoursewareIDs []int64   `json:"courseware_ids"` // 关联课件ID列表
	ChapterIDs    []int64   `json:"chapter_ids"`    // 关联章节ID列表
}
type RespQuestionList struct {
	List  []RespQuestionDetailWithRev `json:"list"`
	Total int64                       `json:"total"`
}
type RespQuestionDetailWithRev struct {
	Questions Questions       `json:"question"`
	Rev       *RevisionRecord `json:"rev,omitempty"`
}
type RespQuestionDetail struct {
	Questions   Questions    `json:"question"`
	Chapters    []Chapter    `json:"chapters,omitempty"`
	Coursewares []Courseware `json:"coursewares,omitempty"` // 关联的课件详情
}
