package model

// Classroom struct is a row record of the textbook table in the tms database
type Classroom struct {
	ID          int64  `gorm:"primary_key;AUTO_INCREMENT;column:id;type:bigint;" json:"id"`
	Title       string `gorm:"column:title;type:varchar;size:255;" json:"title"`             // 教室名称
	UserID      int64  `gorm:"column:user_id;type:bigint;" json:"user_id"`                   // 用户ID
	Description string `gorm:"column:description;type:varchar;size:255;" json:"description"` // 教室描述
	CreatedAt   int64  `gorm:"column:created_at;autoCreateTime" json:"created_at"`           // 创建时间（秒级时间戳）
	UpdatedAt   int64  `gorm:"column:updated_at;autoUpdateTime" json:"updated_at"`           // 更新时间（秒级时间戳）
}

// TableName sets the insert table name for this struct type
func (t *Classroom) TableName() string {
	return "classroom"
}

type ReqClassroomSearch struct {
	Page     int    `form:"page,default=1" json:"page"`
	PageSize int    `form:"page_size,default=10" json:"page_size"`
	Title    string `form:"title" json:"title"`     // 按标题模糊搜索
	UserID   int64  `form:"user_id" json:"user_id"` // 用户ID
}

// RespClassroomList 分页返回结果
type RespClassroomList struct {
	List  []Classroom `json:"list"`
	Total int64       `json:"total"`
}
