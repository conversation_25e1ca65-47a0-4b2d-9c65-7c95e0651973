package model

import (
	"gorm.io/gorm"
)

// Users struct is a row record of the users table in the tms database
type Users struct {
	ID int64 `gorm:"primary_key;AUTO_INCREMENT;type:bigint;column:id;" json:"id"` // 用户唯一标识符，主键，自增
	//[0] account                                        varchar(50)          null: false  primary: false  isArray: false  auto: false  col: varchar         len: 50      default: []
	Account string `gorm:"column:account;type:varchar;size:50;uniqueIndex;" json:"account"` // 用户登录账号，需唯一，可考虑添加索引以优化查询
	//[1] username                                       varchar(50)          null: false  primary: false  isArray: false  auto: false  col: varchar         len: 50      default: []
	Username string `gorm:"column:username;type:varchar;size:50;" json:"username"` // 用户昵称或显示名称
	//[2] password                                       varchar(100)         null: false  primary: false  isArray: false  auto: false  col: varchar         len: 100     default: []
	Password string `gorm:"column:password;type:varchar;size:100;" json:"password"` // BCrypt加密后的密码哈希值，确保密码安全存储
	//[3] email                                          varchar(100)         null: false  primary: false  isArray: false  auto: false  col: varchar         len: 100     default: []
	Email string `gorm:"column:email;type:varchar;size:100;" json:"email"` // 用户邮箱，可用于找回密码或通知
	//[4] phone                                          varchar(20)          null: true   primary: false  isArray: false  auto: false  col: varchar         len: 20      default: []
	Phone string `gorm:"column:phone;type:varchar;size:20;" json:"phone"`
	//[5] status                                         char(6)              null: false  primary: false  isArray: false  auto: false  col: char            len: 6       default: [1]
	Status string `gorm:"column:status;type:char;size:6;default:1;" json:"status"` // 用户状态，例如：1-正常，0-禁用
	//[6] created_at                                     int64          null: false  primary: false  isArray: false  auto: false  col: timestamp       len: -1      default: [CURRENT_TIMESTAMP]
	CreatedAt int64 `gorm:"column:created_at" json:"created_at"` // 记录创建时间，默认当前时间戳
	//[7] updated_at                                     int64          null: false  primary: false  isArray: false  auto: false  col: timestamp       len: -1      default: [CURRENT_TIMESTAMP]
	UpdatedAt int64 `gorm:"column:updated_at" json:"updated_at"` // 记录更新时间
	//[8] salt                                           varchar(100)         null: false  primary: false  isArray: false  auto: false  col: varchar         len: 100     default: []
	Salt string `gorm:"column:salt;type:varchar;size:100;" json:"salt"` // 密码加盐，增加安全性
}

// TableName sets the insert table name for this struct type
func (u *Users) TableName() string {
	return "users"
}

func (u Users) Columns() []string {
	return []string{
		"id",
		"account",
		"username",
		"password",
		"email",
		"phone",
		"status",
		"created_at",
		"updated_at",
		"salt"}
}

func (u Users) UpdateColumns(mode string) []string {
	columns := []string{
		"account",
		"username",
		"email",
		"phone",
		"password",
		"status",
		"salt",
		"updated_at",
	}
	if mode == "exclude_primary_key" {
		return columns
	}
	return u.Columns()
}

type ReqUsersSearch struct {
	Page     int    `form:"page,default=1" json:"page"`
	PageSize int    `form:"page_size,default=10" json:"page_size"`
	SysCode  string `form:"sys_code" json:"sys_code"` // 系统标识代码，例如 "teacher" 或 "admin"
	Username string `form:"username" json:"username"`
}

type UserEntity struct {
	Users
	Roles []Roles `json:"roles"`
}

// GetUsersBySysCode 根据 sys_code 获取用户列表（支持分页和用户名搜索）
func GetUsersBySysCode(db *gorm.DB, req ReqUsersSearch) (users []Users, total int64, err error) {
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 || req.PageSize > 100 {
		req.PageSize = 10
	}

	offset := (req.Page - 1) * req.PageSize

	// 创建基础查询
	query := db.Model(&Users{}).
		Joins("JOIN user_codes ON users.id = user_codes.user_id").
		Where("user_codes.sys_code = ?", req.SysCode)

	// 添加用户名搜索条件（如果提供了用户名）
	if req.Username != "" {
		query = query.Where("users.username LIKE ?", "%"+req.Username+"%")
	}

	// 查询符合条件的用户总数
	err = query.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	// 查询分页数据
	err = query.
		Select("id, account, username, email, phone, status").
		Offset(offset).
		Limit(req.PageSize).
		Order("users.created_at DESC").
		Find(&users).Error

	return users, total, err
}
