package model

// RoleMenu struct is a row record of the menu_role table in the tms database
type RoleMenu struct {
	RoleCode string `gorm:"primary_key;column:role_code;type:varchar;size:50;" json:"role_code"` // 角色编码，用于权限控制
	MenuID   int64  `gorm:"primary_key;column:menu_id;type:bigint;" json:"menu_id"`
}

// TableName sets the insert table name for this struct type
func (m *RoleMenu) TableName() string {
	return "role_menu"
}
