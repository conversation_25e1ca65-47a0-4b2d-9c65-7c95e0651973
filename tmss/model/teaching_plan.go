package model

const (
	TeachingPlanStatusDraft     = "draft"     // 草稿
	TeachingPlanStatusReviewing = "reviewing" // 审核中
	TeachingPlanStatusPublished = "published" // 已发布
	TeachingPlanStatusRejected  = "rejected"  // 审核拒绝
)

// 教学计划要关联已发布的大纲，班级（多个）
// TeachingPlan struct is a row record of the teaching_plan table in the tms database
type TeachingPlan struct {
	ID                    int64  `gorm:"primary_key;AUTO_INCREMENT;column:id;type:bigint;" json:"id"`
	SyllabusID            int64  `gorm:"column:syllabus_id;type:bigint;" json:"syllabus_id"` // 大纲ID
	UserID                int64  `gorm:"column:user_id;type:bigint;" json:"user_id"`         // 用户ID
	Name                  string `gorm:"column:name;type:varchar;size:200;" json:"name"`
	Term                  string `gorm:"column:term;type:varchar;size:100;" json:"term"`                         // 学期（YYYY-YYYY-N格式）
	ClassroomRequirements string `gorm:"column:classroom_requirements;type:text;" json:"classroom_requirements"` // 教室需求描述
	Status                string `gorm:"column:status;type:varchar;size:20;" json:"status"`
	CreatedAt             int64  `gorm:"column:created_at;autoCreateTime" json:"created_at"`
	UpdatedAt             int64  `gorm:"column:updated_at;autoUpdateTime" json:"updated_at"`
	StartAt               int64  `gorm:"column:start_at;" json:"start_at"`                                 // 开始时间（计划开始时间）
	EndAt                 int64  `gorm:"column:end_at;" json:"end_at"`                                     // 结束时间（计划截止时间）
	SubmitAt              int64  `gorm:"column:submit_at;" json:"submit_at"`                               // 提交时间（审核提交时间）
	PublishedAt           int64  `gorm:"column:published_at;" json:"published_at"`                         // 发布时间
	Version               string `gorm:"column:version;type:varchar;size:20;default:v1.0;" json:"version"` // 版本号
	PreviousID            int64  `gorm:"column:previous_id;type:bigint;" json:"previous_id"`               // 前序计划ID（用于调整关联）

}

// TableName sets the insert table name for this struct type
func (t *TeachingPlan) TableName() string {
	return "teaching_plan"
}

type ReqTeachingPlanUpdate struct {
	TeachingPlan TeachingPlan `json:"teaching_plan"`
	ClassIDs     []int64      `json:"class_ids"` // 班级列表
	StartAtStr   string       `json:"start_at"`  // 新增字段
	EndAtStr     string       `json:"end_at"`    // 新增字段
}

type ReqTeachingPlanSearch struct {
	Page         int    `form:"page,default=1" json:"page"`
	PageSize     int    `form:"page_size,default=10" json:"page_size"`
	Name         string `form:"name" json:"name"`                   // 按名称模糊搜索
	Status       string `form:"status" json:"status"`               // 状态筛选 draft, published, reviewing
	UserID       int64  `form:"user_id" json:"user_id"`             // 创建人ID
	SyllabusID   int64  `form:"syllabus_id" json:"syllabus_id"`     // 关联大纲ID
	SyllabusName string `form:"syllabus_name" json:"syllabus_name"` // 按大纲名称模糊搜索
	ClassName    string `form:"class_name" json:"class_name"`       // 按班级名称模糊搜索
}
type RespTeachingPlanList struct {
	List  []PlanWithFormattedTime `json:"list"`
	Total int64                   `json:"total"`
}
type PlanWithFormattedTime struct {
	TeachingPlan
	SyllabusName string  `json:"syllabus_name"`
	Classes      []Class `json:"classes"`
	StartAtStr   string  `json:"start_at"`
	EndAtStr     string  `json:"end_at"`
}
type RespPlanWithLatestRevision struct {
	Plan PlanWithFormattedTime `json:"plan"`
	Rev  *RevisionRecord       `json:"rev,omitempty"` // 可能为空
}
type RespTeachPlanList struct {
	List  []RespPlanWithLatestRevision `json:"list"`
	Total int64                        `json:"total"`
}
type RespTeachingPlanDetail struct {
	TeachingPlan TeachingPlan  `json:"teaching_plan"`
	Classes      []Class       `json:"classes"`
	Syllabus     Syllabus      `json:"syllabus"`
	PreviousPlan *TeachingPlan `json:"previous_plan,omitempty"`
	StartAtStr   string        `json:"start_at_str"`
	EndAtStr     string        `json:"end_at_str"`
}
type TeachPlanWithExts struct {
	Plan TeachingPlan      `json:"plan"`
	Exts []TeachingPlanExt `json:"exts"`
}
