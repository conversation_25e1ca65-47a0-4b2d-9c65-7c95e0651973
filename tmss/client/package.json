{"name": "<PERSON><PERSON><PERSON>", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --host --open", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@tinymce/tinymce-vue": "^6.2.0", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "axios": "^1.10.0", "dayjs": "^1.11.13", "echarts": "^5.6.0", "element-plus": "^2.10.2", "go-captcha-vue": "^2.0.6", "pinia": "^3.0.3", "pinia-plugin-persistedstate": "^4.4.1", "sortablejs": "^1.15.6", "tinymce": "^7.9.1", "uuid": "^11.1.0", "vite-plugin-vue-devtools": "^7.7.7", "vue": "^3.5.13", "vue-router": "^4.5.1", "vuedraggable": "^4.1.0"}, "devDependencies": {"@tailwindcss/vite": "^4.1.10", "@types/node": "^24.0.3", "@types/sortablejs": "^1.15.8", "@vitejs/plugin-vue": "^5.2.3", "@vue/tsconfig": "^0.7.0", "tailwindcss": "^4.1.10", "typescript": "~5.8.3", "unplugin-auto-import": "^19.3.0", "unplugin-vue-components": "^28.7.0", "vite": "^6.3.5", "vue-tsc": "^2.2.8"}}