/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    Admin: typeof import('./src/components/home/<USER>')['default']
    ApproveButton: typeof import('./src/components/button/ApproveButton.vue')['default']
    AssignmentActionSelector: typeof import('./src/components/assignment/AssignmentActionSelector.vue')['default']
    AssignmentForm: typeof import('./src/components/form/AssignmentForm.vue')['default']
    AssignmentItemList: typeof import('./src/components/assignment/AssignmentItemList.vue')['default']
    BatchApprove: typeof import('./src/components/button/BatchApprove.vue')['default']
    Breadcrumb: typeof import('./src/components/layout/breadcrumb.vue')['default']
    ChapterSelect: typeof import('./src/components/select/ChapterSelect.vue')['default']
    ChapterSelectByCourse: typeof import('./src/components/select/ChapterSelectByCourse.vue')['default']
    Classroom: typeof import('./src/components/select/classroom.vue')['default']
    ClassroomSelect: typeof import('./src/components/select/ClassroomSelect.vue')['default']
    ClassScheduleSelect: typeof import('./src/components/select/ClassScheduleSelect.vue')['default']
    ClassSelect: typeof import('./src/components/select/ClassSelect.vue')['default']
    CourseSelect: typeof import('./src/components/select/CourseSelect.vue')['default']
    CoursewareSelector: typeof import('./src/components/assignment/CoursewareSelector.vue')['default']
    CreatePlan: typeof import('./src/components/exam/plan/createPlan.vue')['default']
    Editor: typeof import('./src/components/editor/Editor.vue')['default']
    ElAvatar: typeof import('element-plus/es')['ElAvatar']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElButtonGroup: typeof import('element-plus/es')['ElButtonGroup']
    ElCard: typeof import('element-plus/es')['ElCard']
    ElCascader: typeof import('element-plus/es')['ElCascader']
    ElCheckbox: typeof import('element-plus/es')['ElCheckbox']
    ElCheckboxGroup: typeof import('element-plus/es')['ElCheckboxGroup']
    ElCol: typeof import('element-plus/es')['ElCol']
    ElCollapse: typeof import('element-plus/es')['ElCollapse']
    ElCollapseItem: typeof import('element-plus/es')['ElCollapseItem']
    ElCollapseTransition: typeof import('element-plus/es')['ElCollapseTransition']
    ElCountdown: typeof import('element-plus/es')['ElCountdown']
    ElDatePicker: typeof import('element-plus/es')['ElDatePicker']
    ElDescriptions: typeof import('element-plus/es')['ElDescriptions']
    ElDescriptionsItem: typeof import('element-plus/es')['ElDescriptionsItem']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElDrawer: typeof import('element-plus/es')['ElDrawer']
    ElDropdown: typeof import('element-plus/es')['ElDropdown']
    ElDropdownItem: typeof import('element-plus/es')['ElDropdownItem']
    ElDropdownMenu: typeof import('element-plus/es')['ElDropdownMenu']
    ElEmpty: typeof import('element-plus/es')['ElEmpty']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElInputNumber: typeof import('element-plus/es')['ElInputNumber']
    ElMenu: typeof import('element-plus/es')['ElMenu']
    ElMenuItem: typeof import('element-plus/es')['ElMenuItem']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElPagination: typeof import('element-plus/es')['ElPagination']
    ElPopover: typeof import('element-plus/es')['ElPopover']
    ElProgress: typeof import('element-plus/es')['ElProgress']
    ElRadio: typeof import('element-plus/es')['ElRadio']
    ElRadioButton: typeof import('element-plus/es')['ElRadioButton']
    ElRadioGroup: typeof import('element-plus/es')['ElRadioGroup']
    ElRow: typeof import('element-plus/es')['ElRow']
    ElScrollbar: typeof import('element-plus/es')['ElScrollbar']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElSkeleton: typeof import('element-plus/es')['ElSkeleton']
    ElSlider: typeof import('element-plus/es')['ElSlider']
    ElStep: typeof import('element-plus/es')['ElStep']
    ElSteps: typeof import('element-plus/es')['ElSteps']
    ElSubMenu: typeof import('element-plus/es')['ElSubMenu']
    ElSwitch: typeof import('element-plus/es')['ElSwitch']
    ElTable: typeof import('element-plus/es')['ElTable']
    ElTableColumn: typeof import('element-plus/es')['ElTableColumn']
    ElTableRow: typeof import('element-plus/es')['ElTableRow']
    ElTabPane: typeof import('element-plus/es')['ElTabPane']
    ElTabs: typeof import('element-plus/es')['ElTabs']
    ElTag: typeof import('element-plus/es')['ElTag']
    ElText: typeof import('element-plus/es')['ElText']
    ElTimeline: typeof import('element-plus/es')['ElTimeline']
    ElTimelineItem: typeof import('element-plus/es')['ElTimelineItem']
    ElTimePicker: typeof import('element-plus/es')['ElTimePicker']
    ElTooltip: typeof import('element-plus/es')['ElTooltip']
    ElTree: typeof import('element-plus/es')['ElTree']
    ElTreeSelect: typeof import('element-plus/es')['ElTreeSelect']
    ElUpload: typeof import('element-plus/es')['ElUpload']
    Fill: typeof import('./src/components/exam/paper/fill.vue')['default']
    FixedPaper: typeof import('./src/components/exam/paper/fixedPaper.vue')['default']
    IconSelector: typeof import('./src/components/common/IconSelector.vue')['default']
    Judge: typeof import('./src/components/exam/paper/judge.vue')['default']
    MajorSelect: typeof import('./src/components/select/MajorSelect.vue')['default']
    MaterialSelector: typeof import('./src/components/assignment/MaterialSelector.vue')['default']
    Multiple: typeof import('./src/components/exam/paper/multiple.vue')['default']
    MySelect: typeof import('./src/components/select/mySelect.vue')['default']
    PaperGrade: typeof import('./src/components/exam/paper/paperGrade.vue')['default']
    PaperManage: typeof import('./src/components/exam/paper/paperManage.vue')['default']
    PaperPublish: typeof import('./src/components/exam/paper/paperPublish.vue')['default']
    PlanList: typeof import('./src/components/exam/plan/planList.vue')['default']
    QuestionSelector: typeof import('./src/components/assignment/QuestionSelector.vue')['default']
    RandomPaper: typeof import('./src/components/exam/paper/randomPaper.vue')['default']
    ResourceCategorySelect: typeof import('./src/components/select/ResourceCategorySelect.vue')['default']
    ResourceCreateForm: typeof import('./src/components/form/ResourceCreateForm.vue')['default']
    ResourceEditForm: typeof import('./src/components/form/ResourceEditForm.vue')['default']
    ResourceSelect: typeof import('./src/components/select/ResourceSelect.vue')['default']
    RevisionActions: typeof import('./src/components/revision/RevisionActions.vue')['default']
    RevisionApproveButton: typeof import('./src/components/revision/RevisionApproveButton.vue')['default']
    RevisionButton: typeof import('./src/components/revision/RevisionButton.vue')['default']
    RevisonList: typeof import('./src/components/revision/RevisonList.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    ScheduleCreateForm: typeof import('./src/components/form/ScheduleCreateForm.vue')['default']
    ScheduleEditForm: typeof import('./src/components/form/ScheduleEditForm.vue')['default']
    ScheduleViewForm: typeof import('./src/components/form/ScheduleViewForm.vue')['default']
    ScoreEntry: typeof import('./src/components/exam/score/scoreEntry.vue')['default']
    ScoreList: typeof import('./src/components/exam/score/scoreList.vue')['default']
    ScormPlayer: typeof import('./src/components/exam/paper/ScormPlayer.vue')['default']
    Short: typeof import('./src/components/exam/paper/short.vue')['default']
    Side: typeof import('./src/components/layout/side.vue')['default']
    Single: typeof import('./src/components/exam/paper/single.vue')['default']
    SlideCaptcha: typeof import('./src/components/cahtcha/SlideCaptcha.vue')['default']
    SyllabusDialogForm: typeof import('./src/components/form/SyllabusDialogForm.vue')['default']
    SyllabusSelect: typeof import('./src/components/select/SyllabusSelect.vue')['default']
    Tabs: typeof import('./src/components/layout/tabs.vue')['default']
    Teacher: typeof import('./src/components/home/<USER>')['default']
    TeacherSelect: typeof import('./src/components/select/TeacherSelect.vue')['default']
    TeachingPlanDialogForm: typeof import('./src/components/form/TeachingPlanDialogForm.vue')['default']
    TeachingPlanSelect: typeof import('./src/components/select/TeachingPlanSelect.vue')['default']
    TEditor: typeof import('./src/components/certificate/t-editor.vue')['default']
    TextbookSelect: typeof import('./src/components/select/TextbookSelect.vue')['default']
    Tips: typeof import('./src/components/exam/paper/tips.vue')['default']
    ViewPaper: typeof import('./src/components/exam/paper/viewPaper.vue')['default']
  }
  export interface GlobalDirectives {
    vLoading: typeof import('element-plus/es')['ElLoadingDirective']
  }
}
