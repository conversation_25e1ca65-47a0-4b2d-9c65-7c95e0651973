import { defineStore } from 'pinia'
import { ref } from 'vue'
import { post, setToken, clearToken } from '@/utils/request'
import { errMsg, successMsg } from '@/utils/msg'
export const useUserStore = defineStore(
  'user',
  () => {
    const token = ref('')
    const path = ref('/home')
    const is_locked = ref(false)
    const menuData: any = ref([])
    const rolesData: any = ref([])
    const userInfo = ref<any>({})
    const userCode = ref('')
    const sysCode = ref('')
    //scorm会话id
    const scormSessionId = ref<number>(0)
    //课件终止状态
    const scormTerminated = ref<boolean>(false)
    interface LoginForm {
      account: string
      password: string
      remember: boolean
      user_code?: string
    }
    const loginForm = ref<LoginForm>({
      account: 'admin',
      password: '123456',
      remember: false,
    })
    const onLogin = async () => {
      if (userCode.value != '') {
        loginForm.value.user_code = userCode.value
      }
      const res = await post('user/auth/login', loginForm.value)

      if (!res.success) return false
      //console.log(res)
      token.value = res.data.token
      setToken(res.data.token)
      menuData.value = res.data.menus
      rolesData.value = res.data.roles
      userInfo.value = res.data.user
      userCode.value = res.data.user_code
      sysCode.value = res.data.sys_code

      if (!loginForm.value.remember) {
        loginForm.value.password = ''
      }
      return true
    }
    const changeRole = (user_code: string) => {
      post('user/auth/change-roles', { user_code }).then((res) => {
        //console.log(res)
        if (res.success) {
          menuData.value = res.data.menus
          userCode.value = user_code
          token.value = res.data.token
          sysCode.value = res.data.sys_code
          setToken(res.data.token)
        }
      })
    }
    const onLogOut = async () => {
      await post('user/auth/logout').then((res) => {
        token.value = ''
        clearToken()
        menuData.value = []
        rolesData.value = []
        userInfo.value = {}
        path.value = '/home'
        successMsg('退出成功')
      })
    }

    return {
      token,
      menuData,
      rolesData,
      loginForm,
      userInfo,
      path,
      is_locked,
      onLogin,
      changeRole,
      onLogOut,
      userCode,
      sysCode,
      scormTerminated,
      scormSessionId,
    }
  },
  {
    persist: {
      key: 'userStore',
      pick: [
        'menuData',
        'token',
        'userCode',
        'sysCode',
        'is_locked',
        'path',
        'rolesData',
        'userInfo',
        'loginForm',
      ],
    },
  }
)
