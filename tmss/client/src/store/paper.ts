import { defineStore } from 'pinia'
import { ref } from 'vue'

export const usePaperStore = defineStore(
  'paper',
  () => {
    const paper: any = ref({})
    const questions: any = ref([])
    // 课件完成状态
    const complete = ref(false)

    // 记录是否课件
    const is_courseware = ref(false)
    return {
      paper,
      questions,
      complete,
      is_courseware,
    }
  },
  {
    persist: true, // 开启持久化（默认使用 localStorage）
  }
)
