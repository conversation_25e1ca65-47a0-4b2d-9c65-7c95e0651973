import { createRouter, createWebHistory } from 'vue-router'

const routes = [
  {
    path: '/login',
    name: 'login',
    component: () => import('@/views/login.vue'),
  },
  {
    path: '/',
    name: 'index',
    component: () => import('@/views/layout.vue'),
    children: [
      {
        path: '/home',
        name: '首页',
        component: () => import('@/views/home/<USER>'),
      },
      {
        path: '/role/user',
        name: '用户管理',
        component: () => import('@/views/role/user.vue'),
      },
      {
        path: '/role/permission',
        name: '角色权限管理',
        component: () => import('@/views/role/permission.vue'),
      },
      {
        path: '/role/menu',
        name: '菜单管理',
        component: () => import('@/views/role/menu.vue'),
      },
      {
        path: '/role/data',
        name: '数据权限',
        component: () => import('@/views/role/data.vue'),
      },
      {
        path: 'teaching/major',
        name: '专业管理',
        component: () => import('@/views/role/major.vue'),
      },
      {
        path: 'teaching/class',
        name: '班级管理',
        component: () => import('@/views/role/class.vue'),
      },
      {
        path: 'teaching/classroom',
        name: '教室管理',
        component: () => import('@/views/role/classroom.vue'),
      },
      {
        path: '/system',
        name: '核心设置',
        component: () => import('@/views/system/index.vue'),
      },
      {
        path: '/logs',
        name: '系统日志',
        component: () => import('@/views/system/logs.vue'),
      },
      {
        path: '/dict',
        name: '字典管理',
        component: () => import('@/views/system/dict.vue'),
      },
      {
        path: '/approve',
        name: '流程管理',
        component: () => import('@/views/approve/index.vue'),
      },
      {
        path: '/approve-template',
        name: '流程配置',
        component: () => import('@/views/approve/config.vue'),
      },
      {
        path: '/exam/plan',
        name: '评估管理',
        component: () => import('@/views/exam/plan.vue'),
      },
      {
        path: '/exam/exams',
        name: '考试管理',
        component: () => import('@/views/exam/plan.vue'),
      },
      {
        path: '/exam/score',
        name: '成绩管理',
        component: () => import('@/views/exam/score.vue'),
      },
      {
        path: '/exam/grade',
        name: '阅卷管理',
        component: () => import('@/views/exam/grade.vue'),
      },
      {
        path: '/exam/question',
        name: '题库管理',
        component: () => import('@/views/exam/question.vue'),
      },
      {
        path: '/exam/paper',
        name: '组卷管理',
        component: () => import('@/views/exam/paper.vue'),
      },
      // {
      //   path: '/exam/class',
      //   name: '班级管理',
      //   component: () => import('@/views/exam/class.vue'),
      // },
      {
        path: '/exam/online',
        name: '线上考试',
        component: () => import('@/views/exam/online.vue'),
      },
      {
        path: '/teaching/syllabus',
        name: '教学大纲',
        component: () => import('@/views/syllabus/index.vue'),
      },
      {
        path: '/teaching/teaching-plan',
        name: '教学计划',
        component: () => import('@/views/teaching-plan/index.vue'),
      },
      {
        path: '/teaching/course',
        name: '课程管理',
        component: () => import('@/views/course/index.vue'),
      },
      {
        path: '/teaching/courseware',
        name: '课件管理',
        component: () => import('@/views/courseware/index.vue'),
      },
      {
        path: '/teaching/question',
        name: '理论试题',
        component: () => import('@/views/question/index.vue'),
      },
      {
        path: '/teaching/learning',
        name: '学习进度',
        component: () => import('@/views/learning-progress/index.vue'),
      },
      {
        path: '/teaching/arrangement',
        name: '教员教学安排',
        component: () => import('@/views/arrangement/index.vue'),
      },
      {
        path: '/teaching/schedule',
        name: '课表管理',
        component: () => import('@/views/schedule/index.vue'),
      },
      {
        path: '/teaching/document',
        name: '教案管理',
        component: () => import('@/views/document/index.vue'),
      },
      {
        path: '/teaching/points',
        name: '知识点管理',
        component: () => import('@/views/knowledgepoints/index.vue'),
      },
      {
        path: '/teaching/assignment',
        name: '作业管理',
        component: () => import('@/views/assignment/index.vue'),
      },
      {
        path: '/material/upload',
        name: '上传资料',
        component: () => import('@/views/material/upload.vue'),
      },
      {
        path: '/material/classify',
        name: '资料分类',
        component: () => import('@/views/material/classify.vue'),
      },
      {
        path: '/player/decrypt',
        name: '解密设置',
        component: () => import('@/views/player/decrypt.vue'),
      },
      {
        path: '/player/strategy',
        name: '加密策略',
        component: () => import('@/views/player/strategy.vue'),
      },
      {
        path: '/player/key',
        name: '用户密钥',
        component: () => import('@/views/player/key.vue'),
      },
      {
        path: '/rule',
        name: '评估规则',
        component: () => import('@/views/rule/index.vue'),
      },
      {
        path: '/teaching/chapter',
        name: '章节管理',
        component: () => import('@/views/chapter/index.vue'),
      },
      {
        path: '/certificate/audit',
        name: '证书审核',
        component: () => import('@/views/certificate/audit.vue'),
      },
      {
        path: '/certificate/generate',
        name: '证书生成',
        component: () => import('@/views/certificate/generate.vue'),
      },
      {
        path: '/certificate/template',
        name: '证书模板',
        component: () => import('@/views/certificate/template.vue'),
      },
      // {
      //   path: '/certificate/disagree',
      //   name: '证书异议',
      //   component: () => import('@/views/certificate/disagree.vue'),
      // },
      {
        path: '/analysis/exam',
        name: '考试评估',
        component: () => import('@/views/analysis/exam.vue'),
      },
      {
        path: '/analysis/learning',
        name: '学习进度评估',
        component: () => import('@/views/analysis/learning.vue'),
      },
      {
        path: '/teaching/textbook',
        name: '教材管理',
        component: () => import('@/views/textbook/index.vue'),
      },
      {
        path: '/approve/syllabus',
        name: '大纲审核',
        component: () => import('@/views/approve/list.vue'),
        props: { moduleKey: 'syllabus' } // 显式传递模块键值
      },
      {
        path: '/approve/teaching_plan',
        name: '计划审核',
        component: () => import('@/views/approve/list.vue'),
        props: { moduleKey: 'teaching_plan' }
      },
      {
        path: '/approve/courses',
        name: '课程审核',
        component: () => import('@/views/approve/list.vue'),
        props: { moduleKey: 'courses' }
      },
      {
        path: '/approve/courseware',
        name: '课件审核',
        component: () => import('@/views/approve/list.vue'),
        props: { moduleKey: 'courseware' }
      },
      {
        path: '/approve/questions',
        name: '试题审核',
        component: () => import('@/views/approve/list.vue'),
        props: { moduleKey: 'questions' }
      },
      {
        path: '/approve/papers',
        name: '组卷审核',
        component: () => import('@/views/approve/list.vue'),
        props: { moduleKey: 'papers' }
      },
      {
        path: '/approve/exams',
        name: '考试审核',
        component: () => import('@/views/approve/list.vue'),
        props: { moduleKey: 'exams' }
      },
      // 保留通用路由作为备用
      {
        path: '/approve/:moduleKey',
        name: '通用审核',
        component: () => import('@/views/approve/list.vue'),
        props: true
      }
      // {
      //   path: '/approve/syllabus',
      //   name: '大纲审核',
      //   component: () => import('@/views/syllabus/approve.vue'),
      // },
      // {
      //   path: '/approve/teachplan',
      //   name: '计划审核',
      //   component: () => import('@/views/teaching-plan/approve.vue'),
      // },
      // {
      //   path: '/approve/course',
      //   name: '课程审核',
      //   component: () => import('@/views/course/approve.vue'),
      // },
      // {
      //   path: '/approve/courseware',
      //   name: '课件审核',
      //   component: () => import('@/views/courseware/approve.vue'),
      // }
    ],
  },
]

const router = createRouter({
  history: createWebHistory(),
  routes,
})

export default router
