import { get, post } from '@/utils/request'
// 获取试题列表
export const getQuestionList = (params?: any) =>
  get('teacher/question/list', params).then((res) => res.data)

// 获取指定课程试题列表
export const getQuestionListByCourse = (params?: any) =>
  get(`teacher/question/course/${params.course_id}`, params).then(
    (res) => {
      if (res.success){
        const list = res.data.list
        if (list.length > 0) {
          list.forEach((item: any) => {
            Object.assign(item, item.question); 
            item.question_category = "theory_question"
            item.question_key = item.id + "_" + item.question_category
          })  
        }
        return {
          list,
          total: res.data.total
        }

      }
      return {
        list : [],
        total : 0
      }
    }
  )
  export const getQuestionListByCourseAndChapter = (params?: any) =>
    get(`teacher/question/chapters`, params).then(
      (res) => {
        if (res.success){
          const list = res.data.list || []
          if (list.length > 0) {
            list.forEach((item: any) => {
              Object.assign(item, item.question); 
              item.question_category = "theory_question"
              item.question_key = item.id + "_" + item.question_category
              item.action_type = "questions"
            })  
          }
          return {
            list,
            total: res.data.total
          }
  
        }
        return {
          list : [],
          total : 0
        }
      }
    )
// 创建试题草稿
export const createQuestionDraft = (data: any) =>
  post('teacher/question/create-draft', data)

// 获取试题关联课件列表
export const getQuestionCoursewareList = (id: number) =>
  get(`teacher/question/coursewares/${id}`).then((res) => res.data)

// 编辑试题草稿
export const editQuestionDraft = (data: any) =>
  post('teacher/question/update-draft', data)

// 删除试题草稿
export const deleteQuestionDraft = (id: number) =>
  post(`teacher/question/delete/${id}`)

// 提交审核
export const submitQuestionReview = (id: number) =>
  post(`teacher/question/submit-review/${id}`)

// 审核试题
export const reviewQuestion = (id: number) =>
  post(`teacher/question/approve/${id}`)

// 待审核试题列表
export const getQuestionReviewingList = (params?: any) =>
  get('teacher/question/reviewing-list', params).then((res) => res.data)

// 获取试题详情
export const getQuestionDetail = (id: number) =>
  get(`teacher/question/detail/${id}`).then((res) => res.data)

// 驳回试题
export const rejectQuestion = (id: number) =>
  post(`teacher/question/reject/${id}`)
