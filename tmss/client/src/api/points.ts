import { post, get } from '@/utils/request';

// 创建知识点草稿
export const createKnowledgePoints = (data: any) => {
  return post('teacher/knowledge-points/create-draft', data);
};

// 更新知识点草稿
export const updateKnowledgePoints = (data: any) => {
  return post('teacher/knowledge-points/update-draft', data);
};

// 删除知识点
export const deleteKnowledgePoints = (id: number) => {
  return post(`teacher/knowledge-points/delete/${id}`);
};

// 获取知识点列表
export const getKnowledgePointssList = (params?: any) => {
  return get('teacher/knowledge-points/list', params).then((res) => res.data);
};

// 获取知识点详情
export const getKnowledgePointsDetail = (id: number) => {
  return get(`teacher/knowledge-points/detail/${id}`).then((res) => res.data);
};