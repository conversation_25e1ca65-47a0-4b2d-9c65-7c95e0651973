import { get, post } from '@/utils/request'

// 获取计划列表
export const getTeachingPlanList = (params?: any) =>
  get('teacher/teaching-plan/list', params).then((res) => res.data)

// 创建教学计划
export const createTeachingPlan = (data: any) =>
  post('teacher/teaching-plan/create-draft', data)

// 编辑教学计划
export const editTeachingPlan = (data: any) =>
  post('teacher/teaching-plan/update-draft', data)

// 删除教学计划
export const deleteTeachingPlan = (id: any) =>
  post(`teacher/teaching-plan/delete/${id}`)

// 获取教学计划详情
export const getTeachingPlanDetail = (id: any) =>
  get(`teacher/teaching-plan/detail/${id}`).then((res) => res.data)


// // 提交审核
// export const submitTeachingPlan = (id: any) =>
//   post(`teacher/teaching-plan/submit-review/${id}`)

// // 审核列表
// export const getTeachingPlanReviewingList = (params?: any) =>
//   get('teacher/teaching-plan/reviewing-list', params).then((res) => res.data)

// // 审核教学计划
// export const reviewTeachingPlan = (id: any) =>
//   post(`teacher/teaching-plan/approve/${id}`)

// // 驳回教学计划
// export const rejectTeachingPlan = (id: any) =>
//   post(`teacher/teaching-plan/reject/${id}`)
