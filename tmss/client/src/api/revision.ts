import { get, post } from '../utils/request'


// 创建修订
export const createRevision = (params: any) =>
  post('teacher/revision/create', params)

// 编辑修订
export const editRevision = (params: any) =>
  post('teacher/revision/update', params)

// 发布修订
export const publishRevision = (id: number | string) =>
  post(`teacher/revision/publish/${id}`).then((res) => res.data)

// 删除修订
export const deleteRevision = (id: number) =>
  post(`teacher/revision/delete/${id}`)

// 获取修订详情
export const getRevisionDetail = (id: number) =>
  get(`teacher/revision/detail/${id}`).then((res) => res.data)

// 获取修订列表
export const getRevisionList = (params?: {
  page?: number
  page_size?: number
  module_key?: string
}) => get('teacher/revision/list', params).then((res) => res.data)
