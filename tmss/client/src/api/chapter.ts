import { get, post } from "@/utils/request";

export interface CreateChapter {
    name: string;
    description: string;
    parent_id: number;
}

export type UpdateChapter = CreateChapter & {
    id: number; // 章节ID
}

/** 章节列表 */
export const chapterList = (params?: { parent_id?: number }) => get("teacher/chapters/list", params);

/** 根据课程id获取章节列表 */
export const getChapterByCourseID = (id:number) => get("teacher/chapters/courses/" + id).then((res) => res.data);

/** 创建章节 */
export const createChapter = (data: CreateChapter) => post("teacher/chapters/create", data);

/** 更新章节 */
export const updateChapter = (data: UpdateChapter) => post("teacher/chapters/update", data);

/** 删除章节 */
export const deleteChapter = (id: number) => post(`teacher/chapters/delete/${id}`);