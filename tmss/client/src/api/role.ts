import { get, post } from '@/utils/request'

// 获取数据权限配置列表
export const getDataConfigList = (params?: {
  page?: number
  page_size?: number
  search?: string
}) => get('admin/roles/data-configs/list', params).then(res => res.data)

// 创建数据权限配置
export const createDataConfig = (data: any) =>
  post('admin/roles/data-configs/create', data).then(res => res.data)

// 更新数据权限配置
export const updateDataConfig = (data: any) =>
  post(`/admin/roles/data-configs/update`, data).then(res => res.data)

// 删除数据权限配置
export const deleteDataConfig = (id: number) =>
  post(`admin/roles/data-configs/delete/${id}`).then(res => res.data)


// 获取角色数据权限配置
export const getRoleDataConfig = (roleCode: string, sysCode: string, params?: {
  page?: number
  page_size?: number
  search?: string
}) => get('/admin/roles/data-configs/role', { role_code: roleCode, sys_code: sysCode, ...params }).then(res => res.data)

// 设置角色数据权限配置
export const setRoleDataConfig = (roleCode: string, ids: number[]) =>
  post('/admin/roles/data-configs/set', { role_code: roleCode, ids }).then(res => res.data)