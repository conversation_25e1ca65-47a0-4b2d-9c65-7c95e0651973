import { get, post } from "@/utils/request";

/** 课件列表参数 */
export interface CoursewareListParams {
    page?: number;
    page_size?: number;
    name?: string;  //文件名
    course_id?: number; //课程ID
    chapter_id?: number;    //章节ID
    user_id?: number;   //创建人ID
    status?: string;    //课件流程状态  草稿 draft | 审核中 reviewing | 已发布 published | 已驳回 rejected
    courseware_type?: string;  //课件类型  理论课件 theory_courseware | 虚拟课件 virtual_courseware
}

/** 上传课件 */
export const upload = (data: FormData) => post("teacher/courseware/upload", data);

/** 课件列表 */
export const coursewareList = (params: CoursewareListParams) => {
    params = { page: 1, page_size: 10, ...params }
    return get("teacher/courseware/list", params);
}

export const getCoursewareList = (params: any) =>
    get('teacher/courseware/list', params).then((res) => {
        if (res.success && res.data){
            const list = res.data.list || []
            if (list && list.length > 0) {
              list.forEach((item: any) => {
                Object.assign(item, item.courseware); // 将 courseware 展开到 item 上
                item.question_category = item.courseware_type;
                item.question_key = item.id + "_" + item.courseware_type;
                item.action_type = "courseware";

              })  
            }
            return {
              list,
              total: res.data.total
            }
    
          }
          return {
            list : [],
            total : 0
          }
    })

/** 创建课件草稿 */
export const createCourseware = (data: any) => post("teacher/courseware/create-draft", data);

/** 编辑课件草稿 */
export const updateCourseware = (data: any) => post("teacher/courseware/update-draft", data);

/** 删除课件草稿 */
export const deleteCourseware = (id: number) => post(`teacher/courseware/delete/${id}`);

/** 提交审核 */
export const submitAudit = (id: number) => post(`teacher/courseware/submit-review/${id}`);

/** 通过课件 */
export const auditCourseware = (id: number) => post(`teacher/courseware/approve/${id}`);

/** 驳回课件 */
export const rejectCourseware = (id: number) => post(`teacher/courseware/reject/${id}`);

/** 待审核课件列表 */
export const auditPendingList = (params: { page?: number, page_size?: number }) => {
    params = { page: 1, page_size: 10, ...params }
    return get("teacher/courseware/reviewing-list", params);
}