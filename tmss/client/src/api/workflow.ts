import { get, post } from '@/utils/request'
// 模块键值到名称的映射
const MODULE_NAME_MAP: Record<string, string> = {
    teaching_plan: '教学计划',
    syllabus: '教学大纲',
    courses: '课程',
    papers: '组卷',
    exams: '考试',
    courseware: '课件',
    resources: '资料',
    questions: '试题',
    exam_score: '成绩',
    workflow_approve: '审核',
    certificates: '证书',
    class_schedule: '课表',
    teaching_document: '教案',
    assignment: '作业',
    knowledge_points: '知识点'
  };
  
  /**
   * 根据模块键值获取模块名称
   * @param key 模块键值
   * @returns 模块名称，如果未找到则返回原始键值
   */
  export const getModuleNameByKey = (key: string): string => {
    return MODULE_NAME_MAP[key] || key;
  };
// 获取数据权限配置列表
export const getWorkflowList = (moduleKey : string) => get(`teacher/workflow/list/${moduleKey}`).then(res => res.data)
export const createApprove = (data : any) => post(`teacher/workflow/create`, data).then(res => res.data)

export const getApproveList = (moduleKey : string,page:number,pageSize : number) => 
    get(`teacher/workflow/approve/list/${moduleKey}?page=${page}&page_size=${pageSize}`).then(res => res.data)



// 通过审核API
export const approveWorkflow = (approve_id: number, remark: string) => 
  post(`teacher/workflow/approve/pass`, { 
    approve_id, 
    remark
  }).then(res => res.data);

// 驳回审核API
export const rejectWorkflow = (approve_id: number, reject_reason: string, remark: string) => 
  post(`teacher/workflow/approve/reject`, { 
    approve_id, 
    reject_reason, 
    remark
  }).then(res => res.data);

// 获取审核备注列表
export const getWorkflowRemarkList = (dataId: number) => 
  get(`teacher/workflow/remark/${dataId}`).then(res => res.data);

// 添加批量操作API
export const batchApproveWorkflow = (approve_ids: number[], remark: string) => 
  post(`teacher/workflow/approve/batch-pass`, {
    approve_ids,
    remark
  }).then(res => res.data);

export const batchRejectWorkflow = (approve_ids: number[], reject_reason: string, remark: string) => 
  post(`teacher/workflow/approve/batch-reject`, {
    approve_ids,
    reject_reason,
    remark
  }).then(res => res.data);

export const batchCreateWorkflow = (data: any) => 
  post(`teacher/workflow/batch-create`, data).then(res => res.data);