// 试题类型映射
export const typeMap: Record<string, string> = {
  'single': '单选题',
  'multiple': '多选题',
  'judge': '判断题',
  'fill': '填空题',
  'short': '简答题',
  'discuss': '论述题',
  'analyze': '分析题',
  'comprehensive': '综合题',
  'self': '自拟题'
}

export const tagMap: Record<string, string> = {
  'single': 'primary',
  'multiple': 'success',
  'judge': 'warning',
  'fill': 'info',
  'short': 'danger',
  'discuss': 'primary',
  'analyze': 'success',
  'comprehensive': 'warning',
  'self': 'info'
}
// 状态选项
export const statusOptions = [
  { label: '草稿', value: 'draft' },
  { label: '审核中', value: 'reviewing' },
  { label: '已发布', value: 'published' },
  { label: '已驳回', value: 'rejected' },
  { label: '修订中', value: 'revision' },
  { label: '已归档', value: 'deleted' }
];
export const getStatusLabel = (status: string) => {
  const map: Record<string, string> = {
    draft: '草稿',
    reviewing: '审核中',
    published: '已发布',
    rejected: '已驳回',
    revision: '修订中',
    deleted: '已归档'
  };
  return map[status] || status;
};

export const getStatusTagType = (status: string) => {
  const map: Record<string, string> = {
    draft: 'primary',
    reviewing: 'warning',
    published: 'success',
    rejected: 'danger',
    revision: 'warning',
    deleted: 'info'
  };
  return map[status] || '';
};
// 状态映射
export const statusMap: Record<string, string> = {
  'draft': '草稿',
  'reviewing': '审核中',
  'published': '已发布',
  'rejected': '已驳回'
}

export const statusTagMap: Record<string, string> = {
  'draft': 'primary',
  'reviewing': 'warning',
  'published': 'success',
  'rejected': 'danger'
}
export const shortcutsTimeSelect = [
  {
      text: '未来一天',
      value: () => {
          const end = new Date()
          const start = new Date()
          end.setDate(start.getDate() + 1)
          return [start, end]
      },
  },
  {
      text: '未来一周',
      value: () => {
          const end = new Date()
          const start = new Date()
          end.setDate(start.getDate() + 7)
          return [start, end]
      },
  },
  {
      text: '未来一月',
      value: () => {
          const end = new Date()
          const start = new Date()
          end.setMonth(start.getMonth() + 1)
          return [start, end]
      },
  },
]