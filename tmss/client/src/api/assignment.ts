import { post, get } from '@/utils/request';

// 创建作业草稿
export const createAssignment = (data: any) => {
  return post('teacher/assignment/create-draft', data);
};

// 更新作业草稿
export const updateAssignment = (data: any) => {
  return post('teacher/assignment/update-draft', data);
};

// 删除作业
export const deleteAssignment = (id: number) => {
  return post(`teacher/assignment/delete/${id}`);
};

// 获取作业列表
export const getAssignmentsList = (params?: any) => {
  return get('teacher/assignment/list', params).then((res) => res.data);
};

// 获取作业详情
export const getAssignmentDetail = (id: number) => {
  return get(`teacher/assignment/detail/${id}`).then((res) => res.data);
};