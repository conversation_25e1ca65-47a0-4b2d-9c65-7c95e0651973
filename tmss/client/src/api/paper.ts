import { get, post } from '@/utils/request'

// 获取组卷列表
export const getPaperList = (params: any) => {
  return get('teacher/paper/list', params).then((res) => res.data)
}

// 创建组卷草稿
export const createPaperDraft = (params: any) => {
  return post('teacher/paper/create-draft', params)
}

// 编辑组卷草稿
export const editPaperDraft = (params: any) => {
  return post('teacher/paper/update-draft', params)
}

// 删除组卷草稿
export const deletePaperDraft = (id: number) => {
  return post(`teacher/paper/delete/${id}`)
}

// 提交审核
export const submitPaperReviewing = (id: number) => {
  return post(`teacher/paper/submit-review/${id}`)
}

// 驳回组卷
export const rejectPaper = (id: number) => {
  return post(`teacher/paper/reject/${id}`)
}

// 审核组卷
export const reviewPaper = (id: number) => {
  return post(`teacher/paper/approve/${id}`)
}

// 待审核组卷列表
export const getPaperReviewList = (params: any) => {
  return get('teacher/paper/reviewing-list', params).then((res) => res.data)
}

// 组卷详情
export const getPaperDetail = (id: number) => {
  return get(`teacher/paper/detail/${id}`).then((res) => res.data)
}
