import { get, post } from '@/utils/request'

// 获取班级列表
export const getClassroomList = (params?: any) =>
  get('teacher/classroom/list', params).then((res) => res.data)

export const getClassroomDetail = (id: number) =>
  get(`teacher/classroom/detail/${id}`).then((res) => res.data)

export const updateClassroom = (data: any) =>
  post(`teacher/classroom/update`, data).then((res) => res.data)

export const createClassroom = (data: any) =>
  post('teacher/classroom/create', data).then((res) => res.data)

export const deleteClassroom = (id: number) =>
  post(`teacher/classroom/delete/${id}`).then((res) => res.data)