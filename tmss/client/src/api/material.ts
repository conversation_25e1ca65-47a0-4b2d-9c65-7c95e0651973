import { get, post } from "@/utils/request";

/** 创建资料分类参数 */
export interface CreateMaterialParams {
    name: string;   //分类名称
    description?: string; //分类描述
    parent_id: number;  //父级id
    chapter_id: number; //章节id
}

/** 更新资料分类参数 */
export type UpdateMaterialParams = CreateMaterialParams & {
    id: number; //分类id
}

/** 资料列表参数 */
export interface MaterialListParams {
    page?: number;
    page_size?: number;
    name?: string;  //资料名称
    status?: string; //资料状态
    category_id?: number;
}

type MaterialForm = {
    name: string;   //资料名称
    description?: string;
    category_id: number;    //分类id
    minutes: number;
    files: File[];
}

/** 创建资料参数 */
export interface CreateMaterial extends MaterialForm, FormData {}

/** 更新资料参数 */
export interface UpdateMaterial extends Omit<MaterialForm, "files"> {
    id: number; //资料id
}

/** 资料分类列表 */
export const materialCateList = () => get("teacher/resource_category/list");


/** 创建资料分类 */
export const createMaterialCate = (data: CreateMaterialParams) => post("teacher/resource_category/create", data);

/** 更新资料分类 */
export const updateMaterialCate = (data: UpdateMaterialParams) => post("teacher/resource_category/update", data);

/** 删除资料分类 */
export const deleteMaterialCate = (id: number) => post(`teacher/resource_category/delete/${id}`);

/** 资料列表 */
export const materialList = (params: MaterialListParams) => {
    params = { page: 1, page_size: 10, ...params }
    return get("teacher/resource/list", params);
}
export const getResourcesListByCourseAndChapter = (params?: any) =>
    get(`teacher/resource/list`, params).then(
      (res) => {
        if (res.success){
          const list = res.data.list || []
          if (list.length > 0) {
            list.forEach((item: any) => {
              item.question_category = "resources"
              item.question_key = item.id + "_" + item.question_category
              item.action_type = "resources"
              item.title = item.name
            })  
          }
          return {
            list,
            total: res.data.total
          }
  
        }
        return {
          list : [],
          total : 0
        }
      }
    )
/** 创建资料 */
export const createMaterial = (data: CreateMaterial) => post("teacher/resource/create-draft", data);

/** 更新资料 */
export const updateMaterial = (data: UpdateMaterial) => post("teacher/resource/update-draft", data);

/** 删除资料 */
export const deleteMaterial = (id: number) => post(`teacher/resource/delete/${id}`);


export const uploadMaterial = (data: FormData) => post("teacher/resource/upload", data);