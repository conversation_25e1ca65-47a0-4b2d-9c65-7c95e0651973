import { get, post } from '../utils/request'


export const getSyllabusList = (params?: {
  page?: number
  page_size?: number
  name?: string
  status?: string
  user_id?: number
  user_name?: string
}) => get('teacher/syllabus/list', params).then((res) => res.data)

// 添加草稿
export const addDraft = (params: any) =>
  post('teacher/syllabus/create-draft', params)

// 根据id获取教程列表
// export const getBookById = (id: number) =>
//   get(`teacher/syllabus/textbooks/${id}`).then((res) => res.data)

export const getSyllabusDetail = (id: number) =>
  get(`teacher/syllabus/detail/${id}`).then((res) => res.data)


// 编辑草稿
export const updateDraft = (params: any) =>
  post('teacher/syllabus/update-draft', params)

// 发布草稿
export const publishDraft = (id: number) =>
  post(`teacher/syllabus/publish/${id}`)

// 删除草稿
export const deleteDraft = (id: number) =>
  post(`teacher/syllabus/delete-draft/${id}`)

// 审计列表
// export const getSyllabusReviewingList = (params?: {
//   page?: number
//   page_size?: number
// }) => get('teacher/syllabus/reviewing/list', params).then((res) => res.data)

// // 审核大纲
// export const reviewSyllabus = (id: number) =>
//   post(`teacher/syllabus/approve/${id}`)

// // 驳回大纲
// export const rejectSyllabus = (id: number) =>
//   post(`teacher/syllabus/reject/${id}`)
