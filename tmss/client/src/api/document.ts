import { post, get } from '@/utils/request';

// 创建教案草稿
export const createTeachingDocument = (data: any) => {
  return post('teacher/teaching-documents/create-draft', data);
};

// 更新教案草稿
export const updateTeachingDocument = (data: any) => {
  return post('teacher/teaching-documents/update-draft', data);
};

// 删除教案
export const deleteTeachingDocument = (id: number) => {
  return post(`teacher/teaching-documents/delete/${id}`);
};

// 获取教案列表
export const getTeachingDocumentsList = (params?: any) => {
  return get('teacher/teaching-documents/list', params).then((res) => res.data);
};

// 获取教案详情
export const getTeachingDocumentDetail = (id: number) => {
  return get(`teacher/teaching-documents/detail/${id}`).then((res) => res.data);
};