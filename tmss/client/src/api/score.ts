import { get, post } from '@/utils/request'

// 获取老师需要阅卷的考试列表
export const getExamList = (params: any) =>
  get('teacher/examreview/exams', params).then((res) => res.data)

// 获取考试下学生的考试情况
export const getExamStudents = (params: any) =>
  get(`teacher/examreview/students`, params).then((res) => res.data)

// 下发老师阅卷任务（批量）
export const batchReview = (params: any) =>
  post('teacher/examreview/init-task', params)

// 更改老师分批学生的接口
export const updateReview = (params: any) =>
  post('teacher/examreview/change-reviewer', params)

// 获取老师的阅卷任务列表
export const getTaskList = (params: any) =>
  get(`teacher/examreview/list`, params).then((res) => res.data)
