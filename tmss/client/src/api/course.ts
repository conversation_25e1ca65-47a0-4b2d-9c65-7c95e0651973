import { get, post } from '@/utils/request'

// 获取课程列表
export const getCourseList = (params: any) =>
  get('teacher/courses/list', params).then((res) => res.data)

// 创建课程草稿
export const createCourseDraft = (data: any) => {
  return post('teacher/courses/create-draft', data)
}

// 编辑课程草稿
export const editCourseDraft = (data: any) => {
  return post('teacher/courses/update-draft', data)
}

// 删除课程草稿
export const deleteCourseDraft = (id: number) => {
  return post(`teacher/courses/delete/${id}`)
}

// 提交审核
export const submitCourseForReview = (id: number) => {
  return post(`teacher/courses/submit-review/${id}`)
}

// 审核课程
export const reviewCourse = (id: number) => {
  return post(`teacher/courses/approve/${id}`)
}

// 获取审核课程列表
export const getCourseReviewingList = (params?: any) => {
  return get('teacher/courses/reviewing-list', params).then((res) => res.data)
}

// 驳回课程
export const rejectCourse = (id: number) => {
  return post(`teacher/courses/reject/${id}`)
}

// 获取专业列表
export const getMajorList = () => {
  return get('teacher/major/list').then((res) => res.data)
}

// 获取用户列表
export const getUserList = (params: any) => {
  return get('teacher/users/list', params).then((res) => res.data)
}

// 获取课程详情
export const getCourseDetail = (id: number) => {
  return get(`teacher/courses/detail/${id}`).then((res) => res.data)
}
