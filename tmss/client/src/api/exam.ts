import { get, post } from '@/utils/request'

// 获取考试列表
export const getExamList = (params: any) =>
  get('teacher/exam/list', params).then((res) => res.data)

// 创建考试草稿
export const createExamDraft = (params: any) =>
  post('teacher/exam/create-draft', params)

// 编辑考试草稿
export const editExamDraft = (params: any) =>
  post('teacher/exam/update-draft', params)

// 删除考试草稿
export const deleteExamDraft = (id: number) => post(`teacher/exam/delete/${id}`)

// 提交审核
export const submitExam = (id: number) =>
  post(`teacher/exam/submit-review/${id}`)

// 驳回考试
export const rejectExam = (id: number) => post(`teacher/exam/reject/${id}`)

// 审核考试
export const reviewExam = (id: number) => post(`teacher/exam/approve/${id}`)

// 获取考试关联用户列表
export const getExamUserList = (params: any) =>
  get('teacher/exam/user-list', params).then((res) => res.data)

// 待审核考试列表
export const getExamReviewList = (params: any) =>
  get('teacher/exam/reviewing-list', params).then((res) => res.data)

// 获取考试详情
export const getExamDetail = (id: number) =>
  get(`teacher/exam/detail/${id}`).then((res) => res.data)
