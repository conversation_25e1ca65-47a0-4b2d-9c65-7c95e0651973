import { get, post } from '@/utils/request';

// 生成课表
export const createClassSchedule = (data: any) => {
  return post('teacher/class-schedule/generate', data);
};

// 更新课表
export const updateClassSchedule = (data: any) => {
  return post('teacher/class-schedule/update', data);
};

// 删除课表
export const delClassSchedule = (id: number) => {
  return post(`teacher/class-schedule/delete/${id}`);
};



// 获取课表列表
export const getClassScheduleList = (params?: any) => {
  return get('teacher/class-schedule/list', params).then((res) => res.data);
};

// 获取课表详情
export const getClassScheduleDetail = (id: number) => {
  return get(`teacher/class-schedule/detail/${id}`).then((res) => res.data);
};