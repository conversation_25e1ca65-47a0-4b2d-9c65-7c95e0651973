import { setValue, getValue, commit, terminate } from "@/api/scormService";
import { useUserStore } from "@/store/user";

const userStore = useUserStore();

/** window API注入全局api 供课件窗口调用后端服务 */
export function injectSCORMAPI(session_id: number) {
    (window as any).API_1484_11 = {
        Initialize: (_: string) => true,
        Terminate: (_: string) => {
            terminate({ session_id }).then(() => {
                userStore.scormTerminated = true;
            });
        },
        Commit: (_: string) => commit({ session_id }),
        SetValue: (key: string, value: string) => setValue({ session_id, key, value }),
        GetValue: (key: string) => getValue({ session_id, key }).then(res => res.data),
        GetLastError: () => "0",
        GetErrorString: () => "No error",
        GetDiagnostic: () => "OK",
    };
}

// // 创建同步scorm API，处理并发调用顺序执行
// export function createSynchronizedSCORMAPI(session_id: number) {
//     // 原始API实现
//     const rawAPI = {
//         Initialize: (_: string) => true,
//         Terminate: (_: string) => terminate({ session_id }),
//         Commit: (_: string) => commit({ session_id }),
//         SetValue: (key: string, value: string) => setValue({ session_id, key, value }),
//         GetValue: (key: string) => getValue({ session_id, key }).then(res => res.data),
//         GetLastError: () => "0",
//         GetErrorString: () => "No error",
//         GetDiagnostic: () => "OK",
//     };

//     // 任务队列
//     let taskQueue: Promise<any> = Promise.resolve();
//     let isTerminated = false;

//     // 封装方法，加入队列
//     const enqueue = <T>(fn: () => Promise<T> | T): Promise<T> => {
//         if (isTerminated) {
//             return Promise.reject("SCORM API has been terminated");
//         }

//         return new Promise((resolve, reject) => {
//             taskQueue = taskQueue
//                 .then(() => Promise.resolve(fn()))
//                 .then(resolve)
//                 .catch(reject);
//         });
//     };

//     // 创建同步API
//     const synchronizedAPI = {
//         Initialize: (arg: string) => enqueue(() => rawAPI.Initialize(arg)),
//         Terminate: (arg: string) => {
//             return enqueue(() => {
//                 isTerminated = true;
//                 return rawAPI.Terminate(arg);
//             });
//         },
//         Commit: (arg: string) => enqueue(() => rawAPI.Commit(arg)),
//         SetValue: (key: string, value: string) => enqueue(() => rawAPI.SetValue(key, value)),
//         GetValue: (key: string) => enqueue(() => rawAPI.GetValue(key)),
//         GetLastError: () => rawAPI.GetLastError(), // 同步方法不需要排队
//         GetErrorString: () => rawAPI.GetErrorString(), // 同步方法不需要排队
//         GetDiagnostic: () => rawAPI.GetDiagnostic(), // 同步方法不需要排队
//     };

//     return synchronizedAPI;
// }

// // 使用方式
// export function injectSCORMAPI(session_id: number) {
//     (window as any).API_1484_11 = createSynchronizedSCORMAPI(session_id);
// }
