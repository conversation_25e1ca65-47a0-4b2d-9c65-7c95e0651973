<template>
  <div>
    <!-- 主按钮 -->
    <el-button type="primary" size="small" class="!rounded-button whitespace-nowrap mr-3 ml-3" :loading="loading"
      @click="handleSubmit">
      <slot>提交</slot>
    </el-button>

    <!-- 工作流选择弹窗 -->
    <el-dialog v-model="showWorkflowDialog" title="选择审核流程" width="500px" append-to-body>
      <el-select v-model="selectedWorkflowId" placeholder="请选择审核流程">
        <el-option v-for="workflow in workflowList" :key="workflow.id" :label="workflow.name"
          :value="workflow.id"></el-option>
      </el-select>


      <template #footer>
        <el-button @click="showWorkflowDialog = false">取消</el-button>
        <el-button type="primary" @click="doSubmit">确定提交</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { ElMessage } from 'element-plus';
import { getWorkflowList, createApprove } from '@/api/workflow'; // 假设您有封装好的请求方法
import { publishRevision } from '@/api/revision';
// 定义组件属性
const props = defineProps({
  // 审核模块代码（如：'courseware'）
  approveCode: {
    type: String,
    required: true
  },
  // 要审核的数据ID
  revId: {
    type: [Number, String],
    required: true
  },
  // 数据标题（用于显示）
  dataTitle: {
    type: String,
    default: ''
  }
});

// 定义事件
const emit = defineEmits(['success', 'error']);

// 状态变量
const loading = ref(false);
const showWorkflowDialog = ref(false);
const workflowList = ref<any[]>([]);
const selectedWorkflowId = ref(0);
// 获取工作流列表
const fetchWorkflows = async () => {
  loading.value = true;
  const response = await getWorkflowList(props.approveCode);
  workflowList.value = response || [];
  loading.value = false;
  return response;
};

// 提交审核
const submitApproval = async (workflowId: number) => {
  try {
    loading.value = true;
    const pubId = await publishRevision(props.revId);
    await createApprove({
      module_key: props.approveCode,
      data_id: pubId,
      workflow_id: workflowId,
      title: props.dataTitle
    });
    ElMessage.success('提交成功');
    emit('success');
  } catch (error) {
    ElMessage.error('提交失败');
    emit('error', error);
    throw error;
  } finally {
    loading.value = false;
  }
};

// 处理提交按钮点击
const handleSubmit = async () => {
  try {
    const response = await fetchWorkflows();
    console.log(response)
    if (!response){
      await submitApproval(0);
      return;
    }
    // 如果没有工作流或只有一个，直接提交
    if (response.length <= 1) {
      const workflowId = response.length === 1
        ? response[0].id
        : 0;
      await submitApproval(workflowId);
      return;
    }

    // 多个工作流，显示选择弹窗
    showWorkflowDialog.value = true;
    selectedWorkflowId.value = response[0]?.id || 0;
  } catch (error) {
    // 错误已在子函数处理
  }
};

// 最终提交
const doSubmit = async () => {
  if (selectedWorkflowId.value === 0 && workflowList.value.length > 0) {
    ElMessage.warning('请选择一个审核流程');
    return;
  }

  try {
    showWorkflowDialog.value = false;
    await submitApproval(selectedWorkflowId.value);
  } catch (error) {
    // 错误已在子函数处理
  }
};
</script>
