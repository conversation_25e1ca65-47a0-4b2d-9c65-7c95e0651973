<template>
    <div class="flex-1 flex flex-col overflow-hidden">
        <div class="flex-1 overflow-auto">
            <el-empty v-if="!tableData.length" description="暂无数据" />
            <el-table v-else :data="tableData" v-loading="loading" style="width: 100%" border>
                <el-table-column align="center" show-overflow-tooltip v-for="item in cols" :key="item.prop"
                    :prop="item.prop" :label="item.label" />
                <el-table-column width="100" align="center" label="状态" prop="status">
                    <template #default="{ row }">
                        <el-tag :type="getStatusTagType(row.status)">{{ getStatusLabel(row.status) }}</el-tag>
                    </template>
                </el-table-column>
                <el-table-column align="center" label="创建时间" prop="created_at">
                    <template #default="{ row }">
                        {{ formatDate(row.created_at) }}
                    </template>
                </el-table-column>
            </el-table>
        </div>

        <div class="my-8 flex justify-center" v-if="tableData.length">
            <el-pagination background layout="prev, pager, next" :total="total" v-model:current-page="page"
                :page-size="10" @current-change="handleCurrentChange" />
        </div>
    </div>
</template>

<script setup lang="ts">
import { getRevisionList } from '@/api/revision';
import { onMounted, ref } from 'vue';

const page = ref(1)
const total = ref(0)
const tableData = ref([])
const loading = ref(false)
const props = defineProps({
  // 审核模块代码（如：'courseware'）
  approveCode: {
    type: String,
    required: true
  },
});
const cols = [
    { prop: 'id', label: '序号' },
    { prop: 'user_name', label: '修订人' },
    { prop: 'notes', label: '修订说明' }
]
const getList = async () => {
    loading.value = true

    const params = {
        page: page.value,
        module_key: props.approveCode
    }
    const res = await getRevisionList(params)
    tableData.value = res.list || []
    total.value = res.total || 0
    loading.value = false
}

const formatDate = (date: any) => {
    const dateObj = new Date(date * 1000);
    return `${dateObj.getFullYear()}-${String(dateObj.getMonth() + 1).padStart(2, '0')}-${String(dateObj.getDate()).padStart(2, '0')} ${String(dateObj.getHours()).padStart(2, '0')}:${String(dateObj.getMinutes()).padStart(2, '0')}:${String(dateObj.getSeconds()).padStart(2, '0')}`;
}

const getStatusLabel = (status: string) => {
    const map: Record<string, string> = {
        draft: '草稿',
        published: '已发布',
        rejected: '已驳回',
        submitted: '修订中',
    };
    return map[status] || status;
};

const getStatusTagType = (status: string) => {
    const map: Record<string, string> = {
        draft: 'primary',
        published: 'success',
        rejected: 'danger',
        submitted: 'warning',
    };
    return map[status] || '';
};
const handleCurrentChange = (val: number) => {
    page.value = val
    getList()
}
onMounted(() => {
    getList()
})
</script>

<style scoped></style>