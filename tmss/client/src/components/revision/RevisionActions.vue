<template>
  <div class="flex justify-center">
    <!-- 创建修订按钮 -->
    <el-button 
      v-if="showCreateButton"
      size="small" 
      type="success"
      plain
      class="!rounded-button whitespace-nowrap" 
      @click="$emit('create')">
      创建修订
    </el-button>
    
    <!-- 修订记录操作 -->
    <template v-if="rev">
      <template v-if="isEditable">
        <el-button 
          size="small" 
          class="!rounded-button whitespace-nowrap"
          @click="$emit('edit')">
          编辑
        </el-button>
        <el-button 
          size="small" 
          type="danger" 
          class="!rounded-button whitespace-nowrap"
          @click="$emit('delete')">
          删除
        </el-button>
        <RevisionApproveButton 
          :approve-code="props.approveCode" 
          :rev-id="rev.id" 
          :data-title="'修订：' + data.name"
          @success="$emit('refresh')" />
      </template>
      
      <el-button 
        v-else-if="rev.status === 'submitted'"
        size="small" 
        class="!rounded-button whitespace-nowrap"
        @click="$emit('view')">
        修订中
      </el-button>
      
      <el-button 
        v-else-if="rev.status === 'published'"
        size="small" 
        type="info"
        class="!rounded-button whitespace-nowrap"
        plain
        @click="$emit('view')">
        已修订
      </el-button>
    </template>
  </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue';

const props = defineProps({
  data: { type: Object, required: true },
  rev: { type: Object, default: null },
  approveCode: {
    type: String,
    required: true
  },
  showCreateButton: Boolean
})

const isEditable = computed(() => 
  ['draft', 'rejected'].includes(props.rev?.status)
)

defineEmits(['create', 'edit', 'delete', 'view', 'refresh'])
</script>