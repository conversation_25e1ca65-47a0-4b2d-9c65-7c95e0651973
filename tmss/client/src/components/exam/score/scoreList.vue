<template>

    <!-- 查询区域 -->
    <div class="p-6 border-b border-gray-200">
        <el-form :model="queryForm" class="grid grid-cols-4 gap-6">
            <el-form-item label="考核计划">
                <el-select v-model="queryForm.planId" placeholder="请选择考核计划" class="w-full">
                    <el-option v-for="plan in plans" :key="plan.id" :label="plan.name" :value="plan.id" />
                </el-select>
            </el-form-item>
            <el-form-item label="班级">
                <el-select v-model="queryForm.classId" placeholder="请选择班级" class="w-full">
                    <el-option v-for="cls in classes" :key="cls.id" :label="cls.name" :value="cls.id" />
                </el-select>
            </el-form-item>
            <el-form-item label="知识点">
                <el-select v-model="queryForm.knowledgePoints" multiple placeholder="请选择知识点" class="w-full">
                    <el-option v-for="point in knowledgePoints" :key="point.id" :label="point.name" :value="point.id" />
                </el-select>
            </el-form-item>
            <div class=" flex justify-end space-x-4">
                <el-button type="primary" class="!rounded-button whitespace-nowrap" @click="handleSearch">查询</el-button>
                <el-button class="!rounded-button whitespace-nowrap" @click="handleReset">重置</el-button>
            </div>
        </el-form>
        <div class=" flex items-center justify-between">
            <el-button type="primary" class="!rounded-button" @click="emits('add')">
                <el-icon class="mr-1">
                    <Plus />
                </el-icon>录入成绩
            </el-button>

            <!-- <div class="flex items-center gap-4">
                <el-input v-model="searchQuery" placeholder="搜索成绩记录ID" class="w-64">
                    <template #prefix>
                        <el-icon>
                            <Search />
                        </el-icon>
                    </template>
</el-input>

<el-select v-model="statusFilter" placeholder="审核状态" class="w-32">
    <el-option label="全部" value="" />
    <el-option label="已通过" value="approved" />
    <el-option label="已拒绝" value="rejected" />
    <el-option label="待审核" value="pending" />
</el-select>
</div> -->
        </div>
    </div>


    <!-- 表格区 -->
    <div class="flex-1 overflow-auto p-6">
        <el-table :data="filteredTableData" stripe border style="width: 100%" class="custom-table">
            <el-table-column prop="score_record_id" label="成绩记录ID" width="120" />
            <el-table-column prop="plan_id" label="考核计划" />
            <el-table-column prop="teacher_name" label="录入教师" width="120" />
            <el-table-column :show-overflow-tooltip="true" label="录入时间">
                <template #default="{ row }">
                    {{ formatDate(row.create_time) }}
                </template>
            </el-table-column>
            <el-table-column label="审核状态" width="120">
                <template #default="{ row }">
                    <el-tag :type="getStatusType(row.status)" class="whitespace-nowrap">
                        {{ getStatusText(row.status) }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column :show-overflow-tooltip="true" label="审核时间">
                <template #default="{ row }">
                    {{ row.review_time ? formatDate(row.review_time) : '-' }}
                </template>
            </el-table-column>
            <el-table-column label="操作" width="200">
                <template #default="{ row }">
                    <div class="flex gap-2">
                        <el-button type="primary" class="!rounded-button whitespace-nowrap" @click="handleView(row)">
                            <el-icon class="mr-1">
                                <View />
                            </el-icon>查看
                        </el-button>
                        <el-button type="success" class="!rounded-button whitespace-nowrap" @click="handleReview(row)">
                            <el-icon class="mr-1">
                                <Open />
                            </el-icon>审核
                        </el-button>
                    </div>
                </template>
            </el-table-column>
        </el-table>
    </div>


    <div class="mt-4 flex justify-center">
        <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :total="filteredTableData.length"
            layout=" prev, pager, next" />
    </div>


    <!-- 查看成绩详情 -->
    <el-dialog v-model="detailDialogVisible" title="学生成绩详情" width="500px">
        <el-table :data="selectedScores" border style="width: 100%">
            <el-table-column prop="student_id" label="学号" />
            <el-table-column prop="student_name" label="姓名" />
            <el-table-column prop="score" label="成绩" />
        </el-table>
    </el-dialog>

    <!-- 审核弹窗 -->
    <el-dialog v-model="showReviewDialog" title="审核意见" width="500px">
        <el-form :model="reviewForm" label-width="80px">
            <el-form-item label="审核意见">
                <el-input v-model="reviewForm.comments" type="textarea" :rows="4" placeholder="请输入审核意见" />
            </el-form-item>
            <div class="mt-4 flex justify-end gap-3">
                <el-button @click="showReviewDialog = false">取消</el-button>
                <el-button type="danger" @click="handleReviewSubmit('reject')">不通过</el-button>
                <el-button type="primary" @click="handleReviewSubmit('approve')">通过</el-button>
            </div>
        </el-form>
    </el-dialog>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue';
import dayjs from 'dayjs';
import { successMsg } from '@/utils/msg';

const queryForm = ref({
    planId: '',
    studentId: '',
    classId: '',
    knowledgePoints: [],
    dateRange: [],
});
const plans = ref([
    { id: 1, name: '2023年第一学期期末考试' },
    { id: 2, name: '2023年第二学期期中考试' },
]);
const classes = ref([
    { id: 1, name: '计算机科学1班' },
    { id: 2, name: '软件工程2班' },
]);
const knowledgePoints = ref([
    { id: 1, name: '变量声明' },
    { id: 2, name: '循环结构' },
    { id: 3, name: '函数定义' },
]);

const handleSearch = () => {
    // 实现查询逻辑
};
const handleReset = () => {
    queryForm.value = {
        planId: '',
        studentId: '',
        classId: '',
        knowledgePoints: [],
        dateRange: [],
    };
};
const emits = defineEmits(['add']);
interface Score {
    student_id: string;
    student_name: string;
    score: number;
}

interface TableData {
    score_record_id: string;
    plan_id: string;
    scores: Score[];
    teacher_id: string;
    teacher_name: string;
    create_time: number;
    status: 'approved' | 'rejected' | 'pending';
    review_time?: number;
}

const searchQuery = ref('');
const statusFilter = ref('');
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(100);

const tableData = ref<TableData[]>([
    {
        score_record_id: 'SR001',
        plan_id: '2023年第一学期期末考试',
        scores: [
            { student_id: 'S001', student_name: '张雨晨', score: 95 },
            { student_id: 'S002', student_name: '林思远', score: 88 },
            { student_id: 'S003', student_name: '周子涵', score: 92 }
        ],
        teacher_id: 'T001',
        teacher_name: '王明远',
        create_time: 1677649420000,
        status: 'approved',
        review_time: 1677649720000
    },
    {
        score_record_id: 'SR002',
        plan_id: '2023年第一学期期末考试',
        scores: [
            { student_id: 'S004', student_name: '刘梓萱', score: 87 },
            { student_id: 'S005', student_name: '陈浩宇', score: 94 },
            { student_id: 'S006', student_name: '黄子琪', score: 90 }
        ],
        teacher_id: 'T002',
        teacher_name: '李秀英',
        create_time: 1677649420000,
        status: 'pending'
    }
]);

const filteredTableData = computed(() => {
    return tableData.value.filter(item => {
        const matchQuery = item.score_record_id.toLowerCase().includes(searchQuery.value.toLowerCase());
        const matchStatus = !statusFilter.value || item.status === statusFilter.value;
        return matchQuery && matchStatus;
    });
});

const formatDate = (timestamp: number) => {
    return dayjs(timestamp).format('YYYY-MM-DD HH:mm:ss');
};

const getStatusType = (status: string) => {
    const types: Record<string, string> = {
        approved: 'success',
        rejected: 'danger',
        pending: 'warning'
    };
    return types[status] || 'info';
};

const getStatusText = (status: string) => {
    const texts: Record<string, string> = {
        approved: '已通过',
        rejected: '已拒绝',
        pending: '待审核'
    };
    return texts[status] || status;
};


const detailDialogVisible = ref(false)
const selectedScores = ref<any[]>([]);
const handleView = (row: TableData) => {
    // 处理查看逻辑
    selectedScores.value = row.scores;
    detailDialogVisible.value = true;
};


const showReviewDialog = ref(false);
const reviewForm = ref({
    comments: '',
});

let currentRow: any;
const handleReview = (row: TableData) => {
    // 处理审核逻辑
    showReviewDialog.value = true;
    currentRow = row;
};

// 提交审核结果
const handleReviewSubmit = (type: 'approve' | 'reject') => {
    const statusMap: Record<'approve' | 'reject', string> = {
        approve: 'approved',
        reject: 'rejected',
    };

    if (currentRow) {
        // 更新状态
        currentRow.status = statusMap[type] as any;

        // 可以在此处调用 API 提交审核结果
        console.log('提交审核:', {
            ...currentRow,
            status: currentRow.status,
            comments: reviewForm.value.comments,
        });

        // 关闭弹窗并提示
        showReviewDialog.value = false;
        successMsg(type === 'approve' ? '审核通过' : '审核不通过');
    }
};


</script>

<style scoped>
.custom-table :deep(.el-table__header) {
    background-color: #f5f7fa;
}

.custom-table :deep(.el-table__row) {
    cursor: pointer;
}

.custom-table :deep(.el-table__row:hover) {
    background-color: #f5f7fa;
}

.custom-table :deep(.el-button) {
    padding: 6px 12px;
}
</style>
