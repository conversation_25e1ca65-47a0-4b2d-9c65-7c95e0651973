<template>
    <!-- 返回按钮和标题 -->
    <div class="p-6 border-b border-gray-200">
        <el-button circle @click="emits('cancel')">
            <el-icon>
                <ArrowLeft />
            </el-icon>
        </el-button>
    </div>

    <!-- 表单内容 -->
    <div class="flex-1 overflow-auto p-6">
        <el-form ref="form" :model="formData" label-width="120px" class="bg-white p-6 rounded shadow-sm">

            <!-- 考核计划 -->
            <el-form-item label="考核计划" prop="assessment_plan_id">
                <el-select v-model="formData.assessment_plan_id" placeholder="请选择考核计划" class="w-full">
                    <el-option v-for="item in planOptions" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>

            <!-- 学生成绩表格 -->
            <el-form-item label="学生成绩">
                <el-table :data="formData.scores" border style="width: 100%">
                    <el-table-column prop="student_id" label="学号" width="150"></el-table-column>
                    <el-table-column prop="name" label="姓名" width="150"></el-table-column>
                    <el-table-column label="成绩">
                        <template #default="{ row }">
                            <el-input-number v-model="row.score" :min="0" :max="100" controls-position="right"
                                class="w-full" placeholder="请输入成绩" />
                        </template>
                    </el-table-column>
                </el-table>

                <div class="mt-4 text-right text-sm text-gray-500">
                    共 {{ formData.scores.length }} 名学生
                </div>
            </el-form-item>

            <!-- 备注 -->
            <el-form-item label="备注">
                <el-input v-model="formData.remark" type="textarea" :rows="3" placeholder="可填写备注信息，如缺考说明等" />
            </el-form-item>

            <!-- 操作按钮 -->
            <div class="flex justify-end mt-6 gap-4">
                <el-button @click="handleSaveDraft">取消</el-button>
                <el-button type="primary" @click="handleSubmit">提交成绩</el-button>
            </div>

        </el-form>
    </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { ArrowLeft } from '@element-plus/icons-vue';
import { successMsg, infoMsg } from '@/utils/msg';


const emits = defineEmits(['cancel']);
// 表单数据模型
const formData = ref({
    assessment_plan_id: '',
    scores: [
        { student_id: 'S001', name: '张三', score: null },
        { student_id: 'S002', name: '李四', score: null },
        { student_id: 'S003', name: '王五', score: null }
    ],
    remark: ''
});

// 模拟考核计划选项
const planOptions = [
    { value: 'plan_001', label: '期末考试 - 高等数学' },
    { value: 'plan_002', label: '期中考试 - 计算机基础' }
];

// 保存草稿
const handleSaveDraft = () => {
    console.log('保存草稿:', formData.value);
    successMsg('草稿保存成功');
};

// 提交成绩
const handleSubmit = () => {
    if (!formData.value.assessment_plan_id) {
        infoMsg('请选择考核计划');
        return;
    }

    const invalid = formData.value.scores.some(score => score.score === null || score.score < 0 || score.score > 100);
    if (invalid) {
        infoMsg('请确保所有成绩在 0-100 之间');
        return;
    }

    console.log('提交成绩:', formData.value);
    successMsg('成绩提交成功');
};
</script>

<style scoped>
.el-table__row .el-table__cell {
    white-space: nowrap;
}
</style>