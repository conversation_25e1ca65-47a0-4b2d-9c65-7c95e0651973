<template>
    <!-- 头部区域 -->
    <div class="p-6 border-b border-gray-200">
        <el-button type="primary" class="!rounded-button" @click="emits('create-click')">
            <el-icon class="mr-1">
                <Plus />
            </el-icon>创建考核
        </el-button>
    </div>


    <!-- 表格区域 -->
    <div class="flex-1 overflow-auto p-6">
        <el-table :data="tableData" border class="w-full">
            <el-table-column :show-overflow-tooltip="true" prop="course_id" label="课程" />
            <el-table-column :show-overflow-tooltip="true" prop="assessment_type" label="考核类型" />
            <el-table-column :show-overflow-tooltip="true" prop="assessment_time" label="考核时间" />
            <el-table-column :show-overflow-tooltip="true" prop="score_percentage" label="成绩占比">
                <template #default="{ row }">
                    <span>{{ row.score_percentage }}%</span>
                </template>
            </el-table-column>
            <el-table-column prop="status" label="状态">
                <template #default="{ row }">
                    <el-tag :type="getStatusType(row.status)" class="whitespace-nowrap">
                        {{ row.status }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column :show-overflow-tooltip="true" prop="create_time" label="创建时间">
                <template #default="{ row }">
                    {{ formatTime(row.create_time) }}
                </template>
            </el-table-column>
            <el-table-column label="操作" min-width="200">
                <template #default="{ row }">
                    <div class="flex items-center">
                        <el-button size="small" type="default" class="!rounded-button whitespace-nowrap"
                            @click="handleEdit(row)">
                            <el-icon class="mr-1">
                                <View />
                            </el-icon>查看
                        </el-button>
                        <el-button size="small" type="primary" class="!rounded-button whitespace-nowrap"
                            @click="handleEdit(row)">
                            <el-icon class="mr-1">
                                <Edit />
                            </el-icon>编辑
                        </el-button>
                        <el-button size="small" type="warning" class="!rounded-button whitespace-nowrap"
                            @click="handleReview(row)">
                            <el-icon class="mr-1">
                                <Open />
                            </el-icon>
                            审核
                        </el-button>
                        <el-button size="small" type="success" class="!rounded-button whitespace-nowrap">
                            <el-icon class="mr-1">
                                <Position />
                            </el-icon>发布
                        </el-button>
                    </div>
                </template>
            </el-table-column>
        </el-table>
    </div>


    <!-- 分页 -->
    <div class="mt-4 flex justify-center">
        <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :total="tableData.length"
            layout=" prev, pager, next" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
    </div>

    <!-- 审核弹窗 -->
    <el-dialog v-model="showReviewDialog" title="审核意见" width="500px">
        <el-form :model="reviewForm" label-width="80px">
            <el-form-item label="审核意见">
                <el-input v-model="reviewForm.comments" type="textarea" :rows="4" placeholder="请输入审核意见" />
            </el-form-item>
            <div class="mt-4 flex justify-end gap-3">
                <el-button @click="showReviewDialog = false">取消</el-button>
                <el-button type="danger" @click="handleReviewSubmit('reject')">不通过</el-button>
                <el-button type="primary" @click="handleReviewSubmit('approve')">通过</el-button>
            </div>
        </el-form>
    </el-dialog>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { successMsg } from '@/utils/msg';

const emits = defineEmits(['create-click']);
interface AssessmentPlan {
    course_id: string;
    assessment_type: string;
    assessment_time: string;
    assessment_method: string;
    score_percentage: number;
    status: '待审核' | '已发布' | '已驳回' | '已通过';
    create_time: number;
}

const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(100);


const tableData = ref<AssessmentPlan[]>([
    {
        course_id: '语文',
        assessment_type: '平时',
        assessment_time: '2023-12-20 14:00-16:00',
        assessment_method: '笔试',
        score_percentage: 60,
        status: '待审核',
        create_time: 1673424000000
    },
    {
        course_id: '数学',
        assessment_type: '结业',
        assessment_time: '2023-10-25 09:00-11:00',
        assessment_method: '实操',
        score_percentage: 40,
        status: '已发布',
        create_time: 1673510400000
    },
    {
        course_id: '英语',
        assessment_type: '补考',
        assessment_time: '2023-11-30 23:59',
        assessment_method: '项目',
        score_percentage: 30,
        status: '已驳回',
        create_time: 1673596800000
    }
]);

const getStatusType = (status: string) => {
    const statusMap: Record<string, string> = {
        '待审核': 'warning',
        '已发布': 'success',
        '已驳回': 'danger',
        '已通过': 'success'
    };
    return statusMap[status] || 'info';
};

const formatTime = (timestamp: number) => {
    const date = new Date(timestamp);
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    });
};

const handleSizeChange = (val: number) => {
    pageSize.value = val;
    // 这里添加获取数据的逻辑
};

const handleCurrentChange = (val: number) => {
    currentPage.value = val;
    // 这里添加获取数据的逻辑
};

const handleEdit = (row: any) => {
    // 编辑考核的逻辑
    console.log(row)
};

const showReviewDialog = ref(false);
const reviewForm = ref({
    comments: '',
});

let currentRow: AssessmentPlan | null = null;

// 打开审核弹窗
const handleReview = (row: AssessmentPlan) => {
    currentRow = row;
    showReviewDialog.value = true;
};

// 提交审核结果
const handleReviewSubmit = (type: 'approve' | 'reject') => {
    const statusMap: Record<'approve' | 'reject', string> = {
        approve: '已通过',
        reject: '已驳回',
    };

    if (currentRow) {
        // 更新状态
        currentRow.status = statusMap[type] as any;

        // 可以在此处调用 API 提交审核结果
        console.log('提交审核:', {
            ...currentRow,
            status: currentRow.status,
            comments: reviewForm.value.comments,
        });

        // 关闭弹窗并提示
        showReviewDialog.value = false;
        successMsg(type === 'approve' ? '审核通过' : '审核不通过');
    }
};
</script>

<style scoped>
.el-table__row .el-table__cell:last-child {
    white-space: nowrap;
}

.el-table {
    --el-table-border-color: #e5e7eb;
    --el-table-header-bg-color: #f9fafb;
    --el-table-row-hover-bg-color: #f3f4f6;
}

.el-pagination {
    --el-pagination-button-bg-color: white;
}
</style>
