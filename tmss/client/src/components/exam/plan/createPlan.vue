<!-- 代码已包含 CSS：使用 TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 -->

<template>
    <div class="flex-1 overflow-auto ">
        <!-- 表单内容 -->
        <el-form ref="formRef" :model="formData" :rules="rules" label-width="150px"
            class="bg-white p-4 rounded-lg shadow-sm" :disabled="is_view">
            <!-- 考试名称 -->
            <el-form-item label="考试名称" prop="exams.name">
                <el-input v-model="formData.exams.name" />
            </el-form-item>
            <!-- 考试类型 -->
            <el-form-item label="考试类型" prop="exams.exam_type">
                <el-radio-group v-model="formData.exams.exam_type">
                    <el-radio border value="regular">平时考试</el-radio>
                    <el-radio border value="final">结业考试</el-radio>
                    <el-radio border value="retake">补考</el-radio>
                </el-radio-group>
            </el-form-item>
            <!-- 考试地点 -->
            <el-form-item label="考试地点" prop="exams.location">
                <el-input v-model="formData.exams.location" />
            </el-form-item>
            <!-- 试卷 -->
            <el-form-item label="选择试卷" prop="exams.paper_id">
                <my-select placeholder="请选择试卷" v-model="formData.exams.paper_id" :func="getPaperList" labelKey="title"
                    valueKey="id" searchKey="name" :extraParams="{ status: 'published' }" :initData="initPaper" />
            </el-form-item>
            <el-row>
                <el-col :span="12">
                    <!-- 开始时间 -->
                    <el-form-item label="开始时间" prop="start_at">
                        <el-date-picker class="!w-full" v-model="formData.start_at" type="datetime" placeholder="选择日期时间"
                            format="YYYY-MM-DD HH:mm" value-format="YYYY-MM-DD HH:mm"></el-date-picker>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <!-- 考试时长 -->
                    <!-- <el-form-item label="考试时长(分钟)" prop="exams.total_minutes">
                        <el-input v-model.number="formData.exams.total_minutes" />
                    </el-form-item> -->
                </el-col>
            </el-row>

            <!-- 是否颁发证书 -->
            <el-form-item label="是否颁发证书">
                <el-switch v-model="formData.exams.is_certificate" inline-prompt active-text="是" inactive-text="否" />
            </el-form-item>
            <!-- 考试班级 -->
            <el-form-item label="考试班级(可多选)" prop="class_ids">
                <my-select v-model="formData.class_ids" labelKey="name" valueKey="id" searchKey="name"
                    placeholder="请选择班级" :func="getClassList" multiple :initData="initClass" />
            </el-form-item>
            <el-form-item label="考试专业(可多选)" prop="major_ids">
                <major-select v-model="formData.major_ids" :multiple="true" />
                <!-- <el-tree-select check-strictly :render-after-expand="false" v-model="formData.major_ids"
                    placeholder="请选择考试专业" :data="majors" multiple :props="major_props" /> -->
            </el-form-item>
            <!-- 阅卷教师 -->
            <el-form-item label="阅卷教师(可多选)" prop="teacher_ids">
                <my-select v-model="formData.teacher_ids" labelKey="username" valueKey="id" searchKey="username"
                    placeholder="请选择阅卷教师" :func="getUserList" :extraParams="{ sys_code: 'teacher' }" multiple
                    :initData="initTeacher" />
            </el-form-item>
            <!-- 监考教师 -->
            <el-form-item label="监考教师(可多选)" prop="supervisor_ids">
                <my-select v-model="formData.supervisor_ids" labelKey="username" valueKey="id" searchKey="username"
                    placeholder="请选择监考教师" :func="getUserList" :extraParams="{ sys_code: 'teacher' }" multiple
                    :initData="initSupervisor" />
            </el-form-item>
        </el-form>

    </div>

    <!-- 操作按钮 -->
    <div class="mt-4 flex justify-center" v-if="!is_view">
        <el-button @click="emit('close')">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
            确认创建
        </el-button>
    </div>
</template>

<script lang="ts" setup>
import { getClassList } from '@/api/class';
import { getUserList, getMajorList } from '@/api/course';
import { createExamDraft, getExamDetail, editExamDraft } from '@/api/exam';
import { getPaperList } from '@/api/paper';
import { successMsg } from '@/utils/msg';
import { nextTick, onMounted, ref, watch } from 'vue';
// props
const props = defineProps<{
    is_edit: boolean,
    is_view: boolean,
    plan_id: number
}>()
const majors = ref([])
const major_props = {
    label: 'name',
    value: 'id'
}
const initFormData = () => {
    return {
        exams: {
            name: '', // 考试名称
            exam_type: 'regular', // regular/final/retake
            location: '', // 考试地点
            paper_id: '',// 试卷ID
            // total_minutes: '',//考试时长
            is_certificate: false // 是否颁发证书
        },
        class_ids: [], // 班级ID
        teacher_ids: [], // 阅卷教师ID
        major_ids: [], // 专业ID
        supervisor_ids: [], // 监考教师ID
        start_at: ''  // 开始时间
    }
}
const formData: any = ref(initFormData())
const rules = {
    exams: {
        name: [
            { required: true, message: '请输入考试名称', trigger: 'blur' }
        ],
        exam_type: [
            { required: true, message: '请选择考试类型', trigger: 'change' }
        ],
        location: [
            { required: true, message: '请输入考试地点', trigger: 'blur' }
        ],
        paper_id: [
            { required: true, message: '请选择试卷', trigger: 'change' }
        ],
        // total_minutes: [
        //     { required: true, message: '请输入考试时长', trigger: 'blur' }
        // ],
    },
    class_ids: [
        { required: true, message: '请选择考试班级', trigger: 'change' }
    ],
    major_ids: [
        { required: true, message: '请选择考试专业', trigger: 'change' }
    ],
    teacher_ids: [
        { required: true, message: '请选择阅卷教师', trigger: 'change' }
    ],
    supervisor_ids: [
        { required: true, message: '请选择监考教师', trigger: 'change' }
    ],
    start_at: [
        { required: true, message: '请选择考试时间', trigger: 'change' }
    ]
}

// buttons
const emit = defineEmits(['close'])
const formRef = ref()
const submitting = ref(false)
const handleSubmit = () => {
    formRef.value.validate(async (valid: boolean) => {
        if (valid) {
            // console.log(formData.value)
            submitting.value = true
            const res = props.is_edit ? await editExamDraft(formData.value) : await createExamDraft(formData.value)
            submitting.value = false
            successMsg(res.message)
            clear()
            emit('close')
        } else {
            return false
        }
    })
}
const clear = () => {
    formData.value = initFormData()
    formRef.value.resetFields()
}

const initPaper: any = ref([])
const initClass: any = ref([])
const initTeacher: any = ref([])
const initSupervisor: any = ref([])
watch(() => props.plan_id, async (val) => {
    if (val) {
        await nextTick(clear)
        const res = await getExamDetail(val)
        initPaper.value = [res.paper]
        initClass.value = res.classes
        initTeacher.value = res.teachers
        initSupervisor.value = res.supervisors
        // console.log(res, 'res')
        formData.value = {
            exams: res.exams,
            class_ids: res.classes.map((item: any) => item.id),
            major_ids: res.majors.map((item: any) => item.id),
            teacher_ids: res.teachers.map((item: any) => item.id),
            supervisor_ids: res.supervisors.map((item: any) => item.id),
            start_at: formatTime(res.exams.start_at)
        }
    } else {
        nextTick(clear)
    }
}, {
    immediate: true
})
const formatTime = (time: any) => {
    const date = new Date(time * 1000)
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
}
onMounted(async () => {
    majors.value = await getMajorList()
})
</script>

<style scoped>
.el-input-number :deep(.el-input__wrapper) {
    padding-right: 0;
}
</style>
