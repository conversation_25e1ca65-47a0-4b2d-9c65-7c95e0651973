<template>
    <div class="flex-1 flex flex-col overflow-y-auto">
        <div class="text-gray-700 mb-4">
            {{ content.content }}
            <span>({{ question.score }}分)</span>
        </div>
        <el-checkbox-group v-model="answer" class="flex flex-col space-y-4" @change="handleChange">
            <el-checkbox v-for="item in JSON.parse(content.options)" :key="item.id" :value="item.id">
                {{ item.content }}
            </el-checkbox>
        </el-checkbox-group>
    </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue';

const props = defineProps<{
    question: any
}>()
const content = computed(() => JSON.parse(props.question.content))
const answer = ref([])
const handleChange = (val: any) => {
    props.question.answer = val
}
watch(() => props.question, () => {
    answer.value = props.question.answer || []
}, { immediate: true })
</script>

<style scoped>
:deep(.el-checkbox) {
    height: auto !important;
}

:deep(.el-checkbox__label) {
    flex: 1;
    display: block;
    white-space: normal;
    word-break: break-all;
}
</style>