<template>
    <!-- 试卷管理 -->
    <div class="space-y-6 w-full flex-1 overflow-auto flex flex-col">
        <div class="flex justify-between items-center border-b border-gray-200 pb-6">
            <div class="space-x-4 flex items-center">
                <el-input placeholder="搜索试卷" class="w-60" clearable>
                    <template #prefix>
                        <el-icon>
                            <Search />
                        </el-icon>
                    </template>
                </el-input>
                <el-select placeholder="试卷状态" class="w-40">
                    <el-option label="全部" value="" />
                    <el-option label="未发布" value="draft" />
                    <el-option label="已发布" value="published" />
                    <el-option label="已归档" value="archived" />
                </el-select>
            </div>
            <el-button type="primary" @click="add_drawer = true">新增试卷</el-button>
        </div>
        <div class="flex-1 overflow-auto ">
            <el-table :data="tableData" border v-loading="loading">
                <el-table-column show-overflow-tooltip v-for="item in cols" :key="item.prop" :prop="item.prop"
                    :label="item.label" />
                <el-table-column prop="status" label="状态" width="100">
                    <template #default="{ row: { status } }">
                        <el-tag>
                            {{ paperStatus[status] }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="操作" width="200">
                    <template #default="{ row }">
                        <div class="flex">
                            <el-button size="small" v-if="row.status == 'published' || row.status == 'reviewing'"
                                @click="previewPaper(row)">预览</el-button>
                            <el-button v-if="row.status == 'draft' || row.status == 'rejected'" type="primary"
                                size="small" @click="editPaper(row)">编辑</el-button>
                            <el-button v-if="row.status == 'draft' || row.status == 'rejected'" type="danger"
                                size="small" @click="deletePaper(row)">删除</el-button>
                            <ApproveButton v-if="row.status == 'draft' || row.status == 'rejected'"
                                approve-code="papers" :data-id="row.id" :data-title="row.title" @success="getData" />
                        </div>
                    </template>
                </el-table-column>
            </el-table>
        </div>

        <div class="flex justify-center mt-4">
            <el-pagination v-model:current-page="page" v-model:page-size="pageSize" :total="total"
                layout=" prev, pager, next" @current-change="handleCurrentChange" />
        </div>

        <!-- 新增编辑试卷 -->
        <el-drawer v-model="add_drawer" :title="is_edit ? '编辑试卷' : '新增试卷'" size="100%">
            <fixed-paper v-if="add_drawer" @create="add_drawer = false" :is_edit="is_edit" :paper_id="edit_id" />
        </el-drawer>

        <!-- 预览试卷 -->
        <el-drawer v-model="preview_drawer" title="预览试卷" size="100%" :before-close="handleViewClose">
            <view-paper ref="view_paper" v-if="preview_drawer" :paper_id="view_id" @submit="preview_drawer = false" />
        </el-drawer>
    </div>
</template>
<script lang="ts" setup>
import { deletePaperDraft, getPaperList } from '@/api/paper';
import { confirmMsg, successMsg } from '@/utils/msg';
import { onMounted, ref, watch } from 'vue';
// props
defineProps<{
    paperStatus: Record<string, string>
}>()


// pagination
const page = ref(1)
const pageSize = ref(10)
const total = ref(0)
const handleCurrentChange = (val: number) => {
    page.value = val
    getData()
}

// table
const cols = [
    { prop: 'title', label: '试卷名称' },
    { prop: 'question_count', label: '题目数量' },
    { prop: 'total_score', label: '总分' },
    { prop: 'total_minutes', label: '考试时长(分钟)' },
]
const loading = ref(false)
const tableData = ref([])
const getData = async () => {
    loading.value = true

    const params = {
        page: page.value,
    }
    const res = await getPaperList(params)
    console.log(res, '----')
    tableData.value = res.list || []
    total.value = res.total || 0
    loading.value = false
}

const edit_id = ref(0)
const view_id = ref(0)
const previewPaper = (row: any) => {
    console.log(row, 'preview')
    preview_drawer.value = true
    view_id.value = row.id
}
const editPaper = async (row: any) => {
    console.log(row, 'row')
    is_edit.value = true
    edit_id.value = row.id
    add_drawer.value = true
}
const deletePaper = (row: any) => {
    console.log(row, 'delete')
    confirmMsg('确定要删除该试卷吗？', '提示', async (action) => {
        if (action) {
            const res = await deletePaperDraft(row.id)
            successMsg(res.message)
            getData()
        }
    })
}


// add/edit drawer
const add_drawer = ref(false)
const is_edit = ref(false)
watch(add_drawer, (val) => {
    if (!val) {
        is_edit.value = false
        edit_id.value = 0
        getData()
    }
})

// preview drawer
const view_paper = ref()
const preview_drawer = ref(false)
const handleViewClose = async () => {
    console.log('close')
    await view_paper.value?.handleSubmit()
}
onMounted(() => {
    getData()
})
</script>
<style scoped>
:deep(.el-drawer__header) {
    margin-bottom: 0 !important;
}

:deep(.el-drawer__body) {
    display: flex;
    overflow: hidden;
}
</style>
