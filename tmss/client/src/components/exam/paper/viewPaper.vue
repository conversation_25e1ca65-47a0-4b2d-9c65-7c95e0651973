<template>
    <el-skeleton :rows="10" animated v-if="loading" />
    <div class="flex-1 flex overflow-hidden" v-else>
        <tips v-if="!is_confirm" @confirm="is_confirm = true" />
        <div class="flex-1 bg-gray-50 flex flex-col overflow-hidden" v-else>
            <!-- 顶部考试信息区 -->
            <div class="bg-white shadow-sm py-4 px-6 border-b border-gray-200">
                <div class=" flex justify-between items-center">
                    <h1 class="text-xl font-semibold text-gray-800">{{ store.paper.title }}</h1>
                    <div class="flex items-center mt-1 space-x-4 text-sm">
                        <div class="flex items-center">
                            <el-icon class="mr-1">
                                <Clock />
                            </el-icon>
                            <div class="text-red-500 font-medium">剩余时间: </div>
                            <el-countdown @finish="handleFinish" class="ml-1" format="HH:mm:ss" :value="time"
                                :value-style="{ 'color': 'red' }" />
                        </div>
                        <div class="text-gray-600">总分: {{ store.paper.total_score }}分</div>
                        <div class="text-gray-600">题量: {{ store.paper.question_count }}题</div>
                        <div class="text-green-600 font-medium">状态: 进行中</div>
                    </div>

                </div>
            </div>

            <div class="flex flex-1 overflow-hidden p-6 ">
                <!-- 左侧题目导航栏 -->
                <div
                    :class="['w-64 bg-white rounded-lg shadow-sm flex flex-col mr-2 transition-all duration-300 ease-in-out', { '!w-0': !showSidebar }]">
                    <div class="flex-1 overflow-y-auto">
                        <div class="px-2 py-4">
                            <el-scrollbar height="100%">
                                <div class="grid grid-cols-4 gap-3">

                                    <div v-for="(item, index) in store.questions" :key="index" @click="i = index"
                                        class="h-12 w-12 flex items-center justify-center rounded border cursor-pointer transition-colors"
                                        :class="{
                                            'border-blue-300 border-2': i === index,
                                            'bg-blue-100  border-blue-300': item.answer,
                                            ' border-gray-300': !item.answer && i !== index,
                                            'bg-orange-100 border-orange-300': item.marked
                                        }">
                                        {{ index + 1 }}
                                    </div>
                                </div>
                            </el-scrollbar>
                        </div>
                    </div>
                    <div class="p-3 border-t border-gray-200">
                        <el-button class="w-full !rounded-button whitespace-nowrap" @click="toggleSidebar">
                            <el-icon size="24" v-if="!showSidebar" class="mr-1">
                                <Expand />
                            </el-icon>
                            <el-icon v-else class="mr-1">
                                <Fold />
                            </el-icon>
                            收起导航
                        </el-button>
                    </div>
                </div>

                <!-- 主答题区域 -->
                <div class="flex-1 overflow-hidden  flex flex-col">

                    <div class="flex-1 bg-white rounded-lg shadow-sm pt-6 pb-2 px-6 flex flex-col overflow-hidden">
                        <!-- 顶部操作区 -->
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex-1 mr-4">
                                <div class="flex justify-between text-xs text-gray-500 mb-1">
                                    <span>已完成: {{ answeredCount }} / {{ store.questions.length }}</span>
                                </div>
                                <el-progress :percentage="answeredPercentage"
                                    :color="answeredPercentage === 100 ? '#67C23A' : '#409EFF'" />
                            </div>
                            <div class="space-x-3">
                                <el-button @click="handleSubmit(false)" type="success"
                                    class="!rounded-button whitespace-nowrap">
                                    <el-icon class="mr-1">
                                        <Finished />
                                    </el-icon>
                                    提交试卷
                                </el-button>
                            </div>
                        </div>
                        <!-- 题目内容 -->
                        <div class="mb-6 flex-1 flex flex-col overflow-hidden">
                            <div class="flex gap-4 items-center mb-4">
                                <h2 class="text-lg font-medium text-gray-800">
                                    第 {{ i + 1 }} 题
                                    <span class="text-sm text-gray-500 ml-2">({{
                                        getType(store.questions[i]) }})</span>
                                </h2>
                                <el-button size="small" :type="store.questions[i].marked ? 'warning' : 'default'"
                                    @click="toggleMark" class="!rounded-button whitespace-nowrap">
                                    <el-icon class="mr-1">
                                        <Star />
                                    </el-icon>
                                    {{ store.questions[i].marked ? '取消标记' : '标记此题' }}
                                </el-button>
                            </div>
                            <!-- 课件 -->
                            <el-drawer size="100%" title="课件" v-model="courseVisible" :before-close="handleCourseClose">
                                <template
                                    v-if="currentType == 'theory_courseware' || currentType == 'virtual_courseware'">
                                    <scorm-player v-if="courseVisible" ref="player" :question="store.questions[i]"
                                        :key="store.questions[i].id" />
                                </template>

                            </el-drawer>

                            <!-- 理论试题 -->
                            <div class="flex-1 mb-4 flex overflow-hidden">
                                <single :question="store.questions[i]" v-if="currentType == 'single'" />
                                <multiple :question="store.questions[i]" v-if="currentType == 'multiple'" />
                                <judge :question="store.questions[i]" v-if="currentType == 'judge'" />
                                <fill :question="store.questions[i]" v-if="currentType == 'fill'" />
                                <short :question="store.questions[i]"
                                    v-if="currentType == 'short' || currentType == 'discuss' || currentType == 'analyze' || currentType == 'comprehensive' || currentType == 'self'" />
                                <div v-if="currentType == 'theory_courseware' || currentType == 'virtual_courseware'">
                                    <el-button @click="open">{{ JSON.parse(store.questions[i].content).title
                                        }}</el-button>
                                    <span class=ml-2>({{ store.questions[i].score }})分</span>
                                </div>
                            </div>

                        </div>

                        <!-- 答题控制按钮 -->
                        <div class="flex justify-between pt-2 border-t border-gray-200">
                            <div>
                                <el-button :disabled="i === 0" @click="prevQuestion"
                                    class="!rounded-button whitespace-nowrap">
                                    <el-icon class="mr-1">
                                        <ArrowLeft />
                                    </el-icon>
                                    上一题
                                </el-button>
                                <el-button :disabled="i === store.questions.length - 1" @click="nextQuestion"
                                    class="!rounded-button whitespace-nowrap">
                                    下一题
                                    <el-icon class="ml-1">
                                        <ArrowRight />
                                    </el-icon>
                                </el-button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

</template>

<script lang="ts" setup>
import { ref, computed, onMounted, onBeforeUnmount, watch, h, resolveComponent } from 'vue';

import { confirmMsg, successMsg, warningMsg } from '@/utils/msg';
import { getPaperDetail } from '@/api/paper';
import { usePaperStore } from '@/store/paper';
import { ElMessageBox } from 'element-plus';
import { getValue, virtualComplete } from '@/api/scormService';

const is_confirm = ref(false)
const currentType = computed(() => {
    const content = JSON.parse(store.questions[i.value].content)
    return content.question_type || content.courseware_type
})
const getType = (item: any) => {
    const typeMap: any = {
        'single': '单选题',
        'multiple': '多选题',
        'judge': '判断题',
        'fill': '填空题',
        'short': '简答题',
        'theory_courseware': '理论课件',
        'virtual_courseware': '虚拟课件',
        'discuss': '论述题',
        'analyze': '分析题',
        'comprehensive': '综合题',
        'self': '自拟题'
    }
    const content = JSON.parse(item.content || '')
    return typeMap[content.question_type || content.courseware_type]
}

const store = usePaperStore()

const props = defineProps<{
    paper_id: number;
}>()

const loading = ref(true)
watch(() => props.paper_id, async (newVal) => {
    if (newVal) {
        // 加载试卷数据
        loading.value = true
        const res = await getPaperDetail(newVal)
        loading.value = false
        console.log(res, 'res')
        store.paper = res.papers
        store.questions = res.paper_questions
    }
}, {
    immediate: true,
})

const time: any = computed(() => {
    return Date.now() + 1000 * 60 * store.paper.total_minutes
    // return Date.now() + 1000 * 10

})

const handleFinish = () => {
    handleSubmit(true)
}



const i = ref(0);
const courseVisible = ref(false)
const player = ref()

const handleCourseClose = async () => {
    if (currentType.value == 'theory_courseware') {
        // 理论课件完成状态
        const res = await getValue({ session_id: player.value.sessionId, key: 'cmi.completion_status' })
        store.complete = res.data == 'completed' ? true : false
        store.questions[i.value].answer = res.data == 'completed' ? true : false
    } else if (currentType.value == 'virtual_courseware') {
        // 虚拟课件完成状态
        const res = await virtualComplete(JSON.parse(store.questions[i.value].content).id)
        console.log(res, 'res')
        store.complete = res.data == 'completed' ? true : false
        store.questions[i.value].answer = res.data == 'completed' ? true : false
    }
    if (!store.complete) {
        confirmMsg('确定要退出吗？系统将不会保存您的答题数据', '提示', (action) => {
            if (action) {
                courseVisible.value = false
            }
        })
    } else {
        courseVisible.value = false

    }
    store.is_courseware = false

}
const open = () => {
    if (store.questions[i.value].answer) return successMsg('您已作答过该题，无需再次打开课件')
    courseVisible.value = true
}




const showSidebar = ref(true);

// 计算属性
const answeredCount = computed(() => {
    return store.questions.filter((q: any) => q.answer).length;
});

const answeredPercentage = computed(() => {
    return Math.round((answeredCount.value / store.questions.length) * 100);
});


const toggleSidebar = () => {
    showSidebar.value = !showSidebar.value;
};

const toggleMark = () => {
    store.questions[i.value].marked = !store.questions[i.value].marked;
};

const prevQuestion = () => {
    if (i.value > 0) {
        i.value--;
    }
};

const nextQuestion = () => {
    if (i.value < store.questions.length - 1) {
        i.value++;
    }
};
const emits = defineEmits(['submit'])
const submitText = ref('您确定要提交试卷吗？提交后将无法继续作答。')

const handleSubmit = async (timeout?: boolean) => {
    const data = store.questions.map((q: any) => {
        return {
            ...q,
            answer: JSON.stringify(q.answer)
        }
    })
    if (!timeout) {
        // 提交试卷
        const count = store.questions.filter((q: any) => !q.answer).length
        if (count > 0) {
            submitText.value = `您还有${count}道题未完成，` + submitText.value
        }
        try {
            const is_confirm = await ElMessageBox.confirm(submitText.value, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
                center: true,
            })
            if (is_confirm) {
                console.log(data);
                emits('submit')
            }
        } catch (error) {
            submitText.value = '您确定要提交试卷吗？提交后将无法继续作答。'
        }

    } else {
        ElMessageBox.close()
        // 直接提交
        await ElMessageBox.confirm('答题时间结束，自动提交试卷', '通知', {
            confirmButtonText: '确定',
            type: 'warning',
            center: true,
            showCancelButton: false,
            closeOnClickModal: false,
            showClose: false,
            closeOnPressEscape: false,
        })
        console.log(data)
        emits('submit')

    }

};
defineExpose({
    handleSubmit
})
</script>

<style scoped>
:deep(.el-drawer__body) {
    padding: 2px !important;
}
</style>