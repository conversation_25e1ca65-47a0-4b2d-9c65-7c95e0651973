<template>
    <div class="flex-1 flex flex-col overflow-y-auto">
        <div class="text-gray-700 mb-4">
            {{ content.content }}
            <span>({{ question.score }}分)</span>
        </div>
        <el-radio-group v-model="question.answer" class="flex flex-col space-y-3">
            <el-radio class="!mr-0 w-full" v-for="(option, idx) in JSON.parse(content.options)" :key="idx"
                :value="option.id">
                {{ option.content }}
            </el-radio>

        </el-radio-group>
    </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';

const props = defineProps<{
    question: any
}>()
const content = computed(() => JSON.parse(props.question.content))
</script>

<style scoped>
.el-radio-group {
    align-items: flex-start !important;
}

:deep(.el-radio) {
    /* align-items: flex-start; */
    height: auto !important;
    vertical-align: text-top;
}

:deep(.el-radio__label) {
    flex: 1;
    display: block;
    white-space: normal;
    word-break: break-all;
}
</style>