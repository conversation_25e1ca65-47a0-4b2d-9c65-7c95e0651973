<template>
    <div class="flex-1" v-loading="loading" element-loading-text="加载中...">
        <iframe v-if="scoHref" id="scoFrame" :src="scoHref" width="100%" height="100%"></iframe>
    </div>
</template>

<script setup lang="ts">
import { onMounted, ref, watch } from "vue";
import { useUserStore } from "@/store/user";
import { initialize, navigate, getLastError, virtualNavigate } from "@/api/scormService";
import { injectSCORMAPI } from "@/scorm/scormApi";
import { usePaperStore } from "@/store/paper";

const paperStore = usePaperStore()
onMounted(() => {
    paperStore.complete = false
    paperStore.is_courseware = true
})

const props = defineProps<{
    question: any,
}>();

const content: any = ref({})

const userStore = useUserStore();
const emits = defineEmits(['close'])

//会话id
const sessionId = ref<number | null>(null);
//课件地址
const scoHref = ref<string>("");
const loading = ref(true);
const base = "/courseware-assets";

watch(() => userStore.scormTerminated, newVal => {
    console.log("terminated", newVal);
    if (newVal) {
        navigatePage("continue");
    }
});

/** 虚拟课件导航 */
const virtualNavigatePage = async () => {
    console.log("virtualNavigatePage");
    try {
        const res = await virtualNavigate({
            courseware_id: content.value.id
        });
        if (res.code == 0) {
            scoHref.value = `${base}/${content.value.chapter_id}/${content.value.id}/${res.data || ""}?userid=${content.value.user_id}&courseid=${content.value.id}`;
        }
    } catch { }
    finally {
        loading.value = false;
    }
}

/** 理论课件页面导航 */
const navigatePage = async (request: string = "start") => {
    try {
        const res = await navigate({
            session_id: sessionId.value!,
            request,
        });
        if (res.code == 0) {
            scoHref.value = `${base}/${content.value.chapter_id}/${content.value.id}/${res.data?.href || ""}`;
        }
    } catch (e) {
        const lastError = await getLastError({
            session_id: sessionId.value!
        });
        console.log(lastError);
    }
};

/** 初始化课件页面 */
const initialPlayer = async () => {
    try {
        const res: any = await initialize({ sco_id: content.value.id });
        if (res.code == 0) {
            const data = res.data || {}
            sessionId.value = data.session_id;
            userStore.scormSessionId = data.session_id;
            userStore.scormTerminated = false;
            injectSCORMAPI(data.session_id); // 注入 API_1484_11
            navigatePage();
            console.log(data);
        }
    } catch (err) {
        console.error("初始化失败", err);
    } finally {
        loading.value = false;
    }
}

watch(() => props.question, newVal => {
    content.value = JSON.parse(newVal.content);
    if (content.value.courseware_type == "theory_courseware") {
        initialPlayer();
    } else {
        virtualNavigatePage();
    }
}, {
    immediate: true
})
defineExpose({
    sessionId
})
</script>