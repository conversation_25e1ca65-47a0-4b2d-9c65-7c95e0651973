<template>
    <div class="flex-1 overflow-auto space-y-6">
        <div class="bg-white p-6 rounded-lg shadow-sm">
            <h3 class="text-lg font-medium mb-4">基础设置</h3>
            <div class="grid grid-cols-3 gap-6">
                <el-tree-select placeholder="选择课件" v-model="courseId" :data="courses" :render-after-expand="false"
                    class="w-full" />

                <el-select v-model="assessmentType" placeholder="考核类型" class="w-full">
                    <el-option v-for="type in assessmentTypes" :key="type.value" :label="type.label"
                        :value="type.value" />
                </el-select>

                <el-input-number v-model="paperCount" :min="1" :max="5" placeholder="组卷套数" class="w-full" />
            </div>
        </div>

        <div class="bg-white p-6 rounded-lg shadow-sm">
            <h3 class="text-lg font-medium mb-4">题型分布</h3>
            <el-table :data="questionTypes" border>
                <el-table-column label="题型" prop="name" width="180" />
                <el-table-column label="数量" width="180">
                    <template #default="{ row }">
                        <el-input-number v-model="row.count" :min="0" :max="20" />
                    </template>
                </el-table-column>
                <el-table-column label="单题分值">
                    <template #default="{ row }">
                        <el-input-number v-model="row.score" :min="1" :max="20" />
                    </template>
                </el-table-column>
                <el-table-column label="总分">
                    <template #default="{ row }">
                        {{ row.count * row.score }}
                    </template>
                </el-table-column>
            </el-table>
        </div>

        <div class="bg-white p-6 rounded-lg shadow-sm">
            <h3 class="text-lg font-medium mb-4">难度分布</h3>
            <div class="space-y-4">
                <div v-for="item in difficultyLevels" :key="item.key" class="flex items-center">
                    <span class="w-24">{{ item.label }}</span>
                    <el-slider v-model="item.value" :max="100" class="flex-1 mx-4" />
                    <span class="w-16 text-right">{{ item.value }}%</span>
                </div>
            </div>
        </div>

        <div class="bg-white p-6 rounded-lg shadow-sm">
            <h3 class="text-lg font-medium mb-4">知识点覆盖</h3>
            <el-checkbox-group v-model="selectedKnowledgePoints">
                <el-checkbox v-for="point in knowledgePoints" :key="point.value" :label="point.value" class="mr-6 mb-3">
                    {{ point.label }}
                </el-checkbox>
            </el-checkbox-group>
        </div>

        <div class="flex justify-end space-x-4">
            <el-button type="info" @click="resetForm">重置</el-button>
            <el-button type="primary" @click="generatePaper" :disabled="!isFormValid">
                生成试卷
            </el-button>
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
const assessmentType = ref('');
const paperCount = ref(1);
const selectedKnowledgePoints = ref(['loop', 'condition']);
const courseId = ref('');

const props = defineProps<{
    questionTypes: any[],
    difficultyLevels: any[],
    courses: any[]
}>();

const assessmentTypes = [
    { value: 'final', label: '期末考试' },
    { value: 'midterm', label: '期中考试' },
    { value: 'quiz', label: '随堂测验' }
];
const knowledgePoints = [
    { value: 'loop', label: '循环结构' },
    { value: 'condition', label: '条件语句' },
    { value: 'function', label: '函数' },
    { value: 'array', label: '数组' },
    { value: 'string', label: '字符串' },
    { value: 'class', label: '类与对象' }
];
const isFormValid = computed(() => {
    return courseId.value && assessmentType.value && selectedKnowledgePoints.value.length > 0;
});
function resetForm() {
    courseId.value = '';
    assessmentType.value = '';
    paperCount.value = 1;
    props.questionTypes.forEach(type => {
        type.count = 0;
        type.score = type.value === 'single' ? 2 : type.value === 'multiple' ? 4 : 10;
    });
    props.difficultyLevels.forEach(level => {
        level.value = level.key === 'easy' ? 30 : level.key === 'medium' ? 50 : 20;
    });
    selectedKnowledgePoints.value = ['loop', 'condition'];
}

function generatePaper() {
    // 实现随机组卷逻辑
}
</script>

<style scoped></style>