<template>
    <div class="flex-1 flex flex-col overflow-y-auto">
        <div class="flex space-x-2 items-center flex-wrap space-y-3">
            <div v-for="(item, index) in parsedContent" :key="index">
                <span v-if="!item.isInput">{{ item.text }}</span>
                <el-input v-model.trim="answer[index]" v-else placeholder="请输入答案" @blur="handleBlur(answer[index])" />
            </div>
            <div class="text-gray-700 mb-2">
                <span>({{ question.score }}分)</span>
            </div>
        </div>

    </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue';

const props = defineProps<{
    question: any
}>()
const content = computed(() => JSON.parse(props.question.content))
const answer: any = ref({})
const handleBlur = (value: any) => {
    // 如果 每个输入框都为空，则清空答案
    if (Object.values(answer.value).every((item: any) => !item)) props.question.answer = ''
    if (!value) return
    props.question.answer = answer.value
}
const parsedContent = computed(() => {
    const fill_content = content.value.content
    const arr = []
    const reg = /{content}/g
    let i = 0
    let match

    while ((match = reg.exec(fill_content)) !== null) {
        if (match.index > i) {
            arr.push({ text: fill_content.slice(i, match.index), isInput: false })
        }
        arr.push({ text: '', isInput: true })
        i = match.index + match[0].length
    }

    if (i < fill_content.length) {
        arr.push({ text: fill_content.slice(i), isInput: false })
    }

    return arr
})
// console.log(parsedContent.value, '---')
watch(() => props.question, () => {
    if (props.question.answer) {
        answer.value = props.question.answer
    } else {
        answer.value = {}
    }
}, {
    immediate: true
})
</script>

<style scoped></style>