<template>
    <!-- 考试发布 -->
    <div class="space-y-6 w-full flex-1 overflow-auto flex flex-col">
        <div class="flex justify-between items-center border-b border-gray-200 pb-6">
            <div class="space-x-4 flex items-center">
                <el-input v-model="examSearchKeyword" placeholder="搜索考试" class="w-60" clearable>
                    <template #prefix>
                        <el-icon>
                            <Search />
                        </el-icon>
                    </template>
                </el-input>
                <el-select v-model="examStatus" placeholder="考试状态" class="w-40">
                    <el-option label="全部" value="" />
                    <el-option label="未开始" value="pending" />
                    <el-option label="进行中" value="processing" />
                    <el-option label="已结束" value="finished" />
                </el-select>
            </div>
            <el-button type="primary" icon="Plus" @click="showExamForm = true">发布考试</el-button>
        </div>

        <div class="flex-1 overflow-auto ">
            <el-table :data="exams" border>
                <el-table-column :show-overflow-tooltip="true" prop="title" label="考试标题" />
                <el-table-column :show-overflow-tooltip="true" prop="paper" label="试卷" />
                <el-table-column :show-overflow-tooltip="true" prop="course" label="课程" />
                <el-table-column :show-overflow-tooltip="true" label="考试时间">
                    <template #default="{ row }">
                        <div>{{ row.startTime }} ~ {{ row.endTime }}</div>
                        <div class="text-gray-500 text-sm">时长：{{ row.duration }}分钟</div>
                    </template>
                </el-table-column>
                <el-table-column prop="status" label="状态" width="100">
                    <template #default="{ row }">
                        <el-tag
                            :type="row.status === 'pending' ? 'info' : row.status === 'processing' ? 'success' : 'warning'">
                            {{ row.status === 'pending' ? '未开始' : row.status === 'processing' ? '进行中' : '已结束' }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="操作" width="180">
                    <template #default="{ row }">
                        <div class="space-x-2">
                            <el-button type="primary" link @click="viewExamDetail(row)">查看</el-button>
                            <el-button type="primary" link @click="editExam(row)"
                                :disabled="row.status !== 'pending'">编辑</el-button>
                            <el-button type="danger" link @click="cancelExam(row)"
                                :disabled="row.status !== 'pending'">取消</el-button>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
        </div>

        <div class="flex justify-center mt-4">
            <el-pagination v-model:current-page="examCurrentPage" v-model:page-size="examPageSize" :total="exams.length"
                layout=" prev, pager, next" />
        </div>
    </div>

    <!-- 发布考试表单 -->
    <el-dialog v-model="showExamForm" title="发布考试" width="600px">
        <el-form :model="examForm" label-width="100px">
            <el-form-item label="考试标题">
                <el-input v-model="examForm.title" placeholder="请输入考试标题" />
            </el-form-item>
            <el-form-item label="选择试卷">
                <el-select v-model="examForm.paperId" placeholder="请选择试卷" class="w-full">
                    <el-option v-for="paper in availablePapers" :key="paper.id" :label="paper.title"
                        :value="paper.id" />
                </el-select>
            </el-form-item>
            <el-form-item label="考试时间">
                <el-date-picker v-model="examForm.timeRange" type="datetimerange" range-separator="至"
                    start-placeholder="开始时间" end-placeholder="结束时间" class="w-full" />
            </el-form-item>
            <el-form-item label="考试时长">
                <el-input-number v-model="examForm.duration" :min="30" :max="180" class="w-full" />
                <div class="text-gray-500 text-sm mt-1">单位：分钟</div>
            </el-form-item>
        </el-form>
        <template #footer>
            <el-button @click="showExamForm = false">取消</el-button>
            <el-button type="primary" @click="submitExamForm">确认</el-button>
        </template>
    </el-dialog>
</template>
<script lang="ts" setup>
import { ref, } from 'vue';

// 考试发布相关
const examSearchKeyword = ref('');
const examStatus = ref('');
const examCurrentPage = ref(1);
const examPageSize = ref(10);
const examTotal = ref(100);
const showExamForm = ref(false);

const examForm = ref({
    title: '',
    paperId: '',
    timeRange: [],
    duration: 120
});

const availablePapers = ref([
    { id: 'p001', title: '2024春季学期Python程序设计期末考试A卷' },
    { id: 'p002', title: '2023秋季学期Java程序设计期中考试' }
]);

const exams = ref([
    {
        id: 'e001',
        title: '2024春季学期Python程序设计期末考试',
        paper: '2024春季学期Python程序设计期末考试A卷',
        course: 'Python 程序设计',
        startTime: '2024-01-20 14:00:00',
        endTime: '2024-01-20 16:00:00',
        duration: 120,
        status: 'pending'
    },
    {
        id: 'e002',
        title: '2023秋季学期Java程序设计期中考试',
        paper: '2023秋季学期Java程序设计期中考试',
        course: 'Java 程序设计',
        startTime: '2023-12-25 09:00:00',
        endTime: '2023-12-25 11:00:00',
        duration: 120,
        status: 'processing'
    },
    {
        id: 'e003',
        title: '数据结构与算法第一次月考',
        paper: '数据结构与算法第一次月考',
        course: '数据结构',
        startTime: '2023-12-15 14:00:00',
        endTime: '2023-12-15 15:30:00',
        duration: 90,
        status: 'finished'
    }
]);



// 考试发布相关方法
function viewExamDetail(exam: any) {
    console.log('查看考试详情:', exam);
}

function editExam(exam: any) {
    console.log('编辑考试:', exam);
}

function cancelExam(exam: any) {
    console.log('取消考试:', exam);
}

function submitExamForm() {
    console.log('提交考试表单:', examForm.value);
    showExamForm.value = false;
}
</script>
