<template>
    <div class="flex-1 overflow-hidden bg-gray-50 flex flex-col">
        <!-- 顶部标签页 -->
        <div class="mb-4">
            <el-tabs v-model="activeTab" class="custom-tabs">
                <el-tab-pane label="选择题库" name="questions">
                    <div class="flex gap-4 ">
                        <my-select class="max-w-xs" v-model="courseId" placeholder="请选择课程" :func="getCourseList"
                            labelKey="course.name" valueKey="course.id" searchKey="name" />

                        <el-input v-model="searchQuestion" placeholder="搜索题目" class="max-w-xs" clearable>
                            <template #prefix>
                                <el-icon class="text-gray-400">
                                    <Search />
                                </el-icon>
                            </template>
                        </el-input>
                        <el-button @click="getQuestions">搜索</el-button>
                    </div>
                </el-tab-pane>
                <el-tab-pane label="选择课件" name="courseware">
                    <div class="flex gap-4 ">
                        <my-select class="max-w-xs" v-model="courseId" placeholder="请选择课程" :func="getCourseList"
                            labelKey="course.name" valueKey="course.id" searchKey="name" />
                        <el-input v-model="searchCourseware" placeholder="搜索课件" class="max-w-xs" clearable>
                            <template #prefix>
                                <el-icon class="text-gray-400">
                                    <Search />
                                </el-icon>
                            </template>
                        </el-input>
                        <el-button @click="getCoursewares">搜索</el-button>
                    </div>
                </el-tab-pane>
            </el-tabs>
        </div>

        <!-- 主体内容区 -->
        <div class="flex gap-6 flex-1 overflow-hidden">
            <!-- 左侧题目列表 -->
            <div class="w-1/2 bg-white rounded-lg shadow-sm p-6 overflow-y-auto">
                <h2 class="text-lg font-medium mb-4">可选题目</h2>
                <template v-if="!courseId">
                    <div class="text-center text-xl mt-10 text-slate-400">请选择课程</div>
                </template>
                <template v-else>
                    <el-table v-loading="loading" :data="availableQuestions" row-key="id" border>
                        <el-table-column width="55">
                            <template #header>
                                <el-tooltip :content="selectAllLabel" placement="top">
                                    <el-checkbox :model-value="isAllSelected" :indeterminate="isIndeterminate"
                                        @change="toggleSelectAll" />
                                </el-tooltip>
                            </template>
                            <template #default="{ row }">
                                <el-checkbox v-model="row.selected" @change="handleCheckboxChange(row)"></el-checkbox>
                            </template>
                        </el-table-column>
                        <el-table-column prop="title" :label="activeTab == 'questions' ? '试题标题' : '课件名称'"
                            show-overflow-tooltip />
                        <el-table-column v-if="activeTab == 'courseware'" prop="file_name" label="文件名" width="300"
                            show-overflow-tooltip />
                        <el-table-column align="center" label="类型" width="100">
                            <template #default="{ row }">
                                <el-tag :type="tagTypeMap[row.question_type || row.courseware_type]">{{
                                    typeMap[row.question_type || row.courseware_type] }}</el-tag>
                            </template>
                        </el-table-column>
                    </el-table>
                    <div class="flex justify-center">
                        <el-pagination :current-page="page" :page-size="pageSize" :total="total"
                            layout=" prev, pager, next" @current-change="handlePageChange" />
                    </div>
                </template>

            </div>


            <!-- 右侧已选题目 -->
            <div ref="container" class="w-1/2 bg-white rounded-lg shadow-sm p-6 overflow-y-auto">
                <div class="flex justify-around items-center mb-4">
                    <h2 class="text-lg font-medium ">已选题目</h2>
                    <div>总分：{{ totalScore }}</div>
                    <el-button type="primary" @click="generatePaper">生成</el-button>
                </div>

                <!-- 使用普通表格 -->
                <el-table ref="selectedTable" :data="paper_questions" row-key="question_key">
                    <el-table-column width="55" align="center">
                        <template #default>
                            <el-icon class="drag-handle cursor-move" size="20">
                                <SwitchFilled />
                            </el-icon>
                        </template>
                    </el-table-column>

                    <el-table-column label="序号" width="55" align="center">
                        <template #default="{ $index }"> {{ $index + 1 }} </template>
                    </el-table-column>
                    <el-table-column label="试题标题" show-overflow-tooltip>
                        <template #default="{ row }">
                            {{ JSON.parse(row.content).title }}
                        </template>
                    </el-table-column>

                    <el-table-column align="center" label="试题类型" width="100">
                        <template #default="{ row }">
                            <el-tag
                                :type="tagTypeMap[JSON.parse(row.content).question_type || JSON.parse(row.content).courseware_type]">
                                {{
                                    typeMap[JSON.parse(row.content).question_type ||
                                    JSON.parse(row.content).courseware_type]
                                }}
                            </el-tag>
                        </template>
                    </el-table-column>

                    <el-table-column align="center" label="设置分值" width="100">
                        <template #default="{ row }">
                            <el-input v-model.number="row.score" @change="getTotalScore" />
                        </template>
                    </el-table-column>

                    <el-table-column align="center" label="操作" width="100">
                        <template #default="{ row }">
                            <el-button size="small" type="danger" @click="removeQuestion(row)">删除</el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <!-- 右侧已选题目结束 -->
        </div>

        <!-- 生成试卷弹窗 -->
        <el-dialog v-model="show_dialog" title="生成试卷">
            <el-form :model="formData" :rules="rules" label-width="120px" ref="formRef">
                <el-form-item label="试卷名称" prop="title">
                    <el-input v-model="formData.title" placeholder="请输入试卷名称" />
                </el-form-item>
                <el-form-item label="试题数量">
                    <el-input :value="paper_questions.length" disabled />
                </el-form-item>
                <el-form-item label="总分">
                    <el-input :value="totalScore" disabled />
                </el-form-item>
                <el-form-item label="考试时长" prop="total_minutes">
                    <el-input v-model.number="formData.total_minutes" placeholder="请输入考试时长">
                        <template #append>分钟</template>
                    </el-input>
                </el-form-item>
                <el-form-item>
                    <el-button @click="show_dialog = false">取消</el-button>
                    <el-button type="primary" @click="savePaper">提交</el-button>
                </el-form-item>
            </el-form>
        </el-dialog>
    </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch, nextTick, onMounted, toRaw } from 'vue';
import { Search } from '@element-plus/icons-vue';
import { getCourseList } from '@/api/course';
import { getQuestionListByCourse } from '@/api/question';
import { getCoursewareList } from '@/api/courseware';
import { confirmMsg, errMsg, successMsg } from '@/utils/msg';
import { createPaperDraft, editPaperDraft, getPaperDetail } from '@/api/paper';
import Sortable from 'sortablejs';
const selectedTable = ref();
const props = defineProps<{
    paper_id?: number,
    is_edit?: boolean,
}>()

watch(() => props.paper_id, async (val) => {
    if (val) {
        const res = await getPaperDetail(val)
        console.log(res, 'res')
        paper_questions.value = res.paper_questions,
            formData.value = {
                id: val,
                title: res.papers.title,
                total_minutes: res.papers.total_minutes
            }
    }
}, {
    immediate: true
})
onMounted(() => {
    initSortable();
});
// 初始化拖拽功能
const initSortable = () => {
    nextTick(() => {
        const tbody = selectedTable.value?.$el?.querySelector('.el-table__body-wrapper tbody');
        if (tbody) {
            Sortable.create(tbody, {
                animation: 150,
                ghostClass: 'sortable-ghost',
                onEnd: ({ newIndex, oldIndex }) => {
                    // 添加有效性检查
                    if (typeof oldIndex === 'number' && typeof newIndex === 'number' &&
                        oldIndex >= 0 && newIndex >= 0) {
                        const currRow = paper_questions.value[oldIndex];
                        paper_questions.value.splice(oldIndex, 1);
                        paper_questions.value.splice(newIndex, 0, currRow);
                    }
                },
            });
        }
    });
};


const emits = defineEmits(['create'])
const typeMap: any = {
    'single': '单选题',
    'multiple': '多选题',
    'judge': '判断题',
    'fill': '填空题',
    'short': '简答题',
    'theory_courseware': '理论课件',
    'virtual_courseware': '虚拟课件',
    'discuss': '论述题',
    'analyze': '分析题',
    'comprehensive': '综合题',
    'self': '自拟题'
}
const tagTypeMap: any = {
    'single': 'info',
    'multiple': 'success',
    'judge': 'warning',
    'fill': 'primary',
    'short': 'danger',
    'theory_courseware': 'primary',
    'virtual_courseware': 'success',
    'discuss': 'primary',
    'analyze': 'success',
    'comprehensive': 'warning',
    'self': 'info'
}
const scoreMap: any = {
    'single': 5,
    'multiple': 5,
    'judge': 2,
    'fill': 5,
    'short': 10,
    'theory_courseware': 10,
    'virtual_courseware': 10,
    'discuss': 10,
    'analyze': 10,
    'comprehensive': 10,
    'self': 10
}

const courseId = ref('');
watch(courseId, (val) => {
    if (val) {
        if (activeTab.value === 'questions') {
            getQuestions()
        } else {
            getCoursewares()
        }
    }
})
const activeTab = ref('questions');


watch(activeTab, (val) => {
    page.value = 1
    if (val === 'questions') {
        if (courseId.value) {
            getQuestions()
        }
    } else if (val === 'courseware') {
        if (courseId.value) {
            getCoursewares()
        }
    }
})
const searchCourseware = ref('');
const searchQuestion = ref('');
const availableQuestions: any = ref([]);
const paper_questions: any = ref([]); // 已选题目列表


const container = ref()
watch(() => paper_questions.value.length, () => {
    if (container.value) {
        nextTick(() => {
            container.value.scrollTop = container.value.scrollHeight
        })
    }
})

const page = ref(1);
const pageSize = ref(100);
const total = ref(0);
const loading = ref(false)

const handlePageChange = (newPage: number) => {
    page.value = newPage;
    if (activeTab.value === 'questions') {
        getQuestions()
    } else if (activeTab.value === 'coursewares') {
        getCoursewares()
    }
}
const selectAllLabel = computed(() => {
    if (isAllSelected.value) return '取消全选'
    if (isIndeterminate.value) return '全选'
    return '全选'
})
// 当前页所有题目是否全选
const isAllSelected = computed(() => {
    return availableQuestions.value.every((item: any) => item.selected)
})

// 是否部分选中（半选状态）
const isIndeterminate = computed(() => {
    const selectedCount = availableQuestions.value.filter((item: any) => item.selected).length
    return selectedCount > 0 && selectedCount < availableQuestions.value.length
})

// 切换全选状态
const toggleSelectAll = (checked: boolean) => {
    availableQuestions.value.forEach((item: any) => {
        if (item.selected !== checked) {
            item.selected = checked
            handleCheckboxChange(item)
        }
    })
}
// 根据课程ID获取题目列表
const getQuestions = async () => {
    loading.value = true
    const params = {
        page: page.value,
        page_size: pageSize.value,
        status: 'published',
        course_id: courseId.value,
        ...(searchQuestion.value && { name: searchQuestion.value })
    }
    const res = await getQuestionListByCourse(params)
    availableQuestions.value = res.list || []
    await availableQuestions.value.forEach((item: any) => {
        item.selected = false
        if (paper_questions.value.some((p: any) => p.question_key == item.question_key)) {
            item.selected = true
        }
    })
    total.value = res.total || 0
    loading.value = false
    console.log(res, 'res---')
}

// 根据课程获取课件列表
const getCoursewares = async () => {
    loading.value = true
    const params = {
        course_id: courseId.value,
        page: page.value,
        page_size: pageSize.value,
        status: 'published',
        ...(searchCourseware.value && { name: searchCourseware.value })
    }
    const res = await getCoursewareList(params)
    availableQuestions.value = res.list || []
    await availableQuestions.value.forEach((item: any) => {
        item.selected = false
        if (paper_questions.value.some((p: any) => p.question_key == item.question_key)) {
            item.selected = true
        }
    })
    total.value = res.total || 0
    loading.value = false
    console.log(res, '!res---')
}

const handleCheckboxChange = (row: any) => {
    console.log(row, 'row---')
    if (row.selected) {
        const newQuestion = {
            question_category: row.question_category,
            question_key: row.question_key,
            question_id: row.id,
            content: JSON.stringify(row),
            score: row.score || scoreMap[row.question_type || row.courseware_type] || 0,
            order_num: paper_questions.value.length + 1
        }

        paper_questions.value = [...paper_questions.value, newQuestion]
        getTotalScore()
    } else {
        const index = paper_questions.value.findIndex((item: any) => item.question_key == row.question_key)
        if (index !== -1) {
            paper_questions.value.splice(index, 1)
        }
    }
}

const removeQuestion = (row: any) => {
    confirmMsg('确定要删除吗？', '提示', action => {
        if (action) {
            const index = paper_questions.value.findIndex((item: any) => item.question_key == row.question_key)
            //if (index !== -1) {
            paper_questions.value.splice(index, 1);
            // 更新可选题目选中状态
            availableQuestions.value.forEach((item: any) => {
                if (item.question_key == row.question_key) {
                    item.selected = false;
                }
            });
            getTotalScore()
        }
    })
}

const totalScore = ref(0)
const getTotalScore = () => {
    totalScore.value = paper_questions.value.reduce((sum: number, item: any) => sum + (item.score || 0), 0)
}



// dialog
const show_dialog = ref(false)
const generatePaper = () => {
    if (!paper_questions.value.length) {
        return errMsg('请添加题目')
    }
    console.log(paper_questions.value, 'paper_questions---')
    show_dialog.value = true
}

const formRef = ref()
const formData: any = ref(
    {
        title: '',
        total_minutes: ''
    })

const rules = {
    title: [
        { required: true, message: '请输入试卷名称', trigger: 'blur' }
    ],
    total_minutes: [
        { required: true, message: '请输入考试时长', trigger: 'blur' }
    ]
}
const savePaper = () => {

    formRef.value.validate(async (valid: boolean) => {
        if (valid) {

            const papers = {
                ...formData.value,
                course_id: courseId.value,
                question_count: paper_questions.value.length,
                total_score: totalScore.value,
            }
            const questions: any = JSON.parse(JSON.stringify(paper_questions.value))
            questions.forEach((item: any, index: number) => {
                item.order_num = index + 1
            })
            const data = {
                papers,
                paper_questions: questions
            }

            console.log(data, '----')
            const res = props.is_edit ? await editPaperDraft(data) : await createPaperDraft(data)
            successMsg(res.message)
            show_dialog.value = false
            emits('create')
        }
    })
}

</script>

<style scoped>
.question-card {
    background-color: #ffffff;
    transition: all 0.3s ease;
}

.question-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.selected-question-item {
    background-color: #ffffff;
}

.custom-tabs :deep(.el-tabs__item) {
    font-size: 16px;
    padding: 0 24px;
}

.custom-tabs :deep(.el-tabs__item.is-active) {
    font-weight: 600;
}

.custom-tabs :deep(.el-tabs__nav-wrap::after) {
    height: 1px;
}

/* 拖拽手柄样式 */
.drag-handle {
    cursor: move;
    opacity: 0.6;
    transition: opacity 0.3s;
}

.drag-handle:hover {
    opacity: 1;
}

/* 拖拽时的样式 */
.sortable-ghost {
    opacity: 0.5;
    background-color: #ebeef5;
}
</style>
