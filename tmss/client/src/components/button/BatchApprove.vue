<template>
  <div>
    <!-- 主按钮 -->
    <el-button 
      type="danger" 
      class="!rounded-button whitespace-nowrap mr-3 ml-3" 
      :loading="loading"
      :disabled="props.dataList.length === 0"
      @click="handleSubmit"
    >
      <slot>批量提交</slot>
    </el-button>

    <!-- 工作流选择弹窗 -->
    <el-dialog v-model="showWorkflowDialog" title="选择审核流程" width="500px" append-to-body>
      <el-select v-model="selectedWorkflowId" placeholder="请选择审核流程">
        <el-option 
          v-for="workflow in workflowList" 
          :key="workflow.id" 
          :label="workflow.name" 
          :value="workflow.id"
        />
      </el-select>

      <template #footer>
        <el-button @click="showWorkflowDialog = false">取消</el-button>
        <el-button type="primary" @click="doSubmit">确定提交</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { ElMessage } from 'element-plus';
import { getWorkflowList, batchCreateWorkflow } from '@/api/workflow';

// 定义组件属性
const props = defineProps({
  // 审核模块代码（如：'courseware'）
  approveCode: {
    type: String,
    required: true
  },

  // 要审核的数据列表，每个对象包含id和title
  dataList: {
    type: Array as () => Array<{ id: number | string, title: string }>,
    required: true,
    default: () => []
  }
});

// 定义事件
const emit = defineEmits(['success', 'error']);

// 状态变量
const loading = ref(false);
const showWorkflowDialog = ref(false);
const workflowList = ref<any[]>([]);
const selectedWorkflowId = ref(0);

// 获取工作流列表
const fetchWorkflows = async () => {
    const response = await getWorkflowList(props.approveCode);
    workflowList.value = response || [];
    return response;
};

// 批量提交审核
const submitBatchApproval = async (workflowId: number) => {
  loading.value = true;
  
  try {
    // 批量创建审核请求

    const promises = props.dataList.map(item => 
      {
       return {
        module_key: props.approveCode,
        data_id: item.id,
        workflow_id: workflowId,
        title: item.title
       }
      }
    );

    // 并行执行所有请求
    await batchCreateWorkflow(promises);
    
    ElMessage.success(`成功提交 ${props.dataList.length} 条审核`);
    emit('success');
  } catch (error) {
    ElMessage.error('批量提交失败');
    emit('error', error);
  } finally {
    loading.value = false;
  }
};

// 处理提交按钮点击
const handleSubmit = async () => {
  if (props.dataList.length === 0) {
    ElMessage.warning('请选择要提交的数据');
    return;
  }

  try {
    const response = await fetchWorkflows();
    
    // 如果没有工作流或只有一个，直接提交
    if (!response || response.length <= 1) {
      const workflowId = response?.length === 1 ? response[0].id : 0;
      await submitBatchApproval(workflowId);
      return;
    }

    // 多个工作流，显示选择弹窗
    showWorkflowDialog.value = true;
    selectedWorkflowId.value = response[0]?.id || 0;
  } catch (error) {
    // 错误已在子函数处理
  }
};

// 最终提交
const doSubmit = async () => {
  if (selectedWorkflowId.value === 0 && workflowList.value.length > 0) {
    ElMessage.warning('请选择一个审核流程');
    return;
  }

  try {
    showWorkflowDialog.value = false;
    await submitBatchApproval(selectedWorkflowId.value);
  } catch (error) {
    // 错误已在子函数处理
  }
};
</script>