<template>
  <el-dialog 
    v-model="dialogVisible" 
    title="课表详情" 
    width="800px"
  >
    <el-descriptions 
      v-if="detailData" 
      :column="1" 
      border
    >
      <el-descriptions-item label="课表ID">{{ detailData.id }}</el-descriptions-item>
      <el-descriptions-item label="课表名称">{{ detailData.title }}</el-descriptions-item>
      <el-descriptions-item label="教学计划">{{ detailData.teaching_plan_name }}</el-descriptions-item>
      <el-descriptions-item label="教室">{{ detailData.classroom }}</el-descriptions-item>
      <el-descriptions-item label="授课教师">{{ detailData.teacher_name }}</el-descriptions-item>
      <el-descriptions-item label="状态">
        <el-tag :type="tagTypeMap[detailData.status]">
          {{ statusMap[detailData.status] }}
        </el-tag>
      </el-descriptions-item>
      <el-descriptions-item label="课时内容">
        <div class="bg-gray-100 p-3 rounded">{{ detailData.content || '无' }}</div>
      </el-descriptions-item>
      <el-descriptions-item label="开始时间">
        {{ formatDate(detailData.start_at) }}
      </el-descriptions-item>
      <el-descriptions-item label="结束时间">
        {{ formatDate(detailData.end_at) }}
      </el-descriptions-item>
      <el-descriptions-item label="前置课时" v-if="detailData.previous_name">
        {{ detailData.previous_name }}
      </el-descriptions-item>
    </el-descriptions>
    
    <template #footer>
      <el-button @click="dialogVisible = false">关闭</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, defineProps, defineEmits, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { getClassScheduleDetail } from '@/api/schedule';
import { formatDate } from '@/utils/date';

const props = defineProps<{
  modelValue: boolean
  rowData?: any
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: boolean): void
}>()

const dialogVisible = ref(false)
const detailData = ref<any>(null)

// 状态映射
const statusMap: any = {
  draft: '草稿',
  reviewing: '审核中',
  published: '已发布',
  rejected: '已驳回',
}

const tagTypeMap: any = {
  draft: 'primary',
  reviewing: 'warning',
  published: 'success',
  rejected: 'danger',
}

// 监听modelValue变化
watch(() => props.modelValue, async (val) => {
  dialogVisible.value = val
  if (val && props.rowData) {
    // 获取课表详情
    try {
      detailData.value = await getClassScheduleDetail(props.rowData.id)
    } catch (error) {
      ElMessage.error('获取课表详情失败')
    }
  } else {
    detailData.value = null
  }
})

// 监听dialogVisible变化
watch(dialogVisible, (val) => {
  emit('update:modelValue', val)
})
</script>