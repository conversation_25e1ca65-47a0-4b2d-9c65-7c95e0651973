<template>
  <el-dialog v-model="dialogVisible" title="编辑课表" width="800px" :close-on-click-modal="false" :destroy-on-close="true">
    <el-form ref="formRef" :model="form" :rules="rules" label-width="150px" label-position="right">
      <!-- 教学计划 -->
      <el-form-item label="关联教学计划" prop="teaching_plan_id">
        <TeachingPlanSelect v-model="form.teaching_plan_id" :initData="initPlan" />
      </el-form-item>
      <!-- 关联课程 -->
      <el-form-item label="关联课程" prop="course_id">
        <CourseSelect v-model="form.course_id" :initData="initCourse" :plan_id="form.teaching_plan_id" />
      </el-form-item>
      <!-- 课时标题 -->
      <el-form-item label="课时标题" prop="title">
        <el-input v-model="form.title" placeholder="请输入课时标题" />
      </el-form-item>

      <!-- 课时内容 -->
      <el-form-item label="课时内容" prop="content">
        <el-input v-model="form.content" type="textarea" :rows="3" placeholder="请输入课时内容" />
      </el-form-item>

      <!-- 教室 -->
      <el-form-item label="教室" prop="classroom">
        <ClassroomSelect v-model="form.classroom_id" :initData="initClassroom" />
      </el-form-item>

      <!-- 授课教师 -->
      <el-form-item label="授课教师" prop="teacher_id">
        <TeacherSelect v-model="form.teacher_id" :initData="initTeacher" />
      </el-form-item>

      <!-- 开始时间 -->
      <el-form-item label="开始时间" prop="start_at">
        <el-date-picker v-model="form.start_at" type="datetime" placeholder="请选择开始时间" :disabled-date="disabledPastDates"
          value-format="x" />
      </el-form-item>

      <!-- 结束时间 -->
      <el-form-item label="结束时间" prop="end_at">
        <el-date-picker v-model="form.end_at" type="datetime" placeholder="请选择结束时间" :disabled="!form.start_at"
          :disabled-date="disabledEndDates" value-format="x" />
      </el-form-item>

      <!-- 前置课时 -->
      <el-form-item label="前置课时" prop="previous_id">
        <ClassScheduleSelect v-model="form.previous_id" :initData="initPrevious"
          :teaching_plan_id="form.teaching_plan_id" />
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="dialogVisible = false">取消</el-button>
      <el-button type="primary" @click="submitForm">保存</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, defineProps, defineEmits, watch, computed } from 'vue';
import { ElMessage } from 'element-plus';
import { updateClassSchedule, getClassScheduleDetail } from '@/api/schedule';

const props = defineProps<{
  modelValue: boolean
  rowData?: any
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: boolean): void
  (e: 'success'): void
}>()

const dialogVisible = ref(false)
const formRef: any = ref(null)

// 表单数据
const form: any = reactive({
  id: '',
  teaching_plan_id: '',
  teaching_plan_name: '',
  course_id: '',
  course_name: '',
  title: '',
  content: '',
  classroom_id: '',
  classroom_name: '',
  teacher_id: '',
  teacher_name: '',
  start_at: '',
  end_at: '',
  previous_id: '',
  previous_name: '',
  status: ''
})

// 计算属性用于初始化数据
const initPlan = computed(() => {
  return form.teaching_plan_id ?
    [{ id: form.teaching_plan_id, name: form.teaching_plan_name }] : []
})
const initCourse = computed(() => {
  return form.course_id ? [{ id: form.course_id, name: form.course_name }] : []
})
const initTeacher = computed(() => {
  return form.teacher_id ? [{ id: form.teacher_id, username: form.teacher_name }] : []
})
const initClassroom = computed(() => {
  return form.classroom_id ? [{ id: form.classroom_id, name: form.classroom_name }] : []
})
const initPrevious = computed(() => {
  return form.previous_id > 0 ?
    [{ id: form.previous_id, title: form.previous_name }] : []
})

// 定义表单验证规则
const rules = {
  title: [{ required: true, message: '请输入课时标题', trigger: 'blur' }],
  classroom_id: [{ required: true, message: '请输入教室', trigger: 'blur' }],
  teacher_id: [{ required: true, message: '请选择授课教师', trigger: 'change' }],
  start_at: [
    { required: true, message: '请选择开始时间', trigger: 'change' },
    { validator: validateStartTime, trigger: 'change' }
  ],
  end_at: [
    { required: true, message: '请选择结束时间', trigger: 'change' },
    { validator: validateEndTime, trigger: 'change' }
  ]
}

// 时间验证方法
function validateStartTime(rule: any, value: any, callback: any) {
  if (!value) {
    callback(new Error('请选择开始时间'))
    return
  }

  const now = Date.now()
  if (value < now) {
    callback(new Error('开始时间不能早于当前时间'))
  } else {
    callback()
  }
}

function validateEndTime(rule: any, value: any, callback: any) {
  if (!value) {
    callback(new Error('请选择结束时间'))
    return
  }

  if (form.start_at && value <= form.start_at) {
    callback(new Error('结束时间必须晚于开始时间'))
  } else {
    callback()
  }
}

// 禁用过去的日期
const disabledPastDates = (time: Date) => {
  return time.getTime() < Date.now() - 24 * 3600 * 1000
}

// 禁用早于开始时间的日期
const disabledEndDates = (time: Date) => {
  if (!form.start_at) return false
  return time.getTime() < new Date(form.start_at).getTime()
}

// 监听modelValue变化
watch(() => props.modelValue, async (val) => {
  dialogVisible.value = val
  if (val && props.rowData) {
    // 获取课表详情
    try {
      const detail = await getClassScheduleDetail(props.rowData.id)

      // 填充表单数据
      Object.assign(form, {
        id: detail.id,
        teaching_plan_id: detail.teaching_plan_id,
        teaching_plan_name: detail.teaching_plan_name,
        course_id: detail.course_id,
        course_name: detail.course_name,
        title: detail.title,
        content: detail.content,
        classroom_id: detail.classroom_id,
        classroom_name: detail.classroom_name,
        teacher_id: detail.teacher_id,
        teacher_name: detail.teacher_name,
        start_at: detail.start_at * 1000, // 秒转毫秒
        end_at: detail.end_at * 1000,     // 秒转毫秒
        previous_id: detail.previous_id,
        previous_name: detail.previous_name,
        status: detail.status
      })
    } catch (error) {
      ElMessage.error('获取课表详情失败')
    }
  }
})

// 监听dialogVisible变化
watch(dialogVisible, (val) => {
  emit('update:modelValue', val)
})

// 提交表单
const submitForm = async () => {
  try {
    // 表单验证
    await formRef.value.validate()

    // 准备提交数据
    const payload = {
      id: form.id,
      teaching_plan_id: form.teaching_plan_id * 1,
      course_id: form.course_id * 1,
      classroom_id: form.classroom_id * 1,
      title: form.title,
      content: form.content,
      teacher_id: form.teacher_id,
      start_at: Math.floor(form.start_at / 1000), // 毫秒转秒
      end_at: Math.floor(form.end_at / 1000),     // 毫秒转秒
      previous_id: form.previous_id * 1
    }

    // 调用更新接口
    await updateClassSchedule(payload)

    ElMessage.success('课时更新成功')
    dialogVisible.value = false
    emit('success')
  } catch (error: any) {
    console.log(error)
    ElMessage.error('更新失败')
  }
}
</script>