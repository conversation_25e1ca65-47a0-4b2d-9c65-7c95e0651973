<template>
  <el-dialog v-model="dialogVisible" title="批量创建课表" width="800px" :close-on-click-modal="false"
    :destroy-on-close="true">
    <el-form ref="formRef" :model="form" :rules="rules" label-width="150px" label-position="right">
      <!-- 关联教学计划 -->
      <el-form-item label="关联教学计划" prop="teaching_plan_id">
        <TeachingPlanSelect v-model="form.teaching_plan_id" :initData="initPlan" />
      </el-form-item>
      <!-- 关联课程 -->
      <el-form-item label="关联课程" prop="course_id">
        <CourseSelect v-model="form.course_id" :initData="initCourse" :plan_id="form.teaching_plan_id" />
      </el-form-item>
      <!-- 课表名称 -->
      <el-form-item label="课表名称" prop="title">
        <el-input v-model="form.title" placeholder="请输入课表名称" />
      </el-form-item>

      <!-- 开始日期 -->
      <el-form-item label="开始日期" prop="start_date">
        <el-date-picker v-model="form.start_date" type="date" placeholder="请选择开始日期" format="YYYY-MM-DD"
          value-format="YYYY-MM-DD" />
      </el-form-item>

      <!-- 每天上课时间 -->
      <el-form-item label="每天上课时间" prop="class_time">
        <el-time-picker v-model="form.class_time" placeholder="请选择上课时间" format="HH:mm" value-format="HH:mm" />
      </el-form-item>

      <!-- 课时时长（分钟） -->
      <el-form-item label="课时长（分钟）" prop="class_hours">
        <el-input-number v-model="form.class_hours" controls-position="right" :min="1" :max="1000" />
      </el-form-item>

      <!-- 课时数 -->
      <el-form-item label="课数（个）" prop="lesson_num">
        <el-input-number v-model="form.lesson_num" controls-position="right" :min="1" :max="1000" />
      </el-form-item>

      <!-- 每周上课日 -->
      <el-form-item label="每周上课日" prop="weekdays">
        <el-checkbox-group v-model="form.weekdays">
          <el-checkbox v-for="(day, index) in weekdays" :key="index" :value="index">
            {{ day }}
          </el-checkbox>
        </el-checkbox-group>
      </el-form-item>

      <!-- 教室 -->
      <el-form-item label="教室" prop="classroom_id">
        <ClassroomSelect v-model="form.classroom_id" :initData="initClassroom" />
      </el-form-item>

      <!-- 授课教师 -->
      <el-form-item label="授课教师" prop="teacher_id">
        <TeacherSelect v-model="form.teacher_id" :initData="initTeacher" />

      </el-form-item>

      <!-- 前置课时 -->
      <el-form-item label="前置课表" prop="previous_id">
        <ClassScheduleSelect v-model="form.previous_id" :initData="initPrevious"
          :teaching_plan_id="form.teaching_plan_id" />
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="dialogVisible = false">取消</el-button>
      <el-button type="primary" @click="submitForm">保存</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, defineProps, defineEmits, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { createClassSchedule } from '@/api/schedule';
const props = defineProps<{
  modelValue: boolean
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: boolean): void
  (e: 'success'): void
}>()

const dialogVisible = ref(false)
const formRef: any = ref(null)

// 表单数据
const form: any = reactive({
  id: '',
  teaching_plan_id: '',
  title: '',
  start_date: '',
  class_time: '',
  class_hours: 45,
  lesson_num: 20,
  weekdays: [] as number[],
  classroom_id: '',
  teacher_id: '',
  previous_id: ''
})

const initPlan = ref([])
const initTeacher = ref([])
const initPrevious = ref([])
const initCourse = ref([])
const initClassroom = ref([])

// 星期映射
const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']

// 定义表单验证规则
const rules = {
  teaching_plan_id: [
    { required: true, message: '请选择关联的教学计划', trigger: 'change' },
  ],
  title: [
    { required: true, message: '请输入课表名称', trigger: 'blur' },
  ],
  start_date: [
    { required: true, message: '请选择开始日期', trigger: 'change' },
  ],
  class_time: [
    { required: true, message: '请选择上课时间', trigger: 'change' },
  ],
  weekdays: [
    { required: true, message: '请选择每周上课日', trigger: 'change' },
  ],
  classroom_id: [
    { required: true, message: '请输入教室', trigger: 'blur' },
  ],
  teacher_id: [
    { required: true, message: '请选择授课教师', trigger: 'change' },
  ],
  lesson_num: [
    { required: true, message: '请输入课数', trigger: 'blur', type: 'number', min: 1 },
  ],
  class_hours: [
    { required: true, message: '请输入课程学时数', trigger: 'blur', type: 'number', min: 1 },
  ]
}

// 监听modelValue变化
watch(() => props.modelValue, (val) => {
  dialogVisible.value = val
  if (val) {
    // 重置表单
    Object.assign(form, {
      id: '',
      teaching_plan_id: '',
      title: '',
      start_date: '',
      class_time: '',
      lesson_num: 30,
      class_hours: 45,
      weekdays: [],
      classroom_id: '',
      teacher_id: '',
      previous_id: ''
    })
    initPlan.value = []
    initTeacher.value = []
    initPrevious.value = []
    initCourse.value = []
    initClassroom.value = []
    formRef.value?.resetFields()
  }
})

// 监听dialogVisible变化
watch(dialogVisible, (val) => {
  emit('update:modelValue', val)
})

// 提交表单
const submitForm = () => {
  formRef.value.validate(async (valid: boolean) => {
    if (!valid) return

    try {
      const submitData = {
        ...form,
        previous_id: form.previous_id * 1,
        teaching_plan_id: form.teaching_plan_id * 1,
        classroom_id: form.classroom_id * 1,
        course_id: form.course_id * 1,
        teacher_id: form.teacher_id * 1
      }

      await createClassSchedule(submitData)
      ElMessage.success('操作成功')
      dialogVisible.value = false
      emit('success')
    } catch (error) {
      ElMessage.error('操作失败')
    }
  })
}
</script>