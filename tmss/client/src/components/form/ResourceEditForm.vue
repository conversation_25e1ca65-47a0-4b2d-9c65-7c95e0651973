<template>
  <el-dialog v-model="dialogVisible" :title="dialogTitle" width="600px" :destroy-on-close="true">

    <el-form ref="formRef" :model="form" label-position="right" :disabled="isViewMode">
      <el-form-item label="修订说明" v-if="isReviseMode">
        <el-input v-model="revNotes" type="textarea" :rows="2" placeholder="请输入修订说明" />
      </el-form-item>
      <el-form-item label="资料名称" prop="name">
        <el-input v-model="form.name" placeholder="请输入资料名称"></el-input>
      </el-form-item>

      <el-form-item label="选择分类" prop="category_id">
        <ResourceCategorySelect v-model="form.category_id" placeholder="选择分类"/>
      </el-form-item>

      <el-form-item label="阅读时间" prop="minutes">
        <el-input v-model="form.minutes" type="number" placeholder="请输入阅读时间" class="w-full">
          <template #append>分钟</template>
        </el-input>
      </el-form-item>
    </el-form>
    <el-form-item label="资料描述" prop="description">
      <el-input v-model="form.description" type="textarea" :rows="2" placeholder="请输入资料描述"></el-input>
    </el-form-item>
    <el-form-item label="上传资料">
      <el-upload class="upload-demo" accept=".pdf,.docx,.pptx" drag action="#" :limit="1" :auto-upload="false"
        :on-change="handleFileChange" :on-remove="onFileRemove" v-model:file-list="fileList" :disabled="isViewMode">
        <el-icon class="el-icon--upload">
          <Upload />
        </el-icon>
        <div class="el-upload__text">
          将文件拖到此处，或<em>点击上传</em>，只能上传单个文件

        </div>
        <template #tip>
          <div class="el-upload__tip">
            支持PDF、DOCX、PPTX、XLSX、MD、MP4格式，文件大小不超过 500MB，如需变更文件请先移除当前文件。
          </div>
        </template>
      </el-upload>
    </el-form-item>
    <template #footer v-if="!isViewMode">
      <el-button @click="cancel">取消</el-button>
      <el-button type="primary" @click="submitForm" :loading="submitting">保存</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, reactive, defineProps, defineEmits, watch, onMounted, computed } from 'vue'
import { type FormInstance, type UploadFile, type UploadUserFile } from 'element-plus'
import { updateMaterial, uploadMaterial } from '@/api/material'
import { errMsg, msg } from '@/utils/msg'
import { createRevision, editRevision } from '@/api/revision';

// 定义props
const props = defineProps<{
  modelValue: boolean
  rowData?: any // 要编辑的数据
  dialogTitle: string
  approveCode: string
  actionType: string
}>()

// 定义emits
const emit = defineEmits<{
  (e: 'update:modelValue', value: boolean): void
  (e: 'success'): void
}>()
// 计算属性
const isViewMode = computed(() => props.actionType === 'view' || props.actionType === 'revise-view')
const isReviseMode = computed(() =>
  props.actionType === 'revise-create' ||
  props.actionType === 'revise-edit' ||
  props.actionType === 'revise-view'
)
// 响应式数据
const dialogVisible = ref(false)
const formRef = ref<FormInstance>()
const submitting = ref(false)
const loading = ref(false)
const revNotes = ref('')
const revId = ref(0)
// 表单数据
const form: any = reactive({
  id: "",
  name: "",
  category_id: "",
  minutes: "",
  description: "",
  path: "",
  ext: "",
  size: "",
})

const fileList = ref<UploadUserFile[]>([])


// 监听modelValue变化
watch(() => props.modelValue, (val) => {
  dialogVisible.value = val
  if (val && props.rowData) {
    // 弹窗打开时初始化表单数据
    initFormData()
  }
})

// 监听dialogVisible变化
watch(dialogVisible, (val) => {
  emit('update:modelValue', val)
  // 弹窗关闭时重置表单
  if (!val) {
    resetForm()
  }
})

// 初始化表单数据
const initFormData = () => {
  if (props.rowData) {
    let rowData = props.rowData
    let formData = {...rowData}
    if (props.rowData.rev) {
      revId.value = rowData.rev.id
      revNotes.value = rowData.rev.notes || ""
      formData = JSON.parse(rowData.rev.changes)
      console.log(formData)
    }
    form.id = formData.id || ""
    form.name = formData.name || ""
    form.category_id = formData.category_id || ""
    form.minutes = formData.minutes || ""
    form.description = formData.description || ""
    form.path = formData.path || ""
    form.ext = formData.ext || ""
    form.size = formData.size || ""
    fileList.value = [{ name: formData.name, url: formData.path }]
    
  }
}

// 提交表单
const submitForm = async () => {
  const valid = await formRef.value?.validate()
  if (!valid) return

  if (!form.category_id) {
    msg("warning", "请选择资料分类")
    return
  }
  if (!form.path || form.size < 1) {
    msg("warning", "请选择要上传的文件")
    return
  }

  try {
    submitting.value = true
    let res: any
    form.minutes = form.minutes * 1
    form.id = form.id * 1
    const revData:any = {
        module_key: props.approveCode,
        original_id: form.id,
        notes: revNotes.value,
        changes: JSON.stringify(form)
      }
    if (props.actionType === 'revise-create') {
      res = await createRevision(revData);
    } 
    else if (props.actionType === 'revise-edit') {
      revData.id = revId.value
      res = await editRevision(revData)
    }
    else if (props.actionType === 'edit') {
      res = await updateMaterial(form)
    }
    if (res.code == 0) {
      msg("success", "编辑成功")
      dialogVisible.value = false
      emit('success')
    } else {
      msg("error", res.message)
    }
  } catch (error) {
    console.error('编辑资料失败:', error)
    msg("error", "编辑资料失败")
  } finally {
    submitting.value = false
  }
}

// 取消操作
const cancel = () => {
  dialogVisible.value = false
}

// 重置表单
const resetForm = () => {
  form.id = ""
  form.name = ""
  form.category_id = ""
  form.minutes = ""
}

/** 上传课件 */
const handleFileChange = async (file: UploadFile) => {
  //console.log("文件变更:", file);
  if (file.size && file.size > 1024 * 1024 * 500) {
    errMsg("上传课件大小不超过500M");
    return;
  }
  try {
    const formData = new FormData();
    formData.append("file", file.raw!);
    loading.value = true;
    const res = await uploadMaterial(formData);
    if (res.success) {
      const data = res.data || {}
      msg("success", "上传成功");
      form.name = data.name || "";
      form.ext = data.file_type || "";
      form.path = data.file_path || "";
      form.size = data.size || "";
      fileList.value = [{ name: form.name, url: form.path }]
    } else {
      msg("error", res.message);
    }
    loading.value = false;
  } catch (e) {
    console.log(e);
    msg("error", "上传失败");
    loading.value = false;
  }
};

/** 移除上传课件 */
const onFileRemove = (file: UploadFile) => {
  form.ext = "";
  form.path = "";
};
// 组件挂载时初始化
onMounted(() => {
  if (props.modelValue && props.rowData) {
    initFormData()
  }
})
</script>