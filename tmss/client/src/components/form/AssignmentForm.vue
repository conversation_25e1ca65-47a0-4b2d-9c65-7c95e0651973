<template>

    <el-form ref="assignmentFormRef" :model="form" :rules="assignmentRules" label-width="120px" label-position="right"
        :disabled="isViewMode">
        <el-form-item label="修订说明" v-if="isReviseMode">
            <el-input v-model="revNotes" type="textarea" :rows="2" placeholder="请输入修订说明" />
        </el-form-item>
        <el-form-item label="所属课程" prop="assignment.course_id">
            <!-- <my-select v-model="form.assignment.course_id" placeholder="请选择课程" :func="getCourseList"
                labelKey="course.name" valueKey="course.id" :initData="initCourse" searchKey="name"
                @change="courseChange" /> -->
            <CourseSelect v-model="form.assignment.course_id" :init-data="initCourse" />
        </el-form-item>

        <el-form-item label="所属章节" prop="assignment.chapter_id">
            <!-- <el-tree-select placeholder="请选择章节" v-model="form.assignment.chapter_id" :data="chapterOptions"
                :props="chapterProps" clearable class="w-full" check-strictly /> -->
            <ChapterSelectByCourse v-model="form.assignment.chapter_id" :course-id="form.assignment.course_id"
                check-strictly />
        </el-form-item>

        <el-form-item label="作业名称" prop="assignment.name">
            <el-input v-model="form.assignment.name" placeholder="请输入作业名称" />
        </el-form-item>

        <el-form-item label="作业描述" prop="assignment.description">
            <el-input v-model="form.assignment.description" type="textarea" :rows="3" placeholder="请输入作业描述" />
        </el-form-item>

        <el-form-item label="选择班级" prop="class_ids">
            <!-- <my-select v-model="form.class_ids" multiple placeholder="请选择下发的班级" :func="getClassList" labelKey="name"
                valueKey="id" searchKey="name" :initData="initClass" /> -->
            <ClassSelect v-model="form.class_ids" :init-data="initClass" />
        </el-form-item>

        <el-form-item label="选择专业" prop="major_ids">
            <!-- <el-tree-select check-strictly :render-after-expand="false" v-model="form.major_ids" placeholder="请选择专业"
                :data="majors" multiple :props="major_props" /> -->
            <MajorSelect v-model="form.major_ids" multiple />
        </el-form-item>

        <el-form-item label="批阅教师" prop="teacher_ids">
            <!-- <my-select v-model="form.teacher_ids" multiple placeholder="请选择批阅教师" :func="getUserList" labelKey="username"
                valueKey="id" :extraParams="{ sys_code: 'teacher' }" :initData="initTeacher" /> -->
            <TeacherSelect v-model="form.teacher_ids" :init-data="initTeacher" />
        </el-form-item>

        <el-form-item label="时间范围">
            <el-date-picker v-model="timerange" type="datetimerange" :shortcuts="shortcutsTimeSelect"
                range-separator="至" start-placeholder="开始时间" end-placeholder="结束时间" format="YYYY-MM-DD hh:mm" />
        </el-form-item>

        <!-- 作业动作部分 -->
        <el-form-item label="作业动作" prop="assignment.action_type" v-if="!isViewMode">
            <AssignmentActionSelector ref="actionSelectorRef" v-model="currentActionType"
                :courseId="form.assignment.course_id" :chapterId="form.assignment.chapter_id" :isView="isViewMode"
                @action-click="actionChange" @select="handleActionSelect" :initSelectedIds="initSelectedItemIds" />
        </el-form-item>

        <!-- 作业列表部分 -->
        <el-form-item label="作业列表">
            <AssignmentItemList ref="itemListRef" v-model="selectedAssignment" :isView="isViewMode"
                @remove="handleItemRemove" />
        </el-form-item>

    </el-form>
    <div class="flex justify-end gap-2 mt-6" v-if="!isViewMode">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSubmit">保存</el-button>
    </div>

</template>

<script setup lang="ts">
import { ref, defineProps, defineEmits, watch, onMounted, nextTick, computed } from 'vue';
import { errMsg, successMsg } from '@/utils/msg';
import { shortcutsTimeSelect } from '@/api/status';
import { createAssignment, updateAssignment } from '@/api/assignment';
import ChapterSelectByCourse from '../select/ChapterSelectByCourse.vue';
import { createRevision, editRevision } from '@/api/revision';

const props = defineProps({
    mode: {
        type: String,
        default: 'create' // 'create' 或 'edit' 或 'view'
    },
    assignmentData: {
        type: Object,
        default: () => ({})
    },
    approveCode: {
        type: String,
        required: true
    },
});

const emit = defineEmits(['success', 'cancel']);
const isViewMode = computed(() => props.mode === 'view' || props.mode === 'revise-view')
const isReviseMode = computed(() =>
    props.mode === 'revise-create' ||
    props.mode === 'revise-edit' ||
    props.mode === 'revise-view'
)
const revNotes = ref('')
const revId = ref(0)
// 表单引用
const assignmentFormRef = ref();
const actionSelectorRef = ref();
const itemListRef = ref();
const formInit = () => ({
    assignment: {
        course_id: '',
        chapter_id: '',
        name: '',
        description: '',
        start_at: '',
        end_at: '',
    },
    class_ids: [],
    teacher_ids: [],
    major_ids: [],
    actions: []
})
// 表单数据
const form: any = ref(formInit());

// 初始化数据
const initClass = ref([]);
const initTeacher = ref([]);
const initCourse = ref([]);

// 时间范围
const timerange: any = ref([]);

// 作业动作相关
const currentActionType = ref('');
const selectedAssignment = ref<any[]>([]);
const initSelectedItemIds: any = ref({
    questions: [],
    courseware: [],
    resources: []
});


const assignmentRules = {
    assignment: {
        course_id: [{ required: true, message: '请选择课程', trigger: 'change' }],
        chapter_id: [{ required: true, message: '请选择章节', trigger: 'blur' }],
        name: [{ required: true, message: '请输入作业名称', trigger: 'blur' }]
    },
    class_ids: [{ required: true, message: '请选择班级', trigger: 'change' }],
    teacher_ids: [{ required: true, message: '请选择老师', trigger: 'change' }],
    major_ids: [{ required: true, message: '请选择专业', trigger: 'change' }],
};



// 作业动作变更处理
const actionChange = (val: string) => {
    if (actionSelectorRef.value) {
        currentActionType.value = val;
        actionSelectorRef.value.openSelector(val);
    }
};

// 处理动作选择
const handleActionSelect = (data: { type: string, items: any[] }) => {
    if (itemListRef.value) {
        // 更新选中的ID列表
        initSelectedItemIds.value[data.type] = data.items.map((item: any) => item.id);

        // 使用 nextTick 确保在下次 DOM 更新周期后执行
        nextTick(() => {
            itemListRef.value.addItems(data);
        });
    }
};

// 处理项目移除
const handleItemRemove = (data: { type: string, id: number }) => {
    // 从 initSelectedItemIds 中移除对应的 ID
    if (initSelectedItemIds.value[data.type]) {
        const index = initSelectedItemIds.value[data.type].findIndex((id: number) => id === data.id);
        if (index !== -1) {
            initSelectedItemIds.value[data.type].splice(index, 1);
        }
    }
};
const createAssignmentInit = () => {
    form.value = formInit();

    selectedAssignment.value = [];
    initSelectedItemIds.value = {
        questions: [],
        courseware: [],
        resources: []
    };
    timerange.value = [];
    assignmentFormRef.value?.resetFields();
    if (itemListRef.value) {
        itemListRef.value.initItems();
    }
};
const setFormData = (data: any) => {
    initTeacher.value = data.teachers?.map((item: any) => {
        return { id: item.id, username: item.username }
    }) || [];

    // 初始化班级列表
    initClass.value = data.classes?.map((item: any) => {
        return { id: item.id, name: item.name }
    }) || [];

    // 设置时间范围
    if (data.assignment?.start_at && data.assignment?.end_at) {
        timerange.value = [
            new Date(data.assignment.start_at * 1000),
            new Date(data.assignment.end_at * 1000)
        ];
    }

    // 设置表单基础数据
    form.value = {
        assignment: {
            id: data.assignment?.id || '',
            course_id: data.assignment?.course_id || '',
            chapter_id: data.assignment?.chapter_id || '',
            name: data.assignment?.name || '',
            description: data.assignment?.description || '',
            start_at: data.assignment?.start_at || '',
            end_at: data.assignment?.end_at || '',
        },
        class_ids: data.classes?.map((item: any) => item.id) || [],
        teacher_ids: data.teachers?.map((t: any) => t.id) || [],
        major_ids: data.majors?.map((t: any) => t.id) || [],
        actions: data.actions || []
    };

    // 设置选中的作业项目
    selectedAssignment.value = (data.actions || []).map((action: any) => ({
        ...action,
        action_type: action.action_type,
        question_key: action.ref_id + "_" + action.action_type,
        ref_id: action.ref_id,
        title: action.title || JSON.parse(action.content || '{}').title,
        required: action.required || false,
        content: action.content,
        score: action.score || 0,
        minutes: action.minutes || 0
    }));

    // 初始化ID集合
    nextTick(() => {
        if (itemListRef.value) {
            initSelectedItemIds.value = itemListRef.value.initIds();
        }

    });
};

// 初始化表单数据
const initFormData = () => {
    if (props.mode === 'edit' || props.mode === 'view') {
        const data = props.assignmentData;
        setFormData(data);
    }
    else if (props.mode === 'create') {
        // 新建模式，重置数据
        createAssignmentInit();
    }
    else if (props.mode === 'revise-view' || props.mode === 'revise-edit') {
        const data = props.assignmentData;
        const changes = JSON.parse(data.changes || '{}');
        form.value = changes.form || formInit();
        initTeacher.value = changes.initTeacher || [];
        initClass.value = changes.initClass || [];
        timerange.value = changes.timerange || [];
        selectedAssignment.value = changes.selectedAssignment || [];
        revId.value = data.id;
        revNotes.value = data.notes;
    } else if (props.mode === 'revise-create') {
        const data = props.assignmentData;
        setFormData(data);
        revNotes.value = "";
    }
};

// 提交表单
const handleSubmit = () => {
    if (!timerange.value || timerange.value.length != 2) {
        errMsg('请选择时间范围');
        return false;
    }

    if (!itemListRef.value || itemListRef.value.getItems().length < 1) {
        errMsg('请选择作业内容');
        return false;
    }

    assignmentFormRef.value?.validate(async (valid: boolean) => {
        if (valid) {
            // 处理时间数据
            form.value.assignment.start_at = Math.floor(
                new Date(timerange.value[0]).getTime() / 1000
            );
            form.value.assignment.end_at = Math.floor(
                new Date(timerange.value[1]).getTime() / 1000
            );

            // 处理作业动作数据
            const actionList = itemListRef.value.getItems();
            form.value.actions = actionList.map((item: any, index: number) => {
                return {
                    ...item,
                    order_num: index + 1
                }
            });

            try {
                let res: any;
                if (props.mode === 'edit') {
                    res = await updateAssignment(form.value);
                }
                else if (props.mode === 'create') {
                    form.value.assignment.id = 0;
                    res = await createAssignment(form.value);
                }
                else if (props.mode === 'revise-edit') {
                    if (!revNotes.value) return errMsg('请输入修订说明')
                    const changes = {
                        form: form.value,
                        initTeacher: initTeacher.value,
                        initClass: initClass.value,
                        timerange: timerange.value,
                        selectedAssignment: selectedAssignment.value,
                    }
                    const saveData = {
                        id: revId.value,
                        module_key: props.approveCode,
                        original_id: form.value.assignment.id,
                        notes: revNotes.value,
                        changes: JSON.stringify(changes)
                    }
                    res = await editRevision(saveData);
                }
                else if (props.mode === 'revise-create') {
                    if (!revNotes.value) return errMsg('请输入修订说明')
                    const changes = {
                        form: form.value,
                        initTeacher: initTeacher.value,
                        initClass: initClass.value,
                        timerange: timerange.value,
                        selectedAssignment: selectedAssignment.value,
                    }
                    const saveData = {
                        module_key: props.approveCode,
                        original_id: form.value.assignment.id,
                        notes: revNotes.value,
                        changes: JSON.stringify(changes)
                    }
                    res = await createRevision(saveData)
                }

                successMsg(res.message);
                emit('success', res);
            } catch (error) {
                console.error(error);
            }
        } else {
            return false;
        }
    });
};

// 取消操作
const handleCancel = () => {
    emit('cancel');
};

// 重置表单
const resetForm = () => {
    assignmentFormRef.value?.resetFields();
    initFormData();
};

// 暴露方法给父组件
defineExpose({
    resetForm,
    validate: () => assignmentFormRef.value?.validate()
});

// 监听模式和数据变化
watch(() => [props.mode, props.assignmentData], () => {
    initFormData();
}, { immediate: true });

// 组件挂载时初始化
onMounted(async () => {
    initFormData();
});
</script>