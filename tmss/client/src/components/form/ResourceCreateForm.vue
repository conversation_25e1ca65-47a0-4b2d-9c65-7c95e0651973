<template>
    <el-dialog v-model="dialogVisible" title="批量添加资料" width="80%" :close-on-click-modal="false"
        :destroy-on-close="true">

        <el-form ref="formRef" :model="form" label-position="top" v-loading.fullscreen="loading"
            element-loading-text="正在上传资料..." element-loading-background="rgba(0, 0, 0, 0.2)">

            <el-form-item label="选择分类" prop="category_id">
                <!-- <el-tree-select v-model="form.category_id" :data="categories" :props="defaultProps" placeholder="选择分类"
                    check-strictly clearable class="w-full" /> -->
                    <ResourceCategorySelect v-model="form.category_id" placeholder="选择分类"/>
            </el-form-item>

            <el-form-item label="阅读时间" prop="minutes">
                <el-input v-model="form.minutes" type="number" placeholder="请输入阅读时间" class="w-full">
                    <template #append>分钟</template>
                </el-input>
            </el-form-item>

            <el-form-item label="上传资料">
                <div class="flex flex-1 flex-col">
                    <el-upload drag multiple :auto-upload="false" :on-change="handleFileChange"
                        :on-remove="handleRemove" :file-list="fileList" class="upload-area flex-1">
                        <el-icon class="el-icon--upload">
                            <i class="fas fa-cloud-upload-alt"></i>
                        </el-icon>
                        <div class="el-upload__text">
                            将文件拖到此处，或<em>点击上传</em>
                        </div>
                        <template #tip>
                            <div class="el-upload__tip text-xs text-gray-500">
                                支持 PDF、DOCX、PPTX、XLSX、MD、MP4 格式，单个文件不超过 50MB，支持多文件上传
                            </div>
                        </template>
                    </el-upload>

                    <div v-if="fileList.length > 0" class="mt-4 flex-1">
                        <div v-for="file in fileList" :key="file.uid"
                            class="flex items-center justify-between p-2 border-b border-gray-200">
                            <div class="flex items-center">
                                <span class="text-sm">{{ file.name }}</span>
                            </div>
                            <div class="text-xs text-gray-500">{{ formatFileSize(file.size) }}</div>
                        </div>
                    </div>
                </div>
            </el-form-item>
        </el-form>

        <template #footer>
            <el-button @click="cancel">取消</el-button>
            <el-button type="primary" @click="submitMaterial" :loading="submitting">上传</el-button>
        </template>
    </el-dialog>
</template>

<script lang="ts" setup>
import { ref, reactive, defineProps, defineEmits, watch, onMounted } from 'vue'
import { type FormInstance, type UploadFile } from 'element-plus'
import { createMaterial } from '@/api/material'
import { msg } from '@/utils/msg'
import type { CreateMaterial } from '@/api/material'

// 定义props
const props = defineProps<{
    modelValue: boolean
}>()

// 定义emits
const emit = defineEmits<{
    (e: 'update:modelValue', value: boolean): void
    (e: 'success'): void
}>()

// 响应式数据
const dialogVisible = ref(false)
const formRef = ref<FormInstance>()
const loading = ref(false)
const submitting = ref(false)
const fileList = ref<any[]>([])

// 表单数据
const form = reactive({
    category_id: '',
    minutes: ''
})
watch(() => props.modelValue, (val) => {
    dialogVisible.value = val
    if (val) {
        // 弹窗打开时初始化表单数据
        resetForm()
    }
})

// 监听dialogVisible变化
watch(dialogVisible, (val) => {
    emit('update:modelValue', val)
    // 弹窗关闭时重置表单
    if (!val) {
        resetForm()
    }
})

// 格式化文件大小
const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 Bytes"
    const k = 1024
    const sizes = ["Bytes", "KB", "MB", "GB"]
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i]
}

// 文件变化处理
const handleFileChange = (file: UploadFile, files: any[]) => {
    fileList.value = files
}

// 文件移除处理
const handleRemove = (file: UploadFile) => {
    fileList.value = fileList.value.filter(item => item.uid !== file.uid)
}

// 提交资料
const submitMaterial = async () => {
    if (submitting.value) {
        msg("warning", "请勿重复提交")
        return
    }

    // 表单验证
    const valid = await formRef.value?.validate()
    if (!valid) return

    // 检查必填项
    if (!form.category_id) {
        msg("warning", "请选择资料分类")
        return
    }

    if (fileList.value.length === 0) {
        msg("warning", "请选择要上传的文件")
        return
    }

    try {
        submitting.value = true
        loading.value = true

        // 构造表单数据
        const formData = new FormData()
        formData.append("category_id", form.category_id)
        formData.append("minutes", form.minutes || "0")

        // 添加所有文件
        fileList.value.forEach(file => {
            formData.append(`files`, file.raw)
        })

        // 提交数据
        const res = await createMaterial(formData as CreateMaterial)

        if (res.code == 0) {
            msg("success", "资料上传成功")
            dialogVisible.value = false
            emit('success')
        } else {
            msg("error", res.message)
        }
    } catch (error) {
        console.error('上传资料失败:', error)
        msg("error", "上传资料失败")
    } finally {
        submitting.value = false
        loading.value = false
    }
}

// 取消操作
const cancel = () => {
    dialogVisible.value = false
}

// 重置表单
const resetForm = () => {
    form.category_id = ''
    form.minutes = ''
    fileList.value = []
}


</script>

<style scoped>
.upload-area {
    border: 1px dashed #dcdfe6;
    border-radius: 6px;
    padding: 20px;
    text-align: center;
    background-color: #f5f7fa;
}

.upload-area:hover {
    border-color: #409eff;
}

.el-icon--upload {
    font-size: 67px;
    color: #c0c4cc;
    margin-bottom: 16px;
}

.el-upload__text {
    color: #606266;
    font-size: 14px;
}

.el-upload__tip {
    margin-top: 7px;
}
</style>