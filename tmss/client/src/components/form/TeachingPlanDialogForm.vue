<template>
  <el-dialog 
    v-model="dialogVisible" 
    :title="dialogTitle" 
    width="60%" 
    top="5vh"
    :close-on-click-modal="false"
    :destroy-on-close="true">

    <el-form 
      ref="formRef" 
      :model="formData" 
      :rules="formRules" 
      label-width="120px" 
      label-position="top"
      :disabled="isViewMode">
      
      <el-form-item label="修订说明" v-if="isReviseMode">
        <el-input 
          v-model="revNotes" 
          type="textarea" 
          :rows="2" 
          placeholder="请输入修订说明" />
      </el-form-item>
      
      <el-form-item label="教学大纲" prop="teaching_plan.syllabus_id">
        <SyllabusSelect 
          v-model="formData.teaching_plan.syllabus_id" 
          :init-data="initSyllabus" />
      </el-form-item>
      
      <el-form-item label="计划名称" prop="teaching_plan.name">
        <el-input v-model="formData.teaching_plan.name" placeholder="请输入计划名称" />
      </el-form-item>
      
      <el-form-item label="学期">
        <el-input 
          v-model="formData.teaching_plan.term" 
          placeholder="学期格式建议为 YYYY-MM-DD~YYYY-MM-DD" />
      </el-form-item>
      
      <el-form-item label="教室需求">
        <el-input 
          v-model="formData.teaching_plan.classroom_requirements" 
          placeholder="描述教室需求"
          type="textarea" 
          :rows="3" />
      </el-form-item>
      
      <el-form-item label="关联班级" prop="class_ids">
        <ClassSelect 
          v-model="formData.class_ids" 
          :init-data="initClass" 
          multiple />
      </el-form-item>
      
      <el-row>
        <el-col :span="12">
          <el-form-item label="开始时间" prop="start_at">
            <el-date-picker 
              v-model="formData.start_at" 
              type="date" 
              placeholder="选择开始时间"
              value-format="YYYY-MM-DD" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="结束时间" prop="end_at">
            <el-date-picker 
              v-model="formData.end_at" 
              type="date"
              placeholder="选择结束时间"
              value-format="YYYY-MM-DD" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    
    <template #footer v-if="!isViewMode">
      <el-button @click="dialogVisible = false">取消</el-button>
      <el-button type="primary" @click="submitForm">保存</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, computed, watch, defineProps, defineEmits, defineExpose } from 'vue'
import { type FormInstance } from 'element-plus'
import { 
  createTeachingPlan, 
  editTeachingPlan, 
  getTeachingPlanDetail 
} from '@/api/teaching-plan'
import { createRevision, editRevision, getRevisionDetail } from '@/api/revision'
import { successMsg, errMsg } from '@/utils/msg'

// 定义props
const props = defineProps<{
  modelValue: boolean
  actionType: string
  dialogTitle: string
  row?: any // 用于初始化数据
}>()

// 定义事件
const emit = defineEmits<{
  (e: 'update:modelValue', value: boolean): void
  (e: 'success'): void
}>()

// 内部状态管理
const dialogVisible = ref(false)
const revNotes = ref('')
const revId = ref(0)
const formRef = ref<FormInstance>()

// 初始化表单数据函数
const getInitFormData = () => ({
  teaching_plan: {
    syllabus_id: '',
    name: "",
    term: "",
    classroom_requirements: ""
  },
  class_ids: [],
  start_at: "",
  end_at: ""
})

const formData:any = ref(getInitFormData())
const initSyllabus:any = ref([])
const initClass = ref([])

// 计算属性
const isViewMode = computed(() => props.actionType === 'view' || props.actionType === 'revise-view')
const isReviseMode = computed(() =>
  props.actionType === 'revise-create' ||
  props.actionType === 'revise-edit' ||
  props.actionType === 'revise-view'
)

// 表单验证规则
const formRules = {
  teaching_plan: {
    syllabus_id: [
      { required: true, message: '请选择关联的教学大纲', trigger: 'change' }
    ],
    name: [
      { required: true, message: '请输入计划名称', trigger: 'blur' }
    ],
  },
  class_ids: [
    { required: true, message: '请选择关联的班级', trigger: 'change' }
  ],
  start_at: [
    { required: true, message: '请选择开始时间', trigger: 'change' }
  ],
  end_at: [
    { required: true, message: '请选择结束时间', trigger: 'change' }
  ]
}

// 监听父组件传入的 modelValue 变化
watch(() => props.modelValue, async (val) => {
  dialogVisible.value = val
  if (val) {
    await loadInitialData()
  }
})

// 同步子组件 dialogVisible 变化到父组件
watch(dialogVisible, (val) => {
  emit('update:modelValue', val)
  // 当弹窗关闭时，清空表单
  if (!val) {
    clearForm()
  }
})

// 清空表单
const clearForm = () => {
  formData.value = getInitFormData()
  initSyllabus.value = []
  initClass.value = []
  revNotes.value = ''
  revId.value = 0
}

// 设置表单数据
const setFormData = (plan: any) => {
  initClass.value = plan.classes || []
  initSyllabus.value = [{
    syllabus: {
      id: plan.syllabus_id,
      name: plan.syllabus_name
    }
  }]
  
  formData.value = {
    teaching_plan: {
      id: plan.teaching_plan.id || '',
      syllabus_id: plan.teaching_plan.syllabus_id || '',
      name: plan.teaching_plan.name || '',
      term: plan.teaching_plan.term || '',
      classroom_requirements: plan.teaching_plan.classroom_requirements || ''
    },
    class_ids: plan.classes?.map((item: any) => item.id) || [],
    start_at: plan.start_at_str || '',
    end_at: plan.end_at_str || ''
  }
}

// 加载初始数据
const loadInitialData = async () => {
  if (props.actionType === 'create') {
    clearForm()
  }
  else if (props.actionType === 'edit' || props.actionType === 'view') {
    if (!props.row?.id) return
    try {
      const res = await getTeachingPlanDetail(props.row.id)
      console.log(res)
      setFormData(res)
    } catch (error) {
      console.error('获取教学计划详情失败:', error)
      errMsg('获取数据失败')
    }
  } else if (props.actionType === 'revise-view' || props.actionType === 'revise-edit') {
    if (!props.row?.id) return errMsg('修订ID不能为空')
    try {
      const res = await getRevisionDetail(props.row.id)
      const changes = JSON.parse(res.changes)
      formData.value = changes.formData
      initClass.value = changes.initClass || []
      initSyllabus.value = changes.initSyllabus || []
      revNotes.value = res.notes
      revId.value = props.row.id
    } catch (error) {
      console.error('获取修订详情失败:', error)
      errMsg('获取修订数据失败')
    }
  } else if (props.actionType === 'revise-create' && props.row?.id) {
    try {
      const res = await getTeachingPlanDetail(props.row.id)
      setFormData(res)
      revNotes.value = ''
      revId.value = 0
    } catch (error) {
      console.error('获取教学计划详情失败:', error)
      errMsg('获取数据失败')
    }
  }
}

// 提交表单
const submitForm = async () => {
  try {
    const valid = await formRef.value?.validate()
    if (!valid) return

    let res: any
    if (props.actionType === 'edit') {
      res = await editTeachingPlan(formData.value)
    } else if (props.actionType === 'create') {
      res = await createTeachingPlan(formData.value)
    } else if (props.actionType === 'revise-create') {
      if (!revNotes.value) return errMsg('请输入修订说明')
      const changes = {
        formData: formData.value,
        initClass: initClass.value,
        initSyllabus: initSyllabus.value,
      }
      const saveData = {
        module_key: 'teaching_plan',
        original_id: formData.value.teaching_plan.id,
        notes: revNotes.value,
        changes: JSON.stringify(changes)
      }
      res = await createRevision(saveData)
    } else if (props.actionType === 'revise-edit') {
      if (!revNotes.value) return errMsg('请输入修订说明')
      const changes = {
        formData: formData.value,
        initClass: initClass.value,
        initSyllabus: initSyllabus.value,
      }
      const saveData = {
        id: revId.value,
        module_key: 'teaching_plan',
        original_id: formData.value.teaching_plan.id,
        notes: revNotes.value,
        changes: JSON.stringify(changes)
      }
      res = await editRevision(saveData)
    }

    if (res?.success) {
      successMsg(res.message)
      dialogVisible.value = false // 关闭弹窗
      emit('success')
    } else {
      errMsg(res?.message || '操作失败')
    }
  } catch (error) {
    console.error('表单提交失败:', error)
    errMsg('操作失败')
  }
}

// 暴露方法给父组件
const getFormData = () => formData.value
defineExpose({ getFormData })
</script>