<template>
  <el-dialog v-model="dialogVisible" :title="dialogTitle" width="80%" :close-on-click-modal="false"
    :destroy-on-close="true">

    <!-- 表单内容 -->
    <el-form ref="formRef" :model="formData" :rules="formRules" label-width="120px" :disabled="isViewMode">

      <!-- 修订说明 -->
      <el-form-item label="修订说明" v-if="isReviseMode">
        <el-input v-model="revNotes" type="textarea" :rows="2" placeholder="请输入修订说明" />
      </el-form-item>

      <!-- 教学大纲名称 -->
      <el-form-item label="教学大纲名称" prop="syllabus.name">
        <el-input v-model="formData.syllabus.name" placeholder="请输入教学大纲名称" />
      </el-form-item>

      <!-- 教学目标 -->
      <el-form-item label="教学目标" prop="syllabus.goals">
        <Editor v-model="formData.syllabus.goals" />
      </el-form-item>

      <!-- 大纲描述 -->
      <el-form-item label="大纲描述" prop="syllabus.description">
        <el-input v-model="formData.syllabus.description" type="textarea" :rows="2" placeholder="请输入大纲描述" />
      </el-form-item>

      <!-- 参考文献 -->
      <el-form-item label="参考文献" prop="syllabus.reference">
        <el-input v-model="formData.syllabus.reference" type="textarea" :rows="2" placeholder="请输入参考文献，每行一个" />
      </el-form-item>

      <!-- 推荐资料 -->
      <el-form-item label="推荐资料" prop="resources_ids">
        <ResourceSelect v-model="formData.resources_ids" :init-data="initResources" />
      </el-form-item>

      <!-- 推荐教材 -->
      <el-form-item label="推荐教材" prop="textbook_ids">
        <TextbookSelect v-model="formData.textbook_ids" :init-data="initSelectData" />
      </el-form-item>

    </el-form>

    <!-- 底部按钮 -->
    <template #footer v-if="!isViewMode">
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<!-- SyllabusDialogForm.vue -->
<script lang="ts" setup>
import { ref, computed, watch, defineProps, defineEmits, defineExpose } from 'vue'
import { type FormInstance } from 'element-plus'
import {
  addDraft,
  updateDraft,
  getSyllabusDetail
} from '@/api/syllabus'
import { createRevision, editRevision, getRevisionDetail } from '@/api/revision'
import { successMsg, errMsg } from '@/utils/msg'

// 定义props
const props = defineProps<{
  modelValue: boolean
  actionType: string
  dialogTitle: string
  row?: any // 用于初始化数据
}>()

// 定义事件
const emit = defineEmits<{
  (e: 'update:modelValue', value: boolean): void
  (e: 'success'): void
}>()

// 内部状态管理
const dialogVisible = ref(false)
const revNotes = ref('')
const revId = ref(0)
const formRef = ref<FormInstance>()
const getInitFormData = () => ({
  syllabus: {
    id: 0,
    name: '',
    goals: '',
    description: '',
    reference: '',
  },
  textbook_ids: [],
  resources_ids: [],
})

const formData = ref(getInitFormData())
const initSelectData = ref([])
const initResources = ref([])

// 计算属性
const isViewMode = computed(() => props.actionType === 'view' || props.actionType === 'revise-view')
const isReviseMode = computed(() =>
  props.actionType === 'revise-create' ||
  props.actionType === 'revise-edit' ||
  props.actionType === 'revise-view'
)

// 表单验证规则
const formRules = ref({
  syllabus: {
    name: [{ required: true, message: '请输入教学大纲名称', trigger: 'blur' }],
    goals: [{ required: true, message: '请输入教学目标', trigger: 'blur' }],
    description: [{ required: true, message: '请输入教学大纲描述', trigger: 'blur' }]
  }
})

// 监听父组件传入的 modelValue 变化
watch(() => props.modelValue, async (val) => {
  dialogVisible.value = val
  await loadInitialData()
})

// 同步子组件 dialogVisible 变化到父组件
watch(dialogVisible, (val) => {
  emit('update:modelValue', val)
})
const clearForm = () => {
  formData.value = getInitFormData()
  initSelectData.value = []
  initResources.value = []
}
// onMounted(() => { 
//   clearForm()
// })
const setRes = (res: any) => {
  console.log(res)
  formData.value = {
    syllabus: {
      id: res.syllabus.id,
      name: res.syllabus.name,
      goals: res.syllabus.goals,
      description: res.syllabus.description,
      reference: res.syllabus.reference,
    },
    textbook_ids: res.textbooks?.map((item: any) => item.id) || [],
    resources_ids: res.resources?.map((item: any) => item.id) || [],
  }
  initSelectData.value = res.textbooks || []
  initResources.value = res.resources || []
  //console.log(initResources.value)
}
// 加载初始数据
const loadInitialData = async () => {
  //console.log(props.actionType, props.row?.id)
  if (props.actionType === 'create') {
    clearForm()
  }
  else if (props.actionType === 'edit' || props.actionType === 'view') {
    const res = await getSyllabusDetail(props.row.id)
    setRes(res)
  } else if (props.actionType === 'revise-view' || props.actionType === 'revise-edit') {
    revId.value = props.row?.id
    if (!revId.value) return errMsg('修订ID不能为空')
    const res = await getRevisionDetail(props.row?.id)
    const changes = JSON.parse(res.changes)
    formData.value = changes.formData
    initSelectData.value = changes.textbooks || []
    initResources.value = changes.resources || []
    //console.log(initResources.value)
    revNotes.value = res.notes
  } else if (props.actionType === 'revise-create' && props.row?.id) {
    const res = await getSyllabusDetail(props.row.id)
    setRes(res)
    revNotes.value = ''

  }
}


// 提交表单
const submitForm = async () => {
  try {
    const valid = await formRef.value?.validate()
    if (!valid) return

    let res: any
    if (props.actionType === 'edit') {
      res = await updateDraft(formData.value)
    } else if (props.actionType === 'create') {
      res = await addDraft(formData.value)
    } else if (props.actionType === 'revise-create') {
      if (!revNotes.value) return errMsg('请输入修订说明')
      const changes = {
        formData: formData.value,
        initSelectData: initSelectData.value,
        initResources: initResources.value
      }
      const saveData = {
        module_key: 'syllabus',
        original_id: formData.value.syllabus.id,
        notes: revNotes.value,
        changes: JSON.stringify(changes)
      }
      res = await createRevision(saveData)
    } else if (props.actionType === 'revise-edit') {
      if (!revNotes.value) return errMsg('请输入修订说明')
      const changes = {
        formData: formData.value,
        initSelectData: initSelectData.value,
        initResources: initResources.value
      }
      const saveData = {
        id: revId.value,
        module_key: 'syllabus',
        original_id: formData.value.syllabus.id,
        notes: revNotes.value,
        changes: JSON.stringify(changes)
      }
      res = await editRevision(saveData)
    }

    if (res?.success) {
      successMsg(res.message)
      clearForm()
      dialogVisible.value = false // 关闭弹窗
      emit('success')
    } else {
      errMsg(res?.message || '操作失败')
    }
  } catch (error) {
    console.error('表单提交失败:', error)
  }
}

// 暴露方法给父组件
const getFormData = () => formData.value
defineExpose({ getFormData })

</script>