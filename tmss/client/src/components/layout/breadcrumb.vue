<template>
    <div class="mb-4 text-sm breadcrumbs">
        <el-breadcrumb separator="/">
            <el-breadcrumb-item v-for="(crumb, index) in breadcrumbs" :key="index">
                <a @click.prevent="navigateTo(crumb)" href="#" v-if="crumb.path && index !== breadcrumbs.length - 1">{{
                    crumb.title }}</a>
                <span v-else>{{ crumb.title }}</span>
            </el-breadcrumb-item>
        </el-breadcrumb>
    </div>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'

const props = defineProps<{
    menuData: any[]
}>()

const route = useRoute()
const router = useRouter()

const breadcrumbs = ref<any[]>([])

// 查找当前路径对应的菜单路径
const findBreadcrumbs = (path: string): any[] => {
    for (const item of props.menuData) {
        const result = recursiveFind(item, path)
        if (result) return [item, ...result]
    }
    return []
}

const recursiveFind = (menu: any, path: string): any[] | null => {
    if (menu.path === path) return []

    if (menu.children) {
        for (const child of menu.children) {
            const result = recursiveFind(child, path)
            if (result !== null) {
                return [child, ...result]
            }
        }
    }

    return null
}

// 当前路径变化时更新面包屑
watch(
    () => route.path,
    (newPath) => {
        breadcrumbs.value = findBreadcrumbs(newPath)
    },
    { immediate: true }
)

// 点击面包屑跳转
const navigateTo = (item: any) => {
    if (item.path) {
        router.push(item.path)
    }
}
</script>

<style scoped>
.breadcrumbs a:hover {
    color: #1890ff;
    cursor: pointer;
}
</style>