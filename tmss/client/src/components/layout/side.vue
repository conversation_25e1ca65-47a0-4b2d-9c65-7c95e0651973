<template>
	<div class="w-64 bg-[#1F2937] text-white flex flex-col">
		<!-- <div class="flex justify-center mt-5 mb-5">
      <img src="@/assets/logo.png" alt="Logo" class="h-25 w-auto bg-white object-contain">
    </div> -->
		<el-menu v-if="expand" ref="menuRef" :default-active="store.path" unique-opened
			class="flex-1 overflow-auto custom-scrollbar" background-color="#1F2937" text-color="#fff">
			<el-menu-item index="/home" @click="router.push('/home')">
				<el-icon>
					<House />
				</el-icon>
				<span>首页</span>
			</el-menu-item>
			<div v-for="item in store.menuData" :key="item.id">
				<!-- 一级菜单 -->
				<el-sub-menu v-if="item.children" :index="item.path">
					<template #title>
						<el-icon>
							<component :is="item.icon" />
						</el-icon>
						<span>{{ item.menu_name }}</span>
					</template>

					<!-- 二级菜单 -->
					<div v-for="child in item.children" :key="child.id">
						<el-sub-menu v-if="child.children && child.children.length > 0" :index="child.path">
							<template #title>{{ child.menu_name }}</template>

							<!-- 三级菜单 -->
							<el-menu-item v-for="grandChild in child.children" :key="grandChild.id"
								:index="grandChild.path" @click="handleMenuClick(grandChild)">
								{{ grandChild.menu_name }}
							</el-menu-item>
						</el-sub-menu>

						<!-- 没有子菜单的二级菜单 -->
						<el-menu-item v-else :index="child.path" @click="handleMenuClick(child)">
							{{ child.menu_name }}
						</el-menu-item>
					</div>
				</el-sub-menu>

				<!-- 没有一级子菜单的情况 -->
				<el-menu-item v-else :index="item.path" @click="handleMenuClick(item)">
					<el-icon>
						<component :is="item.icon" />
					</el-icon>
					<span>{{ item.menu_name }}</span>
				</el-menu-item>
			</div>
		</el-menu>
		<div v-else class="flex-1 overflow-auto custom-scrollbar text-white flex flex-col items-center gap-8 py-4">
			<el-tooltip effect="dark" content="首页" placement="right">
				<el-icon size="20" @click="router.push('/home')">
					<House />
				</el-icon>
			</el-tooltip>
			<el-popover v-for="item in store.menuData" :key="item.id" popper-class="my-pop"
				:popper-style="{ minWidth: '120px' }" :show-arrow="false" class="box-item" :title="item.menu_name"
				placement="right">
				<template #reference>
					<el-icon size="20">
						<component :is="item.icon" />
					</el-icon>
				</template>
				<template #default>
					<div @click="handleMenuClick(child)" v-for="child in item.children" :key="child.id"
						class="menu cursor-pointer mt-4">
						{{ child.menu_name }}
					</div>

				</template>
			</el-popover>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import { useUserStore } from "@/store/user";
import { onMounted } from "vue";
import { useRouter } from "vue-router";
import { ElMenu } from "element-plus";
defineProps<{
	expand?: boolean;
}>()
const router = useRouter();
const store = useUserStore();
const menuRef = ref<typeof ElMenu>();

// 菜单点击跳转
const handleMenuClick = (menuItem: any) => {
	store.path = menuItem.path;
	router.push(menuItem.path);
};
onMounted(() => {
	router.push(store.path);
})
</script>

<style>
.el-popover.my-pop {
	background-color: #1F2937;
	border-color: #1F2937;
	color: white;
}

.my-pop .el-popover__title {
	color: white;
}

.my-pop .menu:hover {
	color: #409eff;
}
</style>
<style scoped>
.custom-scrollbar::-webkit-scrollbar {
	width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
	background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
	background: #4b5563;
	/* 深灰色 */
	border-radius: 3px;
}
</style>
