<template>
    <div class="w-full">
        <!-- 标签页容器 -->
        <div class="relative flex items-center bg-white border-b border-gray-300 shadow-sm px-2 py-1">
            <!-- 左侧按钮 -->
            <button v-if="showLeftArrow" @click="scroll('left')"
                class="absolute left-0 top-0 bottom-0 z-10 flex items-center justify-center w-6 bg-white bg-opacity-70 hover:bg-opacity-100 transition">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-500" fill="none" viewBox="0 0 24 24"
                    stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                </svg>
            </button>

            <!-- 滚动容器 -->
            <div ref="tabContainer" class="flex overflow-x-hidden overflow-y-hidden px-6 py-1"
                style="scroll-behavior: smooth;">
                <div v-for="tab in tabsStore.tabs" @click="router.push(tab.path)" :key="tab.path" :data-path="tab.path"
                    class="relative flex items-center gap-1 px-3 py-1.5 text-sm rounded-md transition-all duration-200 whitespace-nowrap
                    hover:bg-blue-50 hover:text-blue-600 border border-transparent"
                    :class="{ 'bg-blue-100 text-blue-700 border-blue-300': activeTab === tab.path }"
                    @contextmenu.prevent.native="openContextMenu(tab, $event)">
                    {{ tab.title }}
                    <button v-if="!tab.affix"
                        class="ml-1 p-0.5 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100"
                        @click.stop="closeTab(tab)">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3" fill="none" viewBox="0 0 24 24"
                            stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
            </div>

            <!-- 右侧按钮 -->
            <button v-if="showRightArrow" @click="scroll('right')"
                class="absolute right-0 top-0 bottom-0 z-10 flex items-center justify-center w-6 bg-white bg-opacity-70 hover:bg-opacity-100 transition">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-500" fill="none" viewBox="0 0 24 24"
                    stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                </svg>
            </button>
        </div>

        <!-- 右键菜单 -->
        <ul v-show="showMenu" :style="{ top: menuTop + 'px', left: menuLeft + 'px' }"
            class="fixed z-50 bg-white border border-gray-200 rounded shadow-md text-sm text-gray-700">
            <!-- <li class="px-4 py-2 hover:bg-gray-100 cursor-pointer" @click="refreshTab">刷新</li> -->
            <li v-if="selectedTab && !selectedTab.affix" class="px-4 py-2 hover:bg-gray-100 cursor-pointer"
                @click="closeTab(selectedTab)">关闭</li>
            <li class="px-4 py-2 hover:bg-gray-100 cursor-pointer" @click="closeOtherTabs">关闭其他</li>
            <li class="px-4 py-2 hover:bg-gray-100 cursor-pointer" @click="closeAllTabs">关闭全部</li>
        </ul>

        <!-- 页面内容 -->
        <!-- <router-view v-slot="{ Component }" class="px-4 py-4">
            <keep-alive>
                <component :is="Component" :key="$route.fullPath" />
            </keep-alive>
            <component :is="Component" :key="$route.fullPath" v-show="!$route.meta.keepAlive" />
        </router-view> -->
    </div>
</template>

<script lang="ts" setup>
import { ref, watch, onMounted, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useTabsStore } from '@/store/tabs' // 注意路径
import { useUserStore } from '@/store/user'

const route = useRoute()
const router = useRouter()
const tabContainer = ref<HTMLElement | null>(null)
const showLeftArrow = ref(false)
const showRightArrow = ref(false)

// 使用 store
const tabsStore = useTabsStore()
const userStore = useUserStore()

// 当前激活 tab
const activeTab = ref(route.path)

// 滚动检查
const checkScrollButtons = () => {
    const container = tabContainer.value
    if (!container) return
    const { scrollWidth, clientWidth, scrollLeft } = container
    showLeftArrow.value = scrollWidth > clientWidth && scrollLeft > 0
    showRightArrow.value = scrollWidth > clientWidth && scrollLeft < scrollWidth - clientWidth - 1
}

// 添加 tab
const addTab = () => {
    const title = route.meta?.title || route.name
    tabsStore.addTab({
        path: route.path,
        title,
        affix: false,
    })
    activeTab.value = route.path
}

// 路由变化时添加或切换 tab
watch(
    () => route.path,
    (newVal) => {
        addTab()
        userStore.path = newVal
        nextTick(() => {
            checkScrollButtons()
            autoScrollToCurrentTab()
        })
    }
)

// 自动滚动当前 tab 到可视区域
const autoScrollToCurrentTab = () => {
    nextTick(() => {
        const container = tabContainer.value
        if (!container) return
        const currentTabElement = Array.from(container.children).find(
            child => (child as HTMLElement).dataset.path === route.path
        ) as HTMLElement | undefined
        if (currentTabElement) {
            scrollTabIntoView(currentTabElement)
        }
    })
}

// 滚动指定 tab 到可视区域
const scrollTabIntoView = (tabElement: HTMLElement) => {
    const container = tabContainer.value
    if (!container) return
    const containerWidth = container.clientWidth
    const tabWidth = tabElement.offsetWidth
    const tabLeft = tabElement.offsetLeft
    const visibleLeft = container.scrollLeft
    const visibleRight = visibleLeft + containerWidth

    if (tabLeft < visibleLeft || tabLeft + tabWidth > visibleRight) {
        container.scrollLeft = tabLeft - (containerWidth - tabWidth) / 2
    }
}

// 滚动控制
const scroll = (direction: 'left' | 'right') => {
    const container = tabContainer.value
    if (!container) return
    const step = 150
    container.scrollLeft += direction === 'left' ? -step : step
    setTimeout(checkScrollButtons, 100)
}

// 右键菜单相关
const showMenu = ref(false)
const menuTop = ref(0)
const menuLeft = ref(0)
const selectedTab = ref<any>(null)

const openContextMenu = (tab: any, e: MouseEvent) => {
    e.preventDefault()
    selectedTab.value = tab
    menuTop.value = e.clientY
    menuLeft.value = e.clientX
    showMenu.value = true
}

const closeTab = (tab: any) => {
    if (tab.affix) return
    if (activeTab.value === tab.path) {
        const index = tabsStore.tabs.findIndex(item => item.path === tab.path)
        if (index !== -1) {
            const nextTab = tabsStore.tabs[index + 1] || tabsStore.tabs[index - 1]
            if (nextTab) router.push(nextTab.path)
            else router.push('/home')
        }

    }
    tabsStore.removeTab(tab.path)
    showMenu.value = false
}

// const refreshTab = () => {
//     router.app.$forceUpdate()
// }

const closeOtherTabs = () => {
    tabsStore.closeOtherTabs(activeTab.value)
    showMenu.value = false
}

const closeAllTabs = () => {
    tabsStore.closeAllTabs()
    router.push('/home')
    showMenu.value = false
}
defineExpose({
    closeAllTabs
})
// 初始化加载
onMounted(() => {
    window.addEventListener('resize', checkScrollButtons)
    nextTick(() => {
        checkScrollButtons()
        autoScrollToCurrentTab()
    })
})
</script>

<style scoped>
.contextmenu {
    z-index: 9999;
}
</style>