<template>
  <div>
    <div style="border: 1px solid #ccc; margin-top: 10px;">
      <Toolbar
        :editor="editorRef"
        :defaultConfig="toolbarConfig"
        :mode="mode"
        style="border-bottom: 1px solid #ccc"
      />
      <Editor
        :defaultConfig="editorConfig"
        :mode="mode"
        style="height: 400px; overflow-y: hidden;"
        @onCreated="handleCreated"
        @onChange="handleChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { shallowRef, onBeforeUnmount, watch,  computed, ref } from 'vue';
import { Editor, Toolbar } from '@wangeditor/editor-for-vue'
import '@wangeditor/editor/dist/css/style.css';

interface EditorProps {
  modelValue: string;
  toolbarConfig?: Record<string, any>;
  editorConfig?: Record<string, any>;
  mode?: 'default' | 'simple';
}
// 新增：解决循环更新的标志位
const ignoreNextChange = ref(false);
const props = withDefaults(defineProps<EditorProps>(), {
  modelValue: '',
  toolbarConfig: () => ({}),
  editorConfig: () => ({ placeholder: '请输入内容...' }),
  mode: 'default'
});

const emit = defineEmits<{
  (e: 'update:modelValue', value: string): void;
}>();

const editorRef = shallowRef();
const mode = computed(() => props.mode);

// 创建编辑器回调
const handleCreated = (editor: any) => {
  editorRef.value = editor;
  editor.setHtml(props.modelValue);
};

// 内容变化回调
const handleChange = (editor: any) => {
  if (ignoreNextChange.value) {
    ignoreNextChange.value = false;
    return;
  }
  
  const html = editor.getHtml();
  ignoreNextChange.value = true; // 设置标志位避免循环更新
  emit('update:modelValue', html);
};

// 监听传入的 modelValue 变化
watch(() => props.modelValue, (newValue) => {
  if (!editorRef.value) return;
  
  // 忽略自身触发的更新
  if (ignoreNextChange.value) {
    ignoreNextChange.value = false;
    return;
  }
  
  // 只有外部更新时才同步到编辑器
  if (editorRef.value.getHtml() !== newValue) {
    editorRef.value.setHtml(newValue);
  }
});

// 组件销毁时销毁编辑器
onBeforeUnmount(() => {
  const editor = editorRef.value;
  if (!editor) return;
  editor.destroy();
});

// 暴露编辑器实例方法
defineExpose({
  getEditor: () => editorRef.value,
  getHtml: () => editorRef.value?.getHtml(),
  getText: () => editorRef.value?.getText(),
  clear: () => editorRef.value?.clear(),
  setHtml: (html: string) => editorRef.value?.setHtml(html)
});
</script>