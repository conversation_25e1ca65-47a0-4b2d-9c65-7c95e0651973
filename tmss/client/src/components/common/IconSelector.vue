<template>
  <el-popover placement="bottom-start" trigger="click" v-model="visible" :width="240">
    <div class="grid grid-cols-4 gap-2 p-2 w-60 max-h-60 overflow-auto">
      <div
        v-for="(icon, index) in icons"
        :key="index"
        class="flex items-center justify-center h-10 cursor-pointer hover:bg-gray-100 rounded"
        @click="handleSelect(icon)"
      >
        <el-icon><component :is="icon" /></el-icon>
      </div>
    </div>

    <template #reference>
      <el-input v-model="selectedIcon" placeholder="请选择图标" readonly class="cursor-pointer" />
    </template>
  </el-popover>
</template>

<script setup>
import { ref } from 'vue'
import * as ElIcons from '@element-plus/icons-vue'

const props = defineProps({
  modelValue: String,
})
const emit = defineEmits(['update:modelValue'])

const visible = ref(false)
const selectedIcon = ref(props.modelValue)

// 获取所有图标名称
const icons = Object.keys(ElIcons)

function handleSelect(iconName) {
  selectedIcon.value = iconName
  emit('update:modelValue', iconName)
  visible.value = false
}
</script>