<template>
  <my-select 
    v-model="modelValue"
    :func="getClassList"
    placeholder="请选择班级"
    labelKey="name"
    valueKey="id"
    searchKey="name"
    multiple
    :initData="initData"
    v-bind="$attrs"
  />
</template>

<script lang="ts" setup>
import { defineProps, defineEmits, computed } from 'vue'
import { getClassList } from '@/api/class'

// 定义props
const props = defineProps<{
  modelValue: any[]
  initData?: any[]
}>()

// 定义emits
const emit = defineEmits<{
  (e: 'update:modelValue', value: any[]): void
}>()

// 双向绑定modelValue
const modelValue = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 默认initData
const initData = computed(() => props.initData || [])
</script>