<template>
  <el-tree-select v-model="modelValue" check-strictly :render-after-expand="false" placeholder="请选择专业" :data="majorList"
    :props="majorProps" v-bind="$attrs" :multiple="multiple" />
</template>

<script lang="ts" setup>
import { defineProps, defineEmits, computed, ref, onMounted } from 'vue'
import { getMajorList } from '@/api/course'

// 定义props
const props = defineProps<{
  modelValue: any,
  multiple?: boolean
}>()

// 定义emits
const emit = defineEmits<{
  (e: 'update:modelValue', value: any): void
}>()

// 双向绑定modelValue
const modelValue = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 专业数据
const majorList = ref([])
const majorProps = {
  label: 'name',
  value: 'id'
}

// 获取专业列表
const fetchMajorList = async () => {
  try {
    majorList.value = await getMajorList()
  } catch (error) {
    console.error('获取专业列表失败:', error)
  }
}

// 组件挂载时获取数据
onMounted(() => {
  fetchMajorList()
})
</script>