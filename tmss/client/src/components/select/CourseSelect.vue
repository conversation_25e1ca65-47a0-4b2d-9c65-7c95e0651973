<template>
  <my-select 
    v-model="modelValue"
    :func="getCourseList"
    :extraParams="extraParams"
    placeholder="请选择课程"
    labelKey="course.name"
    valueKey="course.id"
    searchKey="name"
    :initData="initData"
    v-bind="$attrs"
  />
</template>

<script lang="ts" setup>
import { defineProps, defineEmits, computed, watch, ref } from 'vue'
import { getCourseList } from '@/api/course'

// 定义props
const props = defineProps<{
  modelValue: any
  initData?: any[]
  plan_id?: number
}>()

// 定义emits
const emit = defineEmits<{
  (e: 'update:modelValue', value: any): void
}>()


// 计算额外参数
const extraParams = computed(() => ({
  status: 'published',
  ...(props.plan_id ? { plan_id: props.plan_id } : {})
}))

// 双向绑定modelValue
const modelValue = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 默认initData
const initData = computed(() => props.initData || [])
</script>