<template>
    <my-select 
      v-model="modelValue"
      :func="getClassScheduleList"
      :extraParams="extraParams"
      placeholder="请选择课表"
      labelKey="title"
      valueKey="id"
      searchKey="title"
      :initData="initData"
      v-bind="$attrs"
    />
  </template>
  
  <script lang="ts" setup>
  import { defineProps, defineEmits, computed } from 'vue'
  import { getClassScheduleList } from '@/api/schedule'
  
  // 定义props
  const props = defineProps<{
    modelValue: any
    initData?: any[]
    teaching_plan_id?: number  
  }>()
  
  // 定义emits
  const emit = defineEmits<{
    (e: 'update:modelValue', value: any): void
  }>()
  
  // 双向绑定modelValue
  const modelValue = computed({
    get: () => props.modelValue,
    set: (value) => emit('update:modelValue', value)
  })
  const extraParams = computed(() => ({
  //status: 'published',
  ...(props.teaching_plan_id ? { teaching_plan_id: props.teaching_plan_id } : {})
}))
  // 默认initData
  const initData = computed(() => props.initData || [])
  </script>