<template>
  <el-tree-select 
    v-model="modelValue"
    :data="categories"
    :props="defaultProps"
    :placeholder="placeholder"
    check-strictly
    clearable
    :disabled="disabled"
    v-bind="$attrs"
    @focus="loadCategories"
  />
</template>

<script lang="ts" setup>
import { ref, defineProps, defineEmits, computed, onMounted } from 'vue'
import { materialCateList } from '@/api/material'

// 定义props
const props = defineProps<{
  modelValue: string | number
  placeholder?: string
  disabled?: boolean
}>()

// 定义emits
const emit = defineEmits<{
  (e: 'update:modelValue', value: string | number): void
}>()

// 响应式数据
const categories = ref([])
const defaultProps = {
  label: "name",
  value: "id"
}

// 双向绑定modelValue
const modelValue = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 默认placeholder
const placeholder = computed(() => props.placeholder || '选择分类')

// 加载分类列表
const loadCategories = async () => {
  try {
    const res = await materialCateList()
    if (res.code == 0) {
      categories.value = res.data || []
    }
  } catch (error) {
    console.error('获取资料分类列表失败:', error)
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadCategories()
})

// 暴露方法给父组件
defineExpose({
  loadCategories
})
</script>