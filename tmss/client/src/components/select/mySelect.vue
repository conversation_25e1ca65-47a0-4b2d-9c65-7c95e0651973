<template>
    <el-select v-model="value" :loading="isLoading" class="w-full" filterable :placeholder="placeholder || '请选择'"
        :multiple="multiple" remote :remote-method="debouncedHandleRemoteMethod" clearable>
        <el-option v-for="(item, index) in list" :key="index" :label="getNestedValue(item, labelKey)"
            :value="getNestedValue(item, valueKey)"></el-option>
        <template #footer>
            <el-pagination size="small" background layout="prev, pager, next" :total="total" :page-size="10"
                :current-page="page" @current-change="handlePageChange" style="margin-top: 10px" />
        </template>
    </el-select>
</template>

<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue';
import { debounce } from '@/utils/debounce';

const props = defineProps<{
    modelValue: any,
    func: any,
    labelKey: string,
    valueKey: string,
    placeholder?: string,
    multiple?: boolean,
    searchKey?: string,
    initData?: any[], //回显初始数据
    extraParams?: any, //额外参数
}>()

const emit = defineEmits(['update:modelValue'])
const value = computed({
    get() {
        return props.modelValue
    },
    set(val) {
        emit('update:modelValue', val)
    }
})
// 工具函数：根据路径访问嵌套属性
const getNestedValue = (obj: any, path: string) => {
    return path.split('.').reduce((acc, key) => acc && acc[key], obj)
}

const isLoading = ref(false)
const list: any = ref([])
const getList = async () => {
    isLoading.value = true
    const params = {
        page: page.value,
        ...(query.value && props.searchKey && { [props.searchKey]: query.value }),
        ...(props.extraParams && props.extraParams)
    }
    console.log(params, 'params!!!')
    const res = await props.func(params)
    // console.log(res, '----')
    isLoading.value = false
    list.value = res.list || []
    total.value = res.total || 0
}
const page = ref(1)
const total = ref(0)
const handlePageChange = (val: number) => {
    page.value = val
    getList()
}
const query = ref('')

const debouncedHandleRemoteMethod = debounce((val: any) => {
    if (query.value === val) return
    query.value = val
    page.value = 1
    getList()
}, 500)
watch(() => props.extraParams, (newVal, oldVal) => {
  // 检查是否真的有变化
  if (JSON.stringify(newVal) !== JSON.stringify(oldVal)) {
    getList()
  }
}, { deep: true })
onMounted(() => {
    getList()
    if (props.initData && props.initData.length > 0) {

        list.value = { ...props.initData, ...list.value }
    }
    // console.log(list.value, 'list====')
})
</script>

<style scoped></style>