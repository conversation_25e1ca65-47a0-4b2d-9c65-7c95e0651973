<template>
  <el-tree-select 
    v-model="modelValue"
    placeholder="请选择章节"
    :data="chapterOptions"
    :props="chapterProps"
    clearable 
    class="w-full" 
    v-bind="$attrs"
  />
</template>

<script lang="ts" setup>
import { defineProps, defineEmits, computed, ref, watch } from 'vue'
import { getChapterByCourseID } from '@/api/chapter'

// 定义props
const props = defineProps<{
  modelValue: any
  courseId: number
}>()

// 定义emits
const emit = defineEmits<{
  (e: 'update:modelValue', value: any): void
}>()

// 双向绑定modelValue
const modelValue = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 章节数据
const chapterOptions = ref([])
const chapterProps = {
  label: "name",
  value: "id"
}

// 根据课程ID获取章节列表
const fetchChapters = async (courseId: number) => {
  if (!courseId) {
    chapterOptions.value = []
    return
  }
  
  try {
    chapterOptions.value = await getChapterByCourseID(courseId)
  } catch (error) {
    console.error('获取章节列表失败:', error)
    chapterOptions.value = []
  }
}

// 监听课程ID变化
watch(() => props.courseId, (newCourseId) => {
  fetchChapters(newCourseId)
}, { immediate: true })
</script>