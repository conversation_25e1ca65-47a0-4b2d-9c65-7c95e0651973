<template>
  <my-select 
    v-model="modelValue"
    placeholder="请选择教材"
    :func="getTextBookList"
    labelKey="title" 
    valueKey="id" 
    multiple
    searchKey="title" 
    :initData="initData"
    v-bind="$attrs"
  />
</template>

<script lang="ts" setup>
import { defineProps, defineEmits, computed } from 'vue'
import { getTextBookList } from '@/api/textbook'

// 定义props
const props = defineProps<{
  modelValue: any[]
  initData?: any[]
}>()

// 定义emits
const emit = defineEmits<{
  (e: 'update:modelValue', value: any[]): void
}>()

// 双向绑定modelValue
const modelValue = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 默认initData
const initData = computed(() => props.initData || [])
</script>