<!-- 代码已包含 CSS：使用 TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 -->

<template>
    <div class="min-h-screen bg-gray-50">

        <!-- 主内容区 -->
        <div class=" pt-16 p-6">

            <!-- 数据概览 -->
            <div class="grid grid-cols-4 gap-6 mb-6">
                <div v-for="(card, index) in dataCards" :key="index" class="bg-white rounded-lg p-6 shadow-sm">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-gray-500 text-sm mb-2">{{ card.title }}</p>
                            <p class="text-2xl font-semibold">{{ card.value }}</p>
                        </div>
                        <el-icon class="text-3xl text-purple-500">
                            <component :is="card.icon" />
                        </el-icon>
                    </div>
                    <div class="mt-4 text-sm">
                        <span :class="card.trend > 0 ? 'text-green-500' : 'text-red-500'">
                            {{ card.trend > 0 ? '+' : '' }}{{ card.trend }}%
                        </span>
                        <span class="text-gray-500 ml-2">较上周</span>
                    </div>
                </div>
            </div>

            <!-- 图表区域 -->
            <div class="grid grid-cols-2 gap-6 mb-6">
                <div class="bg-white rounded-lg p-6 shadow-sm">
                    <h3 class="text-lg font-medium mb-4">用户活跃度趋势</h3>
                    <div ref="chartContainer1" class="h-80"></div>
                </div>
                <div class="bg-white rounded-lg p-6 shadow-sm">
                    <h3 class="text-lg font-medium mb-4">系统资源使用率</h3>
                    <div ref="chartContainer2" class="h-80"></div>
                </div>
            </div>

            <!-- 最近操作日志 -->
            <div class="bg-white rounded-lg p-6 shadow-sm">
                <h3 class="text-lg font-medium mb-4">最近操作日志</h3>
                <el-table :data="logList" style="width: 100%">
                    <el-table-column prop="time" label="操作时间" />
                    <el-table-column prop="user" label="操作人" />
                    <el-table-column prop="action" label="操作内容" />
                    <el-table-column prop="status" label="状态">
                        <template #default="scope">
                            <el-tag :type="scope.row.status === '成功' ? 'success' : 'danger'">
                                {{ scope.row.status }}
                            </el-tag>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import * as echarts from 'echarts';



const dataCards = ref([
    { title: '总用户数', value: '12,846', trend: 12.5, icon: 'User' },
    { title: '系统在线数', value: '165', trend: 0.5, icon: 'Connection' },
    { title: '待处理工作数', value: '28', trend: -5.2, icon: 'Document' },
    { title: '评估规则数', value: '156', trend: 8.3, icon: 'Setting' }
]);

const logList = ref([
    { time: '2024-01-20 15:30:22', user: '陈思远', action: '修改系统配置参数', status: '成功' },
    { time: '2024-01-20 14:25:16', user: '张雨晴', action: '新增用户权限', status: '成功' },
    { time: '2024-01-20 13:15:45', user: '林明浩', action: '删除历史数据', status: '失败' },
    { time: '2024-01-20 11:30:08', user: '王梓萱', action: '更新工作流模板', status: '成功' },
    { time: '2024-01-20 10:20:33', user: '刘俊杰', action: '修改评估规则', status: '成功' }
]);

const handleLogout = () => {
    // 处理退出登录
};
const chartContainer1 = ref(null);
const chartContainer2 = ref(null);
onMounted(() => {


    if (chartContainer1.value) {
        const chart1 = echarts.init(chartContainer1.value);
        chart1.setOption({
            animation: false,
            tooltip: {
                trigger: 'axis'
            },
            xAxis: {
                type: 'category',
                data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
            },
            yAxis: {
                type: 'value'
            },
            series: [{
                data: [820, 932, 901, 934, 1290, 1330, 1320],
                type: 'line',
                smooth: true,
                color: '#1890ff'
            }]
        });
    }

    if (chartContainer2.value) {
        const chart2 = echarts.init(chartContainer2.value);
        chart2.setOption({
            animation: false,
            tooltip: {
                trigger: 'item'
            },
            series: [{
                type: 'pie',
                radius: ['40%', '70%'],
                data: [
                    { value: 35, name: 'CPU使用率' },
                    { value: 30, name: '内存使用率' },
                    { value: 25, name: '存储使用率' },
                    { value: 10, name: '带宽使用率' }
                ],
                color: ['#1890ff', '#40a9ff', '#69c0ff', '#91d5ff']
            }]
        });
    }
});
</script>

<style scoped>
.el-menu {
    border-right: none;
}

:deep(.el-input__wrapper) {
    box-shadow: none !important;
}

:deep(.el-menu-item.is-active) {
    background-color: #f3f0ff;
    color: #7E57C2;
}

:deep(.el-sub-menu__title:hover),
:deep(.el-menu-item:hover) {
    background-color: #f3f0ff !important;
}
</style>
