<!-- 代码已包含 CSS：使用 TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 -->

<template>
    <div class="min-h-screen bg-gray-50 flex flex-col">

        <div class="flex flex-1 overflow-hidden">
            <!-- 主内容区 -->
            <main class="flex-1 overflow-auto p-6">

                <!-- 欢迎区域 -->
                <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <h2 class="text-xl font-bold text-gray-800 mb-2">上午好，张教官</h2>
                            <p class="text-gray-500">今天是 {{ currentDate }}，您有 {{ pendingCount }} 项待审核内容</p>
                        </div>
                        <el-button type="primary" class="!rounded-button whitespace-nowrap" @click="handleQuickCreate">
                            <el-icon class="mr-1">
                                <Plus />
                            </el-icon>
                            快速创建课程
                        </el-button>
                    </div>
                </div>

                <!-- 数据概览 -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                    <div v-for="(item, index) in overviewData" :key="index"
                        class="bg-white rounded-lg shadow-sm p-6 hover:shadow-md transition-shadow cursor-pointer"
                        @click="handleCardClick(item)">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-gray-500 text-sm">{{ item.title }}</p>
                                <p class="text-2xl font-bold mt-2">{{ item.value }}</p>
                            </div>
                            <div class="w-12 h-12 rounded-full flex items-center justify-center" :class="item.bgColor">
                                <el-icon :size="24" :color="item.iconColor">
                                    <component :is="item.icon" />
                                </el-icon>
                            </div>
                        </div>
                        <p class="text-xs text-gray-400 mt-3">{{ item.desc }}</p>
                    </div>
                </div>

                <!-- 待审核事项 -->
                <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold">待审核事项</h3>
                        <el-button type="text"
                            class="!text-blue-600 hover:!bg-blue-50 !rounded-button whitespace-nowrap"
                            @click="handleViewAll">
                            查看全部
                        </el-button>
                    </div>
                    <el-table :data="pendingList" style="width: 100%">
                        <el-table-column prop="name" label="名称" />
                        <el-table-column prop="type" label="类型">
                            <template #default="{ row }">
                                <el-tag :type="getTagType(row.type)">{{ row.type }}</el-tag>
                            </template>
                        </el-table-column>
                        <el-table-column prop="submitter" label="提交人" />
                        <el-table-column prop="submitTime" label="提交时间" />
                        <el-table-column label="操作">
                            <template #default="{ row }">
                                <el-button size="small" type="primary" class="!rounded-button whitespace-nowrap"
                                    @click="handleReview(row)">
                                    审核
                                </el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>

                <!-- 近期课程 -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div class="bg-white rounded-lg shadow-sm p-6">
                        <h3 class="text-lg font-semibold mb-4">近期课程安排</h3>
                        <div class="space-y-4">
                            <div v-for="(course, index) in recentCourses" :key="index"
                                class="flex items-start p-3 hover:bg-gray-50 rounded-lg cursor-pointer"
                                @click="handleCourseDetail(course)">
                                <div class="w-16 h-16 rounded-md overflow-hidden mr-4">
                                    <img src="https://mastergo.com/ai/api/search-image?query=a classroom with students learning on light background&width=100&height=100&orientation=squarish&flag=0d5d49021817e51ad5c86d575416a9a0"
                                        alt="课程封面" class="w-full h-full object-cover">
                                </div>
                                <div class="flex-1">
                                    <h4 class="font-medium text-gray-800">{{ course.name }}</h4>
                                    <p class="text-sm text-gray-500 mt-1">{{ course.time }}</p>
                                    <p class="text-sm text-gray-500">{{ course.location }}</p>
                                </div>
                                <el-tag :type="course.status === '已准备' ? 'success' : 'warning'">
                                    {{ course.status }}
                                </el-tag>
                            </div>
                        </div>
                    </div>

                    <!-- 系统通知 -->
                    <div class="bg-white rounded-lg shadow-sm p-6">
                        <h3 class="text-lg font-semibold mb-4">系统通知</h3>
                        <div class="space-y-4">
                            <div v-for="(notice, index) in systemNotices" :key="index"
                                class="p-3 hover:bg-gray-50 rounded-lg cursor-pointer"
                                @click="handleNoticeDetail(notice)">
                                <div class="flex items-center justify-between">
                                    <h4 class="font-medium text-gray-800">{{ notice.title }}</h4>
                                    <span class="text-xs text-gray-400">{{ notice.time }}</span>
                                </div>
                                <p class="text-sm text-gray-500 mt-1 line-clamp-2">{{ notice.content }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue';
import {
    Notebook, Document, DataAnalysis, Folder,
    Trophy, Finished, Bell, ArrowRight,
    Plus, Calendar, Collection, Edit, User
} from '@element-plus/icons-vue';

// 当前日期
const currentDate = computed(() => {
    const now = new Date();
    return `${now.getFullYear()}年${now.getMonth() + 1}月${now.getDate()}日`;
});

// 待审核数量
const pendingCount = ref(8);

// 概览数据
const overviewData = ref([
    {
        title: '待审核项',
        value: '8',
        icon: 'Finished',
        bgColor: 'bg-blue-50',
        iconColor: '#3b82f6',
        desc: '比昨日增加2项',
        route: '/review'
    },
    {
        title: '本周课程',
        value: '6',
        icon: 'Calendar',
        bgColor: 'bg-green-50',
        iconColor: '#10b981',
        desc: '已完成2节',
        route: '/course'
    },
    {
        title: '待批改作业',
        value: '24',
        icon: 'Collection',
        bgColor: 'bg-orange-50',
        iconColor: '#f59e0b',
        desc: '来自3个班级',
        route: '/homework'
    },
    {
        title: '学生提问',
        value: '5',
        icon: 'Edit',
        bgColor: 'bg-purple-50',
        iconColor: '#8b5cf6',
        desc: '3个待回复',
        route: '/qa'
    }
]);

// 待审核列表
const pendingList = ref([
    {
        id: 1,
        name: '计算机科学导论教学大纲',
        type: '教学大纲',
        submitter: '李老师',
        submitTime: '2023-05-15 09:30',
        status: '待审核'
    },
    {
        id: 2,
        name: '2023秋季学期教学计划',
        type: '教学计划',
        submitter: '王教授',
        submitTime: '2023-05-14 14:20',
        status: '待审核'
    },
    {
        id: 3,
        name: '数据结构与算法课件',
        type: '课件',
        submitter: '赵老师',
        submitTime: '2023-05-13 16:45',
        status: '待审核'
    },
    {
        id: 4,
        name: '数据库系统期末考试',
        type: '考试',
        submitter: '陈教授',
        submitTime: '2023-05-12 11:10',
        status: '待审核'
    }
]);

// 近期课程
const recentCourses = ref([
    {
        id: 1,
        name: '人工智能基础',
        time: '5月16日 10:00-12:00',
        location: '教学楼A 301',
        status: '已准备'
    },
    {
        id: 2,
        name: '机器学习',
        time: '5月17日 14:00-16:00',
        location: '实验楼B 105',
        status: '准备中'
    },
    {
        id: 3,
        name: '深度学习',
        time: '5月18日 08:00-10:00',
        location: '教学楼C 208',
        status: '已准备'
    }
]);

// 系统通知
const systemNotices = ref([
    {
        id: 1,
        title: '系统维护通知',
        content: '系统将于5月20日凌晨2:00-4:00进行维护升级，期间将无法访问，请提前做好准备。',
        time: '2小时前',
        read: false
    },
    {
        id: 2,
        title: '教学评估提醒',
        content: '2023年春季学期教学评估已经开始，请各位老师及时完成自评和学生评价工作。',
        time: '1天前',
        read: false
    },
    {
        id: 3,
        title: '新功能上线',
        content: '系统新增了课件批量上传功能，现在可以一次上传多个课件文件，大幅提升工作效率。',
        time: '3天前',
        read: true
    }
]);

// 获取标签类型
const getTagType = (type: string) => {
    const map: Record<string, string> = {
        '教学大纲': '',
        '教学计划': 'success',
        '课件': 'warning',
        '考试': 'danger'
    };
    return map[type] || '';
};

// 菜单选择
const handleMenuSelect = (index: string) => {
    console.log('select menu:', index);
};

// 卡片点击
const handleCardClick = (item: any) => {
    console.log('click card:', item);
};

// 审核操作
const handleReview = (row: any) => {
    console.log('review:', row);
};

// 查看全部
const handleViewAll = () => {
    console.log('view all');
};

// 课程详情
const handleCourseDetail = (course: any) => {
    console.log('course detail:', course);
};

// 通知详情
const handleNoticeDetail = (notice: any) => {
    console.log('notice detail:', notice);
};

// 快速创建
const handleQuickCreate = () => {
    console.log('quick create');
};

// 退出登录
const handleLogout = () => {
    console.log('logout');
};
</script>

<style scoped>
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

:deep(.el-menu) {
    border-right: none;
}

:deep(.el-table) {
    --el-table-border-color: transparent;
    --el-table-header-bg-color: #f9fafb;
}

:deep(.el-table th.el-table__cell) {
    background-color: #f9fafb;
}
</style>
