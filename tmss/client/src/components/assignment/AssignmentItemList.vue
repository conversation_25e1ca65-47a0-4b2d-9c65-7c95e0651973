<template>
    <div ref="container" class="w-1/1 bg-white rounded-lg shadow-sm overflow-y-auto">
        <div class="flex justify-around items-center mb-4">
            <h2 class="font-medium">已选（{{ items.length }}）</h2>
            <div>总分：{{ totalScore }}</div>
            <div>总时：{{ totalTime }}分钟</div>
        </div>

        <!-- 使用普通表格 -->
        <el-table ref="selectedTable" :data="items" row-key="question_key" @selection-change="handleSelectionChange">
            <el-table-column width="55" align="center" v-if="!isView">
                <template #default>
                    <el-icon class="drag-handle cursor-move" size="20">
                        <SwitchFilled />
                    </el-icon>
                </template>
            </el-table-column>

            <el-table-column label="序号" width="55" align="center">
                <template #default="{ $index }"> {{ $index + 1 }} </template>
            </el-table-column>

            <el-table-column label="标题" prop="title" show-overflow-tooltip />

            <el-table-column align="center" label="类型" width="100">
                <template #default="{ row }">
                    <el-tag :type="statusTypeMap[row.action_type]">
                        {{ typeMap[row.action_type] }}
                    </el-tag>
                </template>
            </el-table-column>

            <el-table-column align="center" label="必做" width="80">
                <template #default="{ row }">
                    <el-checkbox v-model="row.required" :disabled="isView" />
                </template>
            </el-table-column>

            <el-table-column align="center" label="分值" width="100">
                <template #default="{ row }">
                    <el-input v-model.number="row.score" @change="handleScoreChange" :disabled="isView" />
                </template>
            </el-table-column>

            <el-table-column align="center" label="时间" width="100">
                <template #default="{ row }">
                    <el-input v-model.number="row.minutes" @change="handleTimeChange" :disabled="isView" />
                </template>
            </el-table-column>

            <el-table-column align="center" label="操作" width="100" v-if="!isView">
                <template #default="{ row }">
                    <el-button size="small" type="danger" @click="handleRemove(row)">删除</el-button>
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>

<script setup lang="ts">
import { ref, defineProps, defineEmits, watch, onMounted, nextTick } from 'vue';
import Sortable from 'sortablejs';

const props = defineProps({
    modelValue: {
        type: Array,
        default: () => []
    },
    isView: {
        type: Boolean,
        default: false
    }
});

const emit = defineEmits(['update:modelValue', 'remove']);

const items = ref<any[]>([...props.modelValue]);
const totalScore = ref(0);
const totalTime = ref(0);
const selectedTable = ref();

const typeMap: any = {
    questions: '试题',
    courseware: '课件',
    resources: '资料'
};

const statusTypeMap: any = {
    'questions': 'success',
    'courseware': 'info',
    'resources': 'warning'
};


// 计算总分和总时间
const calculateTotals = () => {
    let score = 0;
    let time = 0;

    items.value.forEach((item: any) => {
        score += Number(item.score) || 0;
        time += Number(item.minutes) || 0;
    });

    totalScore.value = score;
    totalTime.value = time;
};

// 初始化拖拽功能
const initSortable = () => {
    if (props.isView) return;

    const tableWrapper = selectedTable.value?.$el;
    if (!tableWrapper) return;
    const tbody = tableWrapper.querySelector('tbody');
    if (tbody) {
        // 销毁旧实例（防止重复绑定）
        if (tbody.sortableInstance) {
            tbody.sortableInstance.destroy();
        }

        // 创建新实例
        tbody.sortableInstance = Sortable.create(tbody, {
            animation: 150,
            ghostClass: 'sortable-ghost',
            handle: '.drag-handle', // 指定拖拽手柄
            onEnd: ({ newIndex, oldIndex }) => {
                if (typeof oldIndex === 'number' && typeof newIndex === 'number' &&
                    oldIndex >= 0 && newIndex >= 0) {
                    const currRow = items.value[oldIndex];
                    items.value.splice(oldIndex, 1);
                    items.value.splice(newIndex, 0, currRow);
                }
            },
        });
    }
};

// 处理选择变化
const handleSelectionChange = (selection: any[]) => {
    // 可以根据需要处理选择变化
};

// 处理分值变化
const handleScoreChange = () => {
    calculateTotals();
};

// 处理时间变化
const handleTimeChange = () => {
    calculateTotals();
};

// 处理删除
const handleRemove = (row: any) => {
    const index = items.value.findIndex((item: any) => item.question_key == row.question_key);
    if (index !== -1) {
        // 保存被删除的项目信息，用于通知父组件
        const removedItem = items.value[index];
        items.value.splice(index, 1);

        // 通知父组件有项目被删除
        emit('remove', {
            type: removedItem.action_type,
            id: removedItem.ref_id
        });
    }
};

// 添加新项目
const addItems = (newItems: { type: string, items: any[] }) => {
    const newType = newItems.type
    const newItemsArray = newItems.items;

    // 创建新项目ID集合，用于快速查找
    const newItemIds = new Set(newItemsArray.map(item => item.id));

    // 过滤出当前类型的数据（即将被替换的数据）
    const currentTypeItems = items.value.filter(item => item.action_type === newType);

    // 找出需要删除的项目（在旧数据中存在但在新数据中不存在的）
    const itemsToRemove = currentTypeItems.filter(item => !newItemIds.has(item.ref_id));
    items.value = items.value.filter(item => !itemsToRemove.some(i => i.ref_id === item.ref_id));


    // 找出需要保留的项目（在旧数据中存在且在新数据中也存在的）
    const itemsToKeep = currentTypeItems.filter(item => newItemIds.has(item.ref_id));

    // 找出需要新增的项目（在新数据中存在但在旧数据中不存在的）
    const existingKeys = new Set(itemsToKeep.map(item => item.question_key));
    const itemsToAdd = newItemsArray
        .filter(item => {
            // 检查是否已经存在于保留的项目中
            const key = item.id + "_" + item.action_type;
            return !existingKeys.has(key);
        })
        .map(q => ({
            action_type: q.action_type,
            question_key: q.id + "_" + q.action_type,
            ref_id: q.id,
            title: q.title,
            required: false,
            content: JSON.stringify(q),
            score: q.score || 20,
            minutes: q.minutes || 0
        }));
    items.value = [...itemsToAdd, ...items.value];
    calculateTotals();
    // // 创建现有键的集合用于快速查找
    // const existingKeys = new Set(items.value.map((item: any) => item.question_key));

    // // 过滤新项并创建选中数据
    // const selectedData = newItems.items
    //     .filter(item => {
    //         const key = item.id + "_" + item.action_type;
    //         return !existingKeys.has(key);
    //     })
    //     .map(q => ({
    //         action_type: q.action_type,
    //         question_key: q.id + "_" + q.action_type,
    //         ref_id: q.id,
    //         title: q.title,
    //         required: false,
    //         content: JSON.stringify(q),
    //         score: q.score || 20,
    //         minutes: q.minutes || 0
    //     }));

    // // 只有当有新数据时才更新列表
    // if (selectedData.length > 0) {
    //     // 更新选中列表（新项在前）
    //     items.value = [...selectedData, ...items.value];
    //     calculateTotals();
    // }
};

// 初始化ID集合
const initIds = () => {
    // 使用 Map 按类型收集唯一 ID
    const idMap = new Map<string, Set<number>>([
        ['questions', new Set()],
        ['courseware', new Set()],
        ['resources', new Set()]
    ]);

    items.value.forEach((item: any) => {
        const typeSet = idMap.get(item.action_type);
        if (typeSet) {
            typeSet.add(item.ref_id);
        }
    });

    // 返回ID映射，命名与父组件保持一致
    return {
        questions: Array.from(idMap.get('questions') || []),
        courseware: Array.from(idMap.get('courseware') || []),
        resources: Array.from(idMap.get('resources') || [])
    };
};

onMounted(() => {
    calculateTotals();
    if (!props.isView) {
        nextTick(() => {
            initSortable();
        });
    }
});
const getItems = () => {
    return items.value;
};
const initItems = () => { 
    items.value = [];
    totalScore.value = 0;
    totalTime.value = 0;
};
defineExpose({
    addItems,
    initIds,
    getItems,
    initItems
});
</script>

<style scoped>
/* 拖拽手柄样式 */
.drag-handle {
    cursor: move;
    opacity: 0.6;
    transition: opacity 0.3s;
}

.drag-handle:hover {
    opacity: 1;
}

/* 拖拽时的样式 */
.sortable-ghost {
    opacity: 0.5;
    background-color: #ebeef5;
}
</style>