<template>
  <el-dialog v-model="visible" :title="title" width="80%" :close-on-click-modal="false">
    <!-- 搜索区 -->
    <div class="mb-4 flex flex-col sm:flex-row sm:items-center sm:space-x-4">
      <el-input
        v-model="searchQuery"
        placeholder="搜索试题名称"
        clearable
        class="w-full sm:w-64"
        @clear="handleSearch"
        @keyup.enter="handleSearch"
      >
        <template #append>
          <el-button @click="handleSearch">
            <el-icon><Search /></el-icon>
          </el-button>
        </template>
      </el-input>
      
      <el-select v-model="filterType" placeholder="试题类型" clearable class="mt-2 sm:mt-0 w-full sm:w-48">
        <el-option
          v-for="(label, value) in typeMap"
          :key="value"
          :label="label"
          :value="value"
        />
      </el-select>
      
      <el-button type="primary" class="mt-2 sm:mt-0" @click="handleSearch">搜索</el-button>
      <el-button @click="resetSearch">重置</el-button>
    </div>

    <!-- 试题表格 -->
    <div class="h-[60vh] overflow-auto">
      <el-table
        ref="tableRef"
        :data="questions"
        border
        highlight-current-row
        @selection-change="handleSelectionChange"
        @row-click="handleRowClick"
      >
        <el-table-column v-if="selectionMode === 'multiple'" type="selection" width="55" align="center" />
        
        <el-table-column prop="title" label="试题名称" min-width="200" show-overflow-tooltip />
        
        <el-table-column prop="question_type" label="试题类型" width="120" align="center">
          <template #default="{ row }">
            <el-tag :type="tagMap[row.question_type]">
              {{ typeMap[row.question_type] }}
            </el-tag>
          </template>
        </el-table-column>
        
        <!-- <el-table-column prop="status" label="状态" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="statusTagMap[row.status]">
              {{ statusMap[row.status] }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="100" align="center">
          <template #default="{ row }">
            <el-button 
              size="small" 
              type="primary" 
              @click.stop="handlePreview(row)"
            >
              预览
            </el-button>
          </template>
        </el-table-column> -->
      </el-table>
    </div>

    <!-- 分页 -->
    <div class="mt-4 flex justify-center">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :total="total"
        layout="prev, pager, next"
        @current-change="fetchQuestions"
      />
    </div>

    <!-- 底部操作按钮 -->
    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="handleConfirm">确定</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, computed, nextTick } from 'vue'
import { getQuestionListByCourseAndChapter } from '@/api/question'
import { ElMessage } from 'element-plus'
import { typeMap, tagMap } from '@/api/status';
const props = defineProps({
  // 选择模式：single(单选) / multiple(多选)
  mode: {
    type: String,
    default: 'multiple',
    validator: (value: string) => ['single', 'multiple'].includes(value)
  },
  // 初始已选试题ID
  selectedIds: {
    type: Array,
    default: () => []
  },
  // 课程ID
  courseId: {
    type: Number,
    required: true
  },
  // 章节ID
  chapterId: {
    type: Number,
    required: true
  },
  // 对话框标题
  title: {
    type: String,
    default: '选择试题'
  }
})

const emit = defineEmits(['confirm', 'update:visible'])

// 对话框显示控制
const visible = defineModel<boolean>('visible', { default: false })


// 数据状态
const questions = ref<any[]>([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const searchQuery = ref('')
const filterType = ref('')
const selectedQuestions = ref<any[]>([])

const selectionMode = computed(() => props.mode)

// 获取试题列表
const fetchQuestions = async () => {
  try {
    const params = {
      page: currentPage.value,
      page_size: pageSize.value,
      ...(props.courseId && { course_id: props.courseId }), 
      ...(props.chapterId && { chapter_id: props.chapterId }),
      ...(searchQuery.value && { name: searchQuery.value }),
      ...(filterType.value && { question_type: filterType.value })
    }
    
    const res = await getQuestionListByCourseAndChapter(params)
    questions.value = res.list || []
    total.value = res.total || 0
    console.log(props.selectedIds)
    // 设置初始选中状态
    nextTick(() => {
      if (props.selectedIds.length > 0) {
        questions.value.forEach(row => {
          if (props.selectedIds.includes(row.id)) {
            tableRef.value?.toggleRowSelection(row, true)
          }
        })
      }
    })
  } catch (error) {
    ElMessage.error('获取试题列表失败')
    console.error(error)
  }
}

// 处理搜索
const handleSearch = () => {
  currentPage.value = 1
  fetchQuestions()
}

// 重置搜索
const resetSearch = () => {
  searchQuery.value = ''
  filterType.value = ''
  handleSearch()
}

// 处理选择变化
const handleSelectionChange = (selection: any[]) => {
  if (selectionMode.value === 'multiple') {
    selectedQuestions.value = selection
  }
}

// 处理行点击（单选模式）
const handleRowClick = (row: any) => {
  if (selectionMode.value === 'single') {
    selectedQuestions.value = [row]
    tableRef.value?.setCurrentRow(row)
  }
}


// 确认选择
const handleConfirm = () => {
  if (selectedQuestions.value.length === 0) {
    ElMessage.warning('请至少选择一道试题')
    return
  }
  
  emit('confirm', selectedQuestions.value)
  visible.value = false
}

// 监听对话框显示状态
watch(visible, (val) => {
  if (val) {
    selectedQuestions.value = []
    fetchQuestions()
    // 重置选择
    //selectedQuestions.value = []
  }
})

// 表格引用
const tableRef = ref<any>(null)
</script>

<style scoped>
.el-table {
  margin-top: 16px;
}
</style>