<template>
    <div class="assignment-action-selector">

        <el-radio-group v-model="currentActionType">
            <el-radio-button :label="item.label" :value="item.value" v-for="item in actionList" :key="item.value"
                @click="handleActionClick(item.value)" />
        </el-radio-group>


        <QuestionSelector v-model:visible="questionSelectorVisible" :mode="'multiple'" :title="'选择试题'"
            :chapterId="chapterId" :courseId="courseId" :selectedIds="initSelectedIds.questions"
            @confirm="handleQuestionSelect" />

        <CoursewareSelector v-model:visible="coursewareSelectorVisible" :mode="'multiple'" :title="'选择课件'"
            :courseId="courseId" :chapterId="chapterId" :selectedIds="initSelectedIds.courseware"
            @confirm="handleCoursewareSelect" />

        <MaterialSelector v-model:visible="materialSelectorVisible" :mode="'multiple'"
            :selectedIds="initSelectedIds.resources" :chapterId="chapterId" :title="'选择资料'"
            @confirm="handleMaterialSelect" />
    </div>
</template>

<script setup lang="ts">
import { ref, defineProps, defineEmits, watch } from 'vue';
import { errMsg } from '@/utils/msg';

const props = defineProps({
    modelValue: {
        type: String,
        default: ''
    },
    courseId: {
        type: [String, Number],
        default: ''
    },
    chapterId: {
        type: [String, Number],
        default: ''
    },
    initSelectedIds: {
        type: Object,
        default: () => ({
            questions: [],
            courseware: [],
            resources: []
        })
    },
    isView: {
        type: Boolean,
        default: false
    }
});

const emit = defineEmits(['update:modelValue', 'select', 'action-click']);

const currentActionType = ref(props.modelValue);

const actionList = [
    { label: '做试题', value: 'questions' },
    { label: '看课件', value: 'courseware' },
    { label: '看资料', value: 'resources' },
];

const questionSelectorVisible = ref(false);
const coursewareSelectorVisible = ref(false);
const materialSelectorVisible = ref(false);


// 监听外部值变化
watch(() => props.modelValue, (val) => {
    currentActionType.value = val;
});

// 监听当前动作类型变化
watch(() => props.modelValue, (val) => {
    if (val !== currentActionType.value) {
        currentActionType.value = val;
    }
});
const handleActionClick = (value: string) => {
    emit('action-click', value);
};
const handleQuestionSelect = (items: any[]) => {
    emit('select', { type: 'questions', items });
};

const handleCoursewareSelect = (items: any[]) => {
    emit('select', { type: 'courseware', items });
};

const handleMaterialSelect = (items: any[]) => {
    emit('select', { type: 'resources', items });
};

defineExpose({
    openSelector: (type: string) => {
        if (!props.courseId || Number(props.courseId) < 1) {
            errMsg('请选择课程');
            return false;
        }
        if (!props.chapterId || Number(props.chapterId) < 1) {
            errMsg('请选择章节');
            return false;
        }

        currentActionType.value = type;
        emit('update:modelValue', type);

        if (type === 'questions') {
            questionSelectorVisible.value = true;
        } else if (type === 'courseware') {
            coursewareSelectorVisible.value = true;
        } else if (type === 'resources') {
            materialSelectorVisible.value = true;
        }
    }
});
</script>