<template>
  <el-dialog v-model="visible" :title="title" width="80%" :close-on-click-modal="false">
    <!-- 搜索区 -->
    <div class="mb-4 flex flex-col sm:flex-row sm:items-center sm:space-x-4">
      <el-input
        v-model="searchQuery"
        placeholder="搜索课件名称"
        clearable
        class="w-full sm:w-64"
        @clear="handleSearch"
        @keyup.enter="handleSearch"
      >
        <template #append>
          <el-button @click="handleSearch">
            <el-icon><Search /></el-icon>
          </el-button>
        </template>
      </el-input>
      
      <el-select v-model="filterType" placeholder="课件类型" clearable class="mt-2 sm:mt-0 w-full sm:w-48">
        <el-option
          v-for="(label, value) in typeMap"
          :key="value"
          :label="label"
          :value="value"
        />
      </el-select>
      
      <el-button type="primary" class="mt-2 sm:mt-0" @click="handleSearch">搜索</el-button>
      <el-button @click="resetSearch">重置</el-button>
    </div>

    <!-- 课件表格 -->
    <div class="h-[60vh] overflow-auto">
      <el-table
        ref="tableRef"
        :data="coursewares"
        border
        highlight-current-row
        @selection-change="handleSelectionChange"
        @row-click="handleRowClick"
      >
        <el-table-column v-if="selectionMode === 'multiple'" type="selection" width="55" align="center" />
        
        <el-table-column prop="courseware.title" label="课件名称" min-width="200" show-overflow-tooltip />
        
        <el-table-column prop="courseware.courseware_type" label="课件类型" width="120" align="center">
          <template #default="{ row }">
            <el-tag :type="row.courseware.courseware_type === 'virtual_courseware' ? 'primary' : 'success'">
              {{ typeMap[row.courseware.courseware_type] }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="courseware.course_name" label="所属课程" min-width="150" show-overflow-tooltip />
        
        <el-table-column prop="courseware.chapter_name" label="所属章节" min-width="150" show-overflow-tooltip />
        
        <!-- <el-table-column label="操作" width="100" align="center">
          <template #default="{ row }">
            <el-button 
              size="small" 
              type="primary" 
              @click.stop="handlePreview(row)"
            >
              预览
            </el-button>
          </template>
        </el-table-column> -->
      </el-table>
    </div>

    <!-- 分页 -->
    <div class="mt-4 flex justify-center">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :total="total"
        layout="prev, pager, next"
        @current-change="fetchCoursewares"
      />
    </div>

    <!-- 底部操作按钮 -->
    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="handleConfirm">确定</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, computed, nextTick } from 'vue'
import { getCoursewareList } from '@/api/courseware'
import { ElMessage } from 'element-plus'

const props = defineProps({
  // 选择模式：single(单选) / multiple(多选)
  mode: {
    type: String,
    default: 'multiple',
    validator: (value: string) => ['single', 'multiple'].includes(value)
  },
  // 初始已选课件ID
  selectedIds: {
    type: Array,
    default: () => []
  },
  // 课程ID（可选）
  courseId: {
    type: Number,
    default: null
  },
  // 章节ID（可选）
  chapterId: {
    type: Number,
    default: null
  },
  // 对话框标题
  title: {
    type: String,
    default: '选择课件'
  }
})

const emit = defineEmits(['confirm', 'update:visible'])

// 对话框显示控制
const visible = defineModel<boolean>('visible', { default: false })

// 课件类型映射
const typeMap: Record<string, string> = {
  'virtual_courseware': '虚拟课件',
  'theory_courseware': '理论课件'
}

// 数据状态
const coursewares = ref<any[]>([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const searchQuery = ref('')
const filterType = ref('')
const selectedCoursewares = ref<any[]>([])

const selectionMode = computed(() => props.mode)

// 获取课件列表
const fetchCoursewares = async () => {
  try {
    const params: any = {
      page: currentPage.value,
      page_size: pageSize.value,
      ...(searchQuery.value && { name: searchQuery.value }),
      ...(filterType.value && { courseware_type: filterType.value }),
      status: 'published' // 只显示已发布的课件
    }
    
    // 添加课程和章节筛选条件（如果提供）
    if (props.courseId) params.course_id = props.courseId
    if (props.chapterId) params.chapter_id = props.chapterId
    
    const res = await getCoursewareList(params)
    if(!res || !res.list)return;
    coursewares.value = res.list || []
    total.value = res.total || 0
    
    // 设置初始选中状态
    nextTick(() => {
      if (props.selectedIds.length > 0) {
        coursewares.value.forEach(row => {
          if (props.selectedIds.includes(row.id)) {
            tableRef.value?.toggleRowSelection(row, true)
          }
        })
      }
    })
  } catch (error) {
    //ElMessage.error('获取课件列表失败')
    console.error(error)
  }
}

// 处理搜索
const handleSearch = () => {
  currentPage.value = 1
  fetchCoursewares()
}

// 重置搜索
const resetSearch = () => {
  searchQuery.value = ''
  filterType.value = ''
  handleSearch()
}

// 处理选择变化
const handleSelectionChange = (selection: any[]) => {
  if (selectionMode.value === 'multiple') {
    selectedCoursewares.value = selection
  }
}

// 处理行点击（单选模式）
const handleRowClick = (row: any) => {
  if (selectionMode.value === 'single') {
    selectedCoursewares.value = [row]
    tableRef.value?.setCurrentRow(row)
  }
}

// 预览课件
// const handlePreview = (row: any) => {
//   // 这里实现预览逻辑
//   console.log('预览课件:', row)
//   ElMessage.info(`预览课件: ${row.title}`)
// }

// 确认选择
const handleConfirm = () => {
  if (selectedCoursewares.value.length === 0) {
    ElMessage.warning('请至少选择一个课件')
    return
  }
  
  emit('confirm', selectedCoursewares.value)
  visible.value = false
}

// 监听对话框显示状态
watch(visible, (val) => {
  if (val) {
    fetchCoursewares()
    // 重置选择
    selectedCoursewares.value = []
  }
})

// 表格引用
const tableRef = ref<any>(null)
</script>

<style scoped>
.el-table {
  margin-top: 16px;
}
</style>