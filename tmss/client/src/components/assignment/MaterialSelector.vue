<!-- src/components/assignment/MaterialSelector.vue -->
<template>
  <el-dialog v-model="visible" :title="title" width="80%" :close-on-click-modal="false">
    <!-- 搜索区 -->
    <div class="mb-4 flex flex-col sm:flex-row sm:items-center sm:space-x-4">
      <el-input
        v-model="searchQuery"
        placeholder="搜索资料名称"
        clearable
        class="w-full sm:w-64"
        @clear="handleSearch"
        @keyup.enter="handleSearch"
      >
        <template #append>
          <el-button @click="handleSearch">
            <el-icon><Search /></el-icon>
          </el-button>
        </template>
      </el-input>
      
      <el-tree-select
        v-model="filterCategory"
        placeholder="选择分类"
        :data="categories"
        :props="defaultProps"
        clearable
        class="mt-2 sm:mt-0 w-full sm:w-64"
        check-strictly
      />
      
      <el-button type="primary" class="mt-2 sm:mt-0" @click="handleSearch">搜索</el-button>
      <el-button @click="resetSearch">重置</el-button>
    </div>

    <!-- 资料表格 -->
    <div class="h-[60vh] overflow-auto">
      <el-table
        ref="tableRef"
        :data="materials"
        border
        highlight-current-row
        @selection-change="handleSelectionChange"
        @row-click="handleRowClick"
      >
        <el-table-column v-if="selectionMode === 'multiple'" type="selection" width="55" align="center" />
        
        <el-table-column prop="name" label="资料名称" min-width="200" show-overflow-tooltip />
        
        <el-table-column prop="category_name" label="所属分类" min-width="150" show-overflow-tooltip />
        <el-table-column prop="chapter_name" label="章节" min-width="150" show-overflow-tooltip />
        
        <el-table-column label="类型" width="100" align="center">
          <template #default="{ row }">
            {{ row.ext?.slice(1) }}
          </template>
        </el-table-column>
        
        <el-table-column label="大小" width="100" align="center">
          <template #default="{ row }">
            {{ formatFileSize(row.size) }}
          </template>
        </el-table-column>
        
       
      </el-table>
    </div>

    <!-- 分页 -->
    <div class="mt-4 flex justify-center">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :total="total"
        layout="prev, pager, next"
        @current-change="fetchMaterials"
      />
    </div>

    <!-- 底部操作按钮 -->
    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="handleConfirm">确定</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, computed, nextTick } from 'vue'
import { getResourcesListByCourseAndChapter } from '@/api/material'
import { materialCateList } from '@/api/material'
import { ElMessage } from 'element-plus'

const props = defineProps({
  // 选择模式：single(单选) / multiple(多选)
  mode: {
    type: String,
    default: 'multiple',
    validator: (value: string) => ['single', 'multiple'].includes(value)
  },
  // 初始已选资料ID
  selectedIds: {
    type: Array,
    default: () => []
  },
  chapterId: {
    type: Number,
    default: null
  },
  // 对话框标题
  title: {
    type: String,
    default: '选择资料'
  }
})

const emit = defineEmits(['confirm', 'update:visible'])

// 对话框显示控制
const visible = defineModel<boolean>('visible', { default: false })



// 分类树形选择配置
const defaultProps = {
  label: "name",
  value: "id"
}

// 数据状态
const materials = ref<any[]>([])
const categories = ref<any[]>([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const searchQuery = ref('')
const filterCategory = ref('')
const selectedMaterials = ref<any[]>([])

const selectionMode = computed(() => props.mode)

// 获取资料列表
const fetchMaterials = async () => {
  try {
    const params: any = {
      page: currentPage.value,
      page_size: pageSize.value,
      ...(searchQuery.value && { name: searchQuery.value }),
      ...(filterCategory.value && { category_id: filterCategory.value }),
      status: 'published' // 只显示已发布的资料
    }
    if (props.chapterId) params.chapter_id = props.chapterId
    const res = await getResourcesListByCourseAndChapter(params)
    materials.value = res.list || []
    total.value = res.total || 0
    
    // 设置初始选中状态
    nextTick(() => {
      if (props.selectedIds.length > 0) {
        materials.value.forEach(row => {
          if (props.selectedIds.includes(row.id)) {
            tableRef.value?.toggleRowSelection(row, true)
          }
        })
      }
    })
  } catch (error) {
    ElMessage.error('获取资料列表失败')
    console.error(error)
  }
}

// 获取分类列表
const fetchCategories = async () => {
  try {
    const res = await materialCateList()
    if (res.code === 0) {
      categories.value = res.data || []
    }
  } catch (error) {
    console.error('获取分类列表失败', error)
  }
}

// 处理搜索
const handleSearch = () => {
  currentPage.value = 1
  fetchMaterials()
}

// 重置搜索
const resetSearch = () => {
  searchQuery.value = ''
  filterCategory.value = ''
  handleSearch()
}

// 处理选择变化
const handleSelectionChange = (selection: any[]) => {
  if (selectionMode.value === 'multiple') {
    selectedMaterials.value = selection
  }
}

// 处理行点击（单选模式）
const handleRowClick = (row: any) => {
  if (selectionMode.value === 'single') {
    selectedMaterials.value = [row]
    tableRef.value?.setCurrentRow(row)
  }
}

// 确认选择
const handleConfirm = () => {
  if (selectedMaterials.value.length === 0) {
    ElMessage.warning('请至少选择一个资料')
    return
  }
  
  emit('confirm', selectedMaterials.value)
  visible.value = false
}

// 格式化文件大小
const formatFileSize = (bytes: number) => {
  if (bytes === 0) return "0 Bytes";
  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
}


// 监听对话框显示状态
watch(visible, (val) => {
  if (val) {
    selectedMaterials.value = []
    fetchCategories()
    fetchMaterials()
    // 重置选择
    
  }
})

// 表格引用
const tableRef = ref<any>(null)
</script>

<style scoped>
.el-table {
  margin-top: 16px;
}
</style>