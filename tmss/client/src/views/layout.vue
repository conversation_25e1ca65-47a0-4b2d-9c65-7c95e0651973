<template>
  <div class="flex flex-col h-screen overflow-hidden">
    <!-- 顶部导航 -->
    <header class="bg-cyan-600 text-white h-16">
      <div class=" mx-auto  h-full flex items-center justify-between">
        <div :class="['w-64 transition-all duration-300 ease-in-out', { '!w-15': !expand }]"
          class="text-xl h-full  font-medium flex items-center justify-center bg-cyan-700">
          <span v-if="expand">教学管理系统</span>
          <el-icon :class="['ml-18 cursor-pointer', { '!ml-0': !expand }]" :size="24" @click="handleExpand">
            <Fold v-if="expand" />
            <Expand v-else />
          </el-icon>
        </div>
        <div class="flex flex-1 items-center justify-between px-4">
         <div></div>
          <div class="flex  items-center space-x-4 px-4">
            <el-icon @click="store.is_locked = true">
              <Lock />
            </el-icon>
            <span>欢迎您,{{ store.userInfo.username }}</span>
            <el-dropdown v-if="store.rolesData.length > 1" trigger="click" @command="switchRole">
            <el-button class="el-dropdown-link flex items-center text-base text-white cursor-pointer">
              切换角色
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item v-for="item in store.rolesData.filter((item: any) => item.role_code !== 'user')"
                  :key="item.id" :command="item.role_code" :disabled="item.role_code == store.userCode">{{
                    item.role_name
                  }}</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
            <el-button @click="logout" type="info">退出</el-button>
          </div>

        </div>
      </div>
    </header>

    <div class="flex flex-1 bg-white overflow-hidden">
      <!-- 左侧菜单 -->
      <side :class="['transition-all duration-300 ease-in-out', { '!w-15': !expand }]" :expand="expand" />
      <!-- 右侧内容区 -->
      <main class="flex-1 overflow-hidden flex flex-col">
        <tabs ref="myTabs" />
        <router-view></router-view>
      </main>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { useTabsStore } from '@/store/tabs';
import { useUserStore } from '@/store/user';
import { onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';



const store = useUserStore()
const router = useRouter()
//菜单是否展开
const expand = ref<boolean>(true);

/** 菜单展开 */
const handleExpand = () => {
  expand.value = !expand.value;
}

const myTabs = ref()
const switchRole = (role: string) => {
  myTabs.value.closeAllTabs()
  store.changeRole(role)
}
const tabsStore = useTabsStore()
const logout = async () => {
  tabsStore.closeAllTabs()
  router.push('/login')
  await store.onLogOut()
}


onMounted(() => {
  if (!store.token) return router.push('/login')
})
</script>

<style scoped>
.el-menu {
  --el-menu-hover-bg-color: #374151;
}
</style>
