<template>
    <div class="courseware min-h-screen">
        <!-- 搜索区域 -->
        <div class="bg-white rounded-lg shadow p-6 mb-6">
            <div class="grid grid-cols-3 gap-4">
                <!-- 课件ID/文件名搜索 -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">课件ID/文件名</label>
                    <el-input v-model="searchParams.keyword" placeholder="请输入课件ID或文件名" clearable class="w-full" />
                </div>
                <!-- 课程ID选择 -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">课程ID</label>
                    <el-select v-model="searchParams.courseId" placeholder="请选择课程ID" clearable class="w-full">
                        <el-option v-for="course in courseOptions" :key="course.value" :label="course.label"
                            :value="course.value" />
                    </el-select>
                </div>
                <!-- 章节ID选择 -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">章节ID</label>
                    <el-select v-model="searchParams.chapterId" placeholder="请选择章节ID" clearable class="w-full">
                        <el-option v-for="chapter in chapterOptions" :key="chapter.value" :label="chapter.label"
                            :value="chapter.value" />
                    </el-select>
                </div>
                <!-- 状态选择 -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">状态</label>
                    <el-select v-model="searchParams.status" multiple placeholder="请选择状态" clearable class="w-full">
                        <el-option label="草稿" value="draft" />
                        <el-option label="已发布" value="published" />
                        <el-option label="已归档" value="archived" />
                        <el-option label="待审核" value="pending" />
                        <el-option label="已审核" value="approved" />
                    </el-select>
                </div>
                <!-- 上传时间范围 -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">上传时间范围</label>
                    <el-date-picker v-model="searchParams.dateRange" type="daterange" range-separator="至"
                        start-placeholder="开始日期" end-placeholder="结束日期" class="w-full" />
                </div>
                <div class="flex items-end justify-end col-span-3">
                    <el-button @click="handleReset">重置</el-button>
                </div>
            </div>
        </div>
        <!-- 操作按钮区 -->
        <div class="mb-6">
            <el-button type="primary" @click="showCreateDialog">创建课件</el-button>
        </div>
        <!-- 表格区域 -->
        <div class="bg-white rounded-lg shadow overflow-hidden">
            <el-table :data="tableData" border style="width: 100%" v-loading="loading" class="w-full">
                <el-table-column prop="id" label="课件ID" />
                <el-table-column prop="fileName" label="文件名" width="300" />
                <el-table-column prop="courseId" label="课程ID" />
                <el-table-column prop="chapterId" label="章节ID" />
                <el-table-column prop="status" label="状态">
                    <template #default="{ row }">
                        <el-tag :type="getStatusTagType(row.status)" size="small">
                            {{ getStatusText(row.status) }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="uploadTime" label="上传时间" width="180" />
                <el-table-column label="操作" width="300">
                    <template #default="{ row }">
                        <el-button @click="handleEdit(row)">编辑</el-button>
                        <el-button type="primary" v-if="row.status === 'draft' && isTeacherRole"
                            @click="handleSubmitReview(row)">
                            提交审核
                        </el-button>
                        <el-button type="warning" v-if="row.status === 'pending' && isAdminRole"
                            @click="handleReview(row)">
                            审核
                        </el-button>
                        <el-button type="success" v-if="row.status === 'approved'" @click="handlePublish(row)">
                            发布
                        </el-button>
                        <el-button type="info" v-if="row.status === 'published'" @click="showReviseDialog(row)">
                            修订
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <!-- 分页区域 -->
        <div class="mt-6 flex justify-end">
            <el-pagination v-model:current-page="pagination.current" v-model:page-size="pagination.size"
                :total="pagination.total" :page-sizes="[10, 20, 50, 100]"
                layout="total, sizes, prev, pager, next, jumper" @size-change="handleSizeChange"
                @current-change="handleCurrentChange" />
        </div>
        <!-- 创建/编辑课件弹窗 -->
        <el-dialog v-model="dialogVisible" :title="isEditMode ? '编辑课件' : '创建课件'" width="50%">
            <el-form :model="form" label-width="120px">
                <el-form-item label="理论试题" required>
                    <el-select v-model="form.theory_question_id" placeholder="请选择理论试题" class="w-full" filterable
                        clearable>
                        <el-option v-for="question in theoryQuestionsOptions" :key="question.id" :label="question.title"
                            :value="question.id" />
                    </el-select>
                </el-form-item>
                <el-form-item label="课程ID" required>
                    <el-select v-model="form.courseId" placeholder="请选择课程ID" class="w-full">
                        <el-option v-for="course in courseOptions" :key="course.value" :label="course.label"
                            :value="course.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="章节ID" required>
                    <el-select v-model="form.chapterId" placeholder="请选择章节ID" class="w-full">
                        <el-option v-for="chapter in chapterOptions" :key="chapter.value" :label="chapter.label"
                            :value="chapter.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="课件文件" required>
                    <el-upload class="upload-demo" action="#" accept=".zip" :limit="1" :on-change="handleFileChange"
                        :auto-upload="false">
                        <el-button type="primary">点击上传</el-button>
                        <template #tip>
                            <div class="el-upload__tip">
                                支持ZIP格式文件
                            </div>
                        </template>
                    </el-upload>
                </el-form-item>
                <el-form-item label="文件名" required>
                    <el-input v-model="form.fileName" />
                </el-form-item>
                <el-form-item label="课件描述">
                    <el-input v-model="form.description" type="textarea" :rows="3" />
                </el-form-item>
                <el-form-item label="适用对象">
                    <el-input v-model="form.target" />
                </el-form-item>
                <el-form-item label="关联知识点">
                    <el-select v-model="form.knowledgePoints" multiple filterable allow-create placeholder="请输入或选择知识点"
                        class="w-full" />
                </el-form-item>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="handleSubmit">提交</el-button>
                </span>
            </template>
        </el-dialog>
        <!-- 修订弹窗 -->
        <el-dialog v-model="reviseDialogVisible" title="修订课件" width="40%">
            <el-form :model="reviseForm" label-width="120px">
                <el-form-item label="新课件文件" required>
                    <el-upload class="upload-demo" action="#" accept=".zip" :limit="1"
                        :on-change="handleReviseFileChange" :auto-upload="false">
                        <el-button type="primary">点击上传</el-button>
                        <template #tip>
                            <div class="el-upload__tip">
                                支持ZIP格式文件
                            </div>
                        </template>
                    </el-upload>
                </el-form-item>
                <el-form-item label="修订说明">
                    <el-input v-model="reviseForm.revisionNote" type="textarea" :rows="3" />
                </el-form-item>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="reviseDialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="handleReviseSubmit">提交</el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>
<script lang="ts" setup>
import { ref, reactive } from "vue";
import { msg, confirmMsg as confirm } from "@/utils/msg";

// 理论试题模拟数据
const theoryQuestionsOptions: any = ref([
    { id: "1", title: "单选题1" },
    { id: "2", title: "多选题2" },
    { id: "3", title: "判断题3" },
    { id: "4", title: "填空题4" },
    { id: "5", title: "简答题5" },
])
// 角色判断
const isTeacherRole = ref(true); // 假设当前是教师角色
const isAdminRole = ref(false); // 假设当前不是管理员角色
// 搜索参数
const searchParams = reactive({
    keyword: "", // 课件ID/文件名
    courseId: "", // 课程ID
    chapterId: "", // 章节ID
    status: [], // 状态
    dateRange: [], // 上传时间范围
});
// 课程选项
const courseOptions = ref([
    { value: "CS001", label: "CS001 - 计算机科学导论" },
    { value: "CS002", label: "CS002 - 数据结构与算法" },
    { value: "CS003", label: "CS003 - 数据库系统原理" },
    { value: "CS004", label: "CS004 - 操作系统" },
    { value: "CS005", label: "CS005 - 计算机网络" },
]);
// 章节选项
const chapterOptions = ref([
    { value: "CH001", label: "CH001 - 第一章 绪论" },
    { value: "CH002", label: "CH002 - 第二章 Python基础" },
    { value: "CH003", label: "CH003 - 第三章 面向对象编程" },
    { value: "CH004", label: "CH004 - 第四章 数据结构" },
    { value: "CH005", label: "CH005 - 第五章 算法设计" },
]);
// 表格数据
const tableData = ref([
    {
        id: "CW001",
        fileName: "Python_变量与数据类型.pptx",
        courseId: "CS001",
        chapterId: "CH002",
        status: "published",
        uploadTime: "2025-06-15 10:30",
    },
    {
        id: "CW002",
        fileName: "数据库设计原理.pdf",
        courseId: "CS003",
        chapterId: "CH003",
        status: "draft",
        uploadTime: "2025-06-18 14:20",
    },
    {
        id: "CW003",
        fileName: "操作系统进程管理.mp4",
        courseId: "CS004",
        chapterId: "CH004",
        status: "archived",
        uploadTime: "2025-06-20 09:15",
    },
    {
        id: "CW004",
        fileName: "网络协议基础.pptx",
        courseId: "CS005",
        chapterId: "CH001",
        status: "pending",
        uploadTime: "2025-06-22 16:45",
    },
    {
        id: "CW005",
        fileName: "排序算法比较.pdf",
        courseId: "CS002",
        chapterId: "CH005",
        status: "approved",
        uploadTime: "2025-06-25 11:30",
    },
]);
// 分页参数
const pagination = reactive({
    current: 1,
    size: 20,
    total: 100,
});
// 加载状态
const loading = ref(false);
// 弹窗可见性
const dialogVisible = ref(false);
// 修订弹窗可见性
const reviseDialogVisible = ref(false);
// 是否为编辑模式
const isEditMode = ref(false);
// 表单数据
const form = reactive({
    courseId: "",
    chapterId: "",
    fileContent: null,
    fileName: "",
    description: "",
    target: "",
    knowledgePoints: [],
    theory_question_id: ''
});
// 修订表单数据
const reviseForm = reactive({
    newFileContent: null,
    revisionNote: "",
});
// 当前操作的课件ID
const currentCoursewareId = ref("");

/** 获取状态标签类型 */
const getStatusTagType = (status: string) => {
    switch (status) {
        case "draft": return "primary";
        case "published": return "success";
        case "archived": return "info";
        case "pending": return "warning";
        case "approved": return "primary";
        default: return "primary";
    }
};

/** 获取状态文本 */
const getStatusText = (status: string) => {
    switch (status) {
        case "draft": return "草稿";
        case "published": return "已发布";
        case "archived": return "已归档";
        case "pending": return "待审核";
        case "approved": return "已审核";
        default: return status;
    }
};

/** 处理重置 */
const handleReset = () => {
    searchParams.keyword = "";
    searchParams.courseId = "";
    searchParams.chapterId = "";
    searchParams.status = [];
    searchParams.dateRange = [];
};

/** 显示创建弹窗 */
const showCreateDialog = () => {
    isEditMode.value = false;
    Object.assign(form, {
        courseId: "",
        chapterId: "",
        fileContent: null,
        fileName: "",
        description: "",
        target: "",
        knowledgePoints: [],
    });
    dialogVisible.value = true;
};

/** 处理编辑 */
const handleEdit = (row: any) => {
    isEditMode.value = true;
    currentCoursewareId.value = row.id;
    Object.assign(form, {
        courseId: row.courseId,
        chapterId: row.chapterId,
        fileContent: null,
        fileName: row.fileName,
        description: "",
        target: "",
        knowledgePoints: [],
    });
    dialogVisible.value = true;
};

/** 处理提交审核 */
const handleSubmitReview = (row: any) => {
    confirm("确认提交审核?", "提示", action => {
        if (action) {
            // 模拟API请求
            setTimeout(() => {
                row.status = "pending";
                msg("success", "提交审核成功");
            }, 500);
        }
    })
};

/** 处理审核 */
const handleReview = (row: any) => {
    confirm("确认审核通过?", "提示", action => {
        if (action) {
            // 模拟API请求
            setTimeout(() => {
                row.status = "approved";
                msg("success", "审核通过");
            }, 500);
        }
    })
};

/** 处理发布 */
const handlePublish = (row: any) => {
    confirm("确认发布该课件?", "提示", action => {
        if (action) {
            // 模拟API请求
            setTimeout(() => {
                row.status = "published";
                msg("success", "发布成功");
            }, 500);
        }
    })
};

/** 显示修订弹窗 */
const showReviseDialog = (row: any) => {
    currentCoursewareId.value = row.id;
    Object.assign(reviseForm, {
        newFileContent: null,
        revisionNote: "",
    });
    reviseDialogVisible.value = true;
};

/** 处理文件变更 */
const handleFileChange = (file: any) => {
    form.fileContent = file.raw;
};

/** 处理修订文件变更 */
const handleReviseFileChange = (file: any) => {
    reviseForm.newFileContent = file.raw;
};

/** 处理表单提交 */
const handleSubmit = () => {
    if (!form.courseId || !form.chapterId || !form.fileName) {
        msg("warning", "请填写必填项");
        return;
    }
    if (!form.fileContent) {
        msg("warning", "请上传课件文件");
        return;
    }
    loading.value = true;
    // 模拟API请求
    setTimeout(() => {
        loading.value = false;
        dialogVisible.value = false;
        msg("success", isEditMode.value ? "编辑成功" : "创建成功");
        if (!isEditMode.value) {
            // 模拟新增数据
            tableData.value.unshift({
                id: `CW${Math.floor(Math.random() * 1000).toString().padStart(3, "0")}`,
                fileName: form.fileName,
                courseId: form.courseId,
                chapterId: form.chapterId,
                status: "draft",
                uploadTime: new Date().toLocaleString(),
            });
        }
    }, 1000);
};

/** 处理修订提交 */
const handleReviseSubmit = () => {
    if (!reviseForm.newFileContent) {
        msg("warning", "请上传新课件文件");
        return;
    }
    loading.value = true;
    // 模拟API请求
    setTimeout(() => {
        loading.value = false;
        reviseDialogVisible.value = false;
        msg("success", "修订提交成功");
        // 更新表格数据状态
        const item = tableData.value.find(item => item.id === currentCoursewareId.value);
        if (item) {
            item.status = "draft";
        }
    }, 1000);
};

/** 处理分页大小变更 */
const handleSizeChange = (size: number) => {
    pagination.size = size;
};

/** 处理当前页变更 */
const handleCurrentChange = (current: number) => {
    pagination.current = current;
};
</script>
<style scoped>
.upload-demo {
    width: 100%;
}
</style>