<template>
    <div class="courseware-approve flex-1 flex flex-col bg-gray-50 overflow-auto">
        <!-- 待审核列表 -->
        <div class="flex flex-1 p-6 overflow-y-scroll">
            <el-table :data="pendingList" v-loading="loading" border style="width: 100%;">
                <el-table-column show-overflow-tooltip prop="title" label="课件名称" width="300" />
                <el-table-column show-overflow-tooltip prop="description" label="课件描述" width="300" />
                <el-table-column prop="courseware_type" label="课件类型">
                    <template #default="{ row }">
                        {{ row.courseware_type === 'virtual_courseware' ? '虚拟课件' : '理论课件' }}
                    </template>
                </el-table-column>
                <el-table-column show-overflow-tooltip label="创建时间">
                    <template #default="{ row }">
                        {{ new Date(row.created_at).toLocaleString() }}
                    </template>
                </el-table-column>
                <el-table-column label="操作" align="center" width="260">
                    <template #default="{ row }">
                        <div class="flex justify-center">
                            <el-button plain @click="view(row)">查看</el-button>
                            <el-button type="primary" plain @click="review(row.id)">通过</el-button>
                            <el-button type="danger" plain @click="reject(row.id)">驳回</el-button>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
        </div>

        <!-- 分页区域 -->
        <div class="shrink-0 flex justify-center mt-4 pb-2">
            <el-pagination background v-model:current-page="pages.page" v-model:page-size="pages.limit"
                :total="pages.total" layout=" prev, pager, next" @current-change="currentChange" />
        </div>

        <!-- 查看详情 -->
        <el-dialog v-model="dialogVisible" title="查看课件" width="50%" :destroy-on-close="true">
            <el-form ref="coursewareFormRef" :model="form" label-width="100px" disabled>
                <el-form-item label="课件名称" prop="title">
                    <el-input v-model="form.title" placeholder="课件名称" />
                </el-form-item>
                <el-form-item label="文件名" prop="file_name">
                    <el-input v-model="form.file_name" placeholder="课件名称" />
                </el-form-item>
                <el-form-item label="课件描述">
                    <el-input v-model="form.description" placeholder="课件描述" />
                </el-form-item>
                <el-form-item label="所属课程">
                    <el-select v-model="form.course_id" placeholder="所属课程" class="w-full">
                        <el-option v-for="course in courses" :key="course.id" :label="course.name" :value="course.id" />
                    </el-select>
                </el-form-item>
                <el-form-item label="所属章节">
                    <el-tree-select placeholder="所属章节" v-model="form.chapter_id" :data="chapters"
                        :props="chapterProps" clearable class="w-full" />
                </el-form-item>
                <el-form-item label="课件类型">
                    <el-radio-group v-model="form.courseware_type">
                        <el-radio value="virtual_courseware">虚拟课件</el-radio>
                        <el-radio value="theory_courseware">理论课件</el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="适用对象">
                    <el-input v-model="form.target" placeholder="适用对象" class="w-full" />
                </el-form-item>
            </el-form>
        </el-dialog>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";
import { msg, confirmMsg as confirm } from "@/utils/msg";
import { getCourseList } from "@/api/course";
import { chapterList } from "@/api/chapter";
import { auditPendingList, auditCourseware, rejectCourseware } from "@/api/courseware";

//待审核课件列表
const pendingList = ref<any[]>([]);
//分页数据
const pages = reactive({
    page: 1,
    limit: 10,
    total: 0
});
//表单数据
const form = ref<any>({
    title: "",
    description: "",
    course_id: "",
    chapter_id: "",
    courseware_type: "virtual_courseware",
    target: "",
    file_name: "",
    file_type: "",
    file_path: "",
});
//课程列表
const courses = ref<any[]>([]);
//章节列表
const chapters = ref<any[]>([]);
//章节选择器prop映射
const chapterProps = {
    label: "name",
    value: "id"
}
//loading
const loading = ref<boolean>(false);
//查看详情显示
const dialogVisible = ref<boolean>(false);

/** 查看详情 */
const view = (row: any) => {
    dialogVisible.value = true;
    form.value = {
        ...form,
        title: row.title,
        description: row.description,
        course_id: row.course_id,
        chapter_id: row.chapter_id,
        courseware_type: row.courseware_type,
        target: row.target,
        file_name: row.file_name
    }
}

/** 审核通过课件 */
const review = (id: number) => {
    confirm("确定通过审核吗？", "通过审核", async action => {
        if (action) {
            try {
                const res = await auditCourseware(id);
                if (res.code == 0) {
                    msg("success", "审核成功");
                    getPendingList();
                } else {
                    msg("error", res.message);
                }
            } catch (e) {
                console.log(e);
            }
        }
    });
}

/** 审核驳回课件 */
const reject = (id: number) => {
    confirm("确定驳回审核吗？", "驳回审核", async action => {
        if (action) {
            try {
                const res = await rejectCourseware(id);
                if (res.code == 0) {
                    msg("success", "驳回成功");
                    getPendingList();
                } else {
                    msg("error", res.message);
                }
            } catch (e) {
                console.log(e);
            }
        }
    });
}

/** 获取课程名称 */
const getCourseName = (id: number) => courses.value.find((course: any) => course.id == id)?.name || "";

/** 获取课程列表 */
const getCourseData = async () => {
    try {
        const res = await getCourseList({});
        courses.value = res.list || [];
    } catch (e) { }
}

/** 获取章节列表 */
const getChapterList = async () => {
    try {
        const res = await chapterList();
        if (res.code == 0) {
            chapters.value = res.data || [];
        }
    } catch (e) { }
}

/** 获取待审核课件列表 */
const getPendingList = async (params?: { page?: number, page_size?: number }) => {
    params = params || {}
    loading.value = true;
    try {
        const res = await auditPendingList(params);
        if (res.code == 0) {
            const data = res.data || {}
            pendingList.value = data.list || [];
        } else {
            msg("error", res.message);
        }
        loading.value = false;
    } catch (e) {
        loading.value = false;
    }
}

//分页改变
const currentChange = (page: number) => {
    pages.page = page;
    getPendingList({ page });
}

getPendingList();
getCourseData();
getChapterList();
</script>