<template>
    <div class="courseware flex-1 flex flex-col bg-gray-50 overflow-auto">
        <!-- 顶部搜索和筛选区域 -->
        <div class="p-6 border-b border-gray-200">
            <el-button class="mb-4" type="primary" @click="handleCreateCourseware">新建课件</el-button>
            <el-button type="success" class="mb-4 !rounded-button whitespace-nowrap ml-auto"
                @click="reviseListDrawerVisible = true">
                <el-icon class="mr-1">
                    <View />
                </el-icon>
                修订列表
            </el-button>
            <div class="grid grid-cols-5 gap-4">
                <div class="col-span-2">
                    <el-input v-model="searchQuery" placeholder="搜索课件名称" class="w-full">
                        <template #prefix>
                            <el-icon>
                                <Search />
                            </el-icon>
                        </template>
                    </el-input>
                </div>
                <!-- <el-select v-model="courseId" placeholder="选择课程" clearable class="w-full">
                    <el-option v-for="course in courses" :key="course.id" :label="course.name" :value="course.id" />
                </el-select> -->
                <my-select v-model="courseId" multiple placeholder="选择课程" :func="getCourseList" labelKey="course.name"
                    valueKey="course.id" :initData="initCourse" searchKey="name" />
                <el-tree-select placeholder="选择章节" v-model="chapterId" :data="chapterOptions" :props="chapterProps"
                    clearable class="w-full" />
                <el-select v-model="status" placeholder="课件状态" clearable class="w-full">
                    <el-option v-for="option in statusOptions" :label="option.label" :value="option.value" />
                </el-select>
            </div>
        </div>

        <!-- 课件列表 -->
        <div class="flex-1 overflow-auto p-6">
            <el-table :data="coursewareData" style="width: 100%" border>
                <el-table-column show-overflow-tooltip prop="courseware.title" label="课件名称" width="180" />
                <!-- <el-table-column show-overflow-tooltip prop="file_name" label="文件名" width="180" />
                <el-table-column show-overflow-tooltip prop="description" label="课件描述" width="180" /> -->
                <el-table-column prop="courseware.courseware_type" label="课件类型">
                    <template #default="{ row }">
                        {{ row.courseware.courseware_type === 'virtual_courseware' ? '虚拟课件' : '理论课件' }}
                    </template>
                </el-table-column>
                <el-table-column label="课程" prop="courseware.course_name" />
                <el-table-column label="章节" prop="courseware.chapter_name" />
                <el-table-column prop="courseware.version" label="当前版本" />
                <el-table-column prop="courseware.status" label="状态" align="center">
                    <template #default="{ row }">
                        <el-tag :type="getStatusTagType(row.courseware.status)" effect="light" class="!rounded-button">
                            {{ getStatusLabel(row.courseware.status) }}
                        </el-tag>
                    </template>
                </el-table-column>
                <!-- <el-table-column show-overflow-tooltip label="上传时间" width="180">
                    <template #default="{ row }">
                        {{ new Date(row.courseware.created_at * 1000).toLocaleString() }}
                    </template>
                </el-table-column> -->
                <!-- <el-table-column prop="courseware.score" label="参考分数" align="center" /> -->
                <!-- <el-table-column prop="target" label="适用对象" align="center" /> -->
                <el-table-column label="操作" width="260">
                    <template #default="{ row }">
                        <div class="flex justify-start">
                            <el-button v-if="row.courseware.status == 'draft' || row.courseware.status == 'rejected'"
                                type="primary" size="small" @click="handleEdit(row)">
                                编辑
                            </el-button>
                            <el-button type="primary" size="small"
                                @click="handleDownload(row.courseware)">
                                下载
                            </el-button>
                            <el-button type="success" size="small"
                                @click="handlePreview(row.courseware)">
                                演示
                            </el-button>
                            <el-button v-if="row.status == 'draft' || row.status == 'rejected'" type="danger"
                                size="small"  @click="handleDelete(row.courseware)">
                                删除
                            </el-button>
                            <ApproveButton approve-code="courseware" :data-id="row.courseware.id"
                                :data-title="row.courseware.title"
                                v-if="row.courseware.status == 'draft' || row.courseware.status == 'rejected'"
                                @success="getData" />
                        </div>
                    </template>
                </el-table-column>
                <!-- 修订 -->
                <el-table-column label="修订" width="190" align="center">
                    <template #default="{ row }">
                        <RevisionActions :approve-code="approveCode" :data="row.courseware" :rev="row.rev"
                            :show-create-button="row.courseware.status === 'published' && !row.rev"
                            @create="showReviseCreateDialog(row.courseware)" @edit="handleReviseEdit(row.rev)"
                            @delete="handleReviseDelete(row.rev)" @view="handleReviseView(row.rev)"
                            @refresh="getData" />
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <!-- 分页 -->
        <div class="shrink-0 flex justify-center mt-4">
            <el-pagination :current-page="pages.page" :page-size="pages.limit" layout="prev, pager, next"
                :total="pages.total" @update:current-page="currentChange" />
        </div>
        <!-- 新建/编辑课件弹窗 -->
        <el-dialog v-model="dialogVisible" :title="dialogTitle" width="60%">
            <el-form ref="coursewareFormRef" :model="coursewareForm" :rules="rules" label-width="100px"
                v-loading.fullscreen="loading" element-loading-text="正在上传课件..."
                element-loading-background="rgba(0, 0, 0, 0.2)"
                :disabled="actionType === 'view' || actionType === 'revise-view'">
                <el-form-item label="修订说明"
                    v-if="actionType === 'revise-create' || actionType === 'revise-edit' || actionType === 'revise-view'">
                    <el-input v-model="revNotes" type="textarea" :rows="2" placeholder="请输入修订说明" />
                </el-form-item>
                <el-form-item label="课件名称" prop="title" required>
                    <el-input v-model="coursewareForm.title" placeholder="请输入课件名称" />
                </el-form-item>
                <el-form-item label="课件描述">
                    <el-input v-model="coursewareForm.description" placeholder="请输入课件描述" />
                </el-form-item>
                <el-form-item label="所属课程">
                    <!-- <el-select v-model="coursewareForm.course_id" placeholder="请选择课程" class="w-full">
                        <el-option v-for="course in courses" :key="course.id" :label="course.name" :value="course.id" />
                    </el-select> -->
                    <my-select v-model="coursewareForm.course_id" placeholder="请选择课程" :func="getCourseList"
                        labelKey="course.name" valueKey="course.id" :initData="initCourse" searchKey="name" />
                </el-form-item>
                <el-form-item label="所属章节">
                    <el-tree-select placeholder="请选择章节" v-model="coursewareForm.chapter_id" :data="chapterOptions"
                        :props="chapterProps" check-strictly clearable class="w-full" />
                </el-form-item>
                <el-form-item label="课件类型">
                    <el-radio-group v-model="coursewareForm.courseware_type">
                        <el-radio value="virtual_courseware">虚拟课件</el-radio>
                        <el-radio value="theory_courseware">理论课件</el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="总步骤数" prop="total_step"
                    v-if="coursewareForm.courseware_type == 'virtual_courseware'">
                    <el-input v-model="coursewareForm.total_step" placeholder="请输入总步骤数" class="w-full" />
                </el-form-item>
                <el-form-item label="参考分数" prop="score">
                    <el-input v-model="coursewareForm.score" type="number" placeholder="请输入参考分数" class="w-full" />
                </el-form-item>
                <el-form-item label="阅读时间" prop="minutes">
                    <el-input v-model="coursewareForm.minutes" type="number" placeholder="请输入阅读时间" class="w-full">
                        <template #append>分钟</template>
                    </el-input>
                </el-form-item>
                <el-form-item label="适用对象">
                    <el-input v-model="coursewareForm.target" placeholder="请输入适用对象" class="w-full" />
                </el-form-item>
                <el-form-item label="上传文件">
                    <el-upload class="upload-demo" accept=".zip, application/zip, application/x-zip-compressed" drag
                        action="#" :limit="1" :auto-upload="false" :on-change="handleFileChange"
                        :on-remove="onFileRemove">
                        <el-icon class="el-icon--upload">
                            <Upload />
                        </el-icon>
                        <div class="el-upload__text">
                            将文件拖到此处，或<em>点击上传</em>
                        </div>
                        <template #tip>
                            <div class="el-upload__tip">
                                支持上传 SCORM 标准压缩包，.zip格式，文件大小不超过 500MB
                            </div>
                        </template>
                    </el-upload>
                </el-form-item>
            </el-form>
            <template #footer v-if="actionType != 'view' && actionType != 'revise-view'">
                <span class="dialog-footer">
                    <el-button @click="handleCancel">取消</el-button>
                    <el-button type="primary" @click="handleSubmit" class="!rounded-button">
                        确定
                    </el-button>
                </span>
            </template>
        </el-dialog>



        <!-- 虚拟课件预览弹窗 -->
        <el-dialog v-model="previewDialogVisible" title="课件预览" width="95%" class="preview-dialog">
            <!-- 新增顶部控制条 -->
            <div class="flex items-center justify-between mb-4"
                v-if="currentCourseware.courseware_type == 'virtual_courseware'">
                <div class="flex items-center space-x-2">
                    <span>跳转到第</span>
                    <el-input-number v-model="stepNumber" :min="1" :max="100" controls-position="right"
                        placeholder="请输入步骤" size="small" />
                    <span>步</span>
                    <el-button type="primary" size="small">跳转</el-button>
                </div>
                <el-button type="primary" @click="togglePlayPause">
                    {{ isPlaying ? '暂停' : '开始' }}
                </el-button>
            </div>

            <!-- 预览内容区 -->
            <div class="preview-container">
                <!-- <img  alt="课件预览" class="w-full" /> -->
                <div class="w-full h-full bg-red-200">
                    虚拟课件
                </div>
            </div>
        </el-dialog>
        <!-- 修订列表 -->
        <el-drawer title="课件修订列表" v-model="reviseListDrawerVisible" size="90%" body-class="my-drawer">
            <RevisonList :approve-code="approveCode" />
        </el-drawer>
    </div>
</template>

<script lang="ts" setup>
import { ref, reactive, watch } from "vue";
import { msg, confirmMsg as confirm, errMsg } from "@/utils/msg";
import { getCourseList } from "@/api/course";
import { chapterList } from "@/api/chapter";
import { upload, coursewareList, createCourseware, updateCourseware, deleteCourseware, submitAudit } from "@/api/courseware";
import type { CoursewareListParams } from "@/api/courseware";
import { type UploadFile, type FormInstance, ElMessageBox, ElMessage } from "element-plus";
import { createRevision, editRevision, getRevisionDetail, deleteRevision } from '@/api/revision'
import { statusOptions, getStatusLabel, getStatusTagType } from '@/api/status'

const reviseListDrawerVisible = ref(false)
const approveCode = ref('courseware');
const revNotes = ref(''); // 修订说明
const revId = ref(0); // 当前修订 ID
const dialogTitle = ref('创建课件')
const actionType = ref('create')
interface CoursewareFormRaw {
    id?: number;
    title: string;
    description: string;
    course_id: number | string;
    chapter_id: number | string;
    courseware_type: "virtual_courseware" | "theory_courseware";
    total_step: number | string;
    score: number | string;
    minutes: number | string;
    target: string;
    file_name: string,
    file_type: string,
    file_path: string,
}

//课件预览弹窗
const previewDialogVisible = ref(false);
const stepNumber = ref<number>(1); // 当前输入的步骤号
const isPlaying = ref<boolean>(false); // 控制播放/暂停状态
//搜索课件名称
const searchQuery = ref("");
//课程ID
const courseId = ref("");
const initCourse: any = ref([]);
//章节ID
const chapterId = ref("");
//课件状态
const status = ref("");
const pages = reactive({
    page: 1,
    limit: 10,
    total: 0
});
//创建弹窗
const dialogVisible = ref(false);

//是否编辑
const isEdit = ref(false);
const togglePlayPause = () => {
    isPlaying.value = !isPlaying.value;
};

//课程列表
const courseData = ref<any[]>([]);
//课程选择列表
const courses = ref<any>([]);
//章节选择器prop映射
const chapterProps = {
    label: "name",
    value: "id"
}
//章节选择列表
const chapterOptions = ref([]);
//课件列表
const coursewareData = ref([]);
//创建课件表单原数据
const formRaw: CoursewareFormRaw = {
    id: 0,
    title: "",
    description: "",
    course_id: "",
    chapter_id: "",
    courseware_type: "virtual_courseware",
    target: "",
    total_step: "",
    score: "",
    minutes: "",
    file_name: "",
    file_type: "",
    file_path: "",
}
//课件表单实例
const coursewareFormRef = ref<FormInstance>();
//课件表单ref数据
const coursewareForm = ref<CoursewareFormRaw>({ ...formRaw });
const rules = ref<any>({
    title: [{ required: true, message: "请输入课件名称", trigger: "blur" }],
    total_step: [{ required: true, message: "请输入总步骤数", trigger: "blur" }],
});
//加载中
const loading = ref<boolean>(false);
//是否正在提交
let inSubmit = false;
//搜索状态
let inSearch = false;
//原始页数
let lastPages = { ...pages }
const currentCourseware = ref<any>({});

/** 筛选课件列表 */
watch([searchQuery, courseId, chapterId, status], newVal => {
    //有筛选条件
    if (newVal.some(s => s)) {
        if (!inSearch) {
            inSearch = true;
        }
        pages.page = 1;
        getCoursewareList({
            page: pages.page,
            name: newVal[0],
            course_id: +newVal[1] || 0,
            chapter_id: +newVal[2] || 0,
            status: newVal[3] || ""
        });
    } else {
        //重置分页
        pages.page = lastPages.page;
        initCourse.value = [];
        getCoursewareList({ page: pages.page });
        inSearch = false;
    }
});

watch(() => coursewareForm.value?.course_id, newVal => {
    const course = getCourse(+newVal).course;
    if (course) {
        getChapterList({
            parent_id: course.chapter_id
        });
    }
});

/** 获取对应课程 */
const getCourse = (id: number) => courseData.value.find((course: any) => course.course.id === id) || {};

/** 点击新增课件 */
const handleCreateCourseware = () => {
    isEdit.value = false;
    dialogVisible.value = true;
    initCourse.value = [];
    coursewareForm.value = { ...formRaw };
    actionType.value = 'create';
    dialogTitle.value = '新增课件';
};

/** 编辑课件 */
const handleEdit = (row: any) => {
    isEdit.value = true;
    dialogVisible.value = true;
    actionType.value = 'edit';
    dialogTitle.value = '编辑课件';
    coursewareForm.value = {
        id: row.courseware.id,
        title: row.courseware.title,
        description: row.courseware.description,
        course_id: row.courseware.course_id,
        chapter_id: row.courseware.chapter_id,
        courseware_type: row.courseware.courseware_type,
        total_step: row.courseware.total_step,
        score: row.courseware.score,
        minutes: row.courseware.minutes,
        target: row.courseware.target,
        file_name: row.courseware.file_name,
        file_type: row.courseware.file_type,
        file_path: row.courseware.file_path
    };
    initCourse.value = [{ id: row.courseware.course_id, name: row.courseware.course_name }]
};

/** 删除课件 */
const handleDelete = (row: any) => {
    confirm("确定删除该课件?", "删除课件", async action => {
        if (action) {
            const res = await deleteCourseware(row.id);
            if (res.code == 0) {
                msg("success", "删除成功");
                getCoursewareList({
                    page: pages.page,
                    name: searchQuery.value,
                    course_id: +courseId.value || 0,
                    chapter_id: +chapterId.value || 0,
                    status: status.value || ""
                });
            } else {
                msg("error", res.message);
            }
        }
    });
};


/** 上传课件 */
const handleFileChange = async (file: UploadFile) => {
    console.log("文件变更:", file);
    if (file.size && file.size > 1024 * 1024 * 500) {
        msg("error", "上传课件大小不超过500M");
        return;
    }
    try {
        const formData = new FormData();
        formData.append("file", file.raw!);
        loading.value = true;
        const res = await upload(formData);
        if (res.code == 0) {
            const data = res.data || {}
            msg("success", "上传成功");
            coursewareForm.value.file_name = data.file_name || "";
            coursewareForm.value.file_type = data.file_type || "";
            coursewareForm.value.file_path = data.file_path || "";
        } else {
            msg("error", res.message);
        }
        loading.value = false;
    } catch (e) {
        console.log(e);
        msg("error", "上传失败");
        loading.value = false;
    }
};

/** 移除上传课件 */
const onFileRemove = (file: UploadFile) => {
    coursewareForm.value.file_name = "";
    coursewareForm.value.file_type = "";
    coursewareForm.value.file_path = "";
};

/** 创建课件取消 */
const handleCancel = () => {
    dialogVisible.value = false;
    coursewareForm.value = { ...formRaw }
}
const getData = () => {
    getCoursewareList({
        page: pages.page,
        name: searchQuery.value,
        course_id: +courseId.value || 0,
        chapter_id: +chapterId.value || 0,
        status: status.value || ""
    });
}
/** 创建课件提交 */
const handleSubmit = () => {
    if (inSubmit) {
        msg("warning", "请勿重复提交");
        return;
    };
    coursewareFormRef.value?.validate(async valid => {
        if (valid) {
            if (!coursewareForm.value.course_id) {
                msg("warning", "请选择课程");
                return;
            }
            if (!coursewareForm.value.chapter_id) {
                msg("warning", "请选择章节");
                return;
            }
            if (!coursewareForm.value.file_path) {
                msg("warning", "请上传课件");
                return;
            }
            try {
                let res: any;
                inSubmit = true;
                //if(coursewareForm.value.courseware_type == "virtual_courseware"){
                coursewareForm.value.total_step = Number(coursewareForm.value.total_step) || 0;
                //}
                coursewareForm.value.score = Number(coursewareForm.value.score) || 0;
                coursewareForm.value.minutes = Number(coursewareForm.value.minutes) || 0;
                if (actionType.value == 'create') {
                    coursewareForm.value.id = 0
                    res = await createCourseware(coursewareForm.value);
                }
                else if (actionType.value == 'edit') {
                    res = await updateCourseware(coursewareForm.value);
                }
                else if (actionType.value == 'revise-create' || actionType.value == 'revise-edit') {
                    if (!revNotes.value) return errMsg('请输入修订说明')
                    const changes = {
                        formData: coursewareForm.value,
                        initCourse: initCourse.value,
                    }
                    const saveData: any = {
                        module_key: approveCode.value,
                        original_id: coursewareForm.value.id,
                        notes: revNotes.value,
                        changes: JSON.stringify(changes),
                    }
                    if (actionType.value == 'revise-edit') {
                        saveData.id = revId.value
                        res = await editRevision(saveData)
                    } else {
                        res = await createRevision(saveData)
                    }

                }
                dialogVisible.value = false;
                getCoursewareList({
                    page: pages.page,
                    name: searchQuery.value,
                    course_id: +courseId.value || 0,
                    chapter_id: +chapterId.value || 0,
                    status: status.value || ""
                });
                msg("success", res.message)
                inSubmit = false;
            } catch (e) {
                inSubmit = false;
            }
        }
    });
};

/** 下载课件 */
const handleDownload = (row: any) => {
    console.log('下载课件:', row);
};


/** 预览课件 */
const handlePreview = (row: any) => {
    currentCourseware.value = row;
    previewDialogVisible.value = true;
};

/** 获取课程列表 */
const getCourseData = async () => {
    try {
        const res = await getCourseList({});
        courseData.value = res.list || [];
        courses.value = res.list?.map((item: any) => ({
            id: item.id,
            name: item.name
        })) || [];
    } catch (e) { }
}

/** 获取章节列表 */
const getChapterList = async (params?: { parent_id?: number }) => {
    try {
        const res = await chapterList(params);
        if (res.code == 0) {
            chapterOptions.value = res.data || [];
        }
    } catch (e) { }
}

/** 获取课件列表 */
const getCoursewareList = async (params?: CoursewareListParams) => {
    params = params || {}
    //记录原分页
    if (!inSearch) {
        lastPages.page = pages.page;
    }
    try {
        const res = await coursewareList(params);
        if (res.code == 0) {
            const data = res.data || {};
            coursewareData.value = data.list || [];
            pages.total = data.total || 0;
        }
    } catch (e) { }
}



//分页改变
const currentChange = (page: number) => {
    pages.page = page;
    getCoursewareList({
        page,
        name: searchQuery.value,
        course_id: +courseId.value || 0,
        chapter_id: +chapterId.value || 0,
        status: status.value || ""
    });
}

getCoursewareList();
getCourseData();
getChapterList();
// 初始化修订表单数据
const initReviseData = async (row: any) => {
    console.log(row, 'row')
    revId.value = row.id;
    const res = await getRevisionDetail(row.id);
    revNotes.value = res.notes;

    const changes = JSON.parse(res.changes);
    coursewareForm.value = changes.formData; // 初始化表单数据
    initCourse.value = changes.initCourse;
};

// 创建修订
const showReviseCreateDialog = async (row: any) => {
    dialogTitle.value = '创建修订';
    actionType.value = 'revise-create';
    revNotes.value = '';
    revId.value = 0;

    // 初始化表单数据
    coursewareForm.value = {...row};
    dialogVisible.value = true;
};

// 编辑修订
const handleReviseEdit = async (row: any) => {
    dialogTitle.value = '编辑修订';
    actionType.value = 'revise-edit';
    await initReviseData(row);
    dialogVisible.value = true;
};

// 查看修订
const handleReviseView = async (row: any) => {
    dialogTitle.value = '查看修订';
    actionType.value = 'revise-view';
    await initReviseData(row);
    dialogVisible.value = true;
};

// 删除修订
const handleReviseDelete = async (row: any) => {
    ElMessageBox.confirm('确定要删除修订吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
    })
        .then(async () => {
            const res = await deleteRevision(row.id);
            if (res.success) {
                ElMessage.success('删除成功');
                getData();
            } else {
                ElMessage.error(res.message || '删除失败');
            }
        })
        .catch(() => { });
};
</script>

<style scoped>
.preview-dialog :deep(.el-dialog__body) {
    padding: 0;
}

.preview-container {
    height: 600px;
    overflow: hidden;
}

.upload-demo {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    padding: 20px;
}

.el-upload__text {
    margin-top: 10px;
    color: #606266;
}

.el-upload__text em {
    color: #409eff;
    font-style: normal;
}
</style>
