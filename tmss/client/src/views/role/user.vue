<!-- 代码已包含 CSS：使用 TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 -->
<template>
    <div class="flex-1 flex flex-col bg-gray-50 overflow-auto">
        <!-- 顶部操作区 -->
        <!-- 顶部操作区 -->
        <div class="p-6 border-b border-gray-200">
            <div class="flex justify-between items-center">
                <!-- 左侧按钮组 -->
                <div class="flex gap-4">
                    <el-button type="primary" class="!rounded-button whitespace-nowrap" @click="showAddUserDialog">
                        <el-icon class="mr-1">
                            <Plus />
                        </el-icon>新增用户
                    </el-button>
                    <el-button class="!rounded-button whitespace-nowrap" @click="handleBatchDelete"
                        :disabled="!selectedUsers.length">
                        <el-icon class="mr-1">
                            <Delete />
                        </el-icon>批量删除
                    </el-button>
                    <el-button class="!rounded-button whitespace-nowrap" @click="handleShowClassDialog"
                        :disabled="!selectedUsers.length">
                        <el-icon class="mr-1">
                            <Delete />
                        </el-icon>设置班级
                    </el-button>
                </div>

                <!-- 右侧搜索框，占30%宽度 -->
                <div class="w-1/3">
                    <el-input v-model="searchQuery" style="max-width: 400px" placeholder="请输入搜索关键词">
                        <template #prepend>
                            <el-select v-model="selectSearch" placeholder="选择类型" style="width: 115px">
                                <el-option :label="item.label" :value="item.value" v-for="item in searchTypes" />
                            </el-select>
                        </template>
                        <template #append>
                            <el-button :icon="Search" @click="handleSearch" />
                        </template>
                    </el-input>
                    <!-- <el-input v-model="searchQuery" clearable @change="handleSearch" placeholder="请输入登录名或真实姓名搜索" class="w-full" :prefix-icon="Search" /> -->
                </div>
            </div>
        </div>
        <!-- 数据展示区 -->
        <div class="flex-1 overflow-auto p-6">
            <el-table :data="userList" @selection-change="handleSelectionChange" border style="width: 100%">
                <el-table-column type="selection" width="55" />
                <el-table-column label="序号" prop="user_info.id" width="80" />
                <el-table-column prop="user_info.account" label="登录名" />
                <el-table-column prop="user_info.username" label="真实姓名" />
                <el-table-column prop="class_info.name" label="班级" />
                <el-table-column prop="major_info.name" label="专业" />
                <el-table-column prop="roles" label="角色">
                    <template #default="scope">
                        <el-tag v-for="(role, index) in scope.row.roles" :key="index" class="mr-2"
                            :type="role.role_code === 'admin' ? 'danger' : role.role_code === 'teacher' ? 'warning' : 'info'">
                            {{ role.role_name }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="操作" width="200">
                    <template #default="scope">
                        <el-button type="primary" class="!rounded-button whitespace-nowrap"
                            @click="handleEdit(scope.row)">
                            <el-icon class="mr-1">
                                <Edit />
                            </el-icon>编辑
                        </el-button>
                        <el-button type="danger" class="!rounded-button whitespace-nowrap"
                            @click="handleDelete(scope.row)">
                            <el-icon class="mr-1">
                                <Delete />
                            </el-icon>删除
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <div class="shrink-0 flex justify-center mt-4" v-if="total > pageSize">
            <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :total="total"
                layout=" prev, pager, next" @current-change="handlePageChange" @size-change="handleSizeChange" />
        </div>
    </div>
    <!-- 用户表单弹窗 -->
    <el-dialog :title="dialogTitle" v-model="dialogVisible" width="500px">
        <el-form ref="userFormRef" :model="userForm" :rules="rules" label-width="100px">
            <el-form-item label="登录名" prop="account">
                <el-input v-model="userForm.account" />
            </el-form-item>
            <el-form-item label="真实姓名" prop="username">
                <el-input v-model="userForm.username" />
            </el-form-item>
            <el-form-item label="密码" prop="password">
                <el-input v-model="userForm.password" type="password" placeholder="不修改密码请留空" />
            </el-form-item>
            <el-form-item label="确认密码" prop="confirmPassword" v-if="!isEdit">
                <el-input v-model="userForm.confirmPassword" type="password" />
            </el-form-item>
            <el-form-item label="邮箱" prop="email">
                <el-input v-model="userForm.email" />
            </el-form-item>
            <el-form-item label="手机号" prop="phone">
                <el-input v-model="userForm.phone" />
            </el-form-item>
            <el-form-item label="角色" prop="role">
                <el-select multiple v-model="userForm.role" class="w-full">
                    <el-option :label="role.role_name" :value="role.role_code" v-for="role in roleList" />
                </el-select>
            </el-form-item>
        </el-form>
        <template #footer>
            <div class="flex justify-end gap-2">
                <el-button @click="dialogVisible = false" class="!rounded-button whitespace-nowrap">取消</el-button>
                <el-button type="primary" @click="handleSubmit" class="!rounded-button whitespace-nowrap">确定</el-button>
            </div>
        </template>
    </el-dialog>
    <el-dialog title="设置班级" v-model="showClassDialog" width="500px">
        <el-form ref="userClassRef" :model="classForm" :rules="rules" label-width="100px">
            <el-form-item label="班级" prop="class_id">
                    <my-select v-model="classForm.class_id" placeholder="请选择班级" :func="getClassList" labelKey="name" valueKey="id"
                        searchKey="name" :initData="classList" @change="handleClassChange"/>
                </el-form-item>
                <el-form-item label="专业" prop="major_id">
                    <el-select v-model="classForm.major_id" placeholder="请选择专业">
                        <el-option :label="major.name" :value="major.id" v-for="major in majorList" />
                        <template #empty>暂无数据</template>
                    </el-select>
                </el-form-item>
        </el-form>
        <template #footer>
            <div class="flex justify-end gap-2">
                <el-button @click="showClassDialog = false" class="!rounded-button whitespace-nowrap">取消</el-button>
                <el-button type="primary" @click="handleSaveStudent" class="!rounded-button whitespace-nowrap">确定</el-button>
            </div>
        </template>
    </el-dialog>
</template>
<script lang="ts" setup>
import { ref, computed, onMounted, watch, nextTick } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import type { FormInstance } from 'element-plus';
import { Delete, Edit, Plus, Search } from '@element-plus/icons-vue';
import { get, post } from '@/utils/request';
interface User {
    id: number;
    account: string;
    username: string;
    password: string;
    phone: string;
    roles: string[];
}
const searchQuery = ref('');
const selectSearch = ref('');
const searchTypes = ref([
    { label: '账号', value: 'account' },
    { label: '用户名', value: 'username' },
    { label: '手机号', value: 'phone' },
    { label: '角色', value: 'roles' },
    { label: '邮箱', value: 'email' },
    { label: '班级', value: 'class' },
    { label: '专业', value: 'major' },
]);
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(8);
const dialogVisible = ref(false);
const isEdit = ref(false);
const selectedUsers = ref<User[]>([]);
const roleList: any = ref([])
const userFormRef = ref<FormInstance>();
const initForm = {
    account: '',
    username: '',
    password: '',
    confirmPassword: '',
    phone: '',
    email: '',
    role: []
}
const userForm: any = ref(initForm);
const userList = ref<User[]>([]);
const showClassDialog = ref(false)
const classForm:any = ref({
    class_id: null,
    major_id: null
})
const classList = ref<any>([]);
const majorList = ref<any>([]);
const rules = {
    account: [{ required: true, message: '请输入登录名', trigger: 'blur' }],
    username: [{ required: true, message: '请输入真实姓名', trigger: 'blur' }],
    password: [{ message: '请输入密码', trigger: 'blur' }],
    confirmPassword: [
        { required: true, message: '请确认密码', trigger: 'blur' },
        {
            validator: (rule: any, value: string, callback: Function) => {
                if (value !== userForm.value.password) {
                    callback(new Error('两次输入的密码不一致'));
                } else {
                    callback();
                }
            },
            trigger: 'blur'
        }
    ],
    email: [
        { pattern: /^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,4}$/, message: '请输入正确的邮箱', trigger: 'blur' },
        { message: '请输入邮箱', trigger: 'blur' }
    ],
    phone: [
        { message: '请输入联系方式', trigger: 'blur' },
        { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
    ],
    role: [{ required: true, message: '请选择角色', trigger: 'change' }]
};
onMounted(async () => {
    await getUserList();
})
const getUserList = async () => {
    const params: any = { page: currentPage.value, pageSize: pageSize.value }
    if (searchQuery.value != "" && selectSearch.value != "") {
        params.query = searchQuery.value;
        params.searchType = selectSearch.value
    }
    const res = await get('/admin/users/list', params);
    if (!res.success) return;
    userList.value = res.data.list.users;
    //console.log(userList.value)
    total.value = res.data.total;
    roleList.value = res.data.list.roles;
};
const handlePageChange = (newPage: number) => {
    currentPage.value = newPage;
    getUserList(); // 重新获取数据
};

const handleSizeChange = (newSize: number) => {
    pageSize.value = newSize;
    currentPage.value = 1; // 重置到第一页
    getUserList(); // 重新获取数据
};
const dialogTitle = computed(() => isEdit.value ? '编辑用户' : '新增用户');
const showAddUserDialog = () => {
    isEdit.value = false;
    userForm.value = initForm;
    dialogVisible.value = true;
};
const handleEdit = (row: any) => {
    isEdit.value = true;
    //console.log(row)
    const roles = []
    for (let i = 0; i < row.roles.length; i++) {
        roles.push(row.roles[i].role_code)
    }
    userForm.value = { ...row.user_info, password: '', confirmPassword: '', role: roles };
    //console.log(userForm.value)
  
    dialogVisible.value = true;
};
const handleDelete = (row: any) => {
    ElMessageBox.confirm('确认删除该用户？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
        post('admin/users/delete/' + row.user_info.id).then((res: any) => {
            if (res.success) {
                ElMessage.success('删除成功');
                getUserList();
            } else {
                ElMessage.error(res.message);
            }
        }).catch(() => {
            ElMessage.error('删除失败');
        })

    });
};
const handleBatchDelete = () => {
    if (selectedUsers.value.length === 0) return;
    ElMessageBox.confirm(`确认删除选中的 ${selectedUsers.value.length} 个用户？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
        const ids = selectedUsers.value.map((user: any) => user.user_info.id);
        //console.log(ids)
        post("admin/users/batch-delete", { ids }).then((res: any) => {
            if (res.success) {
                ElMessage.success('批量删除成功');
                getUserList(); // 刷新列表
            } else {
                ElMessage.error(res.message);
            }
        }).catch(() => {
            ElMessage.error('删除失败');
        });
    });
};
const handleSelectionChange = (val: User[]) => {
    selectedUsers.value = val;
};
const handleSearch = () => {
    currentPage.value = 1;
    if (searchQuery.value != "" && selectSearch.value != "") {
        getUserList()
    } else {
        ElMessage.error('请选择搜索项');
        getUserList()
    }
};
const handleSubmit = async () => {
    if (!userFormRef.value) return;
    await userFormRef.value.validate((valid) => {
        if (valid) {
            const saveData = { 
                user_info: userForm.value, 
                role_codes: userForm.value.role
            }
            if (isEdit.value) {
                post("admin/users/update", saveData).then((res: any) => {
                    if (res.success) {
                        ElMessage.success("修改成功")
                        getUserList();
                    } else {
                        ElMessage.error(res.message)
                    }
                })
            } else {
                post("admin/users/create", saveData).then((res: any) => {
                    if (res.success) {
                        ElMessage.success("添加成功")
                        getUserList();
                    } else {
                        ElMessage.error(res.message)
                    }
                })
            }
            dialogVisible.value = false;
        }
    });
};
const handleShowClassDialog = () => {
    showClassDialog.value = true;
}
const getClassList = (params?: {
    page?: number
    page_size?: number
}) => get('admin/users/classes', params).then((res) => {
    if (!res.success) return
    return res.data
})
const handleClassChange = (classId: number) => {
    majorList.value = []
    getMajorList(classId)
}
const getMajorList = (classId: number) => get('admin/users/majors/'+classId).then((res) => {
    if (!res.success) return
    majorList.value = res.data
})
const handleSaveStudent = () => {
    if (selectedUsers.value.length === 0) return;
    ElMessageBox.confirm(`确认选中的 ${selectedUsers.value.length} 个用户,批量设置将重置以前的关联？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
        const ids = selectedUsers.value.map((user: any) => user.user_info.id);
        //console.log(ids)
        const saveData = {
            ids,
            class_id: classForm.value.class_id*1,
            major_id: classForm.value.major_id*1
        }
        post("admin/users/set-student-info", saveData).then((res: any) => {
            if (res.success) {
                ElMessage.success('批量设置成功');
                getUserList(); // 刷新列表
                showClassDialog.value = false
            } else {
                ElMessage.error(res.message);
            }
        }).catch(() => {
            ElMessage.error('删除失败');
        });
    });
}
</script>
<style scoped>
.el-input :deep(.el-input__wrapper) {
    box-shadow: 0 0 0 1px #dcdfe6 inset;
}

.el-input :deep(.el-input__wrapper.is-focus) {
    box-shadow: 0 0 0 1px #409eff inset;
}

.el-table :deep(.el-table__header-wrapper) {
    background-color: #f5f7fa;
}

.el-pagination {
    justify-content: flex-end;
}
</style>