<template>
    <div class="flex-1 flex flex-col bg-gray-50 overflow-auto">
        <!-- 头部区域 -->
        <div class="p-6 border-b border-gray-200">
            <div class="flex justify-between items-center">
                <!-- 左侧按钮组 -->
                <div class="flex-1 gap-4">
                    <el-button type="primary" class="!rounded-button whitespace-nowrap" @click="handleAdd">
                        <el-icon class="mr-1">
                            <Plus />
                        </el-icon>新增教室
                    </el-button>
                </div>

                <!-- 右侧搜索框 -->
                <div class="w-1/3">
                    <el-input v-model="searchQuery" style="max-width: 400px" placeholder="请输入教室名称搜索"
                        @keyup.enter="handleSearch" clearable>
                        <template #append>
                            <el-button :icon="Search" @click="handleSearch" />
                        </template>
                    </el-input>
                </div>
            </div>
        </div>

        <!-- 表格区域 -->
        <div class="flex-1 overflow-auto p-6">
            <el-table :data="tableData" border style="width: 100%">
                <el-table-column prop="title" label="教室名称" />
                <el-table-column prop="description" label="描述" />
                <el-table-column label="操作" width="200">
                    <template #default="{ row }">
                        <el-button type="primary" size="small" class="!rounded-button whitespace-nowrap"
                            @click="handleEdit(row)">
                            <el-icon>
                                <Edit />
                            </el-icon>编辑
                        </el-button>
                        <el-button type="danger" size="small" class="!rounded-button whitespace-nowrap ml-2"
                            @click="handleDelete(row)">
                            <el-icon>
                                <Delete />
                            </el-icon>删除
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
        </div>

        <!-- 分页组件 -->
        <div class="px-6 py-4 border-t border-gray-200 flex justify-center">
            <el-pagination layout="prev, pager, next" :total="total" :page-size="search.page_size"
                v-model:current-page="search.page" @current-change="getList" background />
        </div>
        <!-- 新增/编辑弹窗 -->
        <el-dialog :title="dialogTitle" v-model="dialogVisible" width="500px">
            <el-form :model="formData" :rules="rules" ref="formRef" label-width="100px">
                <el-form-item label="教室名称" prop="title">
                    <el-input v-model="formData.title" placeholder="请输入教室名称" />
                </el-form-item>
                <el-form-item label="描述" prop="description">
                    <el-input v-model="formData.description" placeholder="请输入描述" type="textarea" :rows="3" />
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="flex justify-end gap-2">
                    <el-button @click="dialogVisible = false" class="!rounded-button whitespace-nowrap">取消</el-button>
                    <el-button type="primary" @click="handleSubmit"
                        class="!rounded-button whitespace-nowrap">确定</el-button>
                </div>
            </template>
        </el-dialog>
    </div>


</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getClassroomList, getClassroomDetail, updateClassroom, createClassroom, deleteClassroom } from '@/api/classroom';
import type { FormInstance } from 'element-plus'
import { errMsg, successMsg } from '@/utils/msg'
import { Search, Edit, Delete, Plus } from '@element-plus/icons-vue'

const tableData = ref([])
const total = ref(0)
const dialogVisible = ref(false)
const formRef = ref<FormInstance>()
const isEdit = ref(false)
const dialogTitle = computed(() => (isEdit.value ? '编辑教室' : '新增教室'))
// 查询参数
const searchQuery = ref('')


const search = ref({
    page: 1,
    page_size: 10
})

// 表单初始化数据
const formInit = {
    id: 0,
    name: '',
    classroom_id: '',
    description: ''
}
const formData: any = ref({ ...formInit })

// 表单校验规则
const rules = {
    name: [{ required: true, message: '请输入教室名称', trigger: 'blur' }]
}

// 批量删除相关
const selectedClasses: any = ref([])

// 获取教室列表
const getList = async () => {
    const params: any = {
        page: search.value.page,
        page_size: search.value.page_size
    }

    // 添加搜索条件
    if (searchQuery.value) {
        params.title = searchQuery.value
    }

    try {
        const res = await getClassroomList({ ...params })
        tableData.value = res.list
        total.value = res.total

    } catch (err) {
        console.error('获取教室列表失败:', err)
        ElMessage.error('获取数据失败')
    }
}

onMounted(() => {
    getList()
})

// 重置表单
const resetForm = () => {
    formData.value = { ...formInit }
}

// 新增按钮
const handleAdd = () => {
    isEdit.value = false
    resetForm()
    dialogVisible.value = true
}

// 编辑按钮
const handleEdit = async (row: any) => {
    isEdit.value = true
    formData.value = await getClassroomDetail(row.id)

    dialogVisible.value = true
}

// 删除按钮
const handleDelete = (row: any) => {
    ElMessageBox.confirm('确认删除该教室吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(async () => {
        await deleteClassroom(row.id)
        getList()
        successMsg('删除成功')
    })
}


// 搜索事件
const handleSearch = () => {
    search.value.page = 1
    getList()
}

// 表格多选变化
const handleSelectionChange = (val: any[]) => {
    selectedClasses.value = val
}

// 提交表单
const handleSubmit = async () => {
    if (!formRef.value) return
    await formRef.value.validate((valid) => {
        if (valid) {
            if (isEdit.value) {
                updateClassroom(formData.value).then((res: any) => {
                    getList()
                    successMsg('修改成功')
                    dialogVisible.value = false
                })
            } else {
                createClassroom(formData.value).then((res: any) => {
                    getList()
                    successMsg('创建成功')
                    dialogVisible.value = false
                })
            }
        }
    })
}
</script>

<style scoped>
.el-input :deep(.el-input__wrapper) {
    box-shadow: 0 0 0 1px #dcdfe6 inset;
}

.el-input :deep(.el-input__wrapper.is-focus) {
    box-shadow: 0 0 0 1px #409eff inset;
}

.el-table :deep(.el-table__header-wrapper) {
    background-color: #f5f7fa;
}

.el-pagination {
    justify-content: flex-end;
}
</style>