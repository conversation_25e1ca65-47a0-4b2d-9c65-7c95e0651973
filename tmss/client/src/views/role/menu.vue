<template>
    <div class="flex-1 flex flex-col bg-gray-50 overflow-auto">
        <!-- 头部区域 -->
        <div class="p-6 border-b border-gray-200">
            <div class="flex justify-start items-center mb-2">
                <el-button type="primary" class="!rounded-button" @click="handleAdd">
                    <el-icon class="mr-1">
                        <Plus />
                    </el-icon>新增菜单
                </el-button>
            </div>
        </div>

        <!-- 树形表格 -->
        <div class=" flex-1 overflow-auto p-6">
            <el-table :data="tableData" row-key="id" default-expand-all border style="width: 100%">
                <el-table-column prop="name" label="菜单名称" width="280">
                    <template #default="{ row, level }">
                        <span :style="{ paddingLeft: level * 20 + 'px' }">
                            <el-icon v-if="row.children && row.children.length" class="mr-1">
                                <Folder />
                            </el-icon>
                            <el-icon v-else class="mr-1">
                                <Document />
                            </el-icon>
                            {{ row.menu_name }}
                        </span>
                    </template>
                </el-table-column>
                <el-table-column prop="path" label="路由地址" />
                <el-table-column label="适配">
                    <template #default="{ row }">
                        <el-tag>
                            {{ getRoleName(row.sys_code) }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="操作" width="200">
                    <template #default="{ row }">
                        <el-button type="primary" size="small" class="!rounded-button" @click="handleEdit(row)">
                            <el-icon>
                                <Edit />
                            </el-icon>编辑
                        </el-button>
                        <el-button type="danger" size="small" class="!rounded-button ml-2" @click="handleDelete(row)">
                            <el-icon>
                                <Delete />
                            </el-icon>删除
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
        </div>
    </div>

    <!-- 新增/编辑弹窗 -->
    <el-dialog :title="dialogTitle" v-model="dialogVisible" width="500px">
        <el-form :model="formData" :rules="rules" ref="formRef" label-width="100px">
            <el-form-item label="上级菜单">
                <el-select v-model="formData.parent_id" placeholder="请选择上级菜单" class="w-full">
                    <el-option label="顶级菜单" :value="0" />
                    <el-option v-for="item in tableData" :key="item.id" :label="item.menu_name" :value="item.id" />
                </el-select>
            </el-form-item>
            <el-form-item label="菜单名称" prop="menu_name">
                <el-input v-model="formData.menu_name" placeholder="请输入菜单名称" />
            </el-form-item>
            <el-form-item label="路由地址" prop="path">
                <el-input v-model="formData.path" placeholder="请输入路由地址" />
            </el-form-item>
            <el-form-item label="图标" prop="icon">
                <IconSelector v-model="formData.icon" />
            </el-form-item>
            <el-form-item label="排序" prop="order_num">
                <el-input v-model="formData.order_num" placeholder="请输入排序" />
            </el-form-item>
            <el-form-item label="适配" prop="sys_code">
                <el-select v-model="formData.sys_code" placeholder="请选择适配角色" class="w-full">
                    <el-option v-for="item in sysList" :key="item.code" :label="item.name"
                        :value="item.code"></el-option>
                </el-select>
            </el-form-item>

        </el-form>
        <template #footer>
            <el-button @click="dialogVisible = false" class="!rounded-button">取 消</el-button>
            <el-button type="primary" @click="handleSubmit" class="!rounded-button">确 定</el-button>
        </template>
    </el-dialog>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import type { FormInstance } from 'element-plus';
import { get, post } from '@/utils/request';
import { sysList,getRoleName } from '@/api/config';
import { errMsg, successMsg } from '@/utils/msg';
const tableData: any = ref([]);

const dialogVisible = ref(false);
const formRef = ref<FormInstance>();
const isEdit = ref(false);
const formInit = {
    id: 0,
    menu_name: '',
    path: '',
    icon: '',
    order_num: 0,
    sys_code: '',
    parent_id: 0,
    level: 0
}
const formData: any = ref(formInit);
const rules = {
    menu_name: [{ required: true, message: '请输入菜单名称', trigger: 'blur' }],
    path: [{ required: true, message: '请输入路由地址', trigger: 'blur' }],
    sys_code: [{ required: true, message: '请选择系统适配', trigger: 'blur' }],
};

const dialogTitle = computed(() => isEdit.value ? '编辑菜单' : '新增菜单');


onMounted(() => {
    getMenuList();
});
const getMenuList = async () => {
    get('/admin/menu/list').then((res: any) => {
        if (!res.success) return;
        tableData.value = res.data;
    })
}
const resetForm = () => {
    formData.value = formInit;
};

const handleAdd = () => {
    isEdit.value = false;
    resetForm();
    dialogVisible.value = true;
};

const handleEdit = (row: any) => {
    isEdit.value = true;
    formData.value = { ...row };
    //console.log(formData.value)
    dialogVisible.value = true;
};

const handleDelete = (row: any) => {
    ElMessageBox.confirm('确认删除该菜单吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
        post('admin/menu/delete/' + row.id).then((res: any) => {
            if (res.success) {
                getMenuList();
                ElMessage.success('删除成功');
            } else {
                ElMessage.error(res.message);
            }
        })
        
    });
};

const handleSubmit = async () => {
    if (!formRef.value) return;

    await formRef.value.validate((valid) => {
        if (valid) {
            formData.value.order_num = formData.value.order_num * 1;
            if (isEdit.value) {
                // 编辑逻辑
                post('admin/menu/update', formData.value).then((res: any) => {
                    if (res.success) {
                        getMenuList();
                        successMsg('修改成功');
                    } else {
                        errMsg(res.message);
                    }
                })
            } else {
                // 新增逻辑
                post('admin/menu/create', formData.value).then((res: any) => {
                    if (res.success) {
                        getMenuList();  
                        successMsg('添加成功');
                    } else {
                        errMsg(res.message);
                    }
                })
            }

            dialogVisible.value = false;
        }
    });
};
</script>

<style scoped>
.el-input-number :deep(.el-input__wrapper) {
    padding-left: 11px;
}
</style>
