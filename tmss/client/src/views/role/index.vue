<template>
    <div class="role w-full min-h-screen">
        <!-- 新增角色按钮 -->
        <div class="mb-6">
            <el-button type="primary" class="!rounded-button whitespace-nowrap" @click="addRoleDialogVisible = true">
                <el-icon class="mr-1">
                    <Plus />
                </el-icon>
                新增角色
            </el-button>
        </div>

        <!-- 角色列表区域 -->
        <div class="bg-white p-4 rounded-lg shadow-sm">
            <h2 class="text-lg font-semibold text-gray-800 mb-4">角色列表</h2>
            <el-table :data="roleList" class="w-full" border>
                <el-table-column prop="roleName" label="角色名称" width="200" align="center" />
                <el-table-column prop="roleDesc" label="角色描述" align="center" />
                <el-table-column label="操作" width="340" fixed="right" align="center">
                    <template #default="{ row }">
                        <el-button class="!rounded-button whitespace-nowrap" @click="handleAssignRole(row)">
                            菜单分配
                        </el-button>
                        <el-button class="!rounded-button whitespace-nowrap" @click="handleViewPermission(row)">
                            权限分配
                        </el-button>
                       
                        <el-button class="!rounded-button whitespace-nowrap" @click="editorRole(row)">
                            编辑角色
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
        </div>

        <!-- 新增角色弹窗 -->
        <el-dialog v-model="addRoleDialogVisible" title="新增角色" width="500px" center>
            <el-form :model="formData" label-width="100px">
                <el-form-item label="角色名称" required>
                    <el-input v-model="formData.roleName" placeholder="请输入角色名称" />
                </el-form-item>
                <el-form-item label="角色描述">
                    <el-input v-model="formData.roleDesc" placeholder="请输入角色描述" type="textarea" :rows="3" />
                </el-form-item>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button class="!rounded-button whitespace-nowrap" @click="addRoleDialogVisible = false">
                        取消
                    </el-button>
                    <el-button type="primary" class="!rounded-button whitespace-nowrap" @click="handleAddRole">
                        确定
                    </el-button>
                </span>
            </template>
        </el-dialog>

        <!-- 角色分配弹窗 -->
        <el-dialog v-model="assignDialogVisible" title="菜单分配" width="50%" center>
            <!-- <div class="mb-6">
                <el-select v-model="selectedUser" placeholder="请选择用户" class="w-full" filterable>
                    <el-option v-for="item in userList" :key="item.id" :label="item.name" :value="item.id" />
                </el-select>
            </div> -->
            <el-tree :data="permissionTree" :props="{
                children: 'children',
                label: 'name',
                class: customNodeClass,
            }" :show-checkbox="true" node-key="id" default-expand-all :expand-on-click-node="false"
                class="permission-tree" />
            <template #footer>
                <span class="dialog-footer">
                    <el-button class="!rounded-button whitespace-nowrap" @click="assignDialogVisible = false">
                        取消
                    </el-button>
                    <el-button type="primary" class="!rounded-button whitespace-nowrap" @click="handleConfirmAssign">
                        确定
                    </el-button>
                </span>
            </template>
        </el-dialog>

    </div>
</template>
<script lang="ts" setup>
import { ref } from 'vue'
import { msg } from "@/utils/msg";

// 角色列表数据
const roleList = ref([
    {
        id: 1,
        roleName: '系统管理员',
        roleDesc: '拥有系统所有权限',
    },
    {
        id: 2,
        roleName: '内容管理员',
        roleDesc: '管理网站内容发布',
    },
    {
        id: 3,
        roleName: '用户管理员',
        roleDesc: '管理用户账号和权限',
    },
    {
        id: 4,
        roleName: '数据分析师',
        roleDesc: '查看和分析系统数据',
    },
    {
        id: 5,
        roleName: '客服主管',
        roleDesc: '管理客服团队和工单',
    },
])
// 表单数据
const formData = ref({
    roleName: '',
    roleDesc: '',
})
// 角色分配弹窗显示状态
const assignDialogVisible = ref(false)
// 权限查看弹窗显示状态
const permissionDialogVisible = ref(false)
// 新增角色弹窗显示状态
const addRoleDialogVisible = ref(false)
// 用户列表数据
const userList = ref([
    { id: 1, name: '张伟' },
    { id: 2, name: '李娜' },
    { id: 3, name: '王芳' },
    { id: 4, name: '赵明' },
    { id: 5, name: '刘强' },
])
// 选中的用户
const selectedUser = ref('')
// 角色选项列表
const roleOptions = ref([
    { id: 1, name: '系统管理员' },
    { id: 2, name: '内容管理员' },
    { id: 3, name: '用户管理员' },
    { id: 4, name: '数据分析师' },
    { id: 5, name: '客服主管' },
])
// 选中的角色列表
const selectedRoles = ref<number[]>([])
// 权限树数据
const permissionTree = ref([
    {
        id: 1,
        name: '系统管理',
        children: [
            {
                id: 101,
                name: '用户管理',
                children: [
                    { id: 10101, name: '新增用户' },
                    { id: 10102, name: '编辑用户' },
                    { id: 10103, name: '删除用户' },
                ],
            },
            {
                id: 102,
                name: '角色管理',
                children: [
                    { id: 10201, name: '新增角色' },
                    { id: 10202, name: '编辑角色' },
                    { id: 10203, name: '删除角色' },
                ],
            },
        ],
    },
    {
        id: 2,
        name: '内容管理',
        children: [
            {
                id: 201,
                name: '文章管理',
                children: [
                    { id: 20101, name: '发布文章' },
                    { id: 20102, name: '编辑文章' },
                    { id: 20103, name: '删除文章' },
                ],
            },
            {
                id: 202,
                name: '评论管理',
                children: [
                    { id: 20201, name: '审核评论' },
                    { id: 20202, name: '删除评论' },
                ],
            },
        ],
    },
])

// 自定义树节点样式
const customNodeClass = (data: any) => {
    return data.children ? 'has-children' : 'no-children'
}

/**
* 新增角色处理函数
*/
const handleAddRole = () => {
    if (!formData.value.roleName.trim()) {
        msg("warning", '请输入角色名称')
        return
    }
    const newRole = {
        id: roleList.value.length + 1,
        roleName: formData.value.roleName,
        roleDesc: formData.value.roleDesc,
    }
    roleList.value.push(newRole)
    roleOptions.value.push({ id: newRole.id, name: newRole.roleName })
    msg("success", '角色添加成功')
    formData.value = { roleName: '', roleDesc: '' }
    addRoleDialogVisible.value = false
}

/**
* 角色分配处理函数
*/
const handleAssignRole = (row: any) => {
    selectedUser.value = ''
    selectedRoles.value = []
    assignDialogVisible.value = true
    // 模拟获取用户已有角色
    setTimeout(() => {
        selectedRoles.value = [row.id]
    }, 100)
}
/**
* 查看权限处理函数
*/
const handleViewPermission = (row: any) => {
    permissionDialogVisible.value = true
    // 根据角色ID过滤权限数据
    // 这里简化处理，直接显示全部权限
}
/**
* 绑定权限处理函数
*/
const editorRole = (row: any) => {
    addRoleDialogVisible.value = true
}

/**
* 确认分配角色处理函数
*/
const handleConfirmAssign = () => {
    if (!selectedUser.value) {
        msg("warning", '请选择用户')
        return
    }
    if (selectedRoles.value.length === 0) {
        msg("warning", '请至少选择一个角色')
        return
    }
    const userName = userList.value.find(
        (user) => user.id === +selectedUser.value
    )?.name
    const roleNames = selectedRoles.value
        .map((roleId) => {
            return roleOptions.value.find((role) => role.id === roleId)?.name
        })
        .join('、')
    msg("success",
        `已为用户 ${userName} 分配角色: ${roleNames} (模拟数据)`
    )
    assignDialogVisible.value = false
}
</script>
<style scoped>
.permission-tree {
    margin: 0 20px;
}

:deep(.has-children) {
    font-weight: bold;
    color: #333;
}

:deep(.no-children) {
    color: #666;
    font-weight: normal;
}
</style>