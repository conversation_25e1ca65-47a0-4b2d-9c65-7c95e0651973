<!-- 代码已包含 CSS：使用 TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 -->

<template>
    <div class="flex-1 flex flex-col bg-gray-50 overflow-auto">
        <!-- Header -->
        <div class="border-b border-gray-200 mb-4">
            <div class="mx-auto px-4 py-4 sm:px-6 lg:px-8">
                <div class="flex items-center justify-between">
                    <div class="flex space-x-4">
                        <el-button type="primary" class="!rounded-button whitespace-nowrap" @click="showAddDialog">
                            <el-icon class="mr-1">
                                <Plus />
                            </el-icon>
                            新增专业
                        </el-button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tree Table -->
        <div class="flex-1 overflow-auto p-6">
            <el-table v-loading="isLoading" :data="majors" row-key="id"
                :tree-props="{ children: 'children', hasChildren: 'hasChildren' }" :default-expand-all="true"
                style="width: 100%" border>
                <el-table-column prop="name" label="专业名称">
                    <template #default="{ row }">
                        <span v-if="!row.parent_id" class="font-semibold">{{ row.name }}</span>
                        <span v-else>{{ row.name }}</span>
                    </template>
                </el-table-column>
                <el-table-column v-for="item in cols" :key="item.prop" :prop="item.prop" :label="item.label"
                    show-overflow-tooltip />
                <el-table-column label="操作" width="180" align="center">
                    <template #default="{ row }">
                        <el-button type="primary" link @click="editMajor(row)">
                            <el-icon>
                                <Edit />
                            </el-icon>
                        </el-button>
                        <el-button type="danger" link @click="confirmDelete(row)">
                            <el-icon>
                                <Delete />
                            </el-icon>
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
        </div>

        <!-- Add/Edit Dialog -->
        <el-dialog v-model="dialogVisible" :title="isEditMode ? '编辑专业' : '新增专业'" width="500px">
            <el-form ref="formRef" :model="majorForm" :rules="rules" label-width="100px">
                <el-form-item prop="parent_id" label="专业分类">
                    <el-tree-select :check-strictly="true" :empty-values="[0]" v-model="majorForm.parent_id"
                        placeholder="请选择专业分类" :data="majors" :props="props" />
                </el-form-item>
                <el-form-item prop="name" label="专业名称">
                    <el-input v-model="majorForm.name" placeholder="请输入专业名称"></el-input>
                </el-form-item>
                <el-form-item prop="major_id" label="专业编号">
                    <el-input v-model="majorForm.major_id" placeholder="请输入专业编号"></el-input>
                </el-form-item>
                <el-form-item prop="description" label="专业描述">
                    <el-input v-model="majorForm.description" type="textarea" :rows="3"
                        placeholder="请输入专业描述"></el-input>
                </el-form-item>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="submitMajorForm">
                        确认
                    </el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, watch } from 'vue';

import { ElMessageBox } from 'element-plus';
import { get, post } from '@/utils/request';
import { errMsg, successMsg } from '@/utils/msg';

const dialogVisible = ref(false);
watch(dialogVisible, (val) => {
    if (!val) {
        majorForm.value = {
            parent_id: 0,
            name: '',
            major_id: '',
            description: ''
        };
        isEditMode.value = false;
        formRef.value?.resetFields();
    }
});
const isEditMode = ref(false);
const currentEditId = ref('');


const isLoading = ref(false);
const majors: any = ref([]);
const cols = [
    { prop: 'major_id', label: '专业编号' },
    { prop: 'description', label: '专业描述' },
]
const props = {
    label: 'name',
    value: 'id'
}

const getMajors = async () => {
    isLoading.value = true;
    try {
        const res = await get('teacher/major/list')
        if (res.success) {
            majors.value = res.data || [];
        } else {
            errMsg(res.message)
        }
    } catch (error) {
        console.error(error);
    } finally {
        isLoading.value = false;
    }
}

// Dialog
const majorForm: any = ref({
    parent_id: 0,
    name: '',
    major_id: '',
    description: ''
});
const formRef = ref()
const rules = ref({
    // parent_id: [
    //     { required: true, message: '请选择专业分类', trigger: 'change' }
    // ],
    name: [
        { required: true, message: '请输入专业名称', trigger: 'blur' },
        { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
    ],
    major_id: [
        { required: true, message: '请输入专业编号', trigger: 'blur' },
    ],
    description: [
        { required: true, message: '请输入专业描述', trigger: 'blur' }
    ]
});
const showAddDialog = () => {
    dialogVisible.value = true;
};

const editMajor = (major: any) => {
    isEditMode.value = true;
    currentEditId.value = major.id;
    majorForm.value = {
        parent_id: major.parent_id,
        name: major.name,
        major_id: major.major_id,
        description: major.description
    };
    dialogVisible.value = true;
};

const submitMajorForm = async () => {
    await formRef.value.validate();
    // 添加
    if (!isEditMode.value) {
        const res = await post('teacher/major/create', majorForm.value)
        if (res.success) {
            successMsg(res.message)
            getMajors()
            dialogVisible.value = false
        } else {
            errMsg(res.message)
        }
    } else {
        // 编辑
        const res = await post('teacher/major/update', { ...majorForm.value, id: currentEditId.value })
        if (res.success) {
            successMsg(res.message)
            getMajors()
            dialogVisible.value = false
        } else {
            errMsg(res.message)
        }
    }

};
onMounted(() => {
    getMajors()
})


const confirmDelete = (major: any) => {
    ElMessageBox.confirm(
        `确定要删除专业 "${major.name}" 吗？`,
        '删除确认',
        {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
        }
    ).then(async () => {
        const res = await post(`teacher/major/delete/${major.id}`)
        if (res.success) {
            successMsg(res.message)
            getMajors()
        } else {
            errMsg(res.message)
        }
    }).catch(() => {
        // Cancel
    });
};
</script>

<style scoped>
.el-table {
    font-size: 14px;
}

.el-table :deep(.el-table__cell) {
    padding: 12px 0;
}

.el-table :deep(.el-table__expand-icon) {
    margin-right: 8px;
}

.dialog-footer {
    display: flex;
    justify-content: flex-end;
}
</style>
