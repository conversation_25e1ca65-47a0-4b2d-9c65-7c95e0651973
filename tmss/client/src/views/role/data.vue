<template>
  <div class="flex-1 flex flex-col bg-gray-50 overflow-auto">
    <!-- 头部操作区域 -->
    <div class="p-6 border-b border-gray-200">
      <div class="flex justify-between items-center">
        <div class="flex gap-4">
          <el-button type="primary" class="!rounded-button whitespace-nowrap" @click="handleAdd">
            <el-icon class="mr-1">
              <Plus />
            </el-icon>新增配置
          </el-button>
        </div>

        <!-- 搜索区域 -->
        <div class="w-1/3">
          <el-input v-model="searchQuery" style="max-width: 400px" placeholder="请输入关键词搜索" @keyup.enter="handleSearch"
            clearable>
            <template #prepend>
              <el-select v-model="selectSearch" placeholder="选择类型" style="width: 115px">
                <el-option :label="item.label" :value="item.value" v-for="item in searchTypes" />
              </el-select>
            </template>
            <template #append>
              <el-button :icon="Search" @click="handleSearch" />
            </template>
          </el-input>
        </div>
      </div>
    </div>

    <!-- 表格区域 -->
    <div class="flex-1 overflow-auto p-6">
      <el-table :data="tableData" border style="width: 100%">
        <el-table-column prop="name" label="配置名称" />
        <el-table-column prop="table_code" label="配置标志" />
        <el-table-column prop="type" label="规则类型">
          <template #default="{ row }">
            <el-tag :type="typeTagMap[row.type]">{{ typeFilter(row.type) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="适配">
          <template #default="{ row }">
            <el-tag>
              {{ getRoleName(row.sys_code) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="field" label="条件字段" />
        <el-table-column prop="value" label="条件值" />
        <el-table-column prop="is_default" label="默认配置">
          <template #default="{ row }">
            <el-tag :type="row.is_default ? 'success' : 'info'">
              {{ row.is_default ? '是' : '否' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200">
          <template #default="{ row }">
            <el-button type="primary" size="small" class="!rounded-button whitespace-nowrap" @click="handleEdit(row)">
              <el-icon>
                <Edit />
              </el-icon>编辑
            </el-button>
            <el-button type="danger" size="small" class="!rounded-button whitespace-nowrap ml-2"
              @click="handleDelete(row)">
              <el-icon>
                <Delete />
              </el-icon>删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页组件 -->
    <div class="px-6 py-4 border-t border-gray-200 flex justify-center">
      <el-pagination layout="prev, pager, next" :total="total" :page-size="search.page_size"
        v-model:current-page="search.page" @current-change="getDataConfigList" background />
    </div>

    <!-- 新增/编辑弹窗 -->
    <el-dialog :title="dialogTitle" v-model="dialogVisible" width="500px">
      <el-form :model="formData" :rules="rules" ref="formRef" label-width="100px">
        <el-form-item label="配置名称" prop="name">
          <el-input v-model="formData.name" placeholder="请输入配置名称" />
        </el-form-item>
        <el-form-item label="配置标志" prop="table_code">
          <!-- <el-input v-model="formData.table_code" placeholder="请输入表名" /> -->
          <el-select v-model="formData.table_code" placeholder="请选择配置标志">
            <el-option :label="item.name" :value="item.value" v-for="item in codeList" />
          </el-select>
        </el-form-item>
        <el-form-item label="适配" prop="sys_code">
          <el-select v-model="formData.sys_code" placeholder="请选择适配角色" class="w-full">
            <el-option v-for="item in sysList" :key="item.code" :label="item.name" :value="item.code"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="规则类型" prop="type">
          <el-select v-model="formData.type" placeholder="请选择规则类型" @change="handleTypeChange">
            <el-option label="条件匹配" value="condition" />
            <el-option label="IN查询" value="in" />
            <el-option label="Like查询" value="like" />
            <el-option label="自己" value="self" />
            <el-option label="无限制" value="all" />
          </el-select>
        </el-form-item>
        <el-form-item label="条件字段" prop="field" v-if="showFieldInput">
          <el-input v-model="formData.field" placeholder="请输入字段名" />
        </el-form-item>
        <el-form-item label="条件值" prop="value" v-if="showValueInput">
          <el-input v-model="formData.value" placeholder="请输入条件值" />
        </el-form-item>
        <el-form-item label="默认配置" prop="is_default">
          <el-switch v-model="formData.is_default" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input v-model="formData.description" placeholder="请输入描述" type="textarea" :rows="3" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="flex justify-end gap-2">
          <el-button @click="dialogVisible = false" class="!rounded-button whitespace-nowrap">取消</el-button>
          <el-button type="primary" @click="handleSubmit" class="!rounded-button whitespace-nowrap">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Edit, Delete, Plus } from '@element-plus/icons-vue'
import type { FormInstance } from 'element-plus'
import {
  getDataConfigList as apiGetDataConfigList,
  createDataConfig,
  updateDataConfig,
  deleteDataConfig
} from '@/api/role.ts'
import { sysList, getRoleName } from '@/api/config'

interface DataConfig {
  id?: number
  name: string
  table_code: string
  sys_code: string
  type: string
  field: string
  value: string
  is_default: boolean
  description: string
}

const tableData = ref<DataConfig[]>([])
const total = ref(0)
const dialogVisible = ref(false)
const formRef = ref<FormInstance>()
const isEdit = ref(false)
const dialogTitle = computed(() => isEdit.value ? '编辑配置' : '新增配置')
const searchQuery = ref('')
const selectSearch = ref('name')
const searchTypes = [
  { label: '配置名称', value: 'name' },
  { label: '配置标志', value: 'table_code' }
]
const codeList: any = ref([])
const search = ref({
  page: 1,
  page_size: 10
})

const formData = ref<DataConfig>({
  name: '',
  table_code: '',
  type: 'condition',
  field: '',
  value: '',
  sys_code: 'admin',
  is_default: false,
  description: ''
})

const rules = {
  name: [{ required: true, message: '请输入配置名称', trigger: 'blur' }],
  table_code: [{ required: true, message: '请输入配置标志', trigger: 'blur' }],
  type: [{ required: true, message: '请选择规则类型', trigger: 'change' }],
  field: [{ required: true, message: '请输入条件字段', trigger: 'blur' }],
  value: [{ required: true, message: '请输入条件值', trigger: 'blur' }]
}

const typeTagMap: any = {
  condition: '',
  in: 'success',
  all: 'warning'
}

// 计算属性：根据规则类型决定是否显示字段输入
const showFieldInput = computed(() =>
  formData.value.type !== 'all' && formData.value.type !== 'self'
)

const showValueInput = computed(() =>
  formData.value.type !== 'all' && formData.value.type !== 'self'
)

// 规则类型过滤器
const typeFilter = (type: string) => {
  const map: Record<string, string> = {
    condition: '条件匹配',
    in: 'IN查询',
    like: 'Like查询',
    self: '自己', // 新增
    all: '无限制'
  }
  return map[type] || type
}

// 获取数据权限配置列表
const getDataConfigList = async () => {
  const params: any = {
    page: search.value.page,
    pageSize: search.value.page_size
  }

  if (searchQuery.value && selectSearch.value) {
    params[selectSearch.value] = searchQuery.value
  }

  try {
    const res = await apiGetDataConfigList(params)
    tableData.value = res.list
    total.value = res.total
    codeList.value = res.codelist
  } catch (err) {
    console.error('获取配置列表失败:', err)
    ElMessage.error('获取数据失败')
  }
}

onMounted(() => {
  getDataConfigList()
})

// 重置表单
const resetForm = () => {
  formData.value = {
    name: '',
    table_code: '',
    type: 'condition',
    sys_code: 'admin',
    field: '',
    value: '',
    is_default: false,
    description: ''
  }
}

// 处理规则类型变化
const handleTypeChange = (type: string) => {
  if (type === 'self') {
    // 设置为特定值并隐藏输入
    if(formData.value.table_code == 'workflow_approve'){
      formData.value.field = 'checker_id'
    }else{
      formData.value.field = 'user_id'
    }
    formData.value.value = '__self'
  } else {
    formData.value.field = ''
    formData.value.value = ''
  }
}

// 新增按钮
const handleAdd = () => {
  isEdit.value = false
  resetForm()
  dialogVisible.value = true
}

// 编辑按钮
const handleEdit = (row: DataConfig) => {
  isEdit.value = true
  formData.value = { ...row }
  dialogVisible.value = true
}

// 删除按钮
const handleDelete = (row: DataConfig) => {
  ElMessageBox.confirm('确认删除该配置吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await deleteDataConfig(row.id!)
      getDataConfigList()
      ElMessage.success('删除成功')
    } catch (err) {
      ElMessage.error('删除失败')
    }
  })
}


// 搜索事件
const handleSearch = () => {
  search.value.page = 1
  getDataConfigList()
}



// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()

    if (isEdit.value) {
      await updateDataConfig(formData.value)
      ElMessage.success('更新配置成功')
    } else {
      await createDataConfig(formData.value)
      ElMessage.success('创建配置成功')
    }

    dialogVisible.value = false
    getDataConfigList()
  } catch (err) {
    console.error('表单提交失败:', err)
  }
}
</script>

<style scoped>
.el-input :deep(.el-input__wrapper) {
  box-shadow: 0 0 0 1px #dcdfe6 inset;
}

.el-input :deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #409eff inset;
}

.el-table :deep(.el-table__header-wrapper) {
  background-color: #f5f7fa;
}

.el-pagination {
  justify-content: flex-end;
}
</style>