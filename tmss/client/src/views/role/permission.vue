<template>
    <div class="flex-1 flex flex-col bg-gray-50 overflow-auto">
        <!-- 头部区域 -->
        <div class="p-6 border-b border-gray-200">
            <div class="flex justify-start items-center">
                <el-button type="primary" class="!rounded-button" @click="handleAddRole">
                    <el-icon class="mr-1">
                        <Plus />
                    </el-icon>新增角色
                </el-button>
            </div>
        </div>

        <!-- 角色列表 -->
        <div class=" flex-1 overflow-auto p-6">
            <el-table style="width: 100%" border :data="roleList">
                <el-table-column prop="role_name" label="角色名称" align="center" width="100" />
                <el-table-column prop="role_code" label="角色标志" align="center" width="100" />
                <el-table-column label="是否内置" align="center" width="100">
                    <template #default="{ row }">
                        <el-tag v-if="row.is_system" type="success">是</el-tag>
                        <el-tag v-else type="danger">否</el-tag>
                    </template>

                </el-table-column>
                <el-table-column label="适配">
                    <template #default="{ row }">
                        <el-tag>
                            {{ getRoleName(row.sys_code) }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="description" label="角色描述" align="center" />
                <el-table-column label="操作" width="450" align="center">
                    <template #default="{ row }">
                        <div class=" flex justify-center">
                            <el-button type="primary" plain @click="handleViewPermissions(row)">
                                编辑
                            </el-button>
                            <el-button type="success" plain @click="handleApiPermissions(row)">
                                角色权限
                            </el-button>
                            <el-button type="danger" plain @click="handleMenuPermissions(row)">
                                菜单权限
                            </el-button>
                            <el-button class="!rounded-button whitespace-nowrap" @click="handleDataPermission(row)">
                                数据权限
                            </el-button>
                            <el-button type="info" plain v-if="!row.is_system" @click="handleDelPermissions(row.id)">
                                删除
                            </el-button>
                        </div>

                    </template>
                </el-table-column>
            </el-table>
        </div>


        <div class="shrink-0 flex justify-center mt-4">
            <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :total="total"
                layout=" prev, pager, next" />
        </div>
    </div>

    <!-- 新增角色对话框 -->
    <el-dialog v-model="addRoleDialogVisible" :title="isEdit ? '编辑角色' : '新增角色'" width="500px" destroy-on-close>
        <el-form ref="addRoleFormRef" :model="addRoleForm" :rules="addRoleRules" label-width="100px">
            <el-form-item label="角色名称" prop="role_name">
                <el-input v-model="addRoleForm.role_name" placeholder="请输入角色名称" />
            </el-form-item>
            <el-form-item label="角色标志" prop="role_code">
                <el-input v-model="addRoleForm.role_code" :readonly="isEdit" placeholder="请输入角色标志" />
            </el-form-item>
            <el-form-item label="适配">
                <el-select v-model="addRoleForm.sys_code" placeholder="请选择适配角色" :disabled="isEdit" class="w-full">
                    <el-option v-for="item in sysList" :key="item.code" :label="item.name"
                        :value="item.code"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="角色描述" prop="description">
                <el-input v-model="addRoleForm.description" type="textarea" placeholder="请输入角色描述" />
            </el-form-item>
        </el-form>
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="addRoleDialogVisible = false">取消</el-button>
                <el-button type="primary" @click="submitAddRole">确定</el-button>
            </span>
        </template>
    </el-dialog>
    <!-- 数据权限配置弹窗 -->
    <el-drawer v-model="dataPermissionDialogVisible" :title="dataConfigTitle" size="70%" destroy-on-close>
        <div class="mb-4">
            <div class="flex justify-end items-center mb-4">
                <div class="w-1/3">
                    <el-input v-model="permissionSearch" placeholder="搜索权限名称" clearable @clear="loadPermissions">
                        <template #append>
                            <el-button icon="Search" @click="loadPermissions" />
                        </template>
                    </el-input>
                </div>
            </div>

            <el-table :data="permissionList" border style="width: 100%" row-key="id" @selection-change="handleSelectionChange"
                v-loading="loading" ref="dataTableRef">
                <el-table-column type="selection" width="55" align="center" />
                <el-table-column prop="name" label="权限名称" min-width="150" />
                <el-table-column prop="table_code" label="权限标志" min-width="120" />
                <el-table-column prop="field" label="条件字段" min-width="120" />
                <el-table-column prop="value" label="条件值" min-width="120" />
                <el-table-column prop="is_default" label="默认配置" min-width="100">
                    <template #default="{ row }">
                        <el-tag :type="row.is_default ? 'success' : 'info'">
                            {{ row.is_default ? '是' : '否' }}
                        </el-tag>
                    </template>
                </el-table-column>
            </el-table>
        </div>

        <template #footer>
            <div class="mt-4 flex justify-between items-center">
                <el-pagination background layout="prev, pager, next" :total="permissionTotal"
                    :page-size="permissionPageSize" v-model:current-page="permissionPage"
                    @current-change="loadPermissions" />

                <div>
                    <span class="text-sm text-gray-500 mr-2">已选 {{ selectedPermissions.length }} 项</span>
                    <el-button type="primary" @click="savePermissions"
                        class="!rounded-button whitespace-nowrap">保存配置</el-button>
                </div>
            </div>
        </template>
    </el-drawer>

    <!-- 权限设置抽屉 -->
    <el-drawer v-model="permissionDrawerVisible" :title="drawerTitle" size="50%" destroy-on-close>

        <el-tree v-if="activeTab == 'api'" ref="apiTreeRef" :data="apiPermissions" show-checkbox node-key="id"
            :props="defaultProps" :default-checked-keys="checkedApiKeys" />
        <el-tree v-if="activeTab == 'menu'" ref="menuTreeRef" :data="menuPermissions" show-checkbox node-key="id"
            :props="menuProps" :default-checked-keys="checkedMenuKeys" />
        <template #footer>
            <div class="flex justify-end space-x-4">
                <el-button @click="permissionDrawerVisible = false">取消</el-button>
                <el-button type="primary" @click="handleSavePermissions">保存</el-button>
            </div>
        </template>
    </el-drawer>
</template>

<script lang="ts" setup>
import { nextTick, onMounted, ref, watch } from 'vue';
import { ElMessage, type FormInstance, type FormRules } from 'element-plus';
import { get, post } from '@/utils/request';
import { sysList, getRoleName } from '@/api/config';
import {  getRoleDataConfig, setRoleDataConfig } from '@/api/role'
const isEdit = ref(false)

// 角色列表数据
const roleList: any = ref([]);

// 分页相关
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);

// 新增角色相关
const addRoleDialogVisible = ref(false);
const addRoleFormRef = ref<FormInstance>();
const addRoleForm: any = ref({
    id: 0,
    role_name: '',
    role_code: '',
    description: '',
});
const addRoleRules: FormRules = {
    role_name: [{ required: true, message: '请输入角色名称', trigger: 'blur' }],
    role_code: [{ required: true, message: '请输入角色标志', trigger: 'blur' }],
};

// 权限抽屉相关
const permissionDrawerVisible = ref(false);
const activeTab = ref('menu');
const drawerTitle = ref('权限设置');
const menuTreeRef: any = ref(null);
const apiTreeRef: any = ref(null);
const currentRole: any = ref({});
// 权限树配置
const defaultProps = {
    children: 'children',
    label: 'label',
};
const menuProps = {
    children: 'children',
    label: 'menu_name',
};
// 菜单权限数据
const menuPermissions: any = ref([]);

// API权限数据
const apiPermissions: any = ref();

const checkedMenuKeys = ref<number[]>([]);
const checkedApiKeys = ref<string[]>([]);
const getRoleList = async () => {
    const res = await get('/admin/roles/list');
    if (!res.success) return;
    roleList.value = res.data;
};
// 数据权限相关变量
const dataPermissionDialogVisible = ref(false)
const permissionList = ref<any[]>([])
const permissionPage = ref(1)
const permissionPageSize = ref(10)
const permissionTotal = ref(0)
const permissionSearch = ref('')
const loading = ref(false)
const dataConfigTitle = ref('数据权限设置')
const selectedPermissions = ref<any[]>([])
const dataTableRef = ref()
onMounted(() => {
    getRoleList();
});
const handleDataPermission = (role: any) => {
    currentRole.value = role
    dataConfigTitle.value = '数据权限设置-' + role.role_name
    dataPermissionDialogVisible.value = true
    loadPermissions()
}
// 处理新增角色
const handleAddRole = () => {
    addRoleDialogVisible.value = true;
};
const handleDelPermissions = (id: number) => {
    post(`/admin/roles/delete/${id}`).then(() => {
        getRoleList();
    }).catch(() => {
        ElMessage.error('删除失败');
    })
}
const submitAddRole = async () => {
    if (!addRoleFormRef.value) return;
    await addRoleFormRef.value.validate((valid) => {
        if (valid) {
            // 处理表单提交
            if (!isEdit.value) {
                post('/admin/roles/create', addRoleForm.value).then((res: any) => {
                    if (res.success) {
                        ElMessage.success('添加成功');
                        getRoleList();
                    } else {
                        ElMessage.error(res.message);
                    }
                })
            } else {
                post('/admin/roles/update', addRoleForm.value).then((res: any) => {
                    if (res.success) {
                        ElMessage.success('修改成功');
                        getRoleList();
                    } else {
                        ElMessage.error(res.message);
                    }
                })

            }
            addRoleDialogVisible.value = false;

        }
    });
};

// 处理权限操作
const handleViewPermissions = (row: any) => {
    isEdit.value = true;
    addRoleDialogVisible.value = true;
    addRoleForm.value = { ...row };
};
watch(addRoleDialogVisible, (val) => {
    if (!val) {
        isEdit.value = false;
        addRoleForm.value = {
            id: 0,
            role_name: '',
            role_code: '',
            description: ''
        }
    }
})

const handleApiPermissions = (row: any) => {
    //console.log(row)
    currentRole.value = row;
    drawerTitle.value = `API权限 - ${row.role_name}`;
    permissionDrawerVisible.value = true;
    activeTab.value = 'api';
    get(`admin/roles/rules/${row.role_code}/${row.sys_code}`).then((res: any) => {
        //console.log(res)
        if (res.success) {
            apiPermissions.value = res.data.rules;
            apiTreeRef.value!.setCheckedKeys([], false)
            if (res.data.checkeds && res.data.checkeds.length > 0) {
                checkedApiKeys.value = res.data.checkeds.map((item: any) => item.id);
            }
        }
    })
};

const handleMenuPermissions = (row: any) => {
    drawerTitle.value = `菜单权限 - ${row.role_name}`;
    permissionDrawerVisible.value = true;
    activeTab.value = 'menu';
    currentRole.value = row;
    get(`admin/menu/listrole/${row.role_code}/${row.sys_code}`).then((res: any) => {
        //console.log(res)
        if (res.success) {
            menuPermissions.value = res.data.menuTree;
            menuTreeRef.value!.setCheckedKeys(res.data.ids, false)
        }
    })
};


// 保存权限设置
const handleSavePermissions = () => {

    if (!currentRole.value.role_code) return ElMessage.error('角色代码为空')
    if (activeTab.value == 'api') {
        const checkedNodes = apiTreeRef.value!.getCheckedNodes(false, false);
        //console.log(checkedNodes)
        if (checkedNodes && checkedNodes.length > 0) {
            const saveData: any = [];
            checkedNodes.forEach((item: any) => {
                if (item.path && item.path != "") {
                    item.role_code = currentRole.value.role_code;
                    saveData.push(item);
                }
            })
            //console.log(saveData)
            if (saveData.length > 0) {
                post('admin/roles/rules', {
                    role_code: currentRole.value.role_code,
                    data: saveData
                }).then((res: any) => {
                    if (res.success) {
                        ElMessage.success('保存成功')
                        permissionDrawerVisible.value = false;
                    } else {
                        ElMessage.error('保存失败')
                    }
                })
            }
        } else {
            ElMessage.error('请选择角色')
        }
    }
    if (activeTab.value == 'menu') {
        const checkedKeys = menuTreeRef.value!.getCheckedKeys(false);
        //console.log(checkedNodes)
        if (checkedKeys && checkedKeys.length > 0) {
            post('admin/menu/updaterole', {
                role_code: currentRole.value.role_code,
                menu_ids: checkedKeys
            }).then((res: any) => {
                if (res.success) {
                    ElMessage.success('保存成功')
                    permissionDrawerVisible.value = false;
                } else {
                    ElMessage.error('保存失败')
                }
            })
        } else {
            ElMessage.error('请选择权限')
        }
    }
};
// 加载权限配置列表
const loadPermissions = async () => {
  try {
    loading.value = true
    const params = {
      page: permissionPage.value,
      page_size: permissionPageSize.value,
      search: permissionSearch.value
    }

    const res = await getRoleDataConfig(currentRole.value.role_code, currentRole.value.sys_code, params)
    if (!res.list) {
      return
    }
    permissionList.value = res.list
    permissionTotal.value = res.total

    // 根据角色获取已配置权限
    const selectedIds = res.ids || []
    
    // 等待 DOM 更新
    await nextTick()
    
    // 清空之前的选中状态
    if (dataTableRef.value) {
      dataTableRef.value.clearSelection()
    }
    
    // 设置新的选中状态
    permissionList.value.forEach(item => {
      if (selectedIds.includes(item.id) && dataTableRef.value) {
        dataTableRef.value.toggleRowSelection(item, true)
      }
    })
  } catch (error) {
    console.error('加载权限配置失败:', error)
    ElMessage.error('加载权限配置失败')
  } finally {
    loading.value = false
  }
}
// 处理表格选择变化
const handleSelectionChange = (selections: any[]) => {
    selectedPermissions.value = selections
}

// 保存权限配置
const savePermissions = async () => {
    try {
        const ids = selectedPermissions.value.map(p => p.id)
        await setRoleDataConfig(currentRole.value.role_code, ids)

        // 显示成功消息
        ElMessage.success('数据权限配置保存成功')
        dataPermissionDialogVisible.value = false
    } catch (error) {
        console.error('保存权限配置失败:', error)
        ElMessage.error('保存配置失败')
    }
}



</script>

<style scoped>
.dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
}
</style>
