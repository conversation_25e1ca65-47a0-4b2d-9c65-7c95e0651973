<template>
	<div class="min-h-screen w-full flex flex-col items-center justify-center bg-gray-800">
		<div class="w-[420px] bg-gray-700 rounded-lg shadow-xl p-8">
			<!-- Logo和标题区域 -->
			<div class="flex flex-col items-center mb-8">
				<div class="w-auto h-20 mb-4 overflow-hidden rounded-lg">
					<!-- <img
						:src="logo"
						alt="TMSC Logo"
						class="w-full h-full object-cover"
					/> -->
				</div>
				<h1 class="text-2xl font-bold text-white mb-2">教学管理系统</h1>
				<p class="text-gray-400">Teaching Management System Center</p>
			</div>

			<!-- 登录表单 -->
			<el-form class="space-y-6" ref="formRef" :model="store.loginForm" :rules="rules">
				<div class="relative">
					<el-form-item prop="account">
						<el-input v-model="store.loginForm.account" :class="{ '!rounded-button': true }"
							placeholder="请输入账号" size="large">
							<template #prefix>
								<el-icon class="text-gray-400">
									<User />
								</el-icon>
							</template>
						</el-input>
					</el-form-item>
				</div>

				<div class="relative">
					<el-form-item prop="password">
						<el-input v-model="store.loginForm.password" :class="{ '!rounded-button': true }"
							type="password" placeholder="请输入密码" size="large" show-password>
							<template #prefix>
								<el-icon class="text-gray-400">
									<Lock />
								</el-icon>
							</template>
						</el-input>
					</el-form-item>
				</div>

				<div class="flex items-center justify-between">
					<el-checkbox v-model="store.loginForm.remember" class="text-sm text-gray-600">记住密码</el-checkbox>
					<!-- <a href="#" class="text-sm text-blue-600 hover:text-blue-800">忘记密码？</a> -->
				</div>

				<el-button type="primary" :class="{ '!rounded-button': true }"
					class="w-full !bg-[#4A90E2] hover:!bg-blue-600 whitespace-nowrap" size="large" @click="handleLogin">
					登录
				</el-button>

				<!-- <div class="text-center text-sm text-gray-600">
					<span>首次使用？</span>
					<a href="#" class="text-blue-600 hover:text-blue-800 ml-1">注册账号</a>
				</div> -->
			</el-form>
		</div>

		<!-- 页脚 -->
		<footer class="mt-8 text-center text-gray-400 text-sm">
			<p class="mb-2">© 2025 TMS教学管理系统 版权所有</p>
		</footer>
		<teleport to="body">
			<SlideCaptcha v-if="showCaptcha" @onSuccess="handleCaptchaConfirm" @onCancel="closeCaptcha" />
		</teleport>
	</div>
</template>

<script lang="ts" setup>
import { ref, reactive } from "vue";
import { User, Lock } from "@element-plus/icons-vue";
import { useUserStore } from "@/store/user";
import { useRouter } from "vue-router";

import logo from "@/assets/logo.png";
import { errMsg } from "@/utils/msg";
import { useTabsStore } from "@/store/tabs";
//const logoUrl = 'https://ai-public.mastergo.com/ai/img_res/badf11400ab55bbe090c06c162eac419.jpg';


const formRef = ref();
const rules = reactive({
	account: [
		{ required: true, message: "请输入用户名", trigger: "blur" },
		{
			min: 3,
			max: 20,
			message: "长度在 3 到 20 个字符",
			trigger: "blur",
		},
	],
	password: [
		{ required: true, message: "请输入密码", trigger: "blur" },
		{
			min: 3,
			max: 20,
			message: "长度在 3 到 20 个字符",
			trigger: "blur",
		},
	],
});
const showCaptcha = ref(false);

const store = useUserStore();
const router = useRouter();
const handleLogin = async () => {
	formRef.value.validate((valid: boolean) => {
		if (valid) {
			showCaptcha.value = true;
		} else {
			errMsg("请检查输入内容");
			return false;
		}
	});

};
const tabStore = useTabsStore()
const handleCaptchaConfirm = (success: boolean) => {
	if (success) {
		//captchaPassed.value = true;
		showCaptcha.value = false;
		store.onLogin().then(() => {
			router.push("/");
			tabStore.initTabs()
		})
	} else {
		errMsg("验证码验证失败，请重试");
		showCaptcha.value = false;
	}
};
const closeCaptcha = () => {
	showCaptcha.value = false;
};
</script>

<style scoped>
.el-input :deep(.el-input__wrapper) {
	box-shadow: 0 0 0 1px #dcdfe6 inset;
}

.el-input :deep(.el-input__wrapper.is-focus) {
	box-shadow: 0 0 0 1px #4a90e2 inset;
}

.el-input :deep(.el-input__inner) {
	height: 40px;
}

.el-checkbox :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
	background-color: #4a90e2;
	border-color: #4a90e2;
}

.el-checkbox :deep(.el-checkbox__input.is-checked + .el-checkbox__label) {
	color: #4a90e2;
}

.el-button.el-button--primary {
	background-color: #4a90e2;
	border-color: #4a90e2;
}

.el-button.el-button--primary:hover {
	background-color: #357abd;
	border-color: #357abd;
}
</style>
