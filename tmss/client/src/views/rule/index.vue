<template>
    <div class="flex-1 flex flex-col bg-gray-50 overflow-auto">
        <!-- Header -->
        <div class=" border-b border-gray-200">
            <div class="mx-auto  px-4 py-4 sm:px-6 lg:px-8">
                <div class="flex items-center justify-start">
                    <el-button type="primary" class="!rounded-button whitespace-nowrap" @click="showDrawer = true">
                        <el-icon class="mr-2">
                            <Plus />
                        </el-icon>
                        新增规则
                    </el-button>
                </div>

                <!-- Search and Filter -->
                <div class="mt-6 grid grid-cols-1 gap-4 sm:grid-cols-4">
                    <div class="col-span-1">
                        <div class="relative rounded-md shadow-sm">
                            <div class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                                <el-icon class="text-gray-400">
                                    <Search />
                                </el-icon>
                            </div>
                            <input type="text"
                                class="block w-full rounded-md border-0 py-1.5 pl-10 text-gray-900 ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6"
                                placeholder="搜索规则名称" v-model="searchQuery" />
                        </div>
                    </div>
                    <div class="col-span-1">
                        <el-select v-model="selectedType" placeholder="规则类型" class="w-full" clearable>
                            <el-option label="操作考试" value="operation" />
                            <el-option label="理论考试" value="theory" />
                        </el-select>
                    </div>
                    <div class="col-span-1">
                        <el-select v-model="selectedSubjects" placeholder="所属学科" class="w-full" multiple clearable>
                            <el-option label="数学" value="math" />
                            <el-option label="计算机" value="computer" />
                            <el-option label="物理" value="physics" />
                            <el-option label="化学" value="chemistry" />
                        </el-select>
                    </div>
                    <div class="col-span-1">
                        <el-select v-model="selectedStatus" placeholder="状态" class="w-full" clearable>
                            <el-option label="启用" value="active" />
                            <el-option label="禁用" value="inactive" />
                        </el-select>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 overflow-auto p-6">
            <el-table :data="filteredRules" class="w-full" :default-sort="{ prop: 'createdAt', order: 'descending' }"
                border>
                <el-table-column prop="name" label="规则名称" sortable min-width="200">
                    <template #default="{ row }">
                        <span class="font-medium text-gray-900">{{ row.name }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="type" label="规则类型" sortable width="120">
                    <template #default="{ row }">
                        <el-tag :type="row.type === 'operation' ? 'primary' : 'success'">
                            {{ row.type === 'operation' ? '操作考试' : '理论考试' }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="subjects" label="所属学科" min-width="150">
                    <template #default="{ row }">
                        <div class="flex flex-wrap gap-1">
                            <el-tag v-for="subject in row.subjects" :key="subject" size="small" type="info">
                                {{ subjectLabels[subject] }}
                            </el-tag>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column prop="creator" label="创建人" sortable width="120" />
                <el-table-column prop="createdAt" label="创建时间" sortable width="150">
                    <template #default="{ row }">
                        {{ formatDate(row.createdAt) }}
                    </template>
                </el-table-column>
                <el-table-column prop="status" label="状态" width="100">
                    <template #default="{ row }">
                        <el-switch v-model="row.status" :active-value="'active'" :inactive-value="'inactive'"
                            @change="handleStatusChange(row)" />
                    </template>
                </el-table-column>
                <el-table-column label="操作" width="180">
                    <template #default="{ row }">
                        <div class="flex items-center space-x-2">
                            <el-button size="small" :disabled="!hasEditPermission" @click="handleEdit(row)">
                                <el-icon>
                                    <Edit />
                                </el-icon>
                            </el-button>
                            <el-button size="small" type="danger" :disabled="!hasDeletePermission"
                                @click="handleDelete(row)">
                                <el-icon>
                                    <Delete />
                                </el-icon>
                            </el-button>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <div class=" flex justify-center mt-4">
            <el-pagination :total="rules.length" layout=" prev, pager, next" />
        </div>
        <!-- Drawer Form -->
        <el-drawer v-model="showDrawer" :title="isEditing ? '编辑规则' : '新增规则'" direction="rtl" size="40%">
            <div class="drawer-content">
                <el-form :model="ruleForm" :rules="rulesFormRules" ref="ruleFormRef" label-position="top" class="p-4">
                    <el-form-item label="规则名称" prop="name">
                        <el-input v-model="ruleForm.name" placeholder="请输入规则名称" />
                    </el-form-item>

                    <el-form-item label="规则类型" prop="type">
                        <el-radio-group v-model="ruleForm.type">
                            <el-radio-button label="operation">操作考试</el-radio-button>
                            <el-radio-button label="theory">理论考试</el-radio-button>
                        </el-radio-group>
                    </el-form-item>

                    <el-form-item label="所属学科" prop="subjects">
                        <el-select v-model="ruleForm.subjects" placeholder="请选择所属学科" multiple class="w-full">
                            <el-option label="数学" value="math" />
                            <el-option label="计算机" value="computer" />
                            <el-option label="物理" value="physics" />
                            <el-option label="化学" value="chemistry" />
                        </el-select>
                    </el-form-item>

                    <el-form-item label="规则内容" prop="content">
                        <el-input v-model="ruleForm.content" type="textarea" :rows="4"
                            placeholder="请输入规则内容，如：实验操作评分标准：步骤错误扣2分/次" />
                    </el-form-item>

                    <el-form-item label="分值设置" v-if="ruleForm.type === 'operation'">
                        <div class="space-y-4">
                            <div class="flex items-center space-x-4">
                                <el-input-number v-model="ruleForm.deduction" :min="0" :max="10" :step="0.5"
                                    controls-position="right" />
                                <span>分/次</span>
                            </div>
                        </div>
                    </el-form-item>

                    <el-form-item label="权重配置" v-if="ruleForm.type === 'theory'">
                        <div class="space-y-4">
                            <div class="flex items-center space-x-4">
                                <span>选择题</span>
                                <el-input-number v-model="ruleForm.choiceWeight" :min="0" :max="100" :step="5"
                                    controls-position="right" />
                                <span>%</span>
                            </div>
                            <div class="flex items-center space-x-4">
                                <span>简答题</span>
                                <el-input-number v-model="ruleForm.essayWeight" :min="0" :max="100" :step="5"
                                    controls-position="right" />
                                <span>%</span>
                            </div>
                        </div>
                    </el-form-item>

                    <el-form-item label="状态" prop="status">
                        <el-switch v-model="ruleForm.status" :active-value="'active'" :inactive-value="'inactive'" />
                    </el-form-item>
                </el-form>
            </div>

            <template #footer>
                <div class="flex justify-end space-x-4">
                    <el-button @click="showDrawer = false">取消</el-button>
                    <el-button type="primary" @click="submitForm" :loading="isSubmitting">
                        保存
                    </el-button>
                </div>
            </template>
        </el-drawer>
    </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue';
import { Delete, Edit, Plus, Search } from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from 'element-plus';

interface Rule {
    id: string;
    name: string;
    type: 'operation' | 'theory';
    subjects: string[];
    creator: string;
    createdAt: string;
    status: 'active' | 'inactive';
    content?: string;
    deduction?: number;
    choiceWeight?: number;
    essayWeight?: number;
}

const subjectLabels: any = {
    math: '数学',
    computer: '计算机',
    physics: '物理',
    chemistry: '化学',
};

const rules = ref<Rule[]>([
    {
        id: '1',
        name: '实验操作评分标准',
        type: 'operation',
        subjects: ['physics', 'chemistry'],
        creator: '张教授',
        createdAt: '2023-05-15',
        status: 'active',
        content: '步骤错误扣2分/次',
        deduction: 2,
    },
    {
        id: '2',
        name: '理论考试得分权重',
        type: 'theory',
        subjects: ['math', 'computer'],
        creator: '李教授',
        createdAt: '2023-06-20',
        status: 'active',
        content: '选择题30%、简答题70%',
        choiceWeight: 30,
        essayWeight: 70,
    },
    {
        id: '3',
        name: '编程实践评分标准',
        type: 'operation',
        subjects: ['computer'],
        creator: '王教授',
        createdAt: '2023-07-10',
        status: 'active',
        content: '编译错误扣1分/次，逻辑错误扣3分/次',
        deduction: 1,
    },
    {
        id: '4',
        name: '数学理论考试权重',
        type: 'theory',
        subjects: ['math'],
        creator: '赵教授',
        createdAt: '2023-08-05',
        status: 'inactive',
        content: '选择题40%、简答题60%',
        choiceWeight: 40,
        essayWeight: 60,
    },
    {
        id: '5',
        name: '化学实验安全规范',
        type: 'operation',
        subjects: ['chemistry'],
        creator: '钱教授',
        createdAt: '2023-09-12',
        status: 'active',
        content: '违反安全规范扣5分/次',
        deduction: 5,
    },
]);

const searchQuery = ref('');
const selectedType = ref('');
const selectedSubjects = ref<string[]>([]);
const selectedStatus = ref('');

const showDrawer = ref(false);
const isEditing = ref(false);
const isSubmitting = ref(false);
const currentRuleId = ref('');

const ruleForm = ref<Partial<Rule>>({
    name: '',
    type: 'operation',
    subjects: [],
    status: 'active',
    content: '',
    deduction: 0,
    choiceWeight: 0,
    essayWeight: 0,
});

const rulesFormRules = {
    name: [
        { required: true, message: '请输入规则名称', trigger: 'blur' },
        { min: 3, max: 50, message: '长度在 3 到 50 个字符', trigger: 'blur' },
    ],
    type: [
        { required: true, message: '请选择规则类型', trigger: 'change' },
    ],
    subjects: [
        { required: true, message: '请至少选择一个学科', trigger: 'change' },
    ],
    content: [
        { required: true, message: '请输入规则内容', trigger: 'blur' },
    ],
};

const hasEditPermission = ref(true);
const hasDeletePermission = ref(true);

const filteredRules = computed(() => {
    return rules.value.filter((rule) => {
        const matchesSearch = rule.name.toLowerCase().includes(searchQuery.value.toLowerCase());
        const matchesType = selectedType.value ? rule.type === selectedType.value : true;
        const matchesSubjects = selectedSubjects.value.length > 0
            ? selectedSubjects.value.some(subject => rule.subjects.includes(subject))
            : true;
        const matchesStatus = selectedStatus.value ? rule.status === selectedStatus.value : true;

        return matchesSearch && matchesType && matchesSubjects && matchesStatus;
    });
});

const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
    });
};

const handleEdit = (rule: Rule) => {
    isEditing.value = true;
    currentRuleId.value = rule.id;
    ruleForm.value = { ...rule };
    showDrawer.value = true;
};

const handleDelete = (rule: Rule) => {
    ElMessageBox.confirm(
        `确定要删除规则 "${rule.name}" 吗？此操作不可恢复。`,
        '删除确认',
        {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        }
    ).then(() => {
        rules.value = rules.value.filter(r => r.id !== rule.id);
        ElMessage.success('删除成功');
    }).catch(() => {
        // 取消删除
    });
};

const handleStatusChange = (rule: Rule) => {
    const action = rule.status === 'active' ? '启用' : '禁用';
    ElMessage.success(`已${action}规则 "${rule.name}"`);
};

const submitForm = () => {
    isSubmitting.value = true;

    setTimeout(() => {
        if (isEditing.value) {
            // 更新现有规则
            const index = rules.value.findIndex(r => r.id === currentRuleId.value);
            if (index !== -1) {
                rules.value[index] = { ...ruleForm.value, id: currentRuleId.value } as Rule;
            }
            ElMessage.success('规则更新成功');
        } else {
            // 添加新规则
            const newRule: Rule = {
                id: Date.now().toString(),
                ...ruleForm.value,
                creator: '当前用户',
                createdAt: new Date().toISOString(),
            } as Rule;
            rules.value.unshift(newRule);
            ElMessage.success('规则添加成功');
        }

        isSubmitting.value = false;
        showDrawer.value = false;
        resetForm();
    }, 1000);
};

const resetForm = () => {
    ruleForm.value = {
        name: '',
        type: 'operation',
        subjects: [],
        status: 'active',
        content: '',
        deduction: 0,
        choiceWeight: 0,
        essayWeight: 0,
    };
    isEditing.value = false;
    currentRuleId.value = '';
};
</script>

<style scoped>
.drawer-content {
    height: calc(100% - 60px);
    overflow-y: auto;
}

.el-table {
    --el-table-border-color: #e5e7eb;
    --el-table-header-bg-color: #f9fafb;
}



.el-drawer__body {
    padding: 0;
}

.el-form-item {
    margin-bottom: 24px;
}

.el-input-number {
    width: 120px;
}
</style>
