<template>
    <div class="flex-1 flex flex-col bg-gray-50 overflow-auto">
        <!-- 列表区域 -->
        <div class="flex-1 overflow-auto p-6">
            <el-table :data="tableData" v-loading="loading" style="width: 100%" border>
                <el-table-column align="center" v-for="item in cols" :key="item.prop" :prop="item.prop"
                    :label="item.label" show-overflow-tooltip />
                <el-table-column align="center" prop="submit_at" label="提交时间">
                    <template #default="{ row }">
                        {{ formatTime(row.submit_at) }}
                    </template>
                </el-table-column>
                <el-table-column label="操作" align="center" width="240">
                    <template #default="{ row }">
                        <div class="flex justify-center">
                            <el-button plain @click="view(row)">查看</el-button>
                            <el-button type="primary" plain @click="review(row.id)">通过</el-button>
                            <el-button type="danger" plain @click="reject(row.id)">驳回</el-button>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
        </div>

        <!-- 分页区域 -->
        <div class="shrink-0 flex justify-center mt-4 pb-2">
            <el-pagination background v-model:current-page="page" v-model:page-size="pageSize" :total="total"
                layout=" prev, pager, next" @current-change="handleCurrentChange" />
        </div>

        <!-- 新建/编辑弹窗 -->
        <el-dialog v-model="dialogVisible" title="查看详情" width="800px">
            <el-form ref="formRef" :model="formData" label-width="120px" disabled>
                <el-form-item label="教学大纲名称">
                    <el-input v-model="formData.name" placeholder="请输入教学大纲名称" />
                </el-form-item>
                <el-form-item label="教学目标">
                    <el-input v-model="formData.goals" type="textarea" :rows="3" placeholder="请输入教学目标" />
                </el-form-item>
                <el-form-item label="大纲描述">
                    <el-input v-model="formData.description" type="textarea" :rows="3" placeholder="请输入大纲描述" />
                </el-form-item>
                <el-form-item label="参考资料">
                    <el-input v-model="formData.reference" type="textarea" :rows="3" placeholder="请输入参考资料，每行一个" />
                </el-form-item>
                <el-form-item label="推荐教材">
                    <my-select v-model="formData.textbook_ids" placeholder="请选择推荐教材" :func="getTextBookList"
                        labelKey="title" valueKey="id" multiple searchKey="title" :initData="initSelectData" />
                </el-form-item>
            </el-form>
        </el-dialog>
    </div>
</template>

<script setup lang="ts">
import { getBookById, getSyllabusReviewingList, getTextBookList, rejectSyllabus, reviewSyllabus } from '@/api/syllabus';
import { confirmMsg, successMsg } from '@/utils/msg';
import { computed, onMounted, ref, watch } from 'vue';

const page = ref(1)
const pageSize = ref(10)
const total = ref(0)
const handleCurrentChange = (val: number) => {
    page.value = val
    getData()
}
const loading = ref(false)
const data: any = ref([])
const tableData = computed(() => {
    return data.value.map((item: any) => item.syllabus)
})
const cols = [
    { prop: 'name', label: '大纲名称' },
    { prop: 'goals', label: '教学目标' },
    { prop: 'description', label: '大纲描述' },
]

const formatTime = (time: any) => {
    return new Date(time * 1000).toLocaleString().replace(/\//g, '-')
}
const getData = async () => {
    loading.value = true

    const res = await getSyllabusReviewingList({ page: page.value })
    loading.value = false

    console.log(res)
    data.value = res.list || []
    total.value = res.total || 0
}

const reject = (id: any) => {
    confirmMsg('确定要驳回吗？', '提示', async (action) => {
        console.log(action)
        if (action) {
            const res = await rejectSyllabus(id)
            successMsg(res.message)
            getData()
        }
    })
}
const review = (id: any) => {
    confirmMsg('确定要通过吗？', '提示', async (action) => {
        console.log(action)
        if (action) {
            const res = await reviewSyllabus(id)
            successMsg(res.message)
            getData()
        }
    })
}
// dialog
const dialogVisible = ref(false)
const initFormData = () => {
    return {
        name: '',
        goals: '',
        description: '',
        reference: '',
        textbook_ids: [],
    }
}
const formData = ref(initFormData())
const initSelectData = ref([])

const view = async (row: any) => {
    const res = await getBookById(row.id)
    initSelectData.value = res

    formData.value = {
        ...row,
        textbook_ids: res.map((item: any) => item.id)
    }
    dialogVisible.value = true
}
watch(dialogVisible, (val) => {
    if (!val) formData.value = initFormData()
    initSelectData.value = []
})

onMounted(() => {
    getData()
})
</script>