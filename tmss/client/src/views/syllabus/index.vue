<template>
    <div class="flex-1 flex flex-col bg-gray-50 overflow-auto">
        <!-- 顶部搜索区域 -->
        <div class="p-6 border-b border-gray-200">
            <el-button type="primary" class="mb-4 !rounded-button whitespace-nowrap ml-auto" @click="showCreateDialog">
                <el-icon class="mr-1">
                    <Plus />
                </el-icon>
                新建大纲
            </el-button>
            <el-button type="success" class="mb-4 !rounded-button whitespace-nowrap ml-auto"
                @click="reviseListDrawerVisible = true">
                <el-icon class="mr-1">
                    <View />
                </el-icon>
                修订列表
            </el-button>
            <div class="flex items-center space-x-4">
                <el-input v-model="searchParams.name" placeholder="请输入大纲名称" class="w-64" clearable />
                <el-select v-model="searchParams.status" placeholder="选择状态" class="w-48" clearable>
                    <el-option v-for="status in statusOptions" :key="status.value" :label="status.label"
                        :value="status.value" />
                </el-select>
                <el-button type="primary" @click="getData">搜索</el-button>
                <el-button @click="resetSearch">重置</el-button>
            </div>
        </div>

        <!-- 列表区域 -->
        <div class="flex-1 overflow-auto p-6">
            <el-table :data="data" v-loading="loading" style="width: 100%" border>
                <el-table-column v-for="item in cols" :key="item.prop" :prop="item.prop" :label="item.label"
                    show-overflow-tooltip />
                <el-table-column prop="created_at" label="创建时间" align="center">
                    <template #default="{ row }">
                        {{ formatTime(row.syllabus.created_at) }}
                    </template>
                </el-table-column>
                <el-table-column prop="status" label="状态" width="120" align="center">
                    <template #default="{ row }">
                        <el-tag :type="getStatusTagType(row.syllabus.status)" effect="light" class="!rounded-button">
                            {{ getStatusLabel(row.syllabus.status) }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="操作" width="190" align="center">
                    <template #default="{ row }">
                        <div class="flex justify-center">
                            <el-button size="small" v-if="checkEditStatus(row.syllabus)"
                                class="!rounded-button whitespace-nowrap" @click="handleEdit(row.syllabus)">
                                编辑
                            </el-button>
                            <el-button size="small" v-if="checkEditStatus(row.syllabus)" type="danger"
                                class="!rounded-button whitespace-nowrap" @click="handleDelete(row.syllabus)">
                                删除
                            </el-button>
                            <ApproveButton approve-code="syllabus" :data-id="row.syllabus.id"
                                :data-title="row.syllabus.name"
                                v-if="checkEditStatus(row.syllabus) || row.syllabus.status === 'revision'"
                                @success="getData" />
                            <el-button size="small" v-if="!checkEditStatus(row.syllabus)"
                                class="!rounded-button whitespace-nowrap" @click="handleEdit(row.syllabus, 'view')">
                                查看
                            </el-button>
                        </div>
                    </template>
                </el-table-column>
                <!-- 修订 -->
                <el-table-column label="修订" width="190" align="center">
                    <template #default="{ row }">
                        <RevisionActions :approve-code="approveCode" :data="row.syllabus" :rev="row.rev"
                            :show-create-button="row.syllabus.status === 'published' && !row.rev"
                            @create="showReviseCreateDialog(row.syllabus)" @edit="handleReviseEdit(row.rev)"
                            @delete="handleReviseDelete(row.rev)" @view="handleReviseView(row.rev)"
                            @refresh="getData" />
                    </template>
                </el-table-column>
            </el-table>
        </div>

        <!-- 分页区域 -->
        <div class="shrink-0 flex justify-center mt-4 pb-2">
            <el-pagination background v-model:current-page="page" v-model:page-size="pageSize" :total="total"
                layout=" prev, pager, next" @current-change="handleCurrentChange" />
        </div>

        <!-- 新建/编辑弹窗 -->
        <SyllabusDialogForm v-model="dialogVisible" :action-type="actionType" :dialog-title="dialogTitle"
            :row="selectedRow" @success="getData" />

        <!-- 修订列表 -->
        <el-drawer title="大纲修订列表" v-model="reviseListDrawerVisible" size="90%" body-class="my-drawer">
            <RevisonList :approve-code="approveCode" />
        </el-drawer>
    </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import { successMsg, errMsg } from "@/utils/msg";
import { ElMessageBox } from 'element-plus';
import { deleteDraft, getSyllabusList } from '@/api/syllabus';
import { deleteRevision } from '@/api/revision'
import { statusOptions, getStatusLabel, getStatusTagType } from '@/api/status'
const dialogVisible = ref(false)
const actionType = ref('create')
const dialogTitle = ref('新建教学大纲')
const selectedRow = ref(null) // 用于传递 row
const approveCode = ref('syllabus')
// 搜索参数
const searchParams = ref({
    name: '',
    status: ''
});
const resetSearch = () => {
    searchParams.value = {
        name: '',
        status: ''
    }
    getData()
}

// 表格数据
const loading = ref(false)
const data: any = ref([])

const cols = [
    { prop: 'syllabus.id', label: '序号' },
    { prop: 'syllabus.name', label: '大纲名称' },
    //{ prop: 'syllabus.goals', label: '教学目标' },
    { prop: 'syllabus.description', label: '大纲描述' },
]
const formatTime = (time: any) => {
    return new Date(time * 1000).toLocaleString()
}
const page = ref(1)
const pageSize = ref(10)
const total = ref(0)

const getData = async () => {
    loading.value = true
    const params = {
        page: page.value,
        //user_id: userStore.userInfo.id,
        ...(searchParams.value.name && { name: searchParams.value.name }),
        ...(searchParams.value.status && { status: searchParams.value.status })
    }
    const res = await getSyllabusList(params)
    loading.value = false
    //console.log(res, 'res----s')
    data.value = res.list || []
    total.value = res.total || 0
}

onMounted(() => {
    getData()
})

const checkEditStatus = (row: any) => {
    return row.status === 'draft' || row.status === 'rejected'
}

const handleEdit = async (row: any, action?: string) => {
    if (action == 'view') {
        dialogTitle.value = '查看教学大纲';
        actionType.value = 'view'
    } else {
        actionType.value = 'edit'
        dialogTitle.value = '编辑教学大纲';
    }
    selectedRow.value = row
    dialogVisible.value = true
};

const handleDelete = async (row: any) => {
    ElMessageBox.confirm(`确定要删除${row.name}吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
    }).then(async () => {
        const res = await deleteDraft(row.id)
        if (res.success) {
            successMsg(res.message)
            getData()
        } else {
            errMsg(res.message)
        }
    })
};
const reviseListDrawerVisible = ref(false)


const handleCurrentChange = (val: number) => {
    page.value = val;
    getData()
};


const showCreateDialog = () => {
    dialogTitle.value = '新建教学大纲';
    actionType.value = 'create';
    dialogVisible.value = true;
};

// 修订
const showReviseCreateDialog = async (row: any) => {
    dialogTitle.value = '创建修订';
    actionType.value = 'revise-create';
    selectedRow.value = row
    dialogVisible.value = true
};

const handleReviseEdit = async (row: any) => {
    dialogTitle.value = '编辑修订';
    actionType.value = 'revise-edit';
    selectedRow.value = row
    dialogVisible.value = true
};
const handleReviseView = async (row: any) => {
    dialogTitle.value = '查看修订';
    actionType.value = 'revise-view';
    selectedRow.value = row
    dialogVisible.value = true
}
const handleReviseDelete = async (row: any) => {
    ElMessageBox.confirm(`确定要删除修订吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
    }).then(async () => {
        const res = await deleteRevision(row.id)
        getData()
        successMsg('删除成功')
        if (!res.success) {
            errMsg(res.message)
        }
    })
}

</script>

<style scoped>
:deep(.el-table .cell) {
    white-space: nowrap;
}

:deep(.el-table th.el-table__cell) {
    background-color: #f8f8f9;
}

:deep(.el-textarea__inner) {
    font-family: inherit;
}

:deep(.el-drawer__body) {
    display: flex;
    overflow: hidden;
}
</style>
