<!-- src/views/assignment/index.vue -->
<template>
  <div class="flex-1 flex flex-col bg-gray-50 overflow-auto">
    <!-- Search Section -->
    <div class="p-6 border-b border-gray-200">
      <div class="mb-4 flex justify-start items-center">
        <el-button type="primary" @click="addAssignment">新建作业</el-button>
        <el-button type="success" class="!rounded-button whitespace-nowrap ml-auto"
          @click="reviseListDrawerVisible = true">
          <el-icon class="mr-1">
            <View />
          </el-icon>
          修订列表
        </el-button>
      </div>
      <div class="grid grid-cols-4 gap-4 flex-1">
        <!-- Assignment ID/Code/Name -->
        <div>
          <el-input v-model="query.name" placeholder="请输入作业名称" clearable class="w-full" />
        </div>

        <!-- Status -->
        <div>
          <el-select v-model="query.status" placeholder="请选择状态" clearable class="w-full">
            <el-option v-for="status in statusOptions" :key="status.value" :label="status.label"
                        :value="status.value" />
          </el-select>
        </div>
        <div>
          <el-button type="primary" class="mr-2" @click="getData">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </div>
      </div>
    </div>

    <!-- Assignment Table -->
    <div class="flex-1 overflow-auto p-6">
      <el-table :data="tableData" style="width: 100%" border v-loading="loading">
        <el-table-column align="center" v-for="col in cols" :key="col.prop" :prop="col.prop" :label="col.label"
          show-overflow-tooltip />
        <el-table-column prop="status" align="center" label="状态" width="100">
          <template #default="{ row }">
            <!-- <el-tag :type="statusTagMap[row.assignment.status]">{{ statusMap[row.assignment.status] }}</el-tag> -->
            <el-tag :type="getStatusTagType(row.status)" effect="light" class="!rounded-button">
              {{ getStatusLabel(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="280" align="center">
          <template #default="{ row }">
            <div class="flex justify-start">
              <el-button size="small"
                v-if="row.status != 'draft' && row.status != 'rejected'"
                @click="viewAssignment(row)">
                查看
              </el-button>
              <el-button size="small" v-if="row.status == 'draft' || row.status == 'rejected'"
                type="primary" @click="updateAssignmentData(row)">
                编辑
              </el-button>
              <el-button size="small" v-if="row.status == 'draft' || row.status == 'rejected'"
                type="danger" @click="deleteAssignmentData(row)">
                删除
              </el-button>
              <ApproveButton approve-code="assignment" :data-id="row.id" :data-title="row.name"
                v-if="row.status == 'draft' || row.status == 'rejected'" @success="getData" />
            </div>
          </template>
        </el-table-column>
        <!-- 修订 -->
        <el-table-column label="修订" width="190" align="center">
          <template #default="{ row }">
            <RevisionActions :approve-code="approveCode" :data="row" :rev="row.rev"
              :show-create-button="row.status === 'published' && !row.rev"
              @create="showReviseCreateDialog(row)" @edit="handleReviseEdit(row.rev)"
              @delete="handleReviseDelete(row.rev)" @view="handleReviseView(row.rev)" @refresh="getData" />
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- Pagination -->
    <div class="shrink-0 flex justify-center mt-4">
      <el-pagination v-model:current-page="page" v-model:page-size="page_size" :total="total"
        layout=" prev, pager, next" @current-change="handleCurrentChange" />
    </div>

    <!-- Assignment Form Dialog -->
    <el-drawer v-model="dialogVisible" :title="dialogTitle" size="100%">
      <AssignmentForm ref="assignmentFormRef" :mode="formMode" :assignment-data="currentAssignmentData"
        @success="handleFormSuccess" @cancel="handleFormCancel" :approve-code="approveCode" />
    </el-drawer>
    <!-- 修订列表 -->
    <el-drawer title="作业修订列表" v-model="reviseListDrawerVisible" size="90%" body-class="my-drawer">
      <RevisonList :approve-code="approveCode" />
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { getAssignmentsList, getAssignmentDetail, deleteAssignment } from '@/api/assignment';
import { confirmMsg, errMsg, successMsg } from '@/utils/msg';
import { statusOptions, getStatusLabel, getStatusTagType } from '@/api/status';
import { ElMessageBox } from 'element-plus';
import { deleteRevision } from '@/api/revision';

// 表格相关
const page = ref(1);
const page_size = ref(10);
const total = ref(0);
const tableData = ref([]);
const loading = ref(false);


const query = ref({
  name: '',
  status: ''
});

const cols = [
  { prop: 'id', label: '编号' },
  { prop: 'name', label: '作业名称' },
  { prop: 'course_name', label: '课程' },
  { prop: 'chapter_name', label: '章节' },
];

// 表单相关
const dialogVisible = ref(false);
const currentAssignmentData = ref({});
const assignmentFormRef = ref();
const approveCode = 'assignment';
const formMode = ref('create')
const dialogTitle = ref('新增作业')
const reviseListDrawerVisible = ref(false);
// 分页处理
const handleCurrentChange = (val: number) => {
  page.value = val;
  getData();
};

// 获取数据
const getData = async () => {
  loading.value = true;
  const params = {
    page: page.value,
    ...(query.value.name && { name: query.value.name }),
    ...(query.value.status && { status: query.value.status }),
  };

  try {
    const res = await getAssignmentsList(params);
    tableData.value = res.list || [];
    total.value = res.total || 0;
  } finally {
    loading.value = false;
  }
};

// 搜索重置
const resetSearch = () => {
  query.value = {
    name: '',
    status: ''
  };
  getData();
};

// 新建作业
const addAssignment = () => {
  formMode.value = 'create';
  dialogTitle.value = '新建作业';
  currentAssignmentData.value = {};
  dialogVisible.value = true;
};

// 查看作业
const viewAssignment = async (row: any) => {
  formMode.value = 'view';
  dialogTitle.value = '作业详情';
  const res = await getAssignmentDetail(row.id);
  currentAssignmentData.value = res;
  dialogVisible.value = true;
};

// 编辑作业
const updateAssignmentData = async (row: any) => {
  formMode.value = 'edit';
  dialogTitle.value = '编辑作业';
  const res = await getAssignmentDetail(row.id);
  currentAssignmentData.value = res;
  dialogVisible.value = true;
};

// 删除作业
const deleteAssignmentData = async (row: any) => {
  confirmMsg('确定要删除吗', '提示', async (action) => {
    if (action) {
      try {
        const res = await deleteAssignment(row.id);
        successMsg(res.message);
        getData();
      } catch (error) {
        console.error(error);
      }
    }
  });
};

// 表单成功处理
const handleFormSuccess = () => {
  dialogVisible.value = false;
  getData();
};

// 表单取消处理
const handleFormCancel = () => {
  dialogVisible.value = false;
};
// 修订相关方法
const showReviseCreateDialog = async (row: any) => {
  dialogTitle.value = '创建修订';
  formMode.value = 'revise-create';
  const res = await getAssignmentDetail(row.id);
  currentAssignmentData.value = res;
  dialogVisible.value = true
}

const handleReviseEdit = async (row: any) => {
  dialogTitle.value = '编辑修订';
  formMode.value = 'revise-edit';
  currentAssignmentData.value = row
  dialogVisible.value = true
}

const handleReviseView = async (row: any) => {
  dialogTitle.value = '查看修订';
  formMode.value = 'revise-view';
  currentAssignmentData.value = row
  dialogVisible.value = true
}

const handleReviseDelete = async (row: any) => {
  ElMessageBox.confirm(`确定要删除修订吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    const res = await deleteRevision(row.id)
    getData()
    successMsg('删除成功')
    if (!res.success) {
      errMsg(res.message)
    }
  })
}
// 组件挂载
onMounted(() => {
  getData();
});
</script>