<template>
    <div class="flex-1 flex flex-col bg-gray-50 overflow-auto">
        <!-- 列表区域 -->
        <div class="flex-1 overflow-auto p-6">
            <el-table :data="tableData" v-loading="loading" style="width: 100%" border>
                <el-table-column align="center" v-for="item in cols" :key="item.prop" :prop="item.prop"
                    :label="item.label" show-overflow-tooltip />
                <el-table-column prop="created_at" label="创建时间">
                    <template #default="{ row }">
                        {{ formatDate(row.created_at) }}
                    </template>
                </el-table-column>
                <el-table-column label="操作" align="center" width="240">
                    <template #default="{ row }">
                        <div class="flex justify-center">
                            <el-button plain @click="view(row)">查看</el-button>
                            <el-button type="primary" plain @click="review(row.id)">通过</el-button>
                            <el-button type="danger" plain @click="reject(row.id)">驳回</el-button>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
        </div>

        <!-- 分页区域 -->
        <div class="shrink-0 flex justify-center mt-4 pb-2">
            <el-pagination background v-model:current-page="page" v-model:page-size="pageSize" :total="total"
                layout=" prev, pager, next" @current-change="handleCurrentChange" />
        </div>

        <el-dialog v-model="showCreateDialog" title="查看详情" width="800px">
            <el-form ref="courseFormRef" :model="courseForm" :rules="courseRules" label-width="120px"
                label-position="right" disabled>
                <el-form-item label="关联教学计划" prop="course.plan_id">
                    <my-select v-model="courseForm.course.plan_id" :func="getTeachingPlanList" labelKey="name"
                        valueKey="id" placeholder="请选择关联的教学计划" searchKey="name" :extraParams="{ status: 'published' }"
                        :initData="initPlan" />
                </el-form-item>
                <el-form-item label="关联专业" prop="course.majors_id">
                    <el-tree-select check-strictly :render-after-expand="false" v-model="courseForm.course.majors_id"
                        placeholder="请选择关联专业" :data="majors" :props="props" />
                </el-form-item>
                <el-form-item label="课程名称" prop="course.name">
                    <el-input v-model="courseForm.course.name" placeholder="请输入课程名称" />
                </el-form-item>
                <el-form-item label="课程描述">
                    <el-input v-model="courseForm.course.description" type="textarea" :rows="3" placeholder="请输入课程描述" />
                </el-form-item>
                <el-form-item label="总课时" prop="course.total_hours">
                    <el-input v-model.number="courseForm.course.total_hours" placeholder="请输入总课时" />
                </el-form-item>
                <el-form-item label="关联教师">
                    <my-select v-model="courseForm.teacher_ids" multiple placeholder="请选择关联教师" :func="getUserList"
                        labelKey="username" valueKey="id" :extraParams="{ sys_code: 'teacher' }"
                        :initData="initTeacher" />
                </el-form-item>
            </el-form>
        </el-dialog>
    </div>
</template>

<script setup lang="ts">
import { getCourseDetail, getCourseReviewingList, getMajorList, getUserList, rejectCourse, reviewCourse } from '@/api/course';
import { getTeachingPlanList } from '@/api/teaching-plan';
import { confirmMsg, successMsg } from '@/utils/msg';
import { onMounted, ref, watch } from 'vue';

const page = ref(1)
const pageSize = ref(10)
const total = ref(0)
const handleCurrentChange = (val: number) => {
    page.value = val
    getData()
}
const loading = ref(false)
const tableData = ref([])
const cols = [
    { label: '课程名称', prop: 'name' },
    { label: '课程描述', prop: 'description' }
]

const formatDate = (date: any) => {
    return new Date(date * 1000).toLocaleDateString().replace(/\//g, '-').split('-').map((item: any) => item.padStart(2, '0')).join('-');
};
const getData = async () => {
    loading.value = true

    const res = await getCourseReviewingList({ page: page.value })
    loading.value = false
    tableData.value = res.list || []
    total.value = res.total || 0
}

const reject = (id: any) => {
    confirmMsg('确定要驳回吗？', '提示', async (action) => {
        if (action) {
            const res = await rejectCourse(id)
            successMsg(res.message)
            getData()
        }
    })
}
const review = (id: any) => {
    confirmMsg('确定要通过吗？', '提示', async (action) => {
        if (action) {
            const res = await reviewCourse(id)
            successMsg(res.message)
            getData()
        }
    })
}
// dialog
const showCreateDialog = ref(false);
watch(showCreateDialog, (val) => {
    if (!val) {
        courseForm.value = initForm();
    }
})
const initForm = () => {
    return {
        course: {
            plan_id: '',               // 必填，关联的教学计划ID
            majors_id: '',             // 必填，专业ID
            name: "",         // 必填，课程名称
            description: "",  // 可选，课程描述
            total_hours: ''           // 必填，总课时数
        },
        teacher_ids: []         // 可选，多个老师ID
    }
}
const courseForm = ref(initForm())
const courseRules = {
    course: {
        plan_id: [{ required: true, message: '请选择教学计划', trigger: 'change' }],
        majors_id: [{ required: true, message: '请选择专业', trigger: 'change' }],
        name: [{ required: true, message: '请输入课程名称', trigger: 'blur' }],
        total_hours: [{ required: true, message: '请输入总课时数', trigger: 'blur' }]
    }
}
const majors = ref([])
const getMajors = async () => {
    const res = await getMajorList()
    majors.value = res || []
}
const props = {
    label: 'name',
    value: 'id'
}
// 回显数据
const initPlan: any = ref([])
const initTeacher: any = ref([])
const setCourseForm = (course: any) => {
    initPlan.value = [
        {
            id: course.plan_id,
            name: course.plan_name
        }
    ]
    initTeacher.value = course.teacher_list
    return {
        course: {
            id: course.id,
            plan_id: course.plan_id,
            majors_id: course.major_id,
            name: course.name,
            description: course.description,
            total_hours: course.total_hours,
        },
        teacher_ids: course.teacher_list?.map((item: any) => item.id) || [],
    }

}
const view = async (plan: any) => {
    const course = await getCourseDetail(plan.id)
    courseForm.value = setCourseForm(course)
    showCreateDialog.value = true;
};



onMounted(() => {
    getData()
    getMajors()
})
</script>