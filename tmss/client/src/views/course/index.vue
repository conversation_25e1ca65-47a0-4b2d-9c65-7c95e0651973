<template>
    <div class="flex-1 flex flex-col bg-gray-50 overflow-auto">
        <!-- Search Section -->
        <div class=" p-6 border-b border-gray-200">
            <div class="flex justify-start mb-4">
                <el-button type="primary" @click="addCourse">新建课程</el-button>
                <el-button type="success" class="mb-4 !rounded-button whitespace-nowrap ml-auto"
                    @click="reviseListDrawerVisible = true">
                    <el-icon class="mr-1">
                        <View />
                    </el-icon>
                    修订列表
                </el-button>
            </div>
            <div class="grid grid-cols-4 gap-4 flex-1">
                <!-- Course ID/Code/Name -->
                <div>
                    <el-input v-model="query.name" placeholder="请输入课程名称" clearable class="w-full" />
                </div>

                <!-- Status -->
                <div>
                    <el-select v-model="query.status" placeholder="请选择状态">
                        <el-option v-for="status in statusOptions" :key="status.value" :label="status.label"
                        :value="status.value" />
                    </el-select>
                </div>
                <div>
                    <el-button type="primary" class="mr-2" @click="getData">搜索</el-button>
                    <el-button @click="resetSearch">重置</el-button>
                </div>
            </div>


        </div>


        <!-- Course Table -->
        <div class="flex-1 overflow-auto p-6">
            <el-table :data="tableData" style="width: 100%" border v-loading="loading">
                <el-table-column align="center" v-for="col in cols" :key="col.prop" :prop="col.prop" :label="col.label"
                    show-overflow-tooltip />
                <el-table-column prop="status" label="状态" width="120" align="center">
                    <template #default="{ row }">
                        <el-tag :type="getStatusTagType(row.course.status)" effect="light" class="!rounded-button">
                            {{ getStatusLabel(row.course.status) }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="操作" width="190" align="center">
                    <template #default="{ row }">
                        <div class="flex justify-center">
                            <el-button size="small" v-if="row.course.status == 'published' || row.course.status == 'reviewing' || row.course.status == 'deleted'"
                                @click="viewCourse(row.course)">查看</el-button>
                            <el-button size="small" v-if="row.course.status == 'draft' || row.course.status == 'rejected'"
                                type="primary" @click="editCourse(row.course)">编辑</el-button>
                            <el-button size="small" v-if="row.course.status == 'draft' || row.course.status == 'rejected'"
                                type="danger" @click="deleteCourse(row.course)">删除</el-button>
                            <ApproveButton approve-code="courses" :data-id="row.course.id" :data-title="row.course.name"
                                v-if="row.course.status == 'draft' || row.course.status == 'rejected'" @success="getData" />
                        </div>
                    </template>
                </el-table-column>
                 <!-- 修订 -->
                 <el-table-column label="修订" width="190" align="center">
                    <template #default="{ row }">
                        <RevisionActions :approve-code="approveCode" :data="row.course" :rev="row.rev"
                            :show-create-button="row.course.status === 'published' && !row.rev"
                            @create="showReviseCreateDialog(row.course)" @edit="handleReviseEdit(row.rev)"
                            @delete="handleReviseDelete(row.rev)" @view="handleReviseView(row.rev)"
                            @refresh="getData" />
                    </template>
                </el-table-column>
            </el-table>
        </div>

        <!-- Pagination -->
        <div class="shrink-0 flex justify-center mt-4">
            <el-pagination v-model:current-page="page" v-model:page-size="page_size" :total="total"
                layout=" prev, pager, next" @current-change="handleCurrentChange" />
        </div>
        <!-- Course Form Dialog -->
        <el-dialog v-model="dialogVisible" :title="dialogTitle" width="800px" :close-on-click-modal="false"
            :destroy-on-close="true">
            <el-form ref="courseFormRef" :model="courseForm" :rules="courseRules" label-width="120px"
                label-position="right" :disabled="actionType === 'view' || actionType === 'revise-view'">
                <el-form-item label="修订说明"
                    v-if="actionType === 'revise-create' || actionType === 'revise-edit' || actionType === 'revise-view'">
                    <el-input v-model="revNotes" type="textarea" :rows="2" placeholder="请输入修订说明" />
                </el-form-item>
                <el-form-item label="关联教学计划" prop="course.plan_id">
                    <my-select v-model="courseForm.course.plan_id" :func="getTeachingPlanList" labelKey="plan.name"
                        valueKey="plan.id" placeholder="请选择关联的教学计划" searchKey="name" :extraParams="{ status: 'published' }"
                        :initData="initPlan" />
                </el-form-item>
                <el-form-item label="关联专业" prop="course.major_id">
                    <el-tree-select check-strictly :render-after-expand="false" v-model="courseForm.course.major_id"
                        placeholder="请选择关联专业" :data="majors" :props="props" />
                </el-form-item>
                <el-form-item label="课程名称" prop="course.name">
                    <el-input v-model="courseForm.course.name" placeholder="请输入课程名称" />
                </el-form-item>
                <el-form-item label="课程编号" prop="course.course_code">
                    <el-input v-model="courseForm.course.course_code" placeholder="请输入课程编号" />
                </el-form-item>
                <el-form-item label="课程描述">
                    <el-input v-model="courseForm.course.description" type="textarea" :rows="3" placeholder="请输入课程描述" />
                </el-form-item>
                <el-form-item label="总课时" prop="course.total_hours">
                    <el-input v-model.number="courseForm.course.total_hours" placeholder="请输入总课时" />
                </el-form-item>
                <el-form-item label="关联章节" prop="course.chapter_id">
                    <el-tree-select v-model="courseForm.course.chapter_id" :data="chapterOptions" check-strictly
                        :render-after-expand="false" :props="props" placeholder="请选择关联章节" />
                </el-form-item>
                <el-form-item label="关联教师">
                    <my-select v-model="courseForm.teacher_ids" multiple placeholder="请选择关联教师" :func="getUserList"
                        labelKey="username" valueKey="id" :extraParams="{ sys_code: 'teacher' }"
                        :initData="initTeacher" />
                </el-form-item>
            </el-form>
            <template #footer v-if="actionType != 'view' && actionType != 'revise-view'">
                <el-button @click="dialogVisible = false">取消</el-button>
                <el-button type="primary" @click="submitForm">保存</el-button>
            </template>
        </el-dialog>
        <!-- 修订列表 -->
        <el-drawer title="课程修订列表" v-model="reviseListDrawerVisible" size="90%" body-class="my-drawer">
            <RevisonList :approve-code="approveCode" />
        </el-drawer>
    </div>
</template>

<script setup lang="ts">
import { createCourseDraft, deleteCourseDraft, editCourseDraft, getCourseDetail, getCourseList, getMajorList, getUserList } from '@/api/course';
import { getTeachingPlanList } from '@/api/teaching-plan';
import { chapterList } from '@/api/chapter';
import { confirmMsg, errMsg, successMsg } from '@/utils/msg';
import { onMounted, ref, watch } from 'vue';
import { createRevision, editRevision, getRevisionDetail, deleteRevision } from '@/api/revision'
import { statusOptions, getStatusLabel, getStatusTagType } from '@/api/status'
import { ElMessage, ElMessageBox } from 'element-plus';
const reviseListDrawerVisible = ref(false)
const approveCode = ref('courses');
const revNotes = ref(''); // 修订说明
const revId = ref(0); // 当前修订 ID
const dialogTitle = ref('创建课程')
const actionType = ref('create')
// table
const page = ref(1)
const page_size = ref(10)
const total = ref(0)
const handleCurrentChange = (val: any) => {
    page.value = val
    getData()
}
const tableData: any = ref([])
const loading = ref(false)

const query = ref({
    name: '',
    status: ''
})
const resetSearch = () => {
    query.value = {
        name: '',
        status: ''
    }
    getData()
}
const cols = [
    { prop: 'course.name', label: '课程名称' },
    { prop: 'course.course_code', label: '课程编号' },
    { prop: 'course.description', label: '课程描述' },
]
const getData = async () => {
    loading.value = true
    const params = {
        page: page.value,
        ...(query.value.name && { name: query.value.name }),
        ...(query.value.status && { status: query.value.status }),
    }
    const res = await getCourseList(params)
    console.log(res, 'res')
    loading.value = false
    tableData.value = res.list || []
    total.value = res.total || 0
    const chapters = await chapterList()
    if (chapters.success) {
        chapterOptions.value = chapters.data
    }
}

// dialog
const dialogVisible = ref(false)
// watch(dialogVisible, (val) => {
//     if (!val) {
//         courseFormRef.value?.resetFields()
//         courseForm.value = initForm()
//         isEdit.value = false
//         isView.value = false
//     }
// })
// const isEdit = ref(false)

const initForm = () => {
    return {
        course: {
            plan_id: '',               // 必填，关联的教学计划ID
            major_id: '',             // 必填，专业ID
            chapter_id: '',           // 必填，关联的章节ID
            course_code: '',          // 必填，课程编码
            name: "",         // 必填，课程名称
            description: "",  // 可选，课程描述
            total_hours: ''           // 必填，总课时数
        },
        teacher_ids: []         // 可选，多个老师ID
    }
}
const courseForm: any = ref(initForm())
const courseRules = {
    course: {
        plan_id: [{ required: true, message: '请选择教学计划', trigger: 'change' }],
        major_id: [{ required: true, message: '请选择专业', trigger: 'change' }],
        cousr_code: [{ required: true, message: '请输入课程编码', trigger: 'blur' }],
        chapter_id: [{ required: true, message: '请选择章节', trigger: 'blur' }],
        name: [{ required: true, message: '请输入课程名称', trigger: 'blur' }],
        total_hours: [{ required: true, message: '请输入总课时数', trigger: 'blur' }]
    }
}
const majors = ref([])
const chapterOptions: any = ref([]);
const getMajors = async () => {
    const res = await getMajorList()
    majors.value = res || []
    console.log(majors.value, 'majors')
}
const props = {
    label: 'name',
    value: 'id'
}
const courseFormRef = ref()
const submitForm = () => {
    courseFormRef.value.validate(async (valid: boolean) => {
        if (valid) {
            //console.log(courseForm.value, 'courseForm')
            //const res = isEdit.value ? await editCourseDraft(courseForm.value) : await createCourseDraft(courseForm.value)
            let res: any;
            if (actionType.value == 'create') {
                res = await createCourseDraft(courseForm.value)
            }
            else if (actionType.value == 'edit') {
                res = await editCourseDraft(courseForm.value)
            }
            else if (actionType.value == 'revise-create' || actionType.value == 'revise-edit') {
                if (!revNotes.value) return errMsg('请输入修订说明')
                const changes = {
                    formData: courseForm.value,
                    initTeacher: initTeacher.value,
                    initPlan: initPlan.value,
                }
                const saveData: any = {
                    module_key: approveCode.value,
                    original_id: courseForm.value.course.id,
                    notes: revNotes.value,
                    changes: JSON.stringify(changes),
                }
                if (actionType.value == 'revise-edit') {
                    saveData.id = revId.value
                    res = await editRevision(saveData)
                } else {
                    res = await createRevision(saveData)
                }

            }
            successMsg(res.message)
            dialogVisible.value = false
            getData()
        } else {
            return false
        }
    })
}
// 回显数据
const initPlan: any = ref([])
const initTeacher: any = ref([])
const setCourseForm = (course: any) => {
    console.log(course, 'course')
    initPlan.value = [
        {
            id: course.plan_id,
            name: course.plan_name
        }
    ]
    initTeacher.value = course.teacher_list
    return {
        course: {
            id: course.id,
            plan_id: course.plan_id,
            major_id: course.major_id,
            chapter_id: course.chapter_id,
            course_code: course.course_code,
            name: course.name,
            description: course.description,
            total_hours: course.total_hours,
        },
        teacher_ids: course.teacher_list?.map((item: any) => item.id) || [],
    }

}
const addCourse = () => {
    courseFormRef.value?.resetFields()
    courseForm.value = initForm()
    actionType.value = 'create'
    dialogVisible.value = true
    dialogTitle.value = '创建课程';
}

const viewCourse = async (row: any) => {
    actionType.value = 'view'
    const course = await getCourseDetail(row.id)
    courseForm.value = setCourseForm(course)
    dialogVisible.value = true
    dialogTitle.value = '查看课程';
}
const editCourse = async (row: any) => {
    actionType.value = 'edit'
    const course = await getCourseDetail(row.id)
    courseForm.value = setCourseForm(course)
    dialogVisible.value = true
    dialogTitle.value = '编辑课程';
}

const deleteCourse = async (row: any) => {
    confirmMsg('确定要删除吗', '提示', async (action) => {
        if (action) {
            const res = await deleteCourseDraft(row.id)
            successMsg(res.message)
            getData()
        }
    })
}
// 初始化修订表单数据
const initReviseData = async (row: any) => {
    console.log(row, 'row')
  revId.value = row.id;
  const res = await getRevisionDetail(row.id);
  revNotes.value = res.notes;

  const changes = JSON.parse(res.changes);
  courseForm.value = changes.formData; // 初始化表单数据
  initPlan.value = changes.initPlan;
  initTeacher.value = changes.initTeacher;
};

// 创建修订
const showReviseCreateDialog = async (row: any) => {
  dialogTitle.value = '创建修订';
  actionType.value = 'revise-create';
  revNotes.value = '';
  revId.value = 0;

  // 初始化表单数据
  courseForm.value = setCourseForm(row);
  dialogVisible.value = true;
};

// 编辑修订
const handleReviseEdit = async (row: any) => {
  dialogTitle.value = '编辑修订';
  actionType.value = 'revise-edit';
  await initReviseData(row);
  dialogVisible.value = true;
};

// 查看修订
const handleReviseView = async (row: any) => {
  dialogTitle.value = '查看修订';
  actionType.value = 'revise-view';
  await initReviseData(row);
  dialogVisible.value = true;
};

// 删除修订
const handleReviseDelete = async (row: any) => {
  ElMessageBox.confirm('确定要删除修订吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      const res = await deleteRevision(row.id);
      if (res.success) {
        ElMessage.success('删除成功');
        getData();
      } else {
        ElMessage.error(res.message || '删除失败');
      }
    })
    .catch(() => {});
};
onMounted(() => {
    getData()
    getMajors()
})
</script>

<style scoped>
.el-table {
    margin-top: 20px;
}

.el-form-item {
    margin-bottom: 20px;
}

.el-dialog__body {
    padding: 20px;
}

.el-select {
    width: 100%;
}

.el-input__inner {
    height: 36px;
}
</style>
