<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { get, post } from '@/utils/request'
import { sysList } from '@/api/config'
import {
  Setting,
  FolderOpened,
  Upload,
  Timer
} from '@element-plus/icons-vue'

const activeMenu = ref('database')

const menuItems = [
  { key: 'database', label: '系统数据库管理', icon: Setting },
  { key: 'logdatabase', label: '日志数据库管理', icon: FolderOpened },
  { key: 'coursePath', label: '课件路径设置', icon: FolderOpened },
  { key: 'upload', label: '资料上传下载设置', icon: Upload },
  { key: 'backup', label: '备份策略', icon: Timer }
]
const fileFormats = [
  { value: 'doc', label: 'Word文档' },
  { value: 'pdf', label: 'PDF文件' },
  { value: 'jpg', label: 'JPG图片' },
  { value: 'png', label: 'PNG图片' },
  { value: 'zip', label: 'ZIP压缩包' }
]
const frequencyList = [
  { value: 'daily', label: '每天' },
  { value: 'weekly', label: '每周' },
  { value: 'monthly', label: '每月' }
]

// 表单数据
const databaseForm = ref({
  host: '',
  port: '',
  user: '',
  dbname: '',
  password: ''
})
const logDatabaseForm = ref({
  enable: true,
  uri: '',
  database: ''
})
const coursePathForm = ref({
  path: '/data/coursewares'
})

const uploadForm = ref({
  path: '/data/uploads',
  formats: ['doc', 'pdf'],
  maxSize: 100,
  permission: ['registered']
})

const backupForm = ref({
  frequency: 'daily',
  time: new Date(2023, 1, 1, 2, 0),
  path: '/data/backups',
  retention: 30
})

// 获取系统配置
const loadSystemConfig = async () => {
  const res = await get('/admin/system/config')
  if (res.success) {
    const data = res.data
    coursePathForm.value = {
      path: data.course?.path || '/data/coursewares'
    }

    uploadForm.value = {
      path: data.upload?.path || '/data/uploads',
      formats: data.upload?.formats || ['doc', 'pdf'],
      maxSize: data.upload?.maxSize || 100,
      permission: data.upload?.permission || ['registered']
    }

    backupForm.value = {
      frequency: data.backup?.frequency || 'daily',
      time: parseTimeString(data.backup?.time || '02:00'),
      path: data.backup?.path || '/data/backups',
      retention: data.backup?.retention || 30
    }
  } else {
    ElMessage.error('加载系统配置失败')
  }
}

// 解析 HH:mm 字符串为 Date 对象
const parseTimeString = (timeStr: string): Date => {
  if (!timeStr) return new Date()
  const [hours, minutes] = timeStr.split(':').map(Number)
  const date = new Date()
  date.setHours(hours)
  date.setMinutes(minutes)
  return date
}

// 格式化 Date 为 HH:mm
const formatTime = (date: Date): string => {
  const h = String(date.getHours()).padStart(2, '0')
  const m = String(date.getMinutes()).padStart(2, '0')
  return `${h}:${m}`
}

// 获取数据库配置
const loadDatabaseConfig = async () => {
  const res = await get('/admin/database/config')
  if (res.success) {
    const data = res.data
    databaseForm.value = {
      host: data.host || '',
      port: data.port || '',
      user: data.user || '',
      dbname: data.dbname || '',
      password: data.password || ''
    }
  } else {
    ElMessage.error('加载数据库配置失败')
  }
}
// 获取 MongoDB 配置
const loadLogDatabaseConfig = async () => {
  const res = await get('admin/mongodb/config')
  if (res.success) {
    const data = res.data
    logDatabaseForm.value = {
      enable: data.enable,
      uri: data.uri || '',
      database: data.database || ''
    }
  } else {
    ElMessage.error('加载日志数据库配置失败')
  }
}

// 测试 MongoDB 连接
const testLogConnection = async () => {
  const success = await post('admin/mongodb/test', logDatabaseForm.value)
  if (success) {
    ElMessage.success('日志数据库连接测试成功')
  } else {
    ElMessage.error('日志数据库连接测试失败')
  }
}

// 保存日志数据库配置
const saveLogDatabase = async () => {
  const payload = {
    enable: true,
    uri: logDatabaseForm.value.uri,
    database: logDatabaseForm.value.database
  }

  const res = await post('admin/mongodb/config', payload)
  if (res.success) {
    ElMessage.success('日志数据库配置已保存')
  } else {
    ElMessage.error('保存日志数据库配置失败')
  }
}
// 初始化加载
onMounted(() => {
  loadSystemConfig()
  loadDatabaseConfig()
  loadLogDatabaseConfig()
})

// 测试连接
const testConnection = async () => {
  const success = await post('/admin/database/test', databaseForm.value)
  if (success) {
    ElMessage.success('数据库连接测试成功')
  } else {
    ElMessage.error('数据库连接测试失败')
  }
}

// 保存数据库配置
const saveDatabase = async () => {
  const payload = {
    enable: true,
    host: databaseForm.value.host,
    port: databaseForm.value.port,
    user: databaseForm.value.user,
    password: databaseForm.value.password,
    dbname: databaseForm.value.dbname,
    sslmode: 'disable' // 默认值
  }

  const res = await post('/admin/database/config', payload)
  if (res.success) {
    ElMessage.success('数据库配置已保存')
  } else {
    ElMessage.error('保存数据库配置失败')
  }
}

// 保存课件路径配置
const saveCoursePath = async () => {
  const payload = {
    course: coursePathForm.value
  }

  const res = await post('/admin/system/config', payload)
  if (res.success) {
    ElMessage.success('课件路径配置已保存')
  } else {
    ElMessage.error('保存课件路径配置失败')
  }
}

// 保存上传下载配置
const saveUpload = async () => {
  const payload = {
    upload: uploadForm.value
  }

  const res = await post('/admin/system/config', payload)
  if (res.success) {
    ElMessage.success('上传下载配置已保存')
  } else {
    ElMessage.error('保存上传下载配置失败')
  }
}

// 保存备份策略配置
const saveBackup = async () => {
  const payload = {
    backup: {
      ...backupForm.value,
      time: formatTime(backupForm.value.time)
    }
  }

  const res = await post('/admin/system/config', payload)
  if (res.success) {
    ElMessage.success('备份策略已保存')
  } else {
    ElMessage.error('保存备份策略失败')
  }
}

// 立即备份（可选）
const backupNow = () => {
  ElMessage.success('备份任务已启动')
}
</script>

<template>
  <div class="min-h-screen">
    <div class="mx-auto px-4 py-6">
      <div class="flex gap-8">
        <!-- 左侧导航 -->
        <aside class="w-64 flex-shrink-0">
          <div class="bg-white rounded-lg shadow-sm p-4">
            <ul class="space-y-2">
              <li v-for="(item, index) in menuItems" :key="index">
                <button @click="activeMenu = item.key" :class="[
                  'w-full text-left px-4 py-3 rounded-lg !rounded-button whitespace-nowrap',
                  activeMenu === item.key
                    ? 'bg-blue-50 text-blue-600'
                    : 'hover:bg-gray-50 text-gray-600'
                ]">
                  <el-icon class="mr-2">
                    <component :is="item.icon" />
                  </el-icon>
                  {{ item.label }}
                </button>
              </li>
            </ul>
          </div>
        </aside>

        <!-- 右侧内容区 -->
        <div class="flex-1">
          <div class="bg-white rounded-lg shadow-sm p-6">
            <!-- 数据库连接管理 -->
            <div v-if="activeMenu === 'database'" class="space-y-6 w-200">
              <el-form :model="databaseForm" label-width="120px">
                <el-form-item label="数据库地址" required>
                  <el-input v-model="databaseForm.host" placeholder="请输入数据库地址" />
                </el-form-item>
                <el-form-item label="端口号" required>
                  <el-input v-model="databaseForm.port" placeholder="请输入端口号" />
                </el-form-item>
                <el-form-item label="数据库名" required>
                  <el-input v-model="databaseForm.dbname" placeholder="请输入数据库名" />
                </el-form-item>
                <el-form-item label="用户名" required>
                  <el-input v-model="databaseForm.user" placeholder="请输入用户名" />
                </el-form-item>
                <el-form-item label="密码" required>
                  <el-input v-model="databaseForm.password" type="password" placeholder="请输入密码" />
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" class="mr-4 !rounded-button whitespace-nowrap" @click="testConnection">
                    测试连接
                  </el-button>
                  <el-button type="success" class="!rounded-button whitespace-nowrap" @click="saveDatabase">
                    保存配置
                  </el-button>
                </el-form-item>
              </el-form>
            </div>
            <!-- 日志数据库管理 -->
            <div v-if="activeMenu === 'logdatabase'" class="space-y-6 w-200">
              <el-form :model="logDatabaseForm" label-width="120px">
                <el-form-item label="MongoDB URI" required>
                  <el-input v-model="logDatabaseForm.uri" placeholder="****************************:port" />
                </el-form-item>
                <el-form-item label="数据库名" required>
                  <el-input v-model="logDatabaseForm.database" placeholder="请输入数据库名" />
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" class="mr-4 !rounded-button whitespace-nowrap" @click="testLogConnection">
                    测试连接
                  </el-button>
                  <el-button type="success" class="!rounded-button whitespace-nowrap" @click="saveLogDatabase">
                    保存配置
                  </el-button>
                </el-form-item>
              </el-form>
            </div>
            <!-- 课件路径设置 -->
            <div v-if="activeMenu === 'coursePath'" class="space-y-6 w-200">
              <el-form :model="coursePathForm" label-width="120px">
                <el-form-item label="存储路径" required>
                  <el-input v-model="coursePathForm.path" placeholder="/data/coursewares" />
                </el-form-item>
                <el-form-item label="可用空间">
                  <div class="text-gray-600">剩余可用空间：128.5 GB</div>
                </el-form-item>
                <el-form-item>
                  <el-button type="success" class="!rounded-button whitespace-nowrap" @click="saveCoursePath">
                    保存配置
                  </el-button>
                </el-form-item>
              </el-form>
            </div>

            <!-- 资料上传下载设置 -->
            <div v-if="activeMenu === 'upload'" class="space-y-6 w-200">
              <el-form :model="uploadForm" label-width="120px">
                <el-form-item label="存储路径" required>
                  <el-input v-model="uploadForm.path" placeholder="/data/uploads" />
                </el-form-item>
                <el-form-item label="允许的格式" required>
                  <el-checkbox-group v-model="uploadForm.formats">
                    <el-checkbox v-for="item in fileFormats" :key="item.value" :value="item.value">
                      {{ item.label }}
                    </el-checkbox>
                  </el-checkbox-group>
                </el-form-item>
                <el-form-item label="文件大小限制" required>
                  <el-input-number v-model="uploadForm.maxSize" :min="1" :max="1000" class="w-40" />
                  <span class="ml-2">MB</span>
                </el-form-item>
                <el-form-item label="下载权限" required>
                  <el-checkbox-group v-model="uploadForm.permission">
                    <el-checkbox v-for="item in sysList" :key="item.code" :value="item.code">
                      {{ item.name }}
                    </el-checkbox>
                  </el-checkbox-group>
                </el-form-item>
                <el-form-item>
                  <el-button type="success" class="!rounded-button whitespace-nowrap" @click="saveUpload">
                    保存配置
                  </el-button>
                </el-form-item>
              </el-form>
            </div>

            <!-- 备份策略 -->
            <div v-if="activeMenu === 'backup'" class="space-y-6 w-200">
              <el-form :model="backupForm" label-width="120px">
                <el-form-item label="备份频率" required>
                  <el-radio-group v-model="backupForm.frequency">
                    <el-radio v-for="item in frequencyList" :key="item.value" :value="item.value" :label="item.label" />
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="备份时间" required>
                  <el-time-picker v-model="backupForm.time" format="HH:mm" />
                </el-form-item>
                <el-form-item label="备份路径" required>
                  <el-input v-model="backupForm.path" placeholder="/data/backups" />
                </el-form-item>
                <el-form-item label="保留天数" required>
                  <el-input-number v-model="backupForm.retention" :min="1" :max="365" class="w-40" />
                  <span class="ml-2">天</span>
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" class="mr-4 !rounded-button whitespace-nowrap" @click="backupNow">
                    立即备份
                  </el-button>
                  <el-button type="success" class="!rounded-button whitespace-nowrap" @click="saveBackup">
                    保存配置
                  </el-button>
                </el-form-item>
              </el-form>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.el-input-number .el-input__inner {
  text-align: left;
}
</style>