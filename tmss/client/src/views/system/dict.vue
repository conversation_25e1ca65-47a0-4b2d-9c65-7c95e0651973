<template>
  <div class="min-h-screen">
    <div class="mx-auto px-4 py-6">
      <div class="flex gap-8">
        <!-- 左侧分类导航 -->
        <aside class="w-64 flex-shrink-0">
          <div class="bg-white rounded-lg shadow-sm p-4">
            <h3 class="text-lg font-semibold mb-4">字典分类</h3>
            <ul class="space-y-2">
              <li v-for="(category, index) in categories" :key="index">
                <button
                  @click="activeCategory = category.id"
                  :class="[
                    'w-full text-left px-4 py-2 rounded-lg whitespace-nowrap',
                    activeCategory === category.id
                      ? 'bg-blue-50 text-blue-600'
                      : 'hover:bg-gray-50 text-gray-600'
                  ]"
                >
                  {{ category.name }}
                </button>
              </li>
            </ul>

            <el-button type="primary" class="mt-4 w-full !rounded-button" @click="openAddCategoryDialog">
              + 新增分类
            </el-button>
          </div>
        </aside>

        <!-- 右侧内容区 -->
        <div class="flex-1">
          <div class="bg-white rounded-lg shadow-sm p-6">
            <!-- 分类详情 -->
            <div v-if="selectedCategory" class="space-y-6">
              <div class="flex justify-between items-center">
                <h3 class="text-lg font-semibold">{{ selectedCategory.name }}</h3>
                <div>
                  <el-button size="small" @click="openEditCategoryDialog(selectedCategory)" circle>
                    <el-icon><edit /></el-icon>
                  </el-button>
                  <el-button size="small" @click="deleteCategory(selectedCategory.id)" circle>
                    <el-icon><delete /></el-icon>
                  </el-button>
                </div>
              </div>

              <!-- 字典条目表格 -->
              <el-table :data="entries" border>
                <el-table-column prop="name" label="键名"></el-table-column>
                <el-table-column prop="value" label="值"></el-table-column>
                <el-table-column label="操作" width="120">
                  <template #default="{ row }">
                    <el-button size="small" @click="openEditEntryDialog(row)" circle>
                      <el-icon><edit /></el-icon>
                    </el-button>
                    <el-button size="small" @click="deleteEntry(row.name)" circle>
                      <el-icon><delete /></el-icon>
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
              
              <el-button type="success" @click="openAddEntryDialog" class="!rounded-button">
                + 添加条目
              </el-button>
            </div>
            <el-empty v-else description="暂无字典条目" />
          </div>
        </div>
      </div>
    </div>

    <!-- 新增/编辑分类对话框 -->
    <el-dialog v-model="showCategoryDialog" :title="isEditingCategory ? '编辑分类' : '新增分类'" width="30%">
      <el-form :model="categoryForm" label-width="80px">
        <el-form-item label="分类ID" required>
          <el-input v-model="categoryForm.id" :disabled="isEditingCategory" />
        </el-form-item>
        <el-form-item label="分类名称" required>
          <el-input v-model="categoryForm.name" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showCategoryDialog = false">取消</el-button>
        <el-button type="primary" @click="saveCategory">保存</el-button>
      </template>
    </el-dialog>

    <!-- 新增/编辑条目对话框 -->
    <el-dialog v-model="showEntryDialog" :title="isEditingEntry ? '编辑条目' : '新增条目'" width="30%">
      <el-form :model="entryForm" label-width="80px">
        <el-form-item label="键名" required>
          <el-input v-model="entryForm.name" :disabled="isEditingEntry" />
        </el-form-item>
        <el-form-item label="值" required>
          <el-input v-model="entryForm.value" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showEntryDialog = false">取消</el-button>
        <el-button type="primary" @click="saveEntry">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { get, post } from '@/utils/request'
import { Edit, Delete } from '@element-plus/icons-vue'

const categories = ref<any[]>([]) // 初始化为空数组
const entries = ref<any[]>([])    // 同上
const activeCategory = ref<string | null>(null)
const showCategoryDialog = ref(false)
const isEditingCategory = ref(false)
const categoryForm:any = ref({ id: '', name: '' })

const showEntryDialog = ref(false)
const isEditingEntry = ref(false)
const entryForm:any = ref({ name: '', value: '' })

// 获取所有分类
const loadCategories = async () => {
  const res = await get('/admin/dict/category/list')
  if (res.success) {
    categories.value = res.data
  } else {
    ElMessage.error('加载分类失败')
  }
}

// 获取某个分类下的条目
const loadEntriesByCategory = async () => {
  if (!activeCategory.value) return
  const res = await get('/admin/dict/entry/list/by-category', { category_id: activeCategory.value })
  if (res.success) {
    entries.value = res.data
  } else {
    ElMessage.error('加载字典条目失败')
  }
}

// 监听 activeCategory 变化
watch(activeCategory, async () => {
  if (activeCategory.value) {
    await loadEntriesByCategory()
  }
})

// 初始化加载
onMounted(async () => {
  await loadCategories()
  if (categories.value && Array.isArray(categories.value) && categories.value.length > 0) {
    activeCategory.value = categories.value[0].id
  }
})

// 打开新增分类对话框
const openAddCategoryDialog = () => {
  isEditingCategory.value = false
  categoryForm.value = { id: '', name: '' }
  showCategoryDialog.value = true
}

// 打开编辑分类对话框
const openEditCategoryDialog = (category:any) => {
  isEditingCategory.value = true
  categoryForm.value = { ...category }
  showCategoryDialog.value = true
}

// 保存分类
const saveCategory = async () => {
  const url = isEditingCategory.value ? '/admin/dict/category/update' : '/admin/dict/category/create'
  const res = await post(url, categoryForm.value)
  if (res.success) {
    ElMessage.success(isEditingCategory.value ? '更新成功' : '添加成功')
    showCategoryDialog.value = false
    await loadCategories()
    if (!activeCategory.value || !categories.value.some(c => c.id === activeCategory.value)) {
      if (categories.value.length > 0) {
        activeCategory.value = categories.value[0].id
      }
    }
  } else {
    ElMessage.error(res.message || '操作失败')
  }
}

// 删除分类
const deleteCategory = async (id:any) => {
  try {
    await ElMessageBox.confirm('确定要删除该分类吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    const res = await post('/admin/dict/category/delete/' + id, {})
    if (res.success) {
      ElMessage.success('删除成功')
      await loadCategories()
      activeCategory.value = null
    } else {
      ElMessage.error('删除失败')
    }
  } catch (err) {
    // 用户取消
  }
}

// 打开新增条目对话框
const openAddEntryDialog = () => {
  isEditingEntry.value = false
  entryForm.value = {
    name: '',
    value: '',
    category_id: activeCategory.value,
  }
  showEntryDialog.value = true
}

// 打开编辑条目对话框
const openEditEntryDialog = (entry:any) => {
  isEditingEntry.value = true
  entryForm.value = { ...entry }
  showEntryDialog.value = true
}

// 保存条目
const saveEntry = async () => {
  entryForm.value.category_id = activeCategory.value
  const url = isEditingEntry.value ? '/admin/dict/entry/update' : '/admin/dict/entry/create'
  const res = await post(url, entryForm.value)
  if (res.success) {
    ElMessage.success(isEditingEntry.value ? '更新成功' : '添加成功')
    showEntryDialog.value = false
    await loadEntriesByCategory()
  } else {
    ElMessage.error(res.message || '操作失败')
  }
}

// 删除条目
const deleteEntry = async (name:any) => {
  try {
    await ElMessageBox.confirm('确定要删除该条目吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    const res = await post('/admin/dict/entry/delete/' + name, {})
    if (res.success) {
      ElMessage.success('删除成功')
      await loadEntriesByCategory()
    } else {
      ElMessage.error('删除失败')
    }
  } catch (err) {
    // 用户取消
  }
}

const selectedCategory = computed(() => {
  if (!categories.value || !Array.isArray(categories.value)) {
    return null
  }
  return categories.value.find((c: any) => c.id === activeCategory.value) || null
})
</script>

<style scoped>
.el-input-number .el-input__inner {
  text-align: left;
}
</style>