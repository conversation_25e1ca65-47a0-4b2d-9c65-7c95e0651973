<template>
  <div class="flex-1 flex flex-col bg-gray-50 overflow-auto">
    <!-- 顶部操作区 -->
    <div class="mb-6 rounded-lg p-6 shadow-sm">
      <div class="mb-4 flex justify-between items-center">
        <h3 class="text-lg font-medium">日志管理</h3>
        <div class="space-x-2">
          <!-- <el-button type="primary" @click="handleExport">
            <el-icon>
              <Download />
            </el-icon> 导出日志
          </el-button> -->
          <el-button type="danger" @click="showClearDialog">
            <el-icon>
              <Delete />
            </el-icon> 清理日志
          </el-button>
        </div>
      </div>

      <!-- 搜索表单 -->
      <div class="grid grid-cols-4 gap-4">
        <div>
          <el-select v-model="search.path" placeholder="按路径搜索" clearable>
            <el-option v-for="item in pathOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div>
          <el-select v-model="search.method" placeholder="选择请求方法" clearable>
            <el-option label="GET" value="GET" />
            <el-option label="POST" value="POST" />
            <!-- <el-option label="PUT" value="PUT" />
            <el-option label="DELETE" value="DELETE" /> -->
          </el-select>
        </div>
        <div>
          <el-input v-model="search.ip" placeholder="请输入 IP 地址" clearable />
        </div>
        <div>
          <el-button type="success" @click="handleSearch">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </div>
      </div>
    </div>

    <!-- 数据表格 -->
    <div class="p-6 flex-1 overflow-auto">
      <el-table :data="tableData" style="width: 100%" border>
        <el-table-column prop="path" label="请求路径" show-overflow-tooltip />
        <el-table-column prop="method" label="请求方法" width="120" />
        <el-table-column prop="user_id" label="请求人ID" width="60" />
        <el-table-column prop="ip" label="IP 地址" width="150" />
        <el-table-column prop="user_agent" label="User-Agent" show-overflow-tooltip />
        <el-table-column prop="created_at" label="时间" width="160" sortable />
        <el-table-column label="操作" width="120">
          <template #default="{ row }">
            <el-button link type="primary" @click="showDetail(row)">查看详情</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <div class="px-6 py-4 border-t border-gray-200 flex justify-end">
      <el-pagination layout="prev, pager, next" :total="total" :page-size="search.page_size"
        v-model:current-page="search.page" @current-change="getList" background />
    </div>
  </div>

  <!-- 日志详情弹窗 -->
  <el-dialog v-model="dialogVisible" title="日志详情" width="50%">
    <div class="space-y-4">
      <div class="grid grid-cols-2 gap-4">
        <div><span class="font-medium">请求路径：</span>{{ currentLog.path }}</div>
        <div><span class="font-medium">请求方法：</span>{{ currentLog.method }}</div>
        <div><span class="font-medium">IP地址：</span>{{ currentLog.ip }}</div>
        <div><span class="font-medium">时间：</span>{{ currentLog.created_at }}</div>
        <div class="col-span-2"><span class="font-medium">User-Agent：</span>{{ currentLog.user_agent }}</div>
        <div class="col-span-2"><span class="font-medium">请求人ID：</span>
          <pre class="mt-1 whitespace-pre-wrap">{{ currentLog.user_id }}</pre>
        </div>
        <div class="col-span-2"><span class="font-medium">请求数据：</span>
          <pre class="mt-1 whitespace-pre-wrap">{{ currentLog.data }}</pre>
        </div>
      </div>
    </div>
    <template #footer>
      <el-button @click="dialogVisible = false">关闭</el-button>
    </template>
  </el-dialog>
  <!-- 清理日志弹窗 -->
<el-dialog v-model="clearDialogVisible" title="清理日志" width="30%">
  <el-form :model="clearForm" label-width="100px">
    <el-form-item label="清理天数前">
      <el-input-number v-model="clearForm.days" :min="1" :max="365" />
    </el-form-item>
  </el-form>
  <template #footer>
    <el-button @click="clearDialogVisible = false">取消</el-button>
    <el-button type="danger" @click="confirmClearLogs">确认清理</el-button>
  </template>
</el-dialog>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { get, post } from '@/utils/request'
import { ElMessage } from 'element-plus'
import { Download } from '@element-plus/icons-vue'

// 表格数据与分页
const tableData = ref([])
const total = ref(0)
const clearDialogVisible = ref(false)
const clearForm = ref({
  days: 30
})

const showClearDialog = () => {
  clearDialogVisible.value = true
}

const confirmClearLogs = async () => {
  try {
    const res = await post(`admin/logs/clear/${clearForm.value.days}`)
    if (res.success) {
      ElMessage.success('日志清理成功')
      clearDialogVisible.value = false
      getList()
    } else {
      ElMessage.error(res.message || '清理失败')
    }
  } catch (err) {
    console.error('清理日志失败:', err)
    ElMessage.error('网络错误')
  }
}
// 查询参数
const search = ref({
  page: 1,
  page_size: 10,
  path: '',
  method: '',
  ip: ''
})

// 路径下拉选项（可根据实际接口获取）
const pathOptions = [
  { label: '/login', value: '/login' },
  { label: '/logout', value: '/logout' },
  { label: '/api/user', value: '/api/user' }
]

// 获取日志列表
const getList = async () => {
  try {
    const res = await get('admin/logs/list', {
      ...search.value
    })
    if (res.success) {
      tableData.value = res.data.list
      total.value = res.data.total
    } else {
      ElMessage.error(res.message || '获取日志失败')
    }
  } catch (err) {
    console.error('获取日志失败:', err)
    ElMessage.error('网络错误，请稍后再试')
  }
}

onMounted(() => {
  getList()
})

// 搜索事件
const handleSearch = () => {
  search.value.page = 1
  getList()
}

// 重置搜索
const resetSearch = () => {
  search.value = {
    page: 1,
    page_size: 10,
    path: '',
    method: '',
    ip: ''
  }
  getList()
}

// 导出日志
const handleExport = () => {
  // 可以调用 export 接口或提示开发中
  ElMessage.info('导出功能暂未实现')
}

// 弹窗相关
const dialogVisible = ref(false)
const currentLog: any = ref(null)

const showDetail = (row: any) => {
  currentLog.value = row
  dialogVisible.value = true
}
</script>

<style scoped>
.el-table :deep(.el-table__header-wrapper) {
  background-color: #f5f7fa;
}
</style>