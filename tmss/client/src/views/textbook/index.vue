<!-- 代码已包含 CSS：使用 TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 -->

<template>
    <div class="flex-1 flex flex-col bg-gray-50 overflow-auto">
        <div class="p-6 border-b border-gray-200">
            <el-button type="primary" class="!rounded-button whitespace-nowrap mb-4" @click="handleAdd">
                <el-icon class="mr-1">
                    <Plus />
                </el-icon>新增教材
            </el-button>
            <el-form :inline="true" class="flex flex-wrap gap-4">
                <el-form-item>
                    <el-input v-model="searchForm.title" placeholder="教材名称" class="w-48">
                        <template #prefix>
                            <el-icon>
                                <Search />
                            </el-icon>
                        </template>
                    </el-input>
                </el-form-item>
                <el-form-item>
                    <el-input v-model="searchForm.author" placeholder="作者" class="w-48">
                        <template #prefix>
                            <el-icon>
                                <User />
                            </el-icon>
                        </template>
                    </el-input>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" class="!rounded-button whitespace-nowrap" @click="handleSearch">
                        <el-icon class="mr-1">
                            <Search />
                        </el-icon>搜索
                    </el-button>
                    <el-button class="!rounded-button whitespace-nowrap ml-2" @click="handleReset">
                        <el-icon class="mr-1">
                            <Refresh />
                        </el-icon>重置
                    </el-button>
                </el-form-item>
            </el-form>
        </div>


        <div class="flex-1 overflow-auto p-6">
            <el-table :data="tableData" border style="width: 100%" v-loading="loading">
                <el-table-column prop="title" label="教材标题" />
                <el-table-column prop="author" label="作者" />
                <el-table-column prop="isbn" label="ISBN" />
                <el-table-column prop="publisher" label="出版社" />
                <el-table-column prop="edition" label="版次" />
                <el-table-column label="操作" width="160">
                    <template #default="scope">
                        <el-button type="primary" link @click="handleEdit(scope.row)">
                            <el-icon>
                                <Edit />
                            </el-icon>编辑
                        </el-button>
                        <el-button type="danger" link @click="handleDelete(scope.row)">
                            <el-icon>
                                <Delete />
                            </el-icon>删除
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <div class="flex justify-center p-4">
            <el-pagination v-model:current-page="currentPage" background v-model:page-size="pageSize" :total="total"
                layout=" prev, pager, next" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
        </div>
        <el-dialog v-model="dialogVisible" :title="dialogType === 'add' ? '新增教材' : '编辑教材'" width="500px"
            destroy-on-close>
            <el-form ref="formRef" :model="form" :rules="rules" label-width="80px" label-position="right">
                <el-form-item label="教材标题" prop="title">
                    <el-input v-model="form.title" placeholder="请输入教材标题" />
                </el-form-item>
                <el-form-item label="作者" prop="author">
                    <el-input v-model="form.author" placeholder="请输入作者" />
                </el-form-item>
                <el-form-item label="ISBN" prop="isbn">
                    <el-input v-model="form.isbn" placeholder="请输入ISBN" />
                </el-form-item>
                <el-form-item label="出版社" prop="publisher">
                    <el-input v-model="form.publisher" placeholder="请输入出版社" />
                </el-form-item>
                <el-form-item label="版次" prop="edition">
                    <el-input v-model="form.edition" placeholder="请输入版次" />
                </el-form-item>
                <el-form-item label="上传教材">
                    <el-upload class="upload-demo" accept=".pdf,.docx,.pptx" drag
                        action="#" :limit="1" :auto-upload="false" :on-change="handleFileChange"
                        :on-remove="onFileRemove">
                        <el-icon class="el-icon--upload">
                            <Upload />
                        </el-icon>
                        <div class="el-upload__text">
                            将文件拖到此处，或<em>点击上传</em>
                        </div>
                        <template #tip>
                            <div class="el-upload__tip">
                                支持.pdf/.docx/.pptx格式，文件大小不超过 500MB
                            </div>
                        </template>
                    </el-upload>
                </el-form-item>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button class="!rounded-button whitespace-nowrap" @click="dialogVisible = false">取消</el-button>
                    <el-button class="!rounded-button whitespace-nowrap" type="primary"
                        @click="handleSubmit">确定</el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import type { FormInstance,UploadFile, FormRules } from 'element-plus';
import { ElMessage, ElMessageBox } from 'element-plus';
import { get, post } from '@/utils/request';
import { errMsg, msg } from '@/utils/msg';

// 接口返回类型定义
interface RespTextbookList {
    list: Textbook[];
    total: number;
}

interface Textbook {
    id: number;
    title: string;
    author: string;
    isbn: string;
    publisher: string;
    edition: string;
    user_id: number;
    created_at: number;
    updated_at: number;
    file_name: string;
    file_type: string;
    file_path: string;
}

interface SearchParams {
    page: number;
    page_size: number;
    title?: string;
    author?: string;
    username?: string;
    user_id?: number;
}

// 响应式变量
const tableData = ref<Textbook[]>([]);
const total = ref(0);
const loading = ref(false);
const currentPage = ref(1);
const pageSize = ref(10);

const dialogVisible = ref(false);
const dialogType = ref<'add' | 'edit'>('add');

const searchForm = ref({
    title: '',
    author: '',
    isbn: ''
});

const form = ref({
    id: 0,
    title: '',
    author: '',
    isbn: '',
    publisher: '',
    edition: '',
    file_name: '',
    file_type: '',
    file_path: ''
});

const rules: FormRules = {
    title: [{ required: true, message: '请输入教材标题', trigger: 'blur' }],
    author: [{ required: true, message: '请输入作者', trigger: 'blur' }],
    isbn: [
        { required: true, message: '请输入ISBN', trigger: 'blur' },
        // { pattern: /^(?=(?:\D*\d){10}(?:(?:\D*\d){3})?$)[\d-]+$/, message: 'ISBN格式不正确', trigger: 'blur' }
    ],
    publisher: [{ required: true, message: '请输入出版社', trigger: 'blur' }],
    edition: [{ required: true, message: '请输入版次', trigger: 'blur' }]
};

const formRef = ref<FormInstance>();

// 初始化加载
onMounted(() => {
    getList();
});

// 获取教材列表
const getList = async () => {
    loading.value = true;

    const params: SearchParams = {
        page: currentPage.value,
        page_size: pageSize.value,
    };

    if (searchForm.value.title) params.title = searchForm.value.title;
    if (searchForm.value.author) params.author = searchForm.value.author;

    try {
        const res = await get<RespTextbookList>('teacher/textbook/list', params);
        if (res.success) {
            tableData.value = res.data.list;
            total.value = res.data.total;
        }
    } catch (err) {
        console.error('获取教材失败:', err);
        ElMessage.error('获取教材失败');
    } finally {
        loading.value = false;
    }
};

// 打开新增弹窗
const handleAdd = () => {
    dialogType.value = 'add';
    form.value = {
        id: 0,
        title: '',
        author: '',
        isbn: '',
        publisher: '',
        edition: '',
        file_name: '',
        file_type: '',
        file_path: ''
    };
    dialogVisible.value = true;
};

// 编辑教材
const handleEdit = (row: Textbook) => {
    dialogType.value = 'edit';
    form.value = {
        id: row.id,
        title: row.title,
        author: row.author,
        isbn: row.isbn,
        publisher: row.publisher,
        edition: row.edition,
        file_name: row.file_name,
        file_type: row.file_type,
        file_path: row.file_path
    };
    dialogVisible.value = true;
};

// 删除教材
const handleDelete = (row: Textbook) => {
    ElMessageBox.confirm(`确定要删除教材 "${row.title}" 吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(async () => {
        const res = await post(`teacher/textbook/delete/${row.id}`);
        if (res.success) {
            ElMessage.success('删除成功');
            getList();
        } else {
            ElMessage.error(res.message || '删除失败');
        }
    }).catch(() => {
        // 取消删除
    });
};
/** 上传课件 */
const handleFileChange = async (file: UploadFile) => {
    //console.log("文件变更:", file);
    if (file.size && file.size > 1024 * 1024 * 500) {
        errMsg("上传课件大小不超过500M");
        return;
    }
    try {
        const formData = new FormData();
        formData.append("file", file.raw!);
        loading.value = true;
        const res = await post("teacher/textbook/upload", formData);
        if (res.code == 0) {
            const data = res.data || {}
            msg("success", "上传成功");
            form.value.file_name = data.file_name || "";
            form.value.file_type = data.file_type || "";
            form.value.file_path = data.file_path || "";
        } else {
            msg("error", res.message);
        }
        loading.value = false;
    } catch (e) {
        console.log(e);
        msg("error", "上传失败");
        loading.value = false;
    }
};

/** 移除上传课件 */
const onFileRemove = (file: UploadFile) => {
    form.value.file_name = "";
    form.value.file_type = "";
    form.value.file_path = "";
};
// 提交表单
const handleSubmit = async () => {
    if (!formRef.value) return;
    await formRef.value.validate(async (valid) => {
        if (valid) {
            if (dialogType.value === 'add') {
                const res = await post('teacher/textbook/create', {
                    title: form.value.title,
                    author: form.value.author,
                    isbn: form.value.isbn,
                    publisher: form.value.publisher,
                    edition: form.value.edition,
                    file_name: form.value.file_name,
                    file_type: form.value.file_type,
                    file_path: form.value.file_path
                });
                if (res.success) {
                    ElMessage.success('新增成功');
                    dialogVisible.value = false;
                    getList();
                } else {
                    ElMessage.error(res.message || '新增失败');
                }
            } else {
                const res = await post('teacher/textbook/update', {
                    id: form.value.id,
                    title: form.value.title,
                    author: form.value.author,
                    isbn: form.value.isbn,
                    publisher: form.value.publisher,
                    edition: form.value.edition,
                    file_name: form.value.file_name,
                    file_type: form.value.file_type,
                    file_path: form.value.file_path
                });
                if (res.success) {
                    ElMessage.success('更新成功');
                    dialogVisible.value = false;
                    getList();
                } else {
                    ElMessage.error(res.message || '更新失败');
                }
            }
        }
    });
};

// 搜索 & 重置
const handleSearch = () => {
    currentPage.value = 1;
    getList();
};

const handleReset = () => {
    searchForm.value = {
        title: '',
        author: '',
        isbn: ''
    };
    currentPage.value = 1;
    getList();
};

// 分页事件
const handleSizeChange = (val: number) => {
    pageSize.value = val;
    getList();
};

const handleCurrentChange = (val: number) => {
    currentPage.value = val;
    getList();
};
</script>
<style scoped>
.el-input :deep(.el-input__wrapper) {
    border-radius: 6px;
}

.dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
}
</style>
