<template>
  <div class="flex-1 flex flex-col bg-gray-50 overflow-auto">
    <!-- 搜索区域 -->
    <div class="mb-6 flex gap-4 p-6 shadow-sm">
      <el-input v-model="searchKeyword" placeholder="请输入模块名称" class="w-32" :prefix-icon="Search" />
    </div>

    <!-- 模块配置列表 -->
    <div class="flex-1 overflow-auto p-6">
      <el-table :data="filteredConfigs" style="width: 100%" border>
        <el-table-column prop="module_name" label="模块名称" />
        <el-table-column prop="module_key" label="模块键值" />
        <el-table-column prop="enable" label="启用状态" align="center">
          <template #default="{ row }">
            <el-tag :type="row.enable ? 'success' : 'info'" disable-transitions>{{ row.enable ? '启用' : '禁用' }}</el-tag>
          </template>
        </el-table-column>
        <!-- <el-table-column prop="auto_review" label="自动审核" align="center">
          <template #default="{ row }">
            <el-tag :type="row.auto_review ? 'success' : 'info'" disable-transitions>{{ row.auto_review ? '启用' : '禁用' }}</el-tag>
          </template>
        </el-table-column> -->
        <!-- <el-table-column prop="auto_review" label="自动审核" align="center">
          <template #default="{ row }">
            <el-switch v-model="row.auto_review" :disabled="!row.enable" @change="handleUpdateConfig(row)" />
          </template>
        </el-table-column> -->
        <!-- <el-table-column prop="workflow_id" label="工作流ID" align="center" /> -->
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" link @click="handleEdit(row)">
              <el-icon>
                <Edit />
              </el-icon>编辑
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 编辑弹窗 -->
    <el-dialog v-model="showDialog" :title="dialogTitle" width="50%" destroy-on-close>
      <el-form :model="formData" label-width="100px">
        <el-form-item label="模块名称" required>
          <el-input v-model="formData.module_name" disabled />
        </el-form-item>
        <el-form-item label="模块键值" required>
          <el-input v-model="formData.module_key" disabled />
        </el-form-item>
        <el-form-item label="启用状态">
          <el-switch v-model="formData.enable" />
        </el-form-item>
        <!-- <el-form-item label="自动审核">
          <el-switch v-model="formData.auto_review" />
        </el-form-item> -->
        <el-form-item label="工作流">
          <!-- <el-input v-model="formData.workflow_id" type="number" placeholder="请输入工作流ID" /> -->
          <my-select v-model="formData.workflow_ids" placeholder="请选择工作流" :func="getFlowList" labelKey="name"
          multiple valueKey="id" searchKey="name" :initData="flowlist" />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showDialog = false">取消</el-button>
          <el-button type="primary" @click="saveConfig">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, computed, toRaw } from 'vue'
import { get, post } from '@/utils/request'
import { Search } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

// 数据定义
const searchKeyword = ref('')
const showDialog = ref(false)
const dialogTitle = ref('编辑模块配置')
const moduleConfigs = ref<any[]>([])
const formData = ref({
  module_name: '',
  module_key: '',
  enable: false,
  auto_review: false,
  workflow_ids: []
})
const flowlist = ref<any[]>([])

// 获取模块配置列表
const fetchModuleConfigs = async () => {
  const res = await get('admin/workflow/configs')
  if (!res.success) return
  moduleConfigs.value = Object.values(res.data)
}
const getFlowList = (params?: {
  page?: number
  page_size?: number
  name?: string
}) => get('admin/workflow/list?module_key=' + formData.value.module_key, params).then((res) => res.data)
// 计算过滤后的模块配置
const filteredConfigs = computed(() => {
  return moduleConfigs.value.filter((config) =>
    config.module_name.includes(searchKeyword.value)
  )
})

// 切换模块启用状态
// const handleToggleModule = async (row: any) => {
//   try {
//     await post('admin/workflow/config/toggle', {
//       module_key: row.module_key,
//       enable: row.enable
//     })
//     ElMessage.success('模块状态已更新')
//   } catch (err) {
//     ElMessage.error('更新模块状态失败')
//   }
// }

// 更新模块配置
// const handleUpdateConfig = async (row: any) => {
//   try {
//     await post('admin/workflow/config/update', row)
//     ElMessage.success('模块配置已更新')
//   } catch (err) {
//     ElMessage.error('更新模块配置失败')
//   }
// }

// 打开编辑弹窗
const handleEdit = (row: any) => {
  dialogTitle.value = '编辑模块配置'
  formData.value = { ...row }
  showDialog.value = true
}
const checkForm = () => {
  const save = toRaw(formData.value)
  //console.log(save)
  //return false
  if (save.enable) {
    if (!save.workflow_ids || !save.workflow_ids.length) {
      ElMessage.error('请选择工作流')
      return false
    }
    // if (save.auto_review) {

    //   if (save.workflow_ids.length > 1) {
    //     ElMessage.error('自动审核模式下请选择一个工作流')
    //     return false
    //   }
    // } else {
    //   if (!save.workflow_ids.length) {
    //     ElMessage.error('请选择工作流')
    //     return false
    //   }
    // }
  }
  return true
}
// 保存模块配置
const saveConfig = async () => {
  if (!checkForm()) return
  await post('admin/workflow/config/update', formData.value)
  ElMessage.success('模块配置已保存')
  showDialog.value = false
  fetchModuleConfigs()
}

onMounted(() => {
  fetchModuleConfigs()
})
</script>

<style scoped>
.el-table {
  margin-bottom: 20px;
}
</style>