<template>
  <div class="flex-1 flex flex-col bg-gray-50 overflow-auto">
    <div class="flex items-center justify-between p-4 border-b border-gray-200">
      <h2 class="text-xl font-semibold">{{ moduleName }}审核列表</h2>
      <div class="flex space-x-2">
        <!-- 批量操作按钮 -->
        <el-button type="primary" :disabled="selectedIds.length === 0" @click="batchApprove">
          批量通过
        </el-button>
        <el-button type="danger" :disabled="selectedIds.length === 0" @click="batchReject">
          批量驳回
        </el-button>
      </div>
    </div>
    <!-- 列表区域 -->
    <div class="flex-1 overflow-auto p-6">
      <el-table :data="tableData" v-loading="loading" style="width: 100%" border
        @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center"
          :selectable="(row: any) => ['reviewing', 'backed_up'].includes(row.status)" />
        <el-table-column prop="id" label="ID" align="center" width="80" />
        <el-table-column prop="data_title" label="数据标题" show-overflow-tooltip />
        <el-table-column prop="username" label="提交人" align="center" width="120" />
        <el-table-column prop="status" label="状态" align="center" width="120">
          <template #default="{ row }">
            <el-tag :type="statusTagType(row.status)">
              {{ statusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="提交时间" align="center" width="180">
          <template #default="{ row }">
            {{ formatDate(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="240">
          <template #default="{ row }">
            <div class="flex justify-center">
              <el-button plain @click="viewDetail(row)" size="small">查看</el-button>
              <el-button v-if="row.status === 'reviewing'" type="primary" size="small" plain
                @click="approve(row.id)">通过</el-button>
              <el-button v-if="row.status === 'backed_up'" type="success" size="small" plain
                @click="approve(row.id)">二次申请</el-button>
              <el-button v-if="row.status === 'reviewing' || row.status === 'backed_up'" type="danger" size="small"
                plain @click="reject(row.id)">驳回</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页区域 -->
    <div class="shrink-0 flex justify-center mt-4 pb-2">
      <el-pagination background v-model:current-page="page" v-model:page-size="pageSize" :total="total"
        layout="prev, pager, next" @current-change="handlePageChange" />
    </div>

    <!-- 审核详情弹窗 -->
    <el-dialog v-model="showDetailDialog" title="审核详情" width="50%" top="5vh">
      <el-descriptions v-if="currentRow" :column="1" border>
        <el-descriptions-item label="数据ID">{{ currentRow.data_id }}</el-descriptions-item>
        <el-descriptions-item label="数据标题">{{ currentRow.data_title }}</el-descriptions-item>
        <el-descriptions-item label="提交人">{{ currentRow.username }}</el-descriptions-item>
        <el-descriptions-item label="模块类型">{{ currentRow.module_key }}</el-descriptions-item>
        <el-descriptions-item label="当前状态">
          <el-tag :type="statusTagType(currentRow.status)">
            {{ statusText(currentRow.status) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="提交时间">{{ formatDate(currentRow.created_at) }}</el-descriptions-item>
        <el-descriptions-item label="驳回原因" v-if="currentRow.status === 'rejected'">
          {{ currentRow.reject_reason || '无' }}
        </el-descriptions-item>
        <el-descriptions-item label="审核备注记录">
          <div class="remark-timeline">
            <el-timeline v-if="remarkList.length > 0">
              <el-timeline-item v-for="(remark, index) in remarkList" :key="index"
                :timestamp="formatDate(remark.created_at)" placement="top">
                <el-card>
                  <p><strong>{{ remark.username }}</strong>：{{ remark.remark }}</p>
                  <p v-if="remark.remark_type === 'rejected'" class="text-red-500">
                    驳回原因：{{ remark.reject_reason || '无' }}
                  </p>
                </el-card>
              </el-timeline-item>
            </el-timeline>
            <div v-else class="text-gray-400">暂无备注记录</div>
          </div>
        </el-descriptions-item>
      </el-descriptions>
    </el-dialog>
    <!-- 驳回审核对话框 -->
    <el-dialog v-model="rejectDialogVisible" title="驳回审核" width="500px" :before-close="handleRejectDialogClose">
      <el-form :model="rejectForm" ref="rejectFormRef" label-width="80px">
        <el-form-item label="驳回原因" prop="rejectReason"
          :rules="[{ required: true, message: '驳回原因不能为空', trigger: 'blur' }]">
          <el-input v-model="rejectForm.rejectReason" type="textarea" :rows="3" placeholder="请输入详细的驳回原因（必填）"
            maxlength="200" show-word-limit></el-input>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="rejectForm.remark" type="textarea" :rows="2" placeholder="请输入备注信息（可选）" maxlength="100"
            show-word-limit></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="rejectDialogVisible = false">取消</el-button>
          <el-button type="danger" @click="confirmReject">确认驳回</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue';
import { ElMessageBox } from 'element-plus';
import { useRoute } from 'vue-router'; // 添加路由支持
import type { FormInstance } from 'element-plus';
import { getModuleNameByKey, getApproveList, approveWorkflow, rejectWorkflow, getWorkflowRemarkList, batchApproveWorkflow, batchRejectWorkflow } from '@/api/workflow';

import { successMsg, errMsg } from '@/utils/msg';
const props = defineProps<{
  moduleKey?: string
}>();
// 获取路由信息
const route = useRoute();
// 从路由参数中获取 moduleKey
const moduleKey = ref<string>(props.moduleKey || (route.params.moduleKey as string) || '');
const selectedIds = ref<number[]>([]);
// 处理表格选择变化
const handleSelectionChange = (selection: any[]) => {
  selectedIds.value = selection.map(item => item.id);
};
console.log('moduleKey:', moduleKey);
const moduleName = ref<string>('');
// 分页参数
const page = ref(1);
const pageSize = ref(10);
const total = ref(0);

// 加载状态
const loading = ref(false);

// 表格数据
const tableData = ref([]);

// 当前选中的行
const currentRow: any = ref(null);
const showDetailDialog = ref(false);
// 驳回对话框相关状态
const rejectDialogVisible = ref(false);
const rejectFormRef = ref<FormInstance>();
const rejectForm = ref({
  id: 0,
  rejectReason: '',
  remark: ''
});
// 状态
const remarkList = ref<any[]>([]);
const remarkLoading = ref(false);
// 添加对路由变化的监听
watch(() => route.fullPath, () => {
  // 当路由变化时更新 moduleKey
  moduleKey.value = props.moduleKey || (route.params.moduleKey as string) || '';

  if (moduleKey.value) {
    moduleName.value = getModuleNameByKey(moduleKey.value);
    // 重置状态并重新加载数据
    resetState();
    fetchData();
  }
});
// 状态标签类型映射
const statusTagType = (status: string) => {
  const map: Record<string, string> = {
    reviewing: 'warning',
    published: 'success',
    rejected: 'danger',
    backed_up: 'info',//上级打回
  };
  return map[status] || 'info';
};
const resetState = () => {
  page.value = 1;
  total.value = 0;
  tableData.value = [];
  currentRow.value = null;
  showDetailDialog.value = false;
};
// 状态文本映射
const statusText = (status: string) => {
  const map: Record<string, string> = {
    reviewing: '审核中',
    published: '已通过',
    rejected: '已驳回',
    backed_up: '已打回',//上级打回
  };
  return map[status] || status;
};

// 日期格式化
const formatDate = (timestamp: number) => {
  return new Date(timestamp * 1000).toLocaleString();
};

// 获取审核列表数据
const fetchData = async () => {
  if (!moduleKey.value) {
    errMsg('缺少模块标识');
    return;
  }

  loading.value = true;
  try {
    const res = await getApproveList(moduleKey.value, page.value, pageSize.value);
    tableData.value = res.list || [];
    total.value = res.total || 0;
  } catch (err) {
    errMsg('获取数据失败');
  } finally {
    loading.value = false;
  }
};
// 新增方法：获取备注列表
const fetchRemarkList = async (dataId: number) => {
  if (!dataId) return;

  remarkLoading.value = true;
  try {
    const res = await getWorkflowRemarkList(dataId);
    remarkList.value = res || [];
  } catch (err) {
    errMsg('获取备注列表失败');
  } finally {
    remarkLoading.value = false;
  }
};

// 分页变化处理
const handlePageChange = (newPage: number) => {
  page.value = newPage;
  fetchData();
};

// 查看详情
const viewDetail = async (row: any) => {
  currentRow.value = row;
  showDetailDialog.value = true;
  await fetchRemarkList(row.data_id);
};

// 通过审核
const approve = async (id: number) => {
  try {
    // 弹出对话框要求输入备注
    const { value: remark } = await ElMessageBox.prompt('请输入审核备注（可选）', '通过审核', {
      confirmButtonText: '确认通过',
      cancelButtonText: '取消',
      inputType: 'textarea',
      inputPlaceholder: '请输入审核备注...',
      inputValidator: (value) => {
        // 备注可以为空，但如果有内容则至少需要2个字符
        if (value && value.trim().length < 2) {
          return '备注至少需要2个字符';
        }
        return true;
      }
    });

    // 调用审批通过API
    await approveWorkflow(id, remark?.trim() || '');
    successMsg('审核已通过');

    // 刷新列表
    fetchData();
  } catch (err) {
    if (err !== 'cancel') {
      errMsg('操作失败: ' + (err as Error).message);
    }
  }
};
// 批量通过按钮点击
const batchApprove = async () => {
  try {
    // 弹出对话框要求输入备注
    const { value: remark } = await ElMessageBox.prompt('请输入审核备注（可选）', '通过审核', {
      confirmButtonText: '确认通过',
      cancelButtonText: '取消',
      inputType: 'textarea',
      inputPlaceholder: '请输入审核备注...',
      inputValidator: (value) => {
        // 备注可以为空，但如果有内容则至少需要2个字符
        if (value && value.trim().length < 2) {
          return '备注至少需要2个字符';
        }
        return true;
      }
    });

    // 调用审批通过API
    await batchApproveWorkflow(selectedIds.value, remark?.trim() || '');
    successMsg('审核已通过');

    // 刷新列表
    fetchData();
  } catch (err) {
    if (err !== 'cancel') {
      errMsg('操作失败: ' + (err as Error).message);
    }
  }
};
// 驳回审核 - 使用对话框
const isBatchReject = ref(false);
const reject = (id: number) => {
  // 设置当前驳回的ID
  rejectForm.value.id = id;
  rejectForm.value.rejectReason = '';
  rejectForm.value.remark = '';
  isBatchReject.value = false;
  // 打开对话框
  rejectDialogVisible.value = true;
};

const batchReject = () => {
  rejectForm.value.rejectReason = '';
  rejectForm.value.remark = '';
  isBatchReject.value = true;
  // 打开对话框
  rejectDialogVisible.value = true;
};
// 关闭驳回对话框前的处理
const handleRejectDialogClose = (done: () => void) => {
  if (rejectForm.value.rejectReason.trim() !== '') {
    ElMessageBox.confirm('确定要关闭驳回对话框吗？输入的内容将不会被保存', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      done();
    }).catch(() => { });
  } else {
    done();
  }
};

// 确认驳回
const confirmReject = async () => {
  // 验证表单
  if (!rejectFormRef.value) return;

  try {
    await rejectFormRef.value.validate();

    // 调用驳回API
    if (isBatchReject.value) {
      await batchRejectWorkflow(
        selectedIds.value,
        rejectForm.value.rejectReason.trim(),
        rejectForm.value.remark.trim()
      );
    } else {
      await rejectWorkflow(
        rejectForm.value.id,
        rejectForm.value.rejectReason.trim(),
        rejectForm.value.remark.trim()
      );
    }


    successMsg('审核已驳回');
    rejectDialogVisible.value = false;

    // 刷新列表
    fetchData();
  } catch (err) {
    // 验证错误已经由表单处理，这里处理API错误
    if (err !== 'cancel') {
      errMsg('操作失败: ' + (err as Error).message);
    }
  }
};

onMounted(() => {
  // 确保 moduleKey 存在后再加载数据
  if (moduleKey.value) {
    moduleName.value = getModuleNameByKey(moduleKey.value);
    fetchData();
  }
});
</script>