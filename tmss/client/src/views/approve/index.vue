<template>
  <div class="flex-1 flex flex-col bg-gray-50 overflow-auto">
    <!-- 搜索区域 -->
    <div class="mb-6 flex gap-4 p-6 shadow-sm">
      <el-button type="primary" class="!rounded-button whitespace-nowrap" @click="handleCreate">
        <el-icon class="mr-1">
          <Plus />
        </el-icon>新建流程
      </el-button>
      <el-input v-model="searchKeyword" placeholder="请输入流程名称" class="w-32" :prefix-icon="Search" />
      <el-select v-model="statusFilter" placeholder="流程状态" class="w-12">
        <el-option label="全部" value="" />
        <el-option label="启用" value="active" />
        <el-option label="停用" value="inactive" />
      </el-select>
    </div>

    <!-- 流程列表 -->
    <div class="flex-1 overflow-auto p-6">
      <el-table :data="workflows.list" style="width: 100%" border>
        <el-table-column prop="name" label="流程名称" />
        <el-table-column prop="moduleName" label="适配模块" />
        <el-table-column prop="nodeCount" label="审批节点数" align="center" />
        <el-table-column prop="createTime" label="创建时间" />
        <!-- <el-table-column label="状态" align="center" width="100">
          <template #default="{ row }">
            <el-switch v-model="row.status" :active-value="'active'" :inactive-value="'inactive'"
              @change="handleStatusChange(row)" />
          </template>
        </el-table-column> -->
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="success" link @click="handleEdit(row)">
              <el-icon>
                <Edit />
              </el-icon>编辑
            </el-button>
            <el-button type="danger" link @click="handleDelete(row)">
              <el-icon>
                <Delete />
              </el-icon>删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <div class="shrink-0 flex justify-center mt-4">
      <el-pagination v-model:current-page="page" v-model:page-size="pageSize" :total="workflows.total"
        layout="prev, pager, next" @current-change="fetchWorkflowList" />
    </div>

    <!-- 流程配置弹窗 -->
    <el-dialog v-model="showDialog" :title="dialogTitle" width="80%" destroy-on-close>
      <div class="workflow-config">
        <!-- 基本信息 -->
        <div class="mb-6">
          <h3 class="text-lg font-medium mb-4">基本信息</h3>
          <el-form :model="formData" label-width="100px">
            <el-form-item label="流程名称" required>
              <el-input v-model="formData.name" placeholder="请输入流程名称" />
            </el-form-item>
            <el-form-item label="适配模块">
              <el-select v-model="formData.module_key" placeholder="请选择适配模块" class="w-full">
                <el-option v-for="module in moduleList" :key="module.value" :label="module.name"
                  :value="module.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="流程描述">
              <el-input v-model="formData.description" type="textarea" placeholder="请输入流程描述" />
            </el-form-item>
          </el-form>
        </div>

        <!-- 流程图区域 -->
        <div class="mb-6">
          <h3 class="text-lg font-medium mb-4">流程配置</h3>
          <div class="workflow-chart" ref="chartRef"></div>
        </div>

        <!-- 节点配置 -->
        <div class="mb-6">
          <h3 class="text-lg font-medium mb-4">节点配置</h3>
          <div class="grid grid-cols-3 gap-4">
            <transition-group name="node-fade">
              <el-card v-for="(node, index) in formData.nodes" :key="node.id || index" class="workflow-node">
                <template #header>
                  <div class="flex justify-between items-center">
                    <span>顺序： {{ index + 1 }}</span>
                    <el-button type="danger" link @click="removeNode(index)">
                      <el-icon>
                        <Delete />
                      </el-icon>
                    </el-button>
                  </div>
                </template>
                <el-form label-width="80px">
                  <el-form-item label="节点名称">
                    <el-input v-model="node.name" placeholder="请输入节点名称" />
                  </el-form-item>
                  <el-form-item label="审批人">
                    <my-select v-model="node.userId" placeholder="请选择教员" :func="getUserList" labelKey="username"
                      valueKey="id" searchKey="username" :initData="users" />
                  </el-form-item>
                </el-form>
              </el-card>
            </transition-group>
          </div>
          <el-button type="primary" plain class="mt-4 !rounded-button whitespace-nowrap" @click="addNode">
            <el-icon class="mr-1">
              <Plus />
            </el-icon>添加节点
          </el-button>
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showDialog = false">取消</el-button>
          <el-button type="primary" @click="saveWorkflow">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, nextTick, watch } from 'vue'
import { get, post } from '@/utils/request'
import * as echarts from 'echarts'
import type { EChartsOption } from 'echarts'
import { Search } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

// 数据定义
const searchKeyword = ref('')
const statusFilter = ref('')
const chartInstance = ref<any>(null)
const showDialog = ref(false)
const dialogTitle = ref('新建流程')
const chartRef = ref<HTMLElement>()
const page = ref(1)
const pageSize = ref(10)
const moduleList: any = ref([])
// 表单数据
interface WorkflowNode {
  id: number
  name: string        // 用于保存流程名称
  username: string
  userId: null
}

interface WorkflowData {
  id?: number
  name: string
  description: string
  module_key?: string
  nodes: WorkflowNode[]
}

const formData = ref<WorkflowData>({
  name: '',
  description: '',
  module_key: 'syllabus',
  nodes: []
})

const workflows = ref({
  list: [] as any[],
  total: 0
})

const users: any = ref([])

// 获取流程列表
const fetchWorkflowList = async () => {
  const res = await get('admin/workflow/list', {
    name: searchKeyword.value,
    status: statusFilter.value,
    page: page.value,
    size: pageSize.value
  })
  if (!res.success) return
  workflows.value = {
    list: res.data.list,
    total: res.data.total
  }
  moduleList.value = res.data.moduleList
}
// 获取用户列表（用于审批人）
const getUserList = (params?: {
  page?: number
  page_size?: number
  username?: string
}) => get('admin/workflow/userlist', params).then((res) => res.data)


// 添加节点
const addNode = () => {
  const index = formData.value.nodes.length
  formData.value.nodes.push({
    id: Date.now(), // 使用时间戳作为唯一ID
    name: `节点${index + 1}`,
    username: '',
    userId: null
  })
}
// 移除节点
const removeNode = (index: number) => {
  formData.value.nodes.splice(index, 1)
}
const checkForm = () => {
  if (formData.value.nodes.length === 0) {
    ElMessage.warning('请添加节点')
    return false
  }
  const invalidNodes = formData.value.nodes.filter(
    node => !node.userId || node.userId === 0
  )

  if (invalidNodes.length > 0) {
    // 构建错误消息
    const errorMessages = invalidNodes.map((node, index) =>
      `节点 ${index + 1} (${node.name || '未命名节点'}) 未选择审批人`
    )

    // 显示错误提示
    ElMessage.error({
      message: `请完善以下节点配置：\n${errorMessages.join('\n')}`,
      duration: 5000
    })
    return false
  }

  // 验证流程名称
  if (!formData.value.name.trim()) {
    ElMessage.error('流程名称不能为空')
    return false
  }
  if (!formData.value.module_key) {
    ElMessage.error('适配模块不能为空')
    return false
  }
  return true
}
// 提交流程
const saveWorkflow = async () => {
  // 验证节点用户选择
  if (!checkForm()) return

  const url = formData.value.id ? 'admin/workflow/update' : 'admin/workflow/create'
  const payload = {
    ...formData.value
  }
  //console.log('saveWorkflow', payload)
  await post(url, payload)
  showDialog.value = false
  fetchWorkflowList()
}
const handleCreate = () => {
  dialogTitle.value = '新建流程'
  formData.value = {
    name: '',
    description: '',
    module_key: 'syllabus',
    nodes: []
  }
  showDialog.value = true
}
// 编辑流程
const handleEdit = async (row: any) => {
  dialogTitle.value = '编辑流程'
  const res = await get('admin/workflow/detail/' + row.id)
  if (!res.success) return
  formData.value = {
    id: res.data.workflow.id,
    name: res.data.workflow.name,
    description: res.data.workflow.description,
    module_key: res.data.workflow.module_key,
    nodes: res.data.nodes.map((n: any) => ({
      id: n.id,
      name: n.name,
      username: n.username,
      userId: n.userId
    }))
  }

  showDialog.value = true
  await nextTick()
  updateFlowChart()
}
// 状态变更
const handleStatusChange = async (row: any) => {
  await post('admin/workflow/change-status', {
    id: row.id,
    status: row.status === 'active' ? 'enable' : 'disable'
  })
}

// 删除流程
const handleDelete = async (row: any) => {
  if (confirm('确认删除该流程？')) {
    await post(`admin/workflow/delete/${row.id}`)
    fetchWorkflowList()
  }
}

watch(() => formData.value.nodes, () => {
  updateFlowChart()
}, { deep: true })
// 更新流程图
const updateFlowChart = () => {
  if (!chartRef.value) return

  chartInstance.value = echarts.init(chartRef.value)


  const nodes = formData.value.nodes
  const myChart = chartInstance.value

  // 清除现有图表数据
  myChart.clear()

  // 处理空节点情况
  if (nodes.length === 0) {
    const option: EChartsOption = {
      title: {
        text: '暂无审批节点',
        left: 'center',
        top: 'center',
        textStyle: {
          fontSize: 16,
          color: '#999'
        }
      },
      series: []
    }
    myChart.setOption(option, true)
    return
  }

  // 生成节点数据 - 使用唯一标识符
  const chartData = nodes.map((item, index) => ({
    id: `node_${item.id || index}`,
    name: item.name || `节点${index + 1}`,
    x: 100 + index * 200,
    y: 100
  }))

  // 生成连线数据 - 使用节点ID而非名称
  const links = nodes.length > 1
    ? nodes.slice(0, -1).map((_, index) => ({
      source: chartData[index].id,
      target: chartData[index + 1].id
    }))
    : []

  const option: EChartsOption = {
    animation: false,
    tooltip: {
      trigger: 'item',
      formatter: '{b}'
    },
    series: [
      {
        type: 'graph',
        layout: 'none',
        symbolSize: 50,
        roam: true,
        label: {
          show: true,
          position: 'inside',
          fontSize: 14
        },
        edgeSymbol: ['circle', 'arrow'],
        edgeSymbolSize: [4, 10],
        data: chartData,
        links,
        // 添加以下配置确保数据一致性
        edgeLabel: {
          show: false
        },
        emphasis: {
          focus: 'adjacency',
          lineStyle: {
            width: 5
          }
        },
        // 添加此配置解决单个节点问题
        focusNodeAdjacency: false,
        // 确保单个节点有样式
        itemStyle: {
          color: '#5470c6'
        }
      }
    ]
  }

  myChart.setOption(option, true)
  // 添加resize确保图表正确渲染
  myChart.resize()
}

onMounted(() => {
  fetchWorkflowList()
})
</script>


<style scoped>
.workflow-config {
  height: 70vh;
  overflow-y: auto;
}

.workflow-chart {
  width: 100%;
  height: 300px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
}

.workflow-node {
  transition: all 0.3s ease;
}

.workflow-node:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

:deep(.el-input__wrapper) {
  box-shadow: 0 0 0 1px #e5e7eb;
}

:deep(.el-input__wrapper:hover) {
  box-shadow: 0 0 0 1px #a3a3a3;
}

:deep(.el-button--primary) {
  background-color: #1a56db;
}

:deep(.el-dialog__body) {
  padding: 20px 30px;
}

.node-fade-enter-active,
.node-fade-leave-active {
  transition: all 0.5s ease;
}

.node-fade-enter-from,
.node-fade-leave-to {
  opacity: 0;
  transform: translateY(30px);
}
</style>
