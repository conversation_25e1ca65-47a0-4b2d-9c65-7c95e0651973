<template>
    <div class="flex-1 flex  bg-gray-50 overflow-auto">
        <!-- 左侧章节树状列表 -->
        <div class="w-full border-r border-gray-200 bg-white flex flex-col min-w-[290px]">
            <!-- 顶部操作栏 -->
            <div class="p-4 border-b border-gray-200 flex items-center gap-2">
                <el-button type="primary" class="!rounded-button whitespace-nowrap" @click="handleAddChapter">
                    <el-icon class="mr-1">
                        <Plus />
                    </el-icon>
                    添加章节
                </el-button>
                <el-input v-model="searchText" placeholder="搜索章节名称" clearable class="ml-2">
                    <template #prefix>
                        <el-icon>
                            <Search />
                        </el-icon>
                    </template>
                </el-input>
            </div>

            <!-- 章节树 -->
            <div class="w-full p-4">
                <el-tree ref="treeRef" default-expand-all :data="filteredChapters" node-key="id" :props="treeProps"
                    :expand-on-click-node="false" :filter-node-method="filterNode" @node-click="handleNodeClick">
                    <template #default="{ node, data }">
                        <div class="w-full group hover:bg-gray-50 rounded px-2 py-1">
                            <div class="w-full">
                                <div class="font-semibold">{{ data.name }}</div>
                                <div
                                    class="flex items-center text-gray-500 text-sm truncate whitespace-nowrap text-ellipsis overflow-hidden">
                                    {{ data.description || "暂无描述" }}
                                    <div class="opacity-0 group-hover:opacity-100 flex">
                                        <el-button class="!px-0 !ml-4" text @click.stop="handleEditChapter(data)">
                                            <el-icon :size="24">
                                                <Edit />
                                            </el-icon>
                                        </el-button>
                                        <el-button class="!px-0 !ml-4" text @click.stop="handleDeleteChapter(data)">
                                            <el-icon :size="24">
                                                <Delete />
                                            </el-icon>
                                        </el-button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </template>
                </el-tree>
            </div>
        </div>

        <el-dialog v-model="showDialog" :modal="false" :close-on-click-modal="false">
            <div class="mb-6">
                <h2 class="text-xl font-semibold mb-4">
                    {{ isEditing ? "编辑章节" : "添加章节" }}
                </h2>
                <el-form ref="formRef" :model="chapterForm" :rules="formRules" label-width="80px">
                    <el-form-item label="章节名称" prop="name">
                        <el-input v-model="chapterForm.name" placeholder="请输入章节名称" />
                    </el-form-item>
                    <el-form-item label="章节描述" prop="description">
                        <el-input v-model="chapterForm.description" type="textarea" :rows="4" placeholder="请输入章节描述" />
                    </el-form-item>
                    <el-form-item label="父章节" prop="parentId">
                        <el-select v-model="chapterForm.parent_id" placeholder="请选择父章节" clearable class="w-full"
                            @clear="selectClear">
                            <el-option v-for="item in parentChapterOptions" :key="item.id" :label="item.name"
                                :value="item.id" />
                        </el-select>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" class="!rounded-button whitespace-nowrap" @click="submitForm">
                            <template v-if="loading">
                                <el-icon class="animate-spin">
                                    <Loading />
                                </el-icon>
                                <span class="ml-2">保存中...</span>
                            </template>
                            <span v-else>保存</span>
                        </el-button>
                        <el-button class="!rounded-button whitespace-nowrap" @click="cancelForm">
                            取消
                        </el-button>
                    </el-form-item>
                </el-form>
            </div>
        </el-dialog>
    </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch } from "vue";
import { msg, confirmMsg as confirm } from "@/utils/msg";
import { chapterList, createChapter, updateChapter, deleteChapter } from "@/api/chapter";
import type { CreateChapter } from "@/api/chapter";

interface Chapter {
    id: number;
    name: string;
    description?: string;
    parent_id?: number;
    children?: Chapter[];
}

// 章节数据
const chapters = ref<Chapter[]>([]);
// 当前选中的章节
const currentChapter = ref<Chapter | null>(null);
const isEditing = ref(false);
//章节搜索内容
const searchText = ref("");
const treeRef = ref();
const formRef = ref();
// 章节表单
const chapterForm = ref({
    id: 0,
    name: "",
    description: "",
    parent_id: 0,
});
const loading = ref(false);
//显示创建弹窗
const showDialog = ref<boolean>(false);

// 表单验证规则
const formRules = {
    name: [
        { required: true, message: "请输入章节名称", trigger: "blur" },
        { max: 50, message: "长度不超过50个字符", trigger: "blur" },
    ],
    description: [
        { max: 200, message: "长度不超过200个字符", trigger: "blur" },
    ],
};
// 树形配置
const treeProps = {
    label: "name",
    children: "children",
};
// 过滤后的章节
const filteredChapters = computed(() => {
    if (!searchText.value) return chapters.value;
    return filterChapters(chapters.value);
});
// 父章节选项
const parentChapterOptions = computed(() => {
    const options: Chapter[] = [];
    const traverse = (nodes: Chapter[]) => {
        nodes.forEach((node) => {
            if (node.id !== chapterForm.value.id) {
                options.push({ ...node, children: undefined });
            }
            if (node.children) {
                traverse(node.children);
            }
        });
    };
    traverse(chapters.value);
    return options;
});

// 过滤章节
const filterChapters = (nodes: Chapter[]): Chapter[] => {
    return nodes
        .map((node) => ({ ...node }))
        .filter((node) => {
            const match = node.name.includes(searchText.value);
            if (node.children) {
                node.children = filterChapters(node.children);
                return match || node.children.length > 0;
            }
            return match;
        });
};

// 过滤节点
const filterNode = (value: string, data: Chapter) => {
    console.log(value, data);
    if (!value) return true;
    return data.name.includes(value);
};

/** 清空父章节选择 */
const selectClear = () => {
    chapterForm.value.parent_id = 0;
};

// 添加章节
const handleAddChapter = () => {
    showDialog.value = true;
    isEditing.value = false;
    chapterForm.value = {
        id: 0,
        name: "",
        description: "",
        parent_id: 0,
    };
    if (currentChapter.value) {
        chapterForm.value.parent_id = currentChapter.value.id;
    }
};

// 编辑章节
const handleEditChapter = (chapter: Chapter) => {
    showDialog.value = true;
    currentChapter.value = chapter;
    isEditing.value = true;
    console.log(chapter);
    chapterForm.value = {
        id: chapter.id,
        name: chapter.name,
        description: chapter.description || "",
        parent_id: chapter.parent_id as number,
    };
};

// 删除章节
const handleDeleteChapter = (chapter: Chapter) => {
    confirm(
        `确定删除章节 "${chapter.name}" 吗？此操作不可恢复。`,
        "提示",
        async action => {
            if (action) {
                try {
                    const res = await deleteChapter(chapter.id)
                    if (res.code == 0) {
                        msg("success", "删除成功");
                        getChapterList();
                    }
                } catch {
                    // 用户取消
                }
            }
        }
    );
};

// 节点点击
const handleNodeClick = (data: Chapter) => {
    currentChapter.value = data;
    handleAddChapter();
};

// 提交表单
const submitForm = async () => {
    if (loading.value) return;
    try {
        await formRef.value.validate();
        let res: any;
        loading.value = true;
        if (isEditing.value) {
            res = await updateChapter(chapterForm.value);
        } else {
            const data: CreateChapter = {
                name: chapterForm.value.name,
                description: chapterForm.value.description,
                parent_id: chapterForm.value.parent_id,
            }
            res = await createChapter(data);
        }
        if (res.code == 0) {
            msg("success", "保存成功");
            showDialog.value = false;
            getChapterList();
            cancelForm();
        } else {
            msg("error", res.message);
        }
        loading.value = false;
    } catch (error) {
        loading.value = false;
    }
};

/** 获取章节列表 */
const getChapterList = async () => {
    const res = await chapterList();
    if (res.code == 0) {
        chapters.value = res.data || [];
    }
}

// 取消表单
const cancelForm = () => {
    showDialog.value = false;
    currentChapter.value = null;
    formRef.value.resetFields();
};

// 搜索变化时过滤
watch(searchText, (val) => {
    treeRef.value.filter(val);
});

getChapterList();
</script>

<style scoped>
:deep(.el-tree-node__content) {
    height: auto;
    padding: 4px 0;
}

:deep(.el-tree-node.is-current > .el-tree-node__content) {
    background-color: #f5f7fa;
}

:deep(.el-tree-node__expand-icon) {
    color: #909399;
}
</style>
