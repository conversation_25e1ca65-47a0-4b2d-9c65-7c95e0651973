<!-- 代码已包含 CSS：使用 TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 -->

<template>
    <div class="flex-1 flex flex-col bg-gray-50 overflow-auto">
        <!-- Header -->
        <div class="p-6 border-b border-gray-200">


            <el-button type="primary" class="!rounded-button whitespace-nowrap mb-4" @click="showAddDialog = true">
                <el-icon class="mr-1">
                    <Plus />
                </el-icon>
                新增排课
            </el-button>

            <div class="  flex justify-between items-center">
                <el-input v-model="searchQuery" placeholder="搜索教师/课程/班级..." class="w-64" clearable>
                    <template #prefix>
                        <el-icon>
                            <Search />
                        </el-icon>
                    </template>
                </el-input>
                <div class="flex space-x-2 ml-2">

                    <el-button class="!rounded-button whitespace-nowrap" @click="handleBatchDelete"
                        :disabled="selectedRows.length === 0">
                        <el-icon class="mr-1">
                            <Delete />
                        </el-icon>
                        批量删除
                    </el-button>
                </div>
            </div>

        </div>

        <!-- Main Content -->
        <div class="flex-1 overflow-auto p-6">

            <el-table :data="filteredSchedules" style="width: 100%" @selection-change="handleSelectionChange"
                :row-class-name="checkConflict">
                <el-table-column type="selection" width="55" />
                <el-table-column prop="teacherName" label="教师姓名" sortable />
                <el-table-column prop="courseName" label="课程名称" sortable />
                <el-table-column prop="className" label="班级" sortable />
                <el-table-column prop="time" label="上课时间" sortable>
                    <template #default="{ row }">
                        <div>
                            {{ row.weekDay }} {{ row.startTime }}-{{ row.endTime }}
                        </div>
                        <div class="text-xs text-gray-500">
                            {{ row.dateRange }}
                        </div>
                    </template>
                </el-table-column>
                <el-table-column prop="hours" label="课时数" sortable />
                <el-table-column label="操作" width="120">
                    <template #default="{ row }">
                        <div class="flex justify-center">
                            <el-button type="text" size="small" @click="editSchedule(row)">
                                编辑
                            </el-button>
                            <el-button type="text" size="small" class="text-red-500" @click="deleteSchedule(row)">
                                删除
                            </el-button>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <div class="shrink-0 flex justify-center mt-4">

            <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" layout="prev, pager, next"
                :total="totalItems" />
        </div>

        <!-- Add/Edit Dialog -->
        <el-dialog v-model="showAddDialog" :title="isEditing ? '编辑排课' : '新增排课'" width="600px">
            <el-form :model="scheduleForm" label-width="100px">
                <el-form-item label="教师" required>
                    <el-select v-model="scheduleForm.teacherId" placeholder="选择教师" class="w-full">
                        <el-option v-for="teacher in teachers" :key="teacher.id" :label="teacher.name"
                            :value="teacher.id" />
                    </el-select>
                </el-form-item>
                <el-form-item label="课程" required>
                    <el-select v-model="scheduleForm.courseId" placeholder="选择课程" class="w-full">
                        <el-option v-for="course in courses" :key="course.id" :label="course.name" :value="course.id" />
                    </el-select>
                </el-form-item>
                <el-form-item label="班级" required>
                    <el-select v-model="scheduleForm.classId" placeholder="选择班级" class="w-full">
                        <el-option v-for="classItem in classes" :key="classItem.id" :label="classItem.name"
                            :value="classItem.id" />
                    </el-select>
                </el-form-item>
                <el-form-item label="上课日期" required>
                    <el-date-picker v-model="scheduleForm.dateRange" type="daterange" range-separator="至"
                        start-placeholder="开始日期" end-placeholder="结束日期" class="w-full" />
                </el-form-item>
                <el-form-item label="星期" required>
                    <el-select v-model="scheduleForm.weekDay" placeholder="选择星期" class="w-full">
                        <el-option v-for="day in weekDays" :key="day.value" :label="day.label" :value="day.value" />
                    </el-select>
                </el-form-item>
                <el-form-item label="时间" required>
                    <el-time-picker v-model="scheduleForm.timeRange" is-range range-separator="至"
                        start-placeholder="开始时间" end-placeholder="结束时间" class="w-full" />
                </el-form-item>
                <el-form-item label="课时数" required>
                    <el-input-number v-model="scheduleForm.hours" :min="1" :max="8" class="w-full" />
                </el-form-item>
            </el-form>
            <template #footer>
                <el-button class="!rounded-button whitespace-nowrap" @click="showAddDialog = false">
                    取消
                </el-button>
                <el-button type="primary" class="!rounded-button whitespace-nowrap" @click="saveSchedule">
                    保存
                </el-button>
            </template>
        </el-dialog>
    </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue';
import { Plus, Search, Download, Delete } from '@element-plus/icons-vue';

// 模拟数据
const teachers = ref([
    { id: 1, name: '张明远' },
    { id: 2, name: '李静怡' },
    { id: 3, name: '王建国' },
    { id: 4, name: '刘芳华' },
    { id: 5, name: '陈志强' },
]);

const courses = ref([
    { id: 1, name: '高等数学' },
    { id: 2, name: '大学英语' },
    { id: 3, name: '计算机基础' },
    { id: 4, name: '数据结构' },
    { id: 5, name: '操作系统' },
]);

const classes = ref([
    { id: 1, name: '计算机2101' },
    { id: 2, name: '计算机2102' },
    { id: 3, name: '软件2101' },
    { id: 4, name: '软件2102' },
    { id: 5, name: '网络2101' },
]);

const weekDays = ref([
    { value: '周一', label: '周一' },
    { value: '周二', label: '周二' },
    { value: '周三', label: '周三' },
    { value: '周四', label: '周四' },
    { value: '周五', label: '周五' },
    { value: '周六', label: '周六' },
    { value: '周日', label: '周日' },
]);

const semesters = ref([
    { value: '2023-2024-1', label: '2023-2024学年第一学期' },
    { value: '2023-2024-2', label: '2023-2024学年第二学期' },
]);

const weeks = ref(
    Array.from({ length: 20 }, (_, i) => ({
        value: i + 1,
        label: `第 ${i + 1} 周`,
    }))
);

// 排课数据
const schedules = ref([
    {
        id: 1,
        teacherId: 1,
        teacherName: '张明远',
        courseId: 1,
        courseName: '高等数学',
        classId: 1,
        className: '计算机2101',
        weekDay: '周一',
        startTime: '08:00',
        endTime: '09:40',
        dateRange: '2023-09-04 至 2023-12-25',
        hours: 2,
    },
    {
        id: 2,
        teacherId: 2,
        teacherName: '李静怡',
        courseId: 2,
        courseName: '大学英语',
        classId: 2,
        className: '计算机2102',
        weekDay: '周二',
        startTime: '10:00',
        endTime: '11:40',
        dateRange: '2023-09-05 至 2023-12-26',
        hours: 2,
    },
    {
        id: 3,
        teacherId: 3,
        teacherName: '王建国',
        courseId: 3,
        courseName: '计算机基础',
        classId: 3,
        className: '软件2101',
        weekDay: '周三',
        startTime: '14:00',
        endTime: '15:40',
        dateRange: '2023-09-06 至 2023-12-27',
        hours: 2,
    },
    {
        id: 4,
        teacherId: 4,
        teacherName: '刘芳华',
        courseId: 4,
        courseName: '数据结构',
        classId: 4,
        className: '软件2102',
        weekDay: '周四',
        startTime: '08:00',
        endTime: '09:40',
        dateRange: '2023-09-07 至 2023-12-28',
        hours: 2,
    },
    {
        id: 5,
        teacherId: 5,
        teacherName: '陈志强',
        courseId: 5,
        courseName: '操作系统',
        classId: 5,
        className: '网络2101',
        weekDay: '周五',
        startTime: '10:00',
        endTime: '11:40',
        dateRange: '2023-09-08 至 2023-12-29',
        hours: 2,
    },
    {
        id: 6,
        teacherId: 1,
        teacherName: '张明远',
        courseId: 1,
        courseName: '高等数学',
        classId: 2,
        className: '计算机2102',
        weekDay: '周一',
        startTime: '10:00',
        endTime: '11:40',
        dateRange: '2023-09-04 至 2023-12-25',
        hours: 2,
    },
    {
        id: 7,
        teacherId: 2,
        teacherName: '李静怡',
        courseId: 2,
        courseName: '大学英语',
        classId: 1,
        className: '计算机2101',
        weekDay: '周二',
        startTime: '08:00',
        endTime: '09:40',
        dateRange: '2023-09-05 至 2023-12-26',
        hours: 2,
    },
]);

// 表单状态
const selectedSemester = ref('2023-2024-1');
const selectedWeek = ref(1);
const selectedTeachers = ref<number[]>([]);
const selectedCourses = ref<number[]>([]);
const selectedClasses = ref<number[]>([]);
const selectedTimeRange = ref<[Date, Date]>();
const searchQuery = ref('');
const currentPage = ref(1);
const pageSize = ref(10);
const selectedRows = ref<any[]>([]);
const showAddDialog = ref(false);
const isEditing = ref(false);
const currentScheduleId = ref(0);

const scheduleForm = ref({
    teacherId: 0,
    courseId: 0,
    classId: 0,
    weekDay: '',
    dateRange: [] as Date[],
    timeRange: [] as Date[],
    hours: 2,
});

// 计算属性
const filteredSchedules = computed(() => {
    let result = schedules.value;

    // 教师筛选
    if (selectedTeachers.value.length > 0) {
        result = result.filter((s) =>
            selectedTeachers.value.includes(s.teacherId)
        );
    }

    // 课程筛选
    if (selectedCourses.value.length > 0) {
        result = result.filter((s) =>
            selectedCourses.value.includes(s.courseId)
        );
    }

    // 班级筛选
    if (selectedClasses.value.length > 0) {
        result = result.filter((s) =>
            selectedClasses.value.includes(s.classId)
        );
    }

    // 搜索
    if (searchQuery.value) {
        const query = searchQuery.value.toLowerCase();
        result = result.filter(
            (s) =>
                s.teacherName.toLowerCase().includes(query) ||
                s.courseName.toLowerCase().includes(query) ||
                s.className.toLowerCase().includes(query)
        );
    }

    return result;
});

const totalItems = computed(() => filteredSchedules.value.length);

// 方法
const handleSelectionChange = (rows: any[]) => {
    selectedRows.value = rows;
};

const checkConflict = ({ row }: { row: any }) => {
    // 检查是否有时间冲突
    const hasConflict = schedules.value.some(
        (s) =>
            s.id !== row.id &&
            s.teacherId === row.teacherId &&
            s.weekDay === row.weekDay &&
            ((s.startTime <= row.startTime && s.endTime > row.startTime) ||
                (s.startTime < row.endTime && s.endTime >= row.endTime) ||
                (s.startTime >= row.startTime && s.endTime <= row.endTime))
    );

    return hasConflict ? 'conflict-row' : '';
};

const editSchedule = (schedule: any) => {
    isEditing.value = true;
    currentScheduleId.value = schedule.id;
    scheduleForm.value = {
        teacherId: schedule.teacherId,
        courseId: schedule.courseId,
        classId: schedule.classId,
        weekDay: schedule.weekDay,
        dateRange: [
            new Date(schedule.dateRange.split(' 至 ')[0]),
            new Date(schedule.dateRange.split(' 至 ')[1]),
        ],
        timeRange: [
            new Date(`2000-01-01T${schedule.startTime}:00`),
            new Date(`2000-01-01T${schedule.endTime}:00`),
        ],
        hours: schedule.hours,
    };
    showAddDialog.value = true;
};

const deleteSchedule = (schedule: any) => {
    schedules.value = schedules.value.filter((s) => s.id !== schedule.id);
};

const handleBatchDelete = () => {
    const ids = selectedRows.value.map((row) => row.id);
    schedules.value = schedules.value.filter((s) => !ids.includes(s.id));
    selectedRows.value = [];
};

const saveSchedule = () => {
    if (isEditing.value) {
        // 更新现有排课
        const index = schedules.value.findIndex(
            (s) => s.id === currentScheduleId.value
        );
        if (index !== -1) {
            const [startTime, endTime] = scheduleForm.value.timeRange;
            schedules.value[index] = {
                ...schedules.value[index],
                teacherId: scheduleForm.value.teacherId,
                teacherName:
                    teachers.value.find((t) => t.id === scheduleForm.value.teacherId)
                        ?.name || '',
                courseId: scheduleForm.value.courseId,
                courseName:
                    courses.value.find((c) => c.id === scheduleForm.value.courseId)
                        ?.name || '',
                classId: scheduleForm.value.classId,
                className:
                    classes.value.find((c) => c.id === scheduleForm.value.classId)
                        ?.name || '',
                weekDay: scheduleForm.value.weekDay,
                startTime: startTime.toTimeString().substring(0, 5),
                endTime: endTime.toTimeString().substring(0, 5),
                dateRange: `${scheduleForm.value.dateRange[0].toISOString().substring(0, 10)} 至 ${scheduleForm.value.dateRange[1].toISOString().substring(0, 10)}`,
                hours: scheduleForm.value.hours,
            };
        }
    } else {
        // 新增排课
        const [startTime, endTime] = scheduleForm.value.timeRange;
        const newSchedule = {
            id: schedules.value.length + 1,
            teacherId: scheduleForm.value.teacherId,
            teacherName:
                teachers.value.find((t) => t.id === scheduleForm.value.teacherId)
                    ?.name || '',
            courseId: scheduleForm.value.courseId,
            courseName:
                courses.value.find((c) => c.id === scheduleForm.value.courseId)
                    ?.name || '',
            classId: scheduleForm.value.classId,
            className:
                classes.value.find((c) => c.id === scheduleForm.value.classId)
                    ?.name || '',
            weekDay: scheduleForm.value.weekDay,
            startTime: startTime.toTimeString().substring(0, 5),
            endTime: endTime.toTimeString().substring(0, 5),
            dateRange: `${scheduleForm.value.dateRange[0].toISOString().substring(0, 10)} 至 ${scheduleForm.value.dateRange[1].toISOString().substring(0, 10)}`,
            hours: scheduleForm.value.hours,
        };
        schedules.value.push(newSchedule);
    }

    showAddDialog.value = false;
    resetForm();
};

const resetForm = () => {
    scheduleForm.value = {
        teacherId: 0,
        courseId: 0,
        classId: 0,
        weekDay: '',
        dateRange: [] as Date[],
        timeRange: [] as Date[],
        hours: 2,
    };
    isEditing.value = false;
    currentScheduleId.value = 0;
};

const applyFilters = () => {
    currentPage.value = 1;
};

const exportToExcel = () => {
    // 实际项目中这里应该调用导出API
    console.log('导出Excel');
};

onMounted(() => {
    // 初始化数据
});
</script>

<style scoped>
.conflict-row {
    background-color: #fff2f0;
}

.conflict-row:hover {
    background-color: #ffccc7 !important;
}

:deep(.conflict-row td) {
    border-bottom: 1px solid #ffa39e !important;
}

:deep(.el-table .conflict-row:hover > td) {
    background-color: #ffccc7 !important;
}
</style>
