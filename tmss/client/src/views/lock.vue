<template>
    <div class="fixed w-full h-full bg-cyan-700 z-[9999] ">
        <div class="mt-30 w-1/3 mx-auto p-10 bg-grary-700 rounded-lg shadow-lg flex flex-col gap-4 items-center">
            <h1 class="text-white text-2xl">系统已锁定</h1>
            <el-form :model="form" :rules="rules" ref="formRef">
                <el-form-item prop="password">
                    <el-input v-model="form.password" placeholder="请输入密码"></el-input>
                </el-form-item>
                <el-form-item>
                    <el-button class="w-full" type="primary" color="#4d5767" @click="unlock">解锁</el-button>
                </el-form-item>
            </el-form>
        </div>
    </div>
</template>

<script setup lang="ts">
import { useUserStore } from '@/store/user';
import { successMsg } from '@/utils/msg';
import { ref } from 'vue';

const store = useUserStore()
const form = ref({
    password: ''
})
const rules = ref({
    password: [
        { required: true, message: '请输入密码', trigger: 'blur' },
        { min: 6, max: 12, message: '长度在 6 到 12 个字符', trigger: 'blur' }
    ]
})
const formRef = ref()
const unlock = async () => {
    const valid = await formRef.value.validate()
    if (valid) {
        store.is_locked = false
        successMsg('解锁成功')
    }
}
</script>

<style scoped></style>