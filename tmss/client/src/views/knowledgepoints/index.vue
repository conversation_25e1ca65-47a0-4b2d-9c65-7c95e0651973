<template>
    <div class="flex-1 flex flex-col bg-gray-50 overflow-auto">
        <!-- 搜索区域 -->
        <div class="p-6 border-b border-gray-200">
            <div class="mb-4 flex justify-start items-center">
                <el-button type="primary" @click="openCreateDialog">新建知识点</el-button>
            </div>
            <div class="grid grid-cols-4 gap-4 flex-1">
                <!-- 知识点标题 -->
                <div>
                    <el-input v-model="query.title" placeholder="请输入知识点标题" clearable class="w-full" />
                </div>

                <!-- 状态 -->
                <div>
                    <el-select v-model="query.status" placeholder="请选择状态" clearable class="w-full">
                        <el-option v-for="(status, key) in statusMap" :key="key" :label="status" :value="key" />
                    </el-select>
                </div>

                <!-- 关联课时计划 -->
                <div>
                    <my-select v-model="query.courseware_id" :func="getCoursewareList" labelKey="title"
                        valueKey="id" placeholder="请选择课件" searchKey="title" />
                </div>

                <div>
                    <el-button type="primary" class="mr-2" @click="getData">搜索</el-button>
                    <el-button @click="resetSearch">重置</el-button>
                </div>
            </div>
        </div>

        <!-- 知识点表格 -->
        <div class="flex-1 overflow-auto p-6">
            <el-table :data="tableData" style="width: 100%" border v-loading="loading">
                <el-table-column prop="title" label="知识点标题" show-overflow-tooltip />
                <el-table-column prop="courseware_title" label="关联课件" show-overflow-tooltip />
                <el-table-column label="状态" width="100" align="center">
                    <template #default="{ row }">
                        <el-tag :type="tagTypeMap[row.status]">{{ statusMap[row.status] }}</el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="操作" width="280" align="center">
                    <template #default="{ row }">
                        <div class="flex justify-start">
                            <el-button size="small" @click="viewDetail(row)">查看</el-button>
                            <el-button size="small" v-if="canEdit(row)" type="primary"
                                @click="editItem(row)">编辑</el-button>
                            <el-button size="small" v-if="canDelete(row)" type="danger"
                                @click="deleteItem(row)">删除</el-button>
                            <ApproveButton v-if="canSubmit(row)" approve-code="knowledge_points" :data-id="row.id"
                                :data-title="row.title" @success="getData" />
                        </div>
                    </template>
                </el-table-column>
            </el-table>
        </div>

        <!-- 分页 -->
        <div class="shrink-0 flex justify-center mt-4">
            <el-pagination v-model:current-page="page" v-model:page-size="page_size" :total="total"
                layout="prev, pager, next" @current-change="handleCurrentChange" />
        </div>

        <!-- 创建/编辑弹窗 -->
        <el-dialog v-model="formDialogVisible" :title="formTitle" width="80%" :close-on-click-modal="false"
            :destroy-on-close="true">
            <el-form ref="formRef" :model="form" :rules="rules" label-width="120px" label-position="right">
                <!-- 知识点标题 -->
                <el-form-item label="知识点标题" prop="title">
                    <el-input v-model="form.title" placeholder="请输入知识点标题" />
                </el-form-item>
                <!-- 知识点内容 -->
                <el-form-item label="知识点内容" prop="content">
                    <Editor v-model="form.content" />
                    <!-- <el-input v-model="form.content" type="textarea" :rows="5" placeholder="请输入知识点内容" /> -->
                </el-form-item>

                <!-- 关联课件 -->
                <el-form-item label="关联课件" prop="courseware_id">
                    <my-select v-model="form.courseware_id" placeholder="请选择关联课件" :func="getCoursewareList"
                        labelKey="title" valueKey="id" searchKey="name" :initData="initCoursewareData" />
                </el-form-item>
                <el-form-item label="关联题库" prop="question_id">
                    <my-select v-model="form.question_id" placeholder="关联题库" :func="getQuestionList" labelKey="title"
                        valueKey="id" searchKey="name" :initData="initQuestionData" />
                </el-form-item>
                <el-form-item label="关联章节" prop="chapter_id">
                    <el-tree-select :check-strictly="true" :default-expand-all="true" v-model="form.chapter_id"
                        :data="chapters" :props="chapter_props" />
                </el-form-item>
            </el-form>

            <template #footer>
                <el-button @click="formDialogVisible = false">取消</el-button>
                <el-button type="primary" @click="submitForm">保存</el-button>
            </template>
        </el-dialog>

        <!-- 查看详情弹窗 -->
        <el-dialog v-model="detailDialogVisible" title="知识点详情" width="800px">
            <el-descriptions v-if="currentItem" :column="1" border>
                <!-- <el-descriptions-item label="知识点ID">{{ currentItem.id }}</el-descriptions-item> -->
                <el-descriptions-item label="知识点标题">{{ currentItem.title }}</el-descriptions-item>
                <el-descriptions-item label="关联课件">{{ currentItem.courseware_title || '无' }}</el-descriptions-item>
                <el-descriptions-item label="关联题库">{{ currentItem.question_title || '无'  }}</el-descriptions-item>
                <el-descriptions-item label="关联章节">{{ currentItem.chapter_title || '无'  }}</el-descriptions-item>
                <el-descriptions-item label="状态">
                    <el-tag :type="tagTypeMap[currentItem.status]">{{ statusMap[currentItem.status] }}</el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="知识点内容">
                    <!-- 安全提示：确保内容来源可信 -->
                    <div class="bg-gray-100 p-3 rounded" v-html="currentItem.content || '无'"></div>
                </el-descriptions-item>

                <!-- <el-descriptions-item label="创建时间">
                    {{ formatDate(currentItem.created_at) }}
                </el-descriptions-item> -->
                <el-descriptions-item label="最后更新时间">
                    {{ formatDate(currentItem.updated_at) }}
                </el-descriptions-item>
            </el-descriptions>
            <template #footer>
                <el-button @click="detailDialogVisible = false">关闭</el-button>
            </template>
        </el-dialog>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import {
    createKnowledgePoints,
    updateKnowledgePoints,
    deleteKnowledgePoints,
    getKnowledgePointssList,
    getKnowledgePointsDetail
} from '@/api/points';
import { chapterList } from '@/api/chapter';
import { getCoursewareList } from '@/api/courseware';
import { getQuestionList } from '@/api/question';
import { formatDate } from '@/utils/date.ts';
const chapter_props = {
    label: 'name',
    value: 'id'
}
// 表格数据
const tableData = ref<any[]>([]);
const loading = ref(false);
const page = ref(1);
const page_size = ref(10);
const total = ref(0);
const formRef = ref<any>(null);
const getChapters = async () => {
    const res = await chapterList()
    chapters.value = res.data || []
}
// 查询条件
const query = ref({
    title: '',
    status: '',
    courseware_id: ''
});

// 状态映射
const statusMap: Record<string, string> = {
    draft: '草稿',
    reviewing: '审核中',
    published: '已发布',
    rejected: '已驳回'
};

const tagTypeMap: Record<string, string> = {
    draft: 'primary',
    reviewing: 'warning',
    published: 'success',
    rejected: 'danger'
};

// 弹窗控制
const formDialogVisible = ref(false);
const detailDialogVisible = ref(false);
const currentItem = ref<any>(null);
const formTitle = ref('新建知识点');
const initQuestionData = ref<any[]>([]);
const initCoursewareData = ref<any[]>([]);
const chapters = ref<any[]>([]);
// 表单数据
const form: any = ref({
    id: '',
    title: '',
    content: '',
    question_id: '',
    courseware_id: ''
});

// 表单验证规则
const rules = ref({
    title: [{ required: true, message: '请输入知识点标题', trigger: 'blur' }],
    content: [{ required: true, message: '请输入知识点内容', trigger: 'blur' }]
});

// 获取知识点列表
const getData = async () => {
    loading.value = true;
    try {
        const params = {
            page: page.value,
            page_size: page_size.value,
            ...query.value
        };

        const res = await getKnowledgePointssList(params);
        tableData.value = res.list || [];
        total.value = res.total || 0;
    } catch (error) {
        ElMessage.error('获取数据失败');
    } finally {
        loading.value = false;
    }
};

// 分页切换
const handleCurrentChange = (val: number) => {
    page.value = val;
    getData();
};

// 重置查询
const resetSearch = () => {
    query.value = { title: '', status: '', courseware_id: '' };
    getData();
};

// 打开创建弹窗
const openCreateDialog = () => {
    form.value = { id: '', title: '', content: '', question_id: '', courseware_id: '' };
    initQuestionData.value = [];
    initCoursewareData.value = [];
    formTitle.value = '新建知识点';
    formDialogVisible.value = true;
};

// 编辑知识点
const editItem = async (row: any) => {
    try {
        const detail = await getKnowledgePointsDetail(row.id);
        form.value = {
            id: detail.id * 1,
            title: detail.title,
            content: detail.content,
            question_id: detail.question_id,
            courseware_id: detail.courseware_id,
            chapter_id: detail.chapter_id,
        };

        // 设置初始化的试题数据
        if (detail.question_id) {
            initQuestionData.value = [{
                id: detail.question_id,
                title: detail.class_schedule_title
            }];
        }
        // 设置初始化的课件数据
        if (detail.courseware_id) {
            initCoursewareData.value = [{ id: detail.courseware_id, title: detail.courseware_title }];
        }


        formTitle.value = '编辑知识点';
        formDialogVisible.value = true;
    } catch (error) {
        ElMessage.error('获取知识点详情失败');
    }
};

// 提交表单
const submitForm = async () => {
    try {
        await formRef.value.validate();
        form.value.question_id = form.value.question_id * 1;
        form.value.courseware_id = form.value.courseware_id * 1;
        form.value.id = form.value.id * 1;
        if (form.value.id) {
            await updateKnowledgePoints(form.value);
            ElMessage.success('知识点更新成功');
        } else {

            await createKnowledgePoints(form.value);
            ElMessage.success('知识点创建成功');
        }

        formDialogVisible.value = false;
        getData();
    } catch (error) {
        // 验证失败或API错误
    }
};

// 删除知识点
const deleteItem = (row: any) => {
    ElMessageBox.confirm('确定要删除该知识点吗？', '提示', {
        type: 'warning'
    }).then(async () => {
        try {
            await deleteKnowledgePoints(row.id);
            ElMessage.success('删除成功');
            getData();
        } catch (error) {
            ElMessage.error('删除失败');
        }
    });
};


// 查看详情
const viewDetail = async (row: any) => {
    try {
        const detail = await getKnowledgePointsDetail(row.id);
        currentItem.value = detail;
        detailDialogVisible.value = true;
    } catch (error) {
        ElMessage.error('获取知识点详情失败');
    }
};

// 权限判断
const canEdit = (row: any) => ['draft', 'rejected'].includes(row.status);
const canDelete = (row: any) => ['draft', 'rejected'].includes(row.status);
const canSubmit = (row: any) => ['draft', 'rejected'].includes(row.status);

// 初始化
onMounted(() => {
    getData();
    getChapters();
});
</script>

<style scoped>
.el-table {
    margin-top: 20px;
}

.el-form-item {
    margin-bottom: 20px;
}

.el-dialog__body {
    padding: 20px;
}
</style>