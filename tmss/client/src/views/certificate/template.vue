<template>
    <div class="flex-1 flex  bg-gray-50 overflow-hidden">
        <!-- 左侧模板列表区 -->
        <div class="w-64 border-r border-gray-200 bg-white overflow-y-auto">
            <div class="p-4 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-800">证书模板</h2>
            </div>
            <div class="space-y-4 p-4">
                <div v-for="(template, index) in templates" :key="index"
                    class="border border-gray-200 rounded-lg p-3 cursor-pointer hover:shadow-md transition-shadow"
                    :class="{ 'ring-2 ring-blue-500': activeTemplateIndex === index }" @click="selectTemplate(index)">
                    <div class="relative aspect-[4/3] bg-gray-100 rounded overflow-hidden mb-2">
                        <img :src="template.thumbnail" class="w-full h-full object-cover" alt="证书模板预览">
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm font-medium text-gray-700">{{ template.name }}</span>
                        <el-switch v-model="template.active" @change="toggleTemplateStatus(index)" />
                    </div>
                </div>
            </div>
        </div>

        <!-- 右侧设计工作区 -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- 顶部工具栏 -->
            <div class="h-16 border-b border-gray-200 bg-white flex items-center px-4 space-x-3">
                <el-button type="primary" class="!rounded-button whitespace-nowrap" @click="saveDesign">
                    <el-icon class="mr-1"><i class="fas fa-save"></i></el-icon>
                    保存设计
                </el-button>
            </div>

            <div class="flex-1 flex overflow-auto">
                <t-editor />
            </div>
        </div>

        <!-- 右键菜单 -->
        <div v-if="contextMenu.visible" class="fixed bg-white shadow-lg rounded-md py-1 z-50"
            :style="{ left: `${contextMenu.x}px`, top: `${contextMenu.y}px` }" @click.stop>
            <div class="px-4 py-2 hover:bg-gray-100 cursor-pointer" @click="copyElement(contextMenu.index)">
                <el-icon class="mr-2"><i class="fas fa-copy"></i></el-icon>
                复制
            </div>
            <div class="px-4 py-2 hover:bg-gray-100 cursor-pointer" @click="pasteElement">
                <el-icon class="mr-2"><i class="fas fa-paste"></i></el-icon>
                粘贴
            </div>
            <div class="px-4 py-2 hover:bg-gray-100 cursor-pointer" @click="deleteElement(contextMenu.index)">
                <el-icon class="mr-2"><i class="fas fa-trash"></i></el-icon>
                删除
            </div>
        </div>

        <!-- 预览弹窗 -->
        <!-- <el-dialog v-model="previewVisible" title="证书预览" width="70%">
            <div class="relative mx-auto bg-white shadow-lg"
                :style="{ width: `${canvasContainer?.clientWidth}px`, height: `${canvasContainer?.clientHeight}px`, backgroundImage: `url(${currentBorder})`, backgroundSize: 'cover', backgroundPosition: 'center' }">
                <div v-for="(element, index) in elements" :key="index" class="absolute" :style="{
                    left: `${element.x}px`,
                    top: `${element.y}px`,
                    width: element.type === 'text' ? '100%' : `${element.width}px`,
                    height: element.type === 'text' ? 'auto' : `${element.height}px`,
                    transform: `rotate(${element.rotate}deg)`,
                    opacity: element.opacity,
                    zIndex: element.zIndex
                }">
                    <div v-if="element.type === 'text'" class="p-1"
                        :style="{ fontFamily: element.fontFamily || 'SimSun', fontSize: `${element.fontSize || 16}px`, color: element.color || '#000000', textAlign: element.textAlign }">
                        {{ element.content }}
                    </div>
                    <img v-else-if="element.type === 'image'" :src="element.src" class="w-full h-full object-contain"
                        :style="{ borderRadius: `${element.borderRadius}px` }">
                </div>
            </div>
            <template #footer>
                <el-button @click="previewVisible = false">关闭</el-button>
                <el-button type="primary" @click="exportToPDF">导出PDF</el-button>
            </template>
</el-dialog> -->
    </div>
</template>

<script lang="ts" setup>
import { ref, reactive, nextTick } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';

const canvasContainer = ref<HTMLElement | null>(null);
const updateTextContent = (index: number, e: Event) => {
    // elements.value[index].content = newContent;
    console.log('change----')
};

// 尺寸选项
const sizeOptions = [
    { label: 'A4 (210x297mm)', value: 'a4', aspectRatio: 210 / 297 },
    { label: 'Letter (8.5x11in)', value: 'letter', aspectRatio: 8.5 / 11 },
    { label: 'Legal (8.5x14in)', value: 'legal', aspectRatio: 8.5 / 14 }
];

// 当前选中的尺寸
const selectedSize = ref('a4');
// 更新画布比例
const updateCanvasAspectRatio = () => {
    const selectedOption = sizeOptions.find(option => option.value === selectedSize.value);
    if (selectedOption && canvasContainer.value) {
        const aspectRatio = selectedOption.aspectRatio;
        const containerRect = canvasContainer.value.parentElement?.getBoundingClientRect();

        if (containerRect) {
            let width, height;

            // 按照容器宽度优先进行等比缩放
            width = containerRect.width - 48; // 留出 padding
            height = width / aspectRatio;

            // 如果高度超过容器高度，则按高度重新计算
            if (height > containerRect.height - 64) { // 减去顶部工具栏和底部padding
                height = containerRect.height - 64;
                width = height * aspectRatio;
            }

            canvasContainer.value.style.width = `${width}px`;
            canvasContainer.value.style.height = `${height}px`;
        }
    }
    console.log(canvasContainer.value?.clientWidth)
};

// 模板数据
const templates = ref([
    {
        name: '优秀员工证书',
        thumbnail: 'https://mastergo.com/ai/api/search-image?query=a professional certificate design with elegant golden border on white background, minimalist style, high resolution, clean layout&width=400&height=300&orientation=landscape&flag=56aca84974c3b9b01f512a6a6b344fed',
        active: true,
        elements: []
    },
    {
        name: '培训结业证书',
        thumbnail: 'https://mastergo.com/ai/api/search-image?query=a modern training certificate design with blue decorative border, professional look, clean white background, high quality&width=400&height=300&orientation=landscape&flag=77581e33a57376c87c9c13e1685a23c5',
        active: true,
        elements: []
    },
    {
        name: '荣誉证书',
        thumbnail: 'https://mastergo.com/ai/api/search-image?query=a prestigious award certificate with red ribbon and golden seal, classic design, elegant typography, high resolution&width=400&height=300&orientation=landscape&flag=9e49f260f0c346e98249e4d60378e61d',
        active: false,
        elements: []
    },
    {
        name: '参与证书',
        thumbnail: 'https://mastergo.com/ai/api/search-image?query=a participation certificate with colorful abstract border, creative design, modern look, white background&width=400&height=300&orientation=landscape&flag=683fc684d17f63b2fbb8cd7df60383c5',
        active: false,
        elements: []
    }
]);

const activeTemplateIndex = ref(0);
const elements = ref<Array<{
    type: string;
    x: number;
    y: number;
    width?: number;
    height?: number;
    rotate?: number;
    opacity?: number;
    zIndex: number;
    // 文字属性
    content?: string;
    fontFamily?: string;
    fontSize?: number;
    color?: string;
    textAlign?: string;
    // 图片属性
    src?: string;
    borderRadius?: number;
}>>([]);

const currentBorder = ref('https://mastergo.com/ai/api/search-image?query=a simple certificate border with golden decorative elements, elegant design, white background, high resolution&width=794&height=1123&orientation=portrait&flag=61b22394c60243b165654edee64b18de');

// 选择模板
const selectTemplate = (index: number) => {
    activeTemplateIndex.value = index;
    elements.value = [...templates.value[index].elements];
};

// 切换模板状态
const toggleTemplateStatus = (index: number) => {
    ElMessage.success(`模板 "${templates.value[index].name}" 已${templates.value[index].active ? '启用' : '停用'}`);
};

// 拖拽相关
const draggingIndex = ref<number | null>(null);
const dragOffset = reactive({ x: 0, y: 0 });

const startDrag = (index: number, e: MouseEvent) => {
    draggingIndex.value = index;
    const rect = (e.target as HTMLElement).getBoundingClientRect();
    dragOffset.x = e.clientX - rect.left;
    dragOffset.y = e.clientY - rect.top;
    document.addEventListener('mousemove', handleDrag);
    document.addEventListener('mouseup', stopDrag);
};

const handleDrag = (e: MouseEvent) => {
    if (draggingIndex.value !== null && canvasContainer.value) {
        const containerRect = canvasContainer.value.getBoundingClientRect();
        const element = elements.value[draggingIndex.value];
        element.x = e.clientX - dragOffset.x - containerRect.left;
        element.y = e.clientY - dragOffset.y - containerRect.top;
    }
};

const stopDrag = () => {
    document.removeEventListener('mousemove', handleDrag);
    document.removeEventListener('mouseup', stopDrag);
    draggingIndex.value = null;
};

const startDragNewElement = (type: string) => {
    const newElement = {
        type,
        x: 0,
        y: 0,
        zIndex: elements.value.length,
        opacity: 1,
        rotate: 0
    };

    if (type === 'text') {
        Object.assign(newElement, {
            content: '双击编辑文字',
            fontFamily: 'SimSun',
            fontSize: 16,
            color: '#000000',
            width: '100%',
            textAlign: 'center',
            height: 'auto'
        });
    } else if (type === 'image') {
        Object.assign(newElement, {
            src: 'https://mastergo.com/ai/api/search-image?query=a placeholder image for certificate design, transparent background, high resolution&width=200&height=200&orientation=squarish&flag=0116b6d0793b6bc6aeebec0b2fd1e64c',
            width: 200,
            height: 200,
            borderRadius: 0
        });
    }

    elements.value.push(newElement);
    selectedElementIndex.value = elements.value.length - 1;
};
const selectElement = (index: number) => {
    selectedElementIndex.value = index;
};

const handleDrop = (e: DragEvent) => {
    e.preventDefault();
};

// 元素属性编辑
const selectedElementIndex = ref<number | null>(null);

// 右键菜单
const contextMenu = reactive({
    visible: false,
    x: 0,
    y: 0,
    index: -1
});

const showContextMenu = (index: number, e: MouseEvent) => {
    contextMenu.visible = true;
    contextMenu.x = e.clientX;
    contextMenu.y = e.clientY;
    contextMenu.index = index;
    selectedElementIndex.value = index;

    document.addEventListener('click', hideContextMenu);
};

const hideContextMenu = () => {
    contextMenu.visible = false;
    document.removeEventListener('click', hideContextMenu);
};

// 复制粘贴
const clipboard = ref<any>(null);

const copyElement = (index: number) => {
    clipboard.value = JSON.parse(JSON.stringify(elements.value[index]));
    contextMenu.visible = false;
    ElMessage.success('元素已复制');
};

const pasteElement = () => {
    if (clipboard.value) {
        const newElement = JSON.parse(JSON.stringify(clipboard.value));
        newElement.x += 20;
        newElement.y += 20;
        newElement.zIndex = elements.value.length;
        elements.value.push(newElement);
        selectedElementIndex.value = elements.value.length - 1;
        contextMenu.visible = false;
        ElMessage.success('元素已粘贴');
    }
};

// 删除元素
const deleteElement = (index: number) => {
    ElMessageBox.confirm('确定要删除这个元素吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
        elements.value.splice(index, 1);
        if (selectedElementIndex.value === index) {
            selectedElementIndex.value = null;
        }
        ElMessage.success('元素已删除');
        contextMenu.visible = false;
    }).catch(() => { });
};

// 图片上传
const handleImageUpload = (file: File) => {
    if (selectedElementIndex.value !== null && elements.value[selectedElementIndex.value].type === 'image') {
        const reader = new FileReader();
        reader.onload = (e) => {
            elements.value[selectedElementIndex.value!].src = e.target?.result as string;
            ElMessage.success('图片上传成功');
        };
        reader.readAsDataURL(file);
    }
    return false;
};

// 边框上传
const handleBorderUpload = (file: File) => {
    const reader = new FileReader();
    reader.onload = (e) => {
        currentBorder.value = e.target?.result as string;
        ElMessage.success('边框上传成功');
    };
    reader.readAsDataURL(file);
    return false;
};

// 保存设计
const saveDesign = () => {
    // templates.value[activeTemplateIndex.value].elements = [...elements.value];
    ElMessage.success('设计已保存');
};

// 预览
const previewVisible = ref(false);

const previewCertificate = () => {
    previewVisible.value = true;
};

// 导出PDF
const exportToPDF = () => {
    ElMessage.info('导出PDF功能需要集成PDF生成库实现');
};

// 撤销/重做
const history = ref<any[]>([]);
const historyIndex = ref(-1);

const undoAction = () => {
    if (historyIndex.value > 0) {
        historyIndex.value--;
        elements.value = JSON.parse(JSON.stringify(history.value[historyIndex.value]));
    }
};

const redoAction = () => {
    if (historyIndex.value < history.value.length - 1) {
        historyIndex.value++;
        elements.value = JSON.parse(JSON.stringify(history.value[historyIndex.value]));
    }
};

// 初始化
const init = () => {
    elements.value = [...templates.value[0].elements];
    selectedSize.value = 'a4'; // 默认尺寸
    nextTick(() => {
        updateCanvasAspectRatio(); // 初始设置画布比例
    });
};

init();
</script>

<style scoped>
/* 自定义样式 */
:deep(.el-upload) {
    width: 100%;
}

:deep(.el-upload .el-button) {
    width: 100%;
}

:deep(.el-dialog__body) {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
}
</style>
