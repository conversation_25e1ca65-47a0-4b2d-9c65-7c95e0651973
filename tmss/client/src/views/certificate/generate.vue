<template>
    <div class="flex-1 flex flex-col bg-gray-50 overflow-auto">
        <!-- 顶部操作栏 -->
        <div>
            <div class=" mx-auto px-6 py-4 flex items-center justify-start">
                <div class="w-50 mr-10">
                    <el-select placeholder="请选择证书模板" v-model="templateId">
                        <el-option label="模板1" value="1"></el-option>
                        <el-option label="模板2" value="2"></el-option>
                    </el-select>
                </div>
                <div class="flex items-center space-x-4">
                    <el-button type="success" class="!rounded-button whitespace-nowrap">
                        <el-icon class="mr-2">
                            <Coin />
                        </el-icon>
                        全部生成
                    </el-button>
                    <el-button type="warning" class="!rounded-button whitespace-nowrap">
                        <el-icon class="mr-1">
                            <Printer />
                        </el-icon>
                        批量打印
                    </el-button>
                </div>
            </div>
        </div>

        <!-- 主内容区 -->
        <div class="px-6 py-4 flex flex-1 overflow-auto">
            <div class="w-full">
                <div class="bg-white rounded-lg shadow-sm p-4 mb-4">
                    <el-input placeholder="搜索学员姓名/ID" class="mb-4" clearable>
                        <template #prefix>
                            <el-icon><i class="fas fa-search"></i></el-icon>
                        </template>
                    </el-input>

                    <el-table :data="studentList" style="width: 100%">
                        <el-table-column type="selection" width="50"></el-table-column>
                        <el-table-column prop="name" label="学员姓名"></el-table-column>
                        <el-table-column prop="course" label="课程信息"></el-table-column>
                        <el-table-column prop="date" label="完成时间"></el-table-column>
                        <el-table-column label="生成状态" width="100">
                            <template #default="{ row }">
                                <el-tag :type="row.status === '已生成' ? 'success' : 'info'" size="small">
                                    {{ row.status }}
                                </el-tag>
                            </template>
                        </el-table-column>
                        <el-table-column label="操作" width="120">
                            <template #default="{ row }">
                                <div class="flex justify-center">
                                    <el-button size="small" @click="previewCertificate(row)">
                                        预览
                                    </el-button>
                                    <el-button size="small" :disabled="row.status !== '已生成'">
                                        生成
                                    </el-button>
                                </div>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
            </div>

            <!-- 证书预览区 -->
            <el-dialog v-model="showPreviewDialog" title="证书预览" width="70%">
                <div class="bg-white rounded-lg shadow-sm p-6">
                    <div class="mb-6">
                        <h3 class="text-lg font-medium text-gray-800 mb-2">当前预览学员: {{ currentStudent.name }}</h3>
                        <div class="text-sm text-gray-600">
                            <span class="mr-4">ID: {{ currentStudent.id }}</span>
                            <span class="mr-4">课程: {{ currentStudent.course }}</span>
                            <span>完成时间: {{ currentStudent.date }}</span>
                        </div>
                    </div>

                    <!-- 证书预览 -->
                    <div class="border-2 border-gray-200 rounded-lg p-8 bg-white shadow-inner">

                        <div class="certificate-container">
                            <div class="certificate border-2 border-gold-500 p-8 bg-white">
                                <div class="text-center mb-8">
                                    <h1 class="text-4xl font-bold text-blue-800 mb-2">结业证书</h1>
                                    <div class="w-32 h-1 bg-blue-500 mx-auto mb-6"></div>
                                    <p class="text-gray-600">兹证明以下学员已完成课程学习并达到结业要求</p>
                                </div>

                                <div class="text-center mb-10">
                                    <h2 class="text-2xl font-semibold text-gray-800 mb-4">{{ currentStudent.name }}</h2>
                                    <p class="text-gray-700 mb-2">已完成课程: {{ currentStudent.course }}</p>
                                    <p class="text-gray-700 mb-2">学员ID: {{ currentStudent.id }}</p>
                                    <p class="text-gray-700">完成日期: {{ currentStudent.date }}</p>
                                </div>

                                <div class="flex justify-end mt-16">
                                    <div class="text-center">
                                        <div class="w-24 h-24 mx-auto mb-2 border border-gray-300"></div>
                                        <p class="text-gray-700">颁发日期</p>
                                    </div>
                                </div>

                                <div class="mt-8 text-center">
                                    <p class="text-gray-500 text-sm">证书编号: {{ currentStudent.certificateId }}</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mt-6 flex justify-center space-x-4">
                        <el-button type="primary" class="!rounded-button whitespace-nowrap">
                            <el-icon class="mr-2"><i class="fas fa-cog"></i></el-icon>
                            生成此证书
                        </el-button>
                        <el-button type="success" class="!rounded-button whitespace-nowrap">
                            <el-icon class="mr-2"><i class="fas fa-download"></i></el-icon>
                            打印证书
                        </el-button>
                    </div>
                </div>
            </el-dialog>
        </div>

        <div class="mt-4 flex justify-center">
            <el-pagination layout="prev, pager, next" :total="10" :page-size="10"></el-pagination>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';

const templateId = ref('')
const showPreviewDialog = ref(false);

interface Student {
    id: string;
    name: string;
    course: string;
    date: string;
    status: string;
    certificateId: string;
}

const zoomLevel = ref(1);
const currentStudent = ref<Student>({
    id: 'S20230001',
    name: '张明远',
    course: '高级数据分析师认证课程',
    date: '2023-06-15',
    status: '已生成',
    certificateId: 'CER-2023-001-001'
});

const studentList = ref<Student[]>([
    {
        id: 'S20230001',
        name: '张明远',
        course: '高级数据分析师认证课程',
        date: '2023-06-15',
        status: '已生成',
        certificateId: 'CER-2023-001-001'
    },
    {
        id: 'S20230002',
        name: '李思雨',
        course: '人工智能基础课程',
        date: '2023-06-18',
        status: '未生成',
        certificateId: ''
    },
    {
        id: 'S20230003',
        name: '王建国',
        course: '云计算架构设计',
        date: '2023-06-20',
        status: '已生成',
        certificateId: 'CER-2023-001-003'
    },
    {
        id: 'S20230004',
        name: '赵晓婷',
        course: '大数据处理技术',
        date: '2023-06-22',
        status: '未生成',
        certificateId: ''
    },
    {
        id: 'S20230005',
        name: '刘伟',
        course: '区块链原理与应用',
        date: '2023-06-25',
        status: '已生成',
        certificateId: 'CER-2023-001-005'
    },
    {
        id: 'S20230006',
        name: '陈芳',
        course: '网络安全基础',
        date: '2023-06-28',
        status: '未生成',
        certificateId: ''
    },
    {
        id: 'S20230007',
        name: '周杰',
        course: 'Python高级编程',
        date: '2023-07-01',
        status: '已生成',
        certificateId: 'CER-2023-001-007'
    },
    {
        id: 'S20230008',
        name: '吴敏',
        course: '机器学习实战',
        date: '2023-07-05',
        status: '未生成',
        certificateId: ''
    },
    {
        id: 'S20230009',
        name: '郑强',
        course: 'DevOps实践',
        date: '2023-07-08',
        status: '已生成',
        certificateId: 'CER-2023-001-009'
    },
    {
        id: 'S20230010',
        name: '孙丽',
        course: '前端工程化',
        date: '2023-07-10',
        status: '未生成',
        certificateId: ''
    }
]);

const previewCertificate = (student: Student) => {
    showPreviewDialog.value = true;
    currentStudent.value = student;
};

const zoomIn = () => {
    if (zoomLevel.value < 1.5) {
        zoomLevel.value += 0.1;
    }
};

const zoomOut = () => {
    if (zoomLevel.value > 0.5) {
        zoomLevel.value -= 0.1;
    }
};

const resetZoom = () => {
    zoomLevel.value = 1;
};
</script>

<style scoped>
.certificate-container {
    margin: 0 auto;
}

.certificate {
    height: 100%;
    position: relative;
    background-image: url('https://mastergo.com/ai/api/search-image?query=a20light20gray20texture20background20with20subtle20patterns20for20a20professional20certificate20design20with20elegant20border20and20clean20layout20for20printing&width=794&height=1123&orientation=portrait&flag=3600b4747c7796610b11a836b1a42ef0');
    background-size: cover;
    background-position: center;
}

.border-gold-500 {
    border-color: #d4af37;
}
</style>
