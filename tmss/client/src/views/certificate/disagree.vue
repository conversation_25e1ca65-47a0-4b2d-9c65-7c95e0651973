<!-- 代码已包含 CSS：使用 TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 -->

<template>
    <div class="flex-1 flex flex-col bg-gray-50 overflow-auto">
        <!-- 顶部导航区 -->
        <div class="p-6 border-b border-gray-200">
            <div class="flex items-center space-x-4">
                <div class="relative">
                    <el-input type="text" placeholder="搜索学员姓名/证书编号..." />
                </div>
                <el-button class="!rounded-button whitespace-nowrap">
                    <el-icon class="mr-1">
                        <search />
                    </el-icon>
                    筛选
                </el-button>
            </div>
        </div>

        <!-- 主内容区 -->
        <div class="flex-1 overflow-auto p-6">
            <!-- 异议列表 -->
            <el-table :data="objectionList" style="width: 100%" border class="cursor-pointer">
                <el-table-column prop="submitTime" label="提交时间" sortable width="180" />
                <el-table-column prop="studentInfo" label="学员信息" width="200">
                    <template #default="{ row }">
                        <div class="flex items-center">
                            <div class="flex-shrink-0 h-10 w-10">
                                <el-avatar size="10" icon="UserFilled" />
                                <!-- <img class="h-10 w-10 rounded-full object-cover" :src="row.avatar" alt="" /> -->
                            </div>
                            <div class="ml-4">
                                <div class="text-sm font-medium text-gray-900">
                                    {{ row.studentInfo.name }}
                                </div>
                                <div class="text-sm text-gray-500">
                                    {{ row.studentInfo.id }}
                                </div>
                            </div>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column prop="certificateNo" label="证书编号" sortable width="180" />
                <el-table-column prop="type" label="异议类型" width="150" />
                <el-table-column prop="description" label="异议描述" />
                <el-table-column prop="status" label="处理状态" width="150">
                    <template #default="{ row }">
                        <el-tag :type="statusTagType(row.status)" effect="light" class="!rounded-button">
                            {{ row.status }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="操作" width="120">
                    <template #default="{ row }">
                        <el-button size="small" class="!rounded-button whitespace-nowrap" @click.stop="showDetail(row)">
                            处理
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>


        </div>
        <!-- 分页 -->
        <div class="flex justify-center items-center mt-4">
            <el-pagination v-model:current-page="currentPage" :page-size="pageSize" :total="total"
                layout="prev, pager, next" />
        </div>

        <!-- 详情面板 -->
        <el-drawer v-model="detailVisible" title="异议处理" direction="rtl" size="40%" :before-close="handleClose">
            <div v-if="currentObjection" class="px-4">
                <!-- 基本信息 -->
                <div class="mb-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">基本信息</h3>
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <p class="text-sm text-gray-500">学员姓名</p>
                            <p class="text-sm font-medium">{{ currentObjection.studentInfo.name }}</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">学员ID</p>
                            <p class="text-sm font-medium">{{ currentObjection.studentInfo.id }}</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">证书编号</p>
                            <p class="text-sm font-medium">{{ currentObjection.certificateNo }}</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">提交时间</p>
                            <p class="text-sm font-medium">{{ currentObjection.submitTime }}</p>
                        </div>
                    </div>
                </div>

                <!-- 异议详情 -->
                <div class="mb-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">异议详情</h3>
                    <div class="mb-4">
                        <p class="text-sm text-gray-500">异议类型</p>
                        <p class="text-sm font-medium">{{ currentObjection.type }}</p>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500">异议描述</p>
                        <p class="text-sm font-medium mt-1">{{ currentObjection.description }}</p>
                    </div>
                </div>

                <!-- 证明材料 -->
                <div class="mb-6" v-if="currentObjection.evidence.length > 0">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">证明材料</h3>
                    <div class="grid grid-cols-3 gap-2">
                        <div v-for="(item, index) in currentObjection.evidence" :key="index"
                            class="border rounded-md p-2 cursor-pointer hover:bg-gray-50" @click="previewImage(item)">
                            <img :src="item" alt="证明材料" class="w-full h-24 object-cover rounded" />
                        </div>
                    </div>
                </div>

                <!-- 处理区域 -->
                <div>
                    <h3 class="text-lg font-medium text-gray-900 mb-4">处理</h3>
                    <div class="space-y-4">
                        <div>
                            <p class="text-sm text-gray-500 mb-1">处理状态</p>
                            <el-select v-model="currentObjection.status" placeholder="请选择" class="w-full">
                                <el-option v-for="item in statusOptions" :key="item.value" :label="item.label"
                                    :value="item.value" />
                            </el-select>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500 mb-1">处理意见</p>
                            <el-input v-model="currentObjection.comment" :rows="4" type="textarea"
                                placeholder="请输入处理意见" />
                        </div>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="mt-8 flex justify-end space-x-3">
                    <el-button class="!rounded-button whitespace-nowrap" @click="detailVisible = false">
                        取消
                    </el-button>
                    <el-button type="primary" class="!rounded-button whitespace-nowrap" @click="saveHandler">
                        保存
                    </el-button>
                    <el-button type="success" class="!rounded-button whitespace-nowrap" @click="submitHandler">
                        提交
                    </el-button>
                </div>
            </div>
        </el-drawer>

        <!-- 图片预览 -->
        <el-dialog v-model="previewVisible" width="70%">
            <img :src="previewImageUrl" alt="预览" class="w-full" />
        </el-dialog>
    </div>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue';
import { ElMessageBox } from 'element-plus';
import { successMsg } from '@/utils/msg';

interface StudentInfo {
    name: string;
    id: string;
}

interface ObjectionItem {
    id: string;
    submitTime: string;
    studentInfo: StudentInfo;
    avatar: string;
    certificateNo: string;
    type: string;
    description: string;
    status: string;
    evidence: string[];
    comment?: string;
}

const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(50);
const detailVisible = ref(false);
const previewVisible = ref(false);
const previewImageUrl = ref('');
const currentObjection = ref<ObjectionItem | null>(null);

const statusOptions = [
    { value: '待处理', label: '待处理' },
    { value: '处理中', label: '处理中' },
    { value: '已解决', label: '已解决' },
    { value: '已驳回', label: '已驳回' },
    { value: '需补充材料', label: '需补充材料' },
];

const objectionList = ref<ObjectionItem[]>([
    {
        id: '1',
        submitTime: '2023-05-15 14:30',
        studentInfo: { name: '张伟', id: 'STU2023001' },
        avatar: 'https://mastergo.com/ai/api/search-image?query=professional asian male student portrait with neutral background&width=100&height=100&orientation=squarish&flag=764340021b514c540bf07bdc4315fbf8',
        certificateNo: 'CERT20230001',
        type: '个人信息错误',
        description: '证书上的姓名拼音有误，应为"Zhang Wei"而非"Zhang Wai"',
        status: '待处理',
        evidence: [
            'https://mastergo.com/ai/api/search-image?query=professional certificate document on white background&width=300&height=200&orientation=landscape&flag=c56ef24c9e7ecbd8d1cd47266b0df899',
            'https://mastergo.com/ai/api/search-image?query=student ID card on white background&width=300&height=200&orientation=landscape&flag=cdfd522c561bf027462a6535cac57eff',
        ],
    },
    {
        id: '2',
        submitTime: '2023-05-16 09:15',
        studentInfo: { name: '李娜', id: 'STU2023002' },
        avatar: 'https://mastergo.com/ai/api/search-image?query=professional asian female student portrait with neutral background&width=100&height=100&orientation=squarish&flag=da0f79a77496d9817eb1accbe8a43e69',
        certificateNo: 'CERT20230002',
        type: '证书内容错误',
        description: '课程名称显示不正确，应为"高级数据分析"而非"基础数据分析"',
        status: '处理中',
        evidence: [
            'https://mastergo.com/ai/api/search-image?query=professional certificate document on white background with error highlight&width=300&height=200&orientation=landscape&flag=f7939ccca77344352880ac95195eb018',
        ],
        comment: '已联系教务部门核实课程信息',
    },
    {
        id: '3',
        submitTime: '2023-05-10 11:20',
        studentInfo: { name: '王强', id: 'STU2023003' },
        avatar: 'https://mastergo.com/ai/api/search-image?query=professional asian male student portrait with neutral background&width=100&height=100&orientation=squarish&flag=ddf3253f23ec22ec698ffa6b8a6bafb6',
        certificateNo: 'CERT20230003',
        type: '证书未收到',
        description: '系统显示证书已发放，但至今未收到实体证书',
        status: '已解决',
        evidence: [
            'https://mastergo.com/ai/api/search-image?query=empty envelope on white background&width=300&height=200&orientation=landscape&flag=76e7422f50ff1154560ad79f777ea70a',
            'https://mastergo.com/ai/api/search-image?query=tracking information screenshot on white background&width=300&height=200&orientation=landscape&flag=a5773954dbcd4bda1a6e3e57383a3ac5',
        ],
        comment: '确认邮寄地址错误，已重新寄送并更新地址信息',
    },
    {
        id: '4',
        submitTime: '2023-05-18 16:45',
        studentInfo: { name: '陈美玲', id: 'STU2023004' },
        avatar: 'https://mastergo.com/ai/api/search-image?query=professional asian female student portrait with neutral background&width=100&height=100&orientation=squarish&flag=e2a7809ec21f0608d2cc8d24f612f0a0',
        certificateNo: 'CERT20230004',
        type: '成绩异议',
        description: '最终成绩与平时表现不符，申请复核',
        status: '需补充材料',
        evidence: [
            'https://mastergo.com/ai/api/search-image?query=exam paper with marks on white background&width=300&height=200&orientation=landscape&flag=7b6c34a5d6cca26e3b7449c2a19fde26',
        ],
        comment: '请提供平时作业和测验成绩记录',
    },
    {
        id: '5',
        submitTime: '2023-05-20 10:10',
        studentInfo: { name: '赵明', id: 'STU2023005' },
        avatar: 'https://mastergo.com/ai/api/search-image?query=professional asian male student portrait with neutral background&width=100&height=100&orientation=squarish&flag=64fc0fafcaff5e4956ab653192f501da',
        certificateNo: 'CERT20230005',
        type: '证书损坏',
        description: '收到的证书有折痕和污渍，影响使用',
        status: '待处理',
        evidence: [
            'https://mastergo.com/ai/api/search-image?query=damaged certificate document on white background&width=300&height=200&orientation=landscape&flag=8e8026133f253d405cdc09b1db00d56a',
        ],
    },
]);

const statusTagType = (status: string) => {
    switch (status) {
        case '待处理':
            return 'danger';
        case '处理中':
            return 'warning';
        case '已解决':
            return 'success';
        case '已驳回':
            return 'info';
        case '需补充材料':
            return '';
        default:
            return '';
    }
};

const showDetail = (row: ObjectionItem) => {
    currentObjection.value = { ...row };
    detailVisible.value = true;
};

const handleClose = (done: () => void) => {
    ElMessageBox.confirm('确定要关闭吗？未保存的更改将会丢失。')
        .then(() => {
            done();
        })
        .catch(() => { });
};

const previewImage = (url: string) => {
    previewImageUrl.value = url;
    previewVisible.value = true;
};

const saveHandler = () => {
    const index = objectionList.value.findIndex(
        (item) => item.id === currentObjection.value?.id
    );
    if (index !== -1 && currentObjection.value) {
        objectionList.value[index] = { ...currentObjection.value };
        successMsg('保存成功');
        detailVisible.value = false;
    }
};

const submitHandler = () => {
    if (currentObjection.value) {
        currentObjection.value.status = '已解决';
        saveHandler();
        successMsg('提交成功，状态已更新为"已解决"');
    }
};
</script>

<style scoped>
:deep(.el-table .cell) {
    padding-left: 12px;
    padding-right: 12px;
}

:deep(.el-drawer__header) {
    margin-bottom: 0;
    padding: 16px 20px;
    border-bottom: 1px solid #f0f0f0;
}

:deep(.el-drawer__body) {
    padding: 0;
}
</style>
