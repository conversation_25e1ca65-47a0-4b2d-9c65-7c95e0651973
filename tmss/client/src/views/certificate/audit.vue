<template>
    <div class="flex-1 flex flex-col bg-gray-50 overflow-auto p-6">

        <!-- Filter Section -->
        <div class="flex justify-start items-center gap-4 p-6 border-b border-gray-200">
            <!-- Date Range -->
            <div>
                <el-date-picker v-model="dateRange" type="daterange" range-separator="至" start-placeholder="开始日期"
                    end-placeholder="结束日期" class="w-full" />
            </div>

            <!-- Search -->
            <div>
                <div class="relative">
                    <el-input v-model="searchQuery" placeholder="搜索学员" class="w-full" @keyup.enter="applyFilters">
                        <template #prefix>
                            <el-icon class="el-input__icon"><i class="fas fa-search"></i></el-icon>
                        </template>
                    </el-input>
                </div>
            </div>
            <div class="flex justify-end">
                <button @click="resetFilters"
                    class="!rounded-button whitespace-nowrap mr-2 px-4 py-2 border border-gray-300 bg-white text-gray-700 hover:bg-gray-50">
                    重置
                </button>
                <button @click="applyFilters"
                    class="!rounded-button whitespace-nowrap px-4 py-2 bg-blue-600 text-white hover:bg-blue-700">
                    筛选
                </button>
            </div>
        </div>



        <!-- Application List -->
        <div class="mt-4 flex-1 overflow-auto ">
            <el-table :data="filteredApplications" style="width: 100%" v-loading="loading">
                <el-table-column label="学员信息" width="220">
                    <template #default="{ row }">
                        <div class="flex items-center">
                            <div class="flex-shrink-0 h-10 w-10">
                                <!-- <img class="h-10 w-10 rounded-full" :src="row.avatar" alt="" /> -->
                                <el-avatar icon="UserFilled" />
                            </div>
                            <div class="ml-4">
                                <div class="text-sm font-medium text-gray-900">{{ row.name }}</div>
                                <div class="text-sm text-gray-500">ID: {{ row.id }}</div>
                            </div>
                        </div>
                    </template>
                </el-table-column>

                <el-table-column prop="applyTime" label="申请时间">
                    <template #default="{ row }">
                        {{ formatDate(row.applyTime) }}
                    </template>
                </el-table-column>


                <el-table-column prop="status" label="申请状态">
                    <template #default="{ row }">
                        <el-tag :type="getStatusTagType(row.status)" size="small" effect="light">
                            {{ getStatusText(row.status) }}
                        </el-tag>
                    </template>
                </el-table-column>

                <el-table-column prop="score" label="考核成绩">
                    <template #default="{ row }">
                        <span :class="{ 'text-green-600': row.score >= 60, 'text-red-600': row.score < 60 }">
                            {{ row.score }}分
                        </span>
                    </template>
                </el-table-column>

                <el-table-column label="操作" width="220">
                    <template #default="{ row }">
                        <div class="flex space-x-2">
                            <!-- <button @click="showDetail(row)"
                                class="!rounded-button whitespace-nowrap px-3 py-1 text-sm border border-gray-300 bg-white text-gray-700 hover:bg-gray-50">
                                查看详情
                            </button> -->
                            <button @click="approveApplication(row)"
                                class="!rounded-button whitespace-nowrap px-3 py-1 text-sm bg-green-600 text-white hover:bg-green-700">
                                通过
                            </button>
                            <button @click="showRejectDialog(row)"
                                class="!rounded-button whitespace-nowrap px-3 py-1 text-sm bg-red-600 text-white hover:bg-red-700">
                                驳回
                            </button>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
        </div>

        <!-- Pagination -->
        <div class="mt-4 flex  justify-center">
            <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" layout=" prev, pager, next"
                :total="total" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
        </div>
        <!-- Detail Dialog -->
        <el-dialog v-model="detailDialogVisible" :title="`学员申请详情 - ${selectedApplication?.name}`" width="50%">
            <div v-if="selectedApplication" class="space-y-4">
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <h3 class="text-sm font-medium text-gray-500">学员信息</h3>
                        <div class="mt-2 flex items-center">
                            <el-avatar icon="UserFilled" />
                            <!-- <img class="h-16 w-16 rounded-full" :src="selectedApplication.avatar" alt="" /> -->
                            <div class="ml-4">
                                <p class="text-lg font-medium text-gray-900">{{ selectedApplication.name }}</p>
                                <p class="text-sm text-gray-500">ID: {{ selectedApplication.id }}</p>
                            </div>
                        </div>
                    </div>
                    <div>
                        <h3 class="text-sm font-medium text-gray-500">申请信息</h3>
                        <div class="mt-2 space-y-1">
                            <p class="text-sm">
                                <span class="text-gray-500">申请时间：</span>
                                {{ formatDate(selectedApplication.applyTime) }}
                            </p>
                            <p class="text-sm">
                                <span class="text-gray-500">证书类型：</span>
                                {{ selectedApplication.certType === 'course' ? '完成课程学习' : '考核达标' }}
                            </p>
                            <p class="text-sm">
                                <span class="text-gray-500">状态：</span>
                                <el-tag :type="getStatusTagType(selectedApplication.status)" size="small">
                                    {{ getStatusText(selectedApplication.status) }}
                                </el-tag>
                            </p>
                        </div>
                    </div>
                </div>

                <div class="border-t border-gray-200 pt-4">
                    <h3 class="text-sm font-medium text-gray-500">学习情况</h3>
                    <div class="mt-2 grid grid-cols-2 gap-4">
                        <div>
                            <p class="text-sm text-gray-500 mb-1">学习进度</p>
                            <el-progress :percentage="selectedApplication.progress"
                                :status="selectedApplication.progress === 100 ? 'success' : ''" :stroke-width="12" />
                        </div>
                        <div>
                            <p class="text-sm text-gray-500 mb-1">考核成绩</p>
                            <p
                                :class="{ 'text-green-600': selectedApplication.score >= 60, 'text-red-600': selectedApplication.score < 60 }">
                                {{ selectedApplication.score }}分
                            </p>
                        </div>
                    </div>
                </div>

                <div v-if="selectedApplication.status === 'rejected'" class="border-t border-gray-200 pt-4">
                    <h3 class="text-sm font-medium text-gray-500">驳回原因</h3>
                    <p class="mt-2 text-sm text-gray-700">{{ selectedApplication.rejectReason }}</p>
                </div>
            </div>
            <template #footer>
                <span class="dialog-footer">
                    <button @click="detailDialogVisible = false"
                        class="!rounded-button whitespace-nowrap px-4 py-2 bg-blue-600 text-white hover:bg-blue-700">
                        关闭
                    </button>
                </span>
            </template>
        </el-dialog>

        <!-- Reject Dialog -->
        <el-dialog v-model="rejectDialogVisible" title="驳回申请" width="40%">
            <el-form :model="rejectForm" label-width="100px">
                <el-form-item label="驳回原因" required>
                    <el-input v-model="rejectForm.reason" type="textarea" :rows="4" placeholder="请输入驳回原因" />
                </el-form-item>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <button @click="rejectDialogVisible = false"
                        class="!rounded-button whitespace-nowrap mr-2 px-4 py-2 border border-gray-300 bg-white text-gray-700 hover:bg-gray-50">
                        取消
                    </button>
                    <button @click="rejectApplication"
                        class="!rounded-button whitespace-nowrap px-4 py-2 bg-red-600 text-white hover:bg-red-700">
                        确认驳回
                    </button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';

// Mock data
const generateMockData = () => {
    const names = ['张伟', '王芳', '李娜', '刘洋', '陈晨', '杨光', '赵敏', '周杰', '吴磊', '郑爽'];
    const statuses = ['pending', 'approved', 'rejected'];
    const certTypes = ['course', 'exam'];
    const mockData = [];

    for (let i = 1; i <= 50; i++) {
        const status = statuses[Math.floor(Math.random() * statuses.length)];
        mockData.push({
            id: `STU${1000 + i}`,
            name: names[Math.floor(Math.random() * names.length)],
            avatar: `https://mastergo.com/ai/api/search-image?query=professional+student+headshot+on+light+grey+background&width=100&height=100&orientation=squarish&flag=b5714355b0d0b68ca0806260a14af77c`,
            applyTime: new Date(Date.now() - Math.floor(Math.random() * 30) * 24 * 60 * 60 * 1000),
            certType: certTypes[Math.floor(Math.random() * certTypes.length)],
            status,
            progress: Math.floor(Math.random() * 30) + 70,
            score: Math.floor(Math.random() * 40) + 60,
            rejectReason: status === 'rejected' ? '考核成绩未达到标准要求' : '',
        });
    }
    return mockData;
};

const applications = ref<any[]>([]);
const filteredApplications = ref<any[]>([]);
const loading = ref(false);
const dateRange = ref<[Date, Date]>();
const filterStatus = ref('all');
const filterType = ref('all');
const searchQuery = ref('');
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const detailDialogVisible = ref(false);
const selectedApplication = ref<any>(null);
const rejectDialogVisible = ref(false);
const rejectForm = ref({
    applicationId: '',
    reason: '',
});

// Format date
const formatDate = (date: Date) => {
    return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
    });
};

// Get status text
const getStatusText = (status: string) => {
    switch (status) {
        case 'pending': return '待审核';
        case 'approved': return '已通过';
        case 'rejected': return '已驳回';
        default: return status;
    }
};

// Get status tag type
const getStatusTagType = (status: string) => {
    switch (status) {
        case 'pending': return 'warning';
        case 'approved': return 'success';
        case 'rejected': return 'danger';
        default: return '';
    }
};

// Refresh list
const refreshList = () => {
    loading.value = true;
    setTimeout(() => {
        loading.value = false;
        ElMessage.success('列表已刷新');
    }, 800);
};

// Apply filters
const applyFilters = () => {
    loading.value = true;
    setTimeout(() => {
        let filtered = applications.value;

        // Filter by date range
        if (dateRange.value) {
            const [start, end] = dateRange.value;
            filtered = filtered.filter(app =>
                app.applyTime >= start && app.applyTime <= end
            );
        }

        // Filter by status
        if (filterStatus.value !== 'all') {
            filtered = filtered.filter(app => app.status === filterStatus.value);
        }

        // Filter by type
        if (filterType.value !== 'all') {
            filtered = filtered.filter(app => app.certType === filterType.value);
        }

        // Filter by search query
        if (searchQuery.value) {
            const query = searchQuery.value.toLowerCase();
            filtered = filtered.filter(app =>
                app.name.toLowerCase().includes(query) ||
                app.id.toLowerCase().includes(query)
            );
        }

        filteredApplications.value = filtered;
        total.value = filtered.length;
        loading.value = false;
    }, 800);
};

// Reset filters
const resetFilters = () => {
    dateRange.value = undefined;
    filterStatus.value = 'all';
    filterType.value = 'all';
    searchQuery.value = '';
    applyFilters();
};

// Handle page size change
const handleSizeChange = (val: number) => {
    pageSize.value = val;
    currentPage.value = 1;
};

// Handle current page change
const handleCurrentChange = (val: number) => {
    currentPage.value = val;
};

// Show detail dialog
const showDetail = (app: any) => {
    selectedApplication.value = app;
    detailDialogVisible.value = true;
};

// Approve application
const approveApplication = (app: any) => {
    ElMessageBox.confirm(
        `确认通过 ${app.name} 的证书申请吗？`,
        '确认通过',
        {
            confirmButtonText: '确认',
            cancelButtonText: '取消',
            type: 'warning',
        }
    ).then(() => {
        app.status = 'approved';
        ElMessage.success('已通过申请');
    }).catch(() => {
        // Cancel
    });
};

// Show reject dialog
const showRejectDialog = (app: any) => {
    rejectForm.value.applicationId = app.id;
    rejectForm.value.reason = '';
    rejectDialogVisible.value = true;
};

// Reject application
const rejectApplication = () => {
    if (!rejectForm.value.reason) {
        ElMessage.warning('请输入驳回原因');
        return;
    }

    const app = applications.value.find(a => a.id === rejectForm.value.applicationId);
    if (app) {
        app.status = 'rejected';
        app.rejectReason = rejectForm.value.reason;
        rejectDialogVisible.value = false;
        ElMessage.success('已驳回申请');
    }
};

// Initialize
onMounted(() => {
    loading.value = true;
    setTimeout(() => {
        applications.value = generateMockData();
        filteredApplications.value = applications.value;
        total.value = applications.value.length;
        loading.value = false;
    }, 1000);
});
</script>

<style scoped>
.el-table {
    margin-top: 16px;
}

.el-pagination {
    justify-content: flex-end;
}

.dialog-footer {
    display: flex;
    justify-content: flex-end;
}
</style>
