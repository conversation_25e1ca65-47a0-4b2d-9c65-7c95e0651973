<template>
    <div class="flex-1 flex flex-col bg-gray-50 overflow-auto p-6">
        <!-- 班级选择区域 -->
        <div class="mb-8 w-64">
            <el-select v-model="selectedClass" class="w-64 !rounded-button" placeholder="请选择班级">
                <el-option v-for="item in classes" :key="item.id" :label="item.name" :value="item.id"> </el-option>
            </el-select>
        </div>
        <!-- 学员列表区域 -->
        <div class="flex-1 overflow-auto">
            <el-table :data="students" stripe style="width: 100%"
                :default-sort="{ prop: 'completion', order: 'descending' }">
                <el-table-column prop="name" label="学员姓名" />
                <el-table-column prop="completion" label="章节完成率">
                    <template #default="scope">
                        <el-progress :percentage="scope.row.completion"
                            :color="getProgressColor(scope.row.completion)" />
                    </template>
                </el-table-column>
                <el-table-column prop="duration" label="学习时长">
                    <template #default="scope">
                        {{ scope.row.duration }}h
                    </template>
                </el-table-column>
                <el-table-column prop="lastStudyTime" label="最近学习时间" />
                <el-table-column label="操作" width="150">
                    <template #default="scope">
                        <el-button type="primary" class="!rounded-button whitespace-nowrap"
                            @click="viewDetail(scope.row)">
                            查看详情
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>

        </div>
        <!-- 分页控制 -->
        <div class="mt-4 flex items-center justify-center">
            <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :total="students.length"
                layout="prev, pager, next" class="!rounded-button" />
        </div>
    </div>
</template>
<script lang="ts" setup>
import { ref } from 'vue';
import { ElMessage } from 'element-plus';

const dateRange = ref([]);
const selectedClass = ref(1);
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(100);
const classes = ref([
    { id: 1, name: '一班', studentCount: 32, duration: 16 },
    { id: 2, name: '二班', studentCount: 28, duration: 12 },
    { id: 3, name: '三班', studentCount: 35, duration: 24 },
    { id: 4, name: '四班', studentCount: 30, duration: 16 },
    { id: 5, name: '五班', studentCount: 25, duration: 20 },
]);
const overview = ref({
    avgCompletion: 85,
    avgDuration: 45.5,
    activeStudents: 28,
    totalChapters: 24,
});
const students = ref([
    {
        name: '赵雨晨',
        completion: 95,
        duration: 68.5,
        lastStudyTime: '2024-01-15 15:30',
    },
    {
        name: '林思远',
        completion: 88,
        duration: 52.3,
        lastStudyTime: '2024-01-15 14:20',
    },
    {
        name: '孙梦琪',
        completion: 76,
        duration: 43.8,
        lastStudyTime: '2024-01-15 10:45',
    },
    {
        name: '周子涵',
        completion: 92,
        duration: 61.2,
        lastStudyTime: '2024-01-15 09:15',
    },
    {
        name: '吴云帆',
        completion: 83,
        duration: 48.6,
        lastStudyTime: '2024-01-14 17:40',
    },
]);
const selectClass = (classId: number) => {
    selectedClass.value = classId;
};
const refreshData = () => {
    ElMessage.success('数据已更新');
};
const exportData = () => {
    ElMessage.success('数据导出成功');
};
const viewDetail = (student: any) => {
    ElMessage.info(`查看 ${student.name} 的详细学习记录`);
};
const getProgressColor = (percentage: number) => {
    if (percentage >= 90) return '#67C23A';
    if (percentage >= 70) return '#409EFF';
    return '#E6A23C';
};
</script>
<style scoped>
.scrollbar-hide::-webkit-scrollbar {
    display: none;
}

.scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
}

:deep(.el-table) {
    --el-table-border-color: #ebeef5;
    --el-table-header-bg-color: #f5f7fa;
}

:deep(.el-progress-bar__outer) {
    border-radius: 4px;
}

:deep(.el-date-editor.el-input__wrapper) {
    background-color: white;
}
</style>