<template>
    <div class=" bg-gray-50 overflow-auto">

        <!-- Filter Area -->
        <div class=" mx-auto px-4 py-6 sm:px-6 lg:px-8 border-b border-gray-200 mt-4 ">
            <el-button type="primary" @click="ruleDialogVisible = true" class="!rounded-button whitespace-nowrap mb-4">
                <el-icon class="mr-2">
                    <Plus />
                </el-icon>
                新建规则
            </el-button>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                    <el-select class="w-full" placeholder="请选择学生" clearable>
                        <el-option label="张三" value="1" />
                        <el-option label="李四" value="2" />
                        <el-option label="王五" value="3" />
                    </el-select>
                </div>
                <div>
                    <el-select class="w-full" placeholder="请选择班级" clearable>
                        <el-option label="计算机科学 2023" value="1" />
                        <el-option label="人工智能 2023" value="2" />
                        <el-option label="数据科学 2023" value="3" />
                    </el-select>
                </div>
                <div>
                    <el-select class="w-full" placeholder="请选择课程" clearable>
                        <el-option label="数据结构与算法" value="1" />
                        <el-option label="机器学习基础" value="2" />
                        <el-option label="深度学习应用" value="3" />
                    </el-select>
                </div>
                <div>
                    <el-date-picker class="w-full" type="daterange" range-separator="至" start-placeholder="开始日期"
                        end-placeholder="结束日期" />
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class=" mx-auto px-4 py-6 sm:px-6 lg:px-8">
            <!-- Progress Rules -->
            <div class="bg-white rounded-lg shadow-sm mb-6">
                <el-collapse v-model="activeCollapse" accordion>
                    <el-collapse-item name="rules">
                        <template #title>
                            <div class="pl-4 flex items-center">
                                <el-icon class="mr-2">
                                    <Document />
                                </el-icon>
                                <span class="font-medium">进度提醒规则设置</span>
                            </div>
                        </template>
                        <div class="p-4">
                            <el-table :data="rules" border style="width: 100%">
                                <el-table-column prop="name" label="规则名称" />
                                <el-table-column prop="condition" label="触发条件">
                                    <template #default="{ row }">
                                        <div>{{ row.condition }}</div>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="method" label="提醒方式" />
                                <el-table-column prop="status" label="状态" width="100">
                                    <template #default="{ row }">
                                        <el-tag :type="row.status === '启用' ? 'success' : 'info'">{{ row.status
                                            }}</el-tag>
                                    </template>
                                </el-table-column>
                                <el-table-column label="操作" width="180">
                                    <template #default="{ row }">
                                        <div class="flex justify-center">
                                            <el-button size="small" type="text" @click="editRule(row)">编辑</el-button>
                                            <el-button size="small" type="text" @click="toggleRuleStatus(row)">
                                                {{ row.status === '启用' ? '停用' : '启用' }}
                                            </el-button>
                                            <el-button size="small" type="text" class="text-red-500"
                                                @click="deleteRule(row)">删除</el-button>
                                        </div>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </div>
                    </el-collapse-item>
                </el-collapse>
            </div>

            <!-- Notification Records -->
            <div class="bg-white rounded-lg shadow-sm mb-6">
                <el-collapse v-model="activeCollapse" accordion>
                    <el-collapse-item name="notifications">
                        <template #title>
                            <div class="pl-4 flex items-center">
                                <el-icon class="mr-2">
                                    <Bell />
                                </el-icon>
                                <span class="font-medium">提醒记录管理</span>
                            </div>
                        </template>
                        <div class="p-4">
                            <div class="flex justify-between mb-4">
                                <div class="flex space-x-4">
                                    <el-select v-model="notificationFilter.status" placeholder="发送状态" clearable>
                                        <el-option label="已发送" value="sent" />
                                        <el-option label="未发送" value="pending" />
                                        <el-option label="发送失败" value="failed" />
                                    </el-select>
                                </div>
                                <el-button size="small" @click="refreshNotifications">刷新</el-button>
                            </div>
                            <el-table :data="filteredNotifications" border style="width: 100%">
                                <el-table-column prop="time" label="提醒时间" width="180" sortable />
                                <el-table-column prop="ruleName" label="规则名称" width="180" />
                                <el-table-column prop="target" label="触发对象" width="180" />
                                <el-table-column prop="content" label="提醒内容" />
                                <el-table-column prop="status" label="发送状态" width="120">
                                    <template #default="{ row }">
                                        <el-tag :type="getStatusTagType(row.status)">{{ row.status }}</el-tag>
                                    </template>
                                </el-table-column>
                                <el-table-column label="操作" width="150">
                                    <template #default="{ row }">
                                        <el-button v-if="row.status !== '已发送'" size="small" type="text"
                                            @click="resendNotification(row)">
                                            重发
                                        </el-button>
                                        <el-button size="small" type="text" class="text-red-500"
                                            @click="deleteNotification(row)">
                                            删除
                                        </el-button>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </div>
                    </el-collapse-item>
                </el-collapse>
            </div>

            <!-- Progress Overview -->
            <div class="bg-white rounded-lg shadow-sm">
                <el-collapse v-model="activeCollapse" accordion>
                    <el-collapse-item name="progress">
                        <template #title>
                            <div class="pl-4 flex items-center">
                                <el-icon class="mr-2">
                                    <DataLine />
                                </el-icon>
                                <span class="font-medium">进度查看</span>
                            </div>
                        </template>
                        <div class="p-4">
                            <!-- Key Metrics -->
                            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                                <div class="bg-blue-50 p-4 rounded-lg">
                                    <div class="text-sm text-gray-500 mb-1">总体完成率</div>
                                    <div class="text-2xl font-bold">78%</div>
                                </div>
                                <div class="bg-green-50 p-4 rounded-lg">
                                    <div class="text-sm text-gray-500 mb-1">平均学习时长</div>
                                    <div class="text-2xl font-bold">12.5 小时</div>
                                </div>
                                <div class="bg-purple-50 p-4 rounded-lg">
                                    <div class="text-sm text-gray-500 mb-1">预警学员数</div>
                                    <div class="text-2xl font-bold">23</div>
                                </div>
                                <div class="bg-yellow-50 p-4 rounded-lg">
                                    <div class="text-sm text-gray-500 mb-1">平均章节完成</div>
                                    <div class="text-2xl font-bold">6.8/10</div>
                                </div>
                            </div>

                            <!-- Progress Chart -->
                            <div class="mb-6">
                                <div class="flex justify-between items-center mb-4">
                                    <h3 class="font-medium min-w-18">进度趋势</h3>
                                    <el-select v-model="chartRange" class="w-40">
                                        <el-option label="最近7天" value="7" />
                                        <el-option label="最近30天" value="30" />
                                        <el-option label="最近90天" value="90" />
                                    </el-select>
                                </div>
                                <div ref="chart" class="w-full h-120"></div>
                            </div>

                            <!-- Detailed Progress -->
                            <div>
                                <div class="flex justify-between items-center mb-4">
                                    <h3 class="font-medium min-w-18">详细进度</h3>
                                    <el-input v-model="progressSearch" placeholder="搜索学员或课程" class="w-64" clearable>
                                        <template #prefix>
                                            <el-icon>
                                                <Search />
                                            </el-icon>
                                        </template>
                                    </el-input>
                                </div>
                                <el-table :data="filteredProgress" border style="width: 100%">
                                    <el-table-column prop="student" label="学员" width="180" sortable />
                                    <el-table-column prop="class" label="班级" width="180" />
                                    <el-table-column prop="course" label="课程" width="180" />
                                    <el-table-column prop="progress" label="章节完成率" width="150" sortable>
                                        <template #default="{ row }">
                                            <el-progress :percentage="row.progress"
                                                :color="getProgressColor(row.progress)" />
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="hours" label="学习时长(小时)" width="150" sortable />
                                    <el-table-column prop="lastActive" label="最后学习时间" width="180" sortable />
                                    <el-table-column label="操作" width="120">
                                        <template #default="{ row }">
                                            <el-button size="small" type="text" @click="viewDetails(row)">详情</el-button>
                                        </template>
                                    </el-table-column>
                                </el-table>
                            </div>
                        </div>
                    </el-collapse-item>
                </el-collapse>
            </div>
        </div>

        <!-- Rule Dialog -->
        <el-dialog v-model="ruleDialogVisible" :title="ruleDialogTitle" width="50%">
            <el-form :model="ruleForm" label-width="120px">
                <el-form-item label="规则名称" required>
                    <el-input v-model="ruleForm.name" placeholder="请输入规则名称" />
                </el-form-item>
                <el-form-item label="触发条件" required>
                    <div class="flex items-center space-x-2">
                        <el-select v-model="ruleForm.conditionType" class="w-32">
                            <el-option label="章节完成率" value="progress" />
                            <el-option label="学习时长" value="hours" />
                        </el-select>
                        <el-select v-model="ruleForm.conditionOperator" class="w-24">
                            <el-option label="<" value="lt" />
                            <el-option label=">" value="gt" />
                            <el-option label="=" value="eq" />
                        </el-select>
                        <el-input-number v-model="ruleForm.conditionValue" :min="0" :max="100" />
                        <span v-if="ruleForm.conditionType === 'progress'">%</span>
                        <span v-else>小时</span>
                    </div>
                    <div class="mt-2 flex items-center space-x-2">
                        <span>且</span>
                        <el-select v-model="ruleForm.timeCondition" class="w-40">
                            <el-option label="已过课程周期" value="coursePeriod" />
                            <el-option label="距截止日期" value="deadline" />
                        </el-select>
                        <el-select v-model="ruleForm.timeOperator" class="w-24">
                            <el-option label="<" value="lt" />
                            <el-option label=">" value="gt" />
                            <el-option label="=" value="eq" />
                        </el-select>
                        <el-input-number v-model="ruleForm.timeValue" :min="0" :max="100" />
                        <span>%</span>
                    </div>
                </el-form-item>
                <el-form-item label="提醒方式" required>
                    <el-checkbox-group v-model="ruleForm.methods">
                        <el-checkbox label="系统消息" value="message" />
                        <el-checkbox label="邮件" value="email" />
                        <el-checkbox label="短信" value="sms" />
                    </el-checkbox-group>
                </el-form-item>
                <el-form-item label="接收对象" required>
                    <el-radio-group v-model="ruleForm.target">
                        <el-radio label="学生" value="student" />
                        <el-radio label="教师" value="teacher" />
                        <el-radio label="两者" value="both" />
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="状态">
                    <el-switch v-model="ruleForm.status" active-text="启用" inactive-text="停用" />
                </el-form-item>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="ruleDialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="saveRule">保存</el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, nextTick, watch } from 'vue';
import { Plus, Download, Document, Bell, DataLine, Search } from '@element-plus/icons-vue';
import * as echarts from 'echarts';

// Collapse control
const activeCollapse = ref(['rules', 'notifications', 'progress']);

// Progress Rules
const rules = ref([
    {
        id: 1,
        name: '进度预警规则1',
        condition: '章节完成率 < 50% 且 已过课程周期 60%',
        method: '系统消息, 邮件',
        status: '启用',
    },
    {
        id: 2,
        name: '进度预警规则2',
        condition: '学习时长 < 10 小时 且 距截止日期 < 30%',
        method: '短信',
        status: '停用',
    },
    {
        id: 3,
        name: '优秀学员提醒',
        condition: '章节完成率 > 90% 且 已过课程周期 < 80%',
        method: '系统消息',
        status: '启用',
    },
]);

const ruleDialogVisible = ref(false);
const ruleDialogTitle = ref('新建规则');
const ruleForm = ref({
    id: null,
    name: '',
    conditionType: 'progress',
    conditionOperator: 'lt',
    conditionValue: 50,
    timeCondition: 'coursePeriod',
    timeOperator: 'gt',
    timeValue: 60,
    methods: ['message'],
    target: 'student',
    status: true,
});

const editRule = (rule: any) => {
    ruleDialogTitle.value = '编辑规则';
    ruleForm.value = {
        id: rule.id,
        name: rule.name,
        conditionType: rule.condition.includes('章节完成率') ? 'progress' : 'hours',
        conditionOperator: rule.condition.includes('<') ? 'lt' : rule.condition.includes('>') ? 'gt' : 'eq',
        conditionValue: parseInt(rule.condition.match(/\d+/)[0]),
        timeCondition: rule.condition.includes('已过课程周期') ? 'coursePeriod' : 'deadline',
        timeOperator: rule.condition.split('且')[1].includes('<') ? 'lt' : 'gt',
        timeValue: parseInt(rule.condition.split('且')[1].match(/\d+/)[0]),
        methods: rule.method.split(',').map((m: string) => m.trim().toLowerCase().replace(' ', '')),
        target: rule.method.includes('教师') ? 'teacher' : 'student',
        status: rule.status === '启用',
    };
    ruleDialogVisible.value = true;
};

const toggleRuleStatus = (rule: any) => {
    rule.status = rule.status === '启用' ? '停用' : '启用';
};

const deleteRule = (rule: any) => {
    rules.value = rules.value.filter((r) => r.id !== rule.id);
};

const saveRule = () => {
    const conditionText = `${ruleForm.value.conditionType === 'progress' ? '章节完成率' : '学习时长'} ${ruleForm.value.conditionOperator === 'lt' ? '<' : ruleForm.value.conditionOperator === 'gt' ? '>' : '='
        } ${ruleForm.value.conditionValue}${ruleForm.value.conditionType === 'progress' ? '%' : '小时'} 且 ${ruleForm.value.timeCondition === 'coursePeriod' ? '已过课程周期' : '距截止日期'
        } ${ruleForm.value.timeOperator === 'lt' ? '<' : '>'} ${ruleForm.value.timeValue}%`;

    const methodText = ruleForm.value.methods
        .map((m: string) => {
            if (m === 'message') return '系统消息';
            if (m === 'email') return '邮件';
            return '短信';
        })
        .join(', ');

    if (ruleForm.value.id) {
        const index = rules.value.findIndex((r) => r.id === ruleForm.value.id);
        if (index !== -1) {
            rules.value[index] = {
                id: ruleForm.value.id,
                name: ruleForm.value.name,
                condition: conditionText,
                method: methodText,
                status: ruleForm.value.status ? '启用' : '停用',
            };
        }
    } else {
        const newId = Math.max(...rules.value.map((r) => r.id), 0) + 1;
        rules.value.push({
            id: newId,
            name: ruleForm.value.name,
            condition: conditionText,
            method: methodText,
            status: ruleForm.value.status ? '启用' : '停用',
        });
    }
    ruleDialogVisible.value = false;
};

// Notification Records
const notifications = ref([
    {
        id: 1,
        time: '2023-06-15 09:30',
        ruleName: '进度预警规则1',
        target: '张三 (计算机科学 2023)',
        content: '您的章节完成率低于50%，请加快学习进度',
        status: '已发送',
    },
    {
        id: 2,
        time: '2023-06-16 14:15',
        ruleName: '进度预警规则1',
        target: '李四 (人工智能 2023)',
        content: '您的章节完成率低于50%，请加快学习进度',
        status: '已发送',
    },
    {
        id: 3,
        time: '2023-06-17 10:00',
        ruleName: '优秀学员提醒',
        target: '王五 (数据科学 2023)',
        content: '您的学习进度优秀，继续保持',
        status: '已发送',
    },
    {
        id: 4,
        time: '2023-06-18 16:45',
        ruleName: '进度预警规则2',
        target: '赵六 (计算机科学 2023)',
        content: '您的学习时长不足10小时，请增加学习时间',
        status: '发送失败',
    },
]);

const notificationFilter = ref({
    status: '',
});

const filteredNotifications = computed(() => {
    return notifications.value.filter((n) => {
        if (notificationFilter.value.status) {
            return (
                (notificationFilter.value.status === 'sent' && n.status === '已发送') ||
                (notificationFilter.value.status === 'pending' && n.status === '未发送') ||
                (notificationFilter.value.status === 'failed' && n.status === '发送失败')
            );
        }
        return true;
    });
});

const getStatusTagType = (status: string) => {
    switch (status) {
        case '已发送':
            return 'success';
        case '未发送':
            return 'warning';
        case '发送失败':
            return 'danger';
        default:
            return 'info';
    }
};

const resendNotification = (notification: any) => {
    notification.status = '已发送';
};

const deleteNotification = (notification: any) => {
    notifications.value = notifications.value.filter((n) => n.id !== notification.id);
};

const refreshNotifications = () => {
    // In a real app, this would fetch new data from the server
};

// Progress Overview
const progressData = ref([
    {
        id: 1,
        student: '张三',
        class: '计算机科学 2023',
        course: '数据结构与算法',
        progress: 65,
        hours: 15,
        lastActive: '2023-06-18 14:30',
    },
    {
        id: 2,
        student: '李四',
        class: '人工智能 2023',
        course: '机器学习基础',
        progress: 42,
        hours: 8,
        lastActive: '2023-06-17 10:15',
    },
    {
        id: 3,
        student: '王五',
        class: '数据科学 2023',
        course: '深度学习应用',
        progress: 88,
        hours: 20,
        lastActive: '2023-06-18 16:45',
    },
    {
        id: 4,
        student: '赵六',
        class: '计算机科学 2023',
        course: '数据结构与算法',
        progress: 35,
        hours: 6,
        lastActive: '2023-06-16 09:20',
    },
    {
        id: 5,
        student: '钱七',
        class: '人工智能 2023',
        course: '机器学习基础',
        progress: 72,
        hours: 14,
        lastActive: '2023-06-18 11:10',
    },
    {
        id: 6,
        student: '孙八',
        class: '数据科学 2023',
        course: '深度学习应用',
        progress: 91,
        hours: 22,
        lastActive: '2023-06-18 18:30',
    },
]);

const progressSearch = ref('');
const filteredProgress = computed(() => {
    const search = progressSearch.value.toLowerCase();
    return progressData.value.filter(
        (p) =>
            p.student.toLowerCase().includes(search) ||
            p.class.toLowerCase().includes(search) ||
            p.course.toLowerCase().includes(search)
    );
});

const getProgressColor = (progress: number) => {
    if (progress < 40) return '#f56c6c';
    if (progress < 70) return '#e6a23c';
    return '#67c23a';
};

const viewDetails = (student: any) => {
    // In a real app, this would navigate to a detailed view
    console.log('View details for:', student);
};

// Chart
const chart = ref();
const chartRange = ref('30');
let chartInstance: echarts.ECharts;

onMounted(() => {
    initChart();
});

const initChart = () => {
    nextTick(() => {
        chartInstance = echarts.init(chart.value, {
            width: 'auto',
            height: 'auto'
        });
        updateChart();
    });
};

const updateChart = () => {
    const days = parseInt(chartRange.value);
    const dates = [];
    const progressData = [];
    const hoursData = [];

    for (let i = days; i >= 0; i--) {
        const date = new Date();
        date.setDate(date.getDate() - i);
        dates.push(`${date.getMonth() + 1}/${date.getDate()}`);
        progressData.push(Math.round(Math.random() * 30 + 50));
        hoursData.push(Math.round(Math.random() * 5 + 5));
    }

    const option = {
        animation: false,
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'cross',
                label: {
                    backgroundColor: '#6a7985',
                },
            },
        },
        legend: {
            data: ['平均完成率', '平均学习时长'],
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true,
        },
        xAxis: [
            {
                type: 'category',
                boundaryGap: false,
                data: dates,
            },
        ],
        yAxis: [
            {
                type: 'value',
                name: '完成率(%)',
                min: 0,
                max: 100,
                axisLabel: {
                    formatter: '{value}%',
                },
            },
            {
                type: 'value',
                name: '时长(小时)',
                min: 0,
                max: 15,
                axisLabel: {
                    formatter: '{value}',
                },
            },
        ],
        series: [
            {
                name: '平均完成率',
                type: 'line',
                smooth: true,
                data: progressData,
                itemStyle: {
                    color: '#409EFF',
                },
                areaStyle: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        {
                            offset: 0,
                            color: 'rgba(64, 158, 255, 0.2)',
                        },
                        {
                            offset: 1,
                            color: 'rgba(64, 158, 255, 0)',
                        },
                    ]),
                },
            },
            {
                name: '平均学习时长',
                type: 'line',
                yAxisIndex: 1,
                smooth: true,
                data: hoursData,
                itemStyle: {
                    color: '#67C23A',
                },
                areaStyle: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        {
                            offset: 0,
                            color: 'rgba(103, 194, 58, 0.2)',
                        },
                        {
                            offset: 1,
                            color: 'rgba(103, 194, 58, 0)',
                        },
                    ]),
                },
            },
        ],
    };

    chartInstance.setOption(option);
};

// Watch for chart range changes
watch(chartRange, () => {
    updateChart();
});
</script>

<style scoped>
.el-collapse-item__header {
    font-size: 16px;
    font-weight: 500;
}

.el-collapse-item__content {
    padding-bottom: 0;
}

.el-table {
    margin-top: 16px;
}

.el-progress {
    display: flex;
    align-items: center;
}

.el-progress :deep(.el-progress__text) {
    margin-left: 10px;
    min-width: 36px;
}
</style>
