<template>
    <div class="flex-1 flex flex-col bg-gray-50 overflow-auto">
        <!-- 列表区域 -->
        <div class="flex-1 overflow-auto p-6">
            <el-table :data="tableData" v-loading="loading" style="width: 100%" border>
                <el-table-column align="center" label="试题标题" prop="title" show-overflow-tooltip />
                <el-table-column prop="type" label="试题类型" align="center" width="100">
                    <template #default="{ row }">
                        <el-text :type="tagMap[row.question_type]" size="small" class="!rounded-xl whitespace-nowrap">
                            {{ typeMap[row.question_type] }}
                        </el-text>
                    </template>
                </el-table-column>
                <el-table-column prop="created_at" label="创建时间" align="center">
                    <template #default="{ row }">
                        {{ formatDate(row.created_at) }}
                    </template>
                </el-table-column>
                <el-table-column label="操作" align="center" width="240">
                    <template #default="{ row }">
                        <div class="flex justify-center">
                            <el-button plain @click="view(row)">查看</el-button>
                            <el-button type="primary" plain @click="review(row.id)">通过</el-button>
                            <el-button type="danger" plain @click="reject(row.id)">驳回</el-button>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
        </div>

        <!-- 分页区域 -->
        <div class="shrink-0 flex justify-center mt-4 pb-2">
            <el-pagination background v-model:current-page="page" v-model:page-size="pageSize" :total="total"
                layout=" prev, pager, next" @current-change="handleCurrentChange" />
        </div>

        <el-dialog v-model="dialogVisible" title="查看详情" width="800px">

            <el-form ref="formRef" :model="question" label-width="100px" label-position="left" disabled>
                <el-form-item label="试题类型" prop="questions.question_type">
                    <el-select v-model="question.questions.question_type" placeholder="请选择试题类型">
                        <el-option v-for="item in Object.keys(typeMap)" :key="item" :value="item"
                            :label="typeMap[item]" />
                    </el-select>
                </el-form-item>
                <el-form-item label="试题标题" prop="questions.title">
                    <el-input v-model="question.questions.title" placeholder="请输入试题名称" />
                </el-form-item>

                <el-form-item label="试题内容" prop="questions.content">
                    <el-input v-model="question.questions.content" type="textarea" :rows="4" placeholder="请输入试题内容" />
                    <el-button v-if="question.questions.question_type === 'fill'" type="primary" size="small"
                        style="margin-top: 8px;">
                        插入空格标识符
                    </el-button>
                </el-form-item>

                <el-form-item label="试题答案" prop="questions.answer">
                    <template
                        v-if="question.questions.question_type === 'single' || question.questions.question_type === 'multiple'">
                        <div class="space-y-2 w-full">
                            <div v-for="(option, index) in question.questions.options" :key="index"
                                class="flex items-center">
                                <el-input v-model="option.content" :placeholder="`选项 ${index + 1}`" class="mr-2" />

                                <el-checkbox v-model="option.is_correct" />
                            </div>

                        </div>
                    </template>

                    <template v-else-if="question.questions.question_type === 'judge'">
                        <el-radio-group v-model="question.questions.answer">
                            <el-radio :value="0">错</el-radio>
                            <el-radio :value="1">对</el-radio>
                        </el-radio-group>
                    </template>

                    <el-input v-else-if="question.questions.question_type === 'fill'"
                        v-model="question.questions.answer" type="textarea" :rows="2" placeholder="请输入填空答案，多个空用 | 分隔" />

                    <el-input v-else v-model="question.questions.answer" type="textarea" :rows="4"
                        placeholder="请输入答案" />
                </el-form-item>

                <el-form-item label="关联课件" prop="courseware_ids">
                    <my-select v-model="question.courseware_ids" multiple placeholder="请选择关联课件" labelKey="title"
                        valueKey="id" searchKey="name" :initData="init_coursewares" />
                </el-form-item>

                <el-form-item label="关联章节" prop="chapter_ids">
                    <el-tree-select :check-strictly="true" :default-expand-all="true" multiple
                        v-model="question.chapter_ids" :data="chapters" :props="chapter_props" />
                </el-form-item>
            </el-form>
        </el-dialog>
    </div>
</template>

<script setup lang="ts">
import { chapterList } from '@/api/chapter';
import { getQuestionDetail, getQuestionReviewingList, rejectQuestion, reviewQuestion } from '@/api/question';
import { confirmMsg, successMsg } from '@/utils/msg';
import { onMounted, ref, watch } from 'vue';

const page = ref(1)
const pageSize = ref(10)
const total = ref(0)
const handleCurrentChange = (val: number) => {
    page.value = val
    getData()
}
const loading = ref(false)
const tableData = ref([])


const formatDate = (date: any) => {
    return new Date(date * 1000).toLocaleDateString().replace(/\//g, '-').split('-').map((item: any) => item.padStart(2, '0')).join('-');
};
const getData = async () => {
    loading.value = true

    const res = await getQuestionReviewingList({ page: page.value })
    loading.value = false
    tableData.value = res.list || []
    total.value = res.total || 0
}
const init_coursewares = ref([])
const setQuestion = (res: any) => {
    init_coursewares.value = res.coursewares
    return {
        questions: {
            ...res.question,
            answer: JSON.parse(res.question.answer),
            options: res.question.options ? JSON.parse(res.question.options) : []
        },
        courseware_ids: res.coursewares.map((item: any) => item.id),
        chapter_ids: res.chapters.map((item: any) => item.id),
    }
}
const view = async (row: any) => {
    const res = await getQuestionDetail(row.id)
    question.value = setQuestion(res)
    dialogVisible.value = true
}
const reject = (id: any) => {
    confirmMsg('确定要驳回吗？', '提示', async (action) => {
        if (action) {
            const res = await rejectQuestion(id)
            successMsg(res.message)
            getData()
        }
    })
}
const review = (id: any) => {
    confirmMsg('确定要通过吗？', '提示', async (action) => {
        if (action) {
            const res = await reviewQuestion(id)
            successMsg(res.message)
            getData()
        }
    })
}
// dialog

const initQuestion = () => {
    return {
        questions: {
            title: '',
            question_type: '', // 单选题（single）、多选题（multiple）、判断题（judge）、填空题（fill）、简答题（short）
            content: '',
            answer: '',
            options: [
                {
                    id: 1, // 选项ID，填空题和简答题不需要此字段
                    content: '', // 选项内容，填空题和简答题不需要此字段
                    is_correct: false, // 是否正确答案，仅适用于单选和多选题
                },
            ], // 仅适用于单选和多选题
        },
        courseware_ids: [], // 关联的课件ID列表，组卷的时候应用
        chapter_ids: [], //关联章节ID，学员前台训练的时候应用
    }
}
const question: any = ref(initQuestion())
const dialogVisible = ref(false)



watch(dialogVisible, (val) => {
    if (!val) {

        question.value = initQuestion()
    }
})

const tagMap: Record<string, string> = {
    'single': 'primary',
    'multiple': 'success',
    'judge': 'warning',
    'fill': 'info',
    'short': 'danger'
}

const typeMap: Record<string, string> = {
    'single': '单选题',
    'multiple': '多选题',
    'judge': '判断题',
    'fill': '填空题',
    'short': '简答题'
}



const chapters = ref([])
const getChapters = async () => {
    const res = await chapterList()
    chapters.value = res.data || []
}
const chapter_props = {
    label: 'name',
    value: 'id'
}
onMounted(() => {
    getChapters()
    getData()
})
</script>