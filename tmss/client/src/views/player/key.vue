<!-- 代码已包含 CSS：使用 TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 -->

<template>
    <div class="flex-1 flex flex-col bg-gray-50 overflow-auto">
        <!-- Content Area -->

        <!-- 顶部操作区 -->
        <div class="p-6 border-b border-gray-200">
            <div class="flex justify-start items-center">
                <el-button type="primary" class="!rounded-button whitespace-nowrap" @click="showAddKeyDialog">
                    <el-icon class="mr-1">
                        <Plus />
                    </el-icon>新增密钥
                </el-button>
            </div>
        </div>
        <!-- User Key Management -->
        <div class="flex-1 overflow-auto p-6">

            <el-table :data="filteredKeys" style="width: 100%" border @selection-change="handleSelectionChange">
                <el-table-column type="selection" width="55" />
                <el-table-column prop="username" label="用户名" sortable />
                <el-table-column prop="keyId" label="密钥ID" />
                <el-table-column prop="createTime" label="创建时间" sortable />
                <el-table-column prop="expiryDate" label="有效期" />
                <el-table-column prop="status" label="状态" width="120">
                    <template #default="{ row }">
                        <el-tag :type="row.status === 'active' ? 'success' : 'danger'">
                            {{ row.status === 'active' ? '激活' : '禁用' }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="操作" width="180">
                    <template #default="{ row }">
                        <div class="flex justify-center">
                            <el-button size="small" type="text" @click="viewKeyDetails(row)">
                                查看
                            </el-button>
                            <el-button size="small" type="text" @click="editKey(row)">
                                编辑
                            </el-button>
                            <el-button size="small" type="text" @click="deleteKey(row)">
                                删除
                            </el-button>
                        </div>
                    </template>
                </el-table-column>
            </el-table>


        </div>
        <div class="shrink-0 flex justify-center mt-4">
            <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :total="filteredKeys.length"
                layout=" prev, pager, next" />
        </div>
    </div>

    <!-- Add/Edit Key Dialog -->
    <el-dialog v-model="keyDialogVisible" :title="isEditMode ? '编辑用户密钥' : '新增用户密钥'" width="600px">
        <el-form :model="keyForm" label-width="120px" label-position="left">
            <el-form-item label="用户名" required>
                <el-select v-model="keyForm.username" filterable remote reserve-keyword placeholder="请输入用户名"
                    :remote-method="searchUsers" :loading="userSearchLoading" class="w-full">
                    <el-option v-for="item in userOptions" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>

            <el-form-item label="密钥算法" required>
                <el-select v-model="keyForm.algorithm" placeholder="请选择密钥算法" class="w-full">
                    <el-option label="SM4" value="sm4" />
                    <el-option label="AES-256" value="aes256" />
                    <el-option label="RSA-2048" value="rsa2048" />
                </el-select>
            </el-form-item>

            <el-form-item label="有效期" required>
                <div class="flex items-center">
                    <el-input-number v-model="keyForm.validityPeriod" :min="1" :max="365" class="w-32" />
                    <el-select v-model="keyForm.validityUnit" class="ml-2 w-32">
                        <el-option label="天" value="day" />
                        <el-option label="月" value="month" />
                        <el-option label="年" value="year" />
                    </el-select>
                </div>
            </el-form-item>

            <el-form-item label="密钥用途">
                <el-input v-model="keyForm.purpose" type="textarea" :rows="2" placeholder="请输入密钥用途描述" />
            </el-form-item>
        </el-form>

        <template #footer>
            <el-button class="!rounded-button whitespace-nowrap" @click="keyDialogVisible = false">取消</el-button>
            <el-button type="primary" class="!rounded-button whitespace-nowrap" @click="saveKey">
                {{ isEditMode ? '保存修改' : '创建密钥' }}
            </el-button>
        </template>
    </el-dialog>

    <!-- Key Details Dialog -->
    <el-dialog v-model="keyDetailsVisible" title="密钥详情" width="600px">
        <div class="space-y-4">
            <div class="flex">
                <span class="w-32 text-gray-500">用户名:</span>
                <span>{{ currentKey.username }}</span>
            </div>
            <div class="flex">
                <span class="w-32 text-gray-500">密钥ID:</span>
                <span>{{ currentKey.keyId }}</span>
            </div>
            <div class="flex">
                <span class="w-32 text-gray-500">算法:</span>
                <span>{{ currentKey.algorithm }}</span>
            </div>
            <div class="flex">
                <span class="w-32 text-gray-500">创建时间:</span>
                <span>{{ currentKey.createTime }}</span>
            </div>
            <div class="flex">
                <span class="w-32 text-gray-500">有效期至:</span>
                <span>{{ currentKey.expiryDate }}</span>
            </div>
            <div class="flex">
                <span class="w-32 text-gray-500">状态:</span>
                <el-tag :type="currentKey.status === 'active' ? 'success' : 'danger'">
                    {{ currentKey.status === 'active' ? '激活' : '禁用' }}
                </el-tag>
            </div>
            <div class="flex">
                <span class="w-32 text-gray-500">用途:</span>
                <span>{{ currentKey.purpose }}</span>
            </div>
        </div>

        <template #footer>
            <el-button type="primary" class="!rounded-button whitespace-nowrap"
                @click="keyDetailsVisible = false">关闭</el-button>
        </template>
    </el-dialog>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';


// User Key Management
interface KeyItem {
    username: string;
    keyId: string;
    createTime: string;
    expiryDate: string;
    status: string;
    algorithm: string;
    purpose: string;
}

const mockKeys: KeyItem[] = [
    {
        username: '张伟',
        keyId: 'KEY-2023-001',
        createTime: '2023-01-15 10:30',
        expiryDate: '2024-01-15',
        status: 'active',
        algorithm: 'SM4',
        purpose: '个人课件解密'
    },
    {
        username: '李娜',
        keyId: 'KEY-2023-002',
        createTime: '2023-02-20 14:15',
        expiryDate: '2023-08-20',
        status: 'active',
        algorithm: 'AES-256',
        purpose: '敏感数据加密'
    },
    {
        username: '王芳',
        keyId: 'KEY-2023-003',
        createTime: '2023-03-05 09:45',
        expiryDate: '2023-09-05',
        status: 'active',
        algorithm: 'RSA-2048',
        purpose: '数字签名验证'
    },
    {
        username: '赵明',
        keyId: 'KEY-2023-004',
        createTime: '2023-04-10 16:20',
        expiryDate: '2023-10-10',
        status: 'disabled',
        algorithm: 'SM4',
        purpose: '测试环境使用'
    },
    {
        username: '刘强',
        keyId: 'KEY-2023-005',
        createTime: '2023-05-18 11:10',
        expiryDate: '2024-05-18',
        status: 'active',
        algorithm: 'AES-256',
        purpose: '生产数据加密'
    },
    {
        username: '陈静',
        keyId: 'KEY-2023-006',
        createTime: '2023-06-22 13:25',
        expiryDate: '2023-12-22',
        status: 'active',
        algorithm: 'SM4',
        purpose: '客户资料保护'
    },
    {
        username: '杨光',
        keyId: 'KEY-2023-007',
        createTime: '2023-07-30 15:40',
        expiryDate: '2024-01-30',
        status: 'disabled',
        algorithm: 'RSA-2048',
        purpose: '临时访问权限'
    },
    {
        username: '周慧',
        keyId: 'KEY-2023-008',
        createTime: '2023-08-12 10:05',
        expiryDate: '2024-02-12',
        status: 'active',
        algorithm: 'AES-256',
        purpose: '财务数据加密'
    }
];

const keys = ref<KeyItem[]>(mockKeys);
const searchQuery = ref('');
const currentPage = ref(1);
const pageSize = ref(10);
const selectedKeys = ref<KeyItem[]>([]);

const filteredKeys = computed(() => {
    return keys.value.filter(key => {
        const query = searchQuery.value.toLowerCase();
        return (
            key.username.toLowerCase().includes(query) ||
            key.keyId.toLowerCase().includes(query)
        );
    });
});

const handleSelectionChange = (val: KeyItem[]) => {
    selectedKeys.value = val;
};

const keyDialogVisible = ref(false);
const isEditMode = ref(false);
const keyForm = ref({
    username: '',
    algorithm: 'sm4',
    validityPeriod: 30,
    validityUnit: 'day',
    purpose: ''
});

const userOptions = ref<{ value: string, label: string }[]>([]);
const userSearchLoading = ref(false);

const searchUsers = (query: string) => {
    if (query) {
        userSearchLoading.value = true;
        setTimeout(() => {
            userOptions.value = [
                { value: '张伟', label: '张伟 (<EMAIL>)' },
                { value: '李娜', label: '李娜 (<EMAIL>)' },
                { value: '王芳', label: '王芳 (<EMAIL>)' },
                { value: '赵明', label: '赵明 (<EMAIL>)' },
                { value: '刘强', label: '刘强 (<EMAIL>)' }
            ].filter(item => item.label.toLowerCase().includes(query.toLowerCase()));
            userSearchLoading.value = false;
        }, 500);
    } else {
        userOptions.value = [];
    }
};

const showAddKeyDialog = () => {
    isEditMode.value = false;
    keyForm.value = {
        username: '',
        algorithm: 'sm4',
        validityPeriod: 30,
        validityUnit: 'day',
        purpose: ''
    };
    keyDialogVisible.value = true;
};

const keyDetailsVisible = ref(false);
const currentKey = ref<KeyItem>({
    username: '',
    keyId: '',
    createTime: '',
    expiryDate: '',
    status: '',
    algorithm: '',
    purpose: ''
});

const viewKeyDetails = (key: KeyItem) => {
    currentKey.value = { ...key };
    keyDetailsVisible.value = true;
};

const editKey = (key: KeyItem) => {
    isEditMode.value = true;
    keyForm.value = {
        username: key.username,
        algorithm: key.algorithm,
        validityPeriod: 30,
        validityUnit: 'day',
        purpose: key.purpose
    };
    keyDialogVisible.value = true;
};

const deleteKey = (key: KeyItem) => {
    ElMessageBox.confirm(
        `确定要删除用户 ${key.username} 的密钥 ${key.keyId} 吗？`,
        '删除确认',
        {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
        }
    ).then(() => {
        keys.value = keys.value.filter(k => k.keyId !== key.keyId);
        ElMessage.success('密钥已删除');
    }).catch(() => {
        // Cancel
    });
};

const saveKey = () => {
    if (isEditMode.value) {
        ElMessage.success('密钥信息已更新');
    } else {
        const newKey: KeyItem = {
            username: keyForm.value.username,
            keyId: `KEY-${new Date().getFullYear()}-${(keys.value.length + 1).toString().padStart(3, '0')}`,
            createTime: new Date().toLocaleString(),
            expiryDate: calculateExpiryDate(keyForm.value.validityPeriod, keyForm.value.validityUnit),
            status: 'active',
            algorithm: keyForm.value.algorithm,
            purpose: keyForm.value.purpose
        };
        keys.value.unshift(newKey);
        ElMessage.success('新密钥已创建');
    }
    keyDialogVisible.value = false;
};

const calculateExpiryDate = (period: number, unit: string) => {
    const date = new Date();
    if (unit === 'day') {
        date.setDate(date.getDate() + period);
    } else if (unit === 'month') {
        date.setMonth(date.getMonth() + period);
    } else if (unit === 'year') {
        date.setFullYear(date.getFullYear() + period);
    }
    return date.toLocaleDateString();
};

const batchExportKeys = () => {
    if (selectedKeys.value.length === 0) {
        ElMessage.warning('请至少选择一条记录进行导出');
        return;
    }
    ElMessage.success(`已成功导出 ${selectedKeys.value.length} 条密钥`);
};

const batchDisableKeys = () => {
    if (selectedKeys.value.length === 0) {
        ElMessage.warning('请至少选择一条记录进行禁用');
        return;
    }

    ElMessageBox.confirm(
        `确定要禁用选中的 ${selectedKeys.value.length} 条密钥吗？`,
        '禁用确认',
        {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
        }
    ).then(() => {
        selectedKeys.value.forEach(key => {
            const found = keys.value.find(k => k.keyId === key.keyId);
            if (found) found.status = 'disabled';
        });
        ElMessage.success('密钥已禁用');
    }).catch(() => {
        // Cancel
    });
};

onMounted(() => {
    // Initialize data if needed
});
</script>

<style scoped>
.el-menu {
    height: 100%;
}

.el-form-item {
    margin-bottom: 22px;
}

.el-table {
    margin-top: 0;
}

.el-dialog__body {
    padding: 20px;
}

.el-breadcrumb {
    padding: 12px 0;
}
</style>
