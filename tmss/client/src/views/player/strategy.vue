<template>
    <!-- Encryption Strategy -->
    <div class="flex-1 flex flex-col bg-gray-50 overflow-auto p-6">
        <el-form class="p-10 bg-white shadow-sm rounded-lg" :model="encryptionForm" label-width="120px"
            label-position="left">
            <el-form-item label="加密算法" required>
                <el-select v-model="encryptionForm.algorithm" placeholder="请选择加密算法" class="w-full">
                    <el-option label="SM4" value="sm4" />
                    <el-option label="AES-256" value="aes256" />
                    <el-option label="RSA-2048" value="rsa2048" />
                </el-select>
            </el-form-item>

            <el-form-item label="文件切割规则" required>
                <el-radio-group v-model="encryptionForm.cutMethod">
                    <el-radio label="size">按大小切割</el-radio>
                    <el-radio label="fixed">固定块切割</el-radio>
                </el-radio-group>
            </el-form-item>

            <el-form-item label="切割大小阈值(MB)" v-if="encryptionForm.cutMethod === 'size'">
                <el-input-number v-model="encryptionForm.cutSize" :min="1" :max="1024" />
            </el-form-item>

            <el-form-item label="固定块大小(MB)" v-if="encryptionForm.cutMethod === 'fixed'">
                <el-input-number v-model="encryptionForm.fixedSize" :min="1" :max="100" />
            </el-form-item>

            <el-form-item label="数据存储策略">
                <el-select v-model="encryptionForm.storageStrategy" placeholder="请选择存储策略" class="w-full">
                    <el-option label="本地存储" value="local" />
                    <el-option label="分布式存储" value="distributed" />
                    <el-option label="混合存储" value="hybrid" />
                </el-select>
            </el-form-item>

            <el-form-item label="策略生效时间" required>
                <el-date-picker v-model="encryptionForm.effectiveDate" type="datetime" placeholder="选择日期时间"
                    class="w-full" />
            </el-form-item>

            <el-form-item>
                <el-button type="primary" class="!rounded-button whitespace-nowrap"
                    @click="saveEncryptionStrategy">保存策略</el-button>
                <el-button class="!rounded-button whitespace-nowrap" @click="resetEncryptionStrategy">重置</el-button>
            </el-form-item>
        </el-form>
    </div>
</template>

<script setup lang="ts">
import { successMsg } from '@/utils/msg';
import { ref } from 'vue';

// Encryption Strategy
const encryptionForm = ref({
    algorithm: 'sm4',
    cutMethod: 'size',
    cutSize: 100,
    fixedSize: 10,
    storageStrategy: 'hybrid',
    effectiveDate: new Date(Date.now() + 86400000)
});

const saveEncryptionStrategy = () => {
    successMsg('加密策略已保存');
};

const resetEncryptionStrategy = () => {
    encryptionForm.value = {
        algorithm: 'sm4',
        cutMethod: 'size',
        cutSize: 100,
        fixedSize: 10,
        storageStrategy: 'hybrid',
        effectiveDate: new Date(Date.now() + 86400000)
    };
};

</script>

<style scoped></style>