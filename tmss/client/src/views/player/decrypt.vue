<template>
    <!-- Decrypt Settings -->
    <div class="flex-1 flex flex-col bg-gray-50 overflow-auto p-6">
        <el-form class="p-10 bg-white shadow-sm rounded-lg" :model="decryptForm" label-width="120px"
            label-position="left">
            <el-form-item label="解密算法" required>
                <el-select v-model="decryptForm.algorithm" placeholder="请选择解密算法" class="w-full">
                    <el-option label="AES-256" value="aes256" />
                    <el-option label="RSA-2048" value="rsa2048" />
                    <el-option label="SM4" value="sm4" />
                    <el-option label="3DES" value="3des" />
                </el-select>
            </el-form-item>

            <el-form-item label="密钥有效期" required>
                <div class="flex items-center">
                    <el-input-number v-model="decryptForm.validityPeriod" :min="1" :max="365" class="w-32" />
                    <el-select v-model="decryptForm.validityUnit" class="ml-2 w-32">
                        <el-option label="天" value="day" />
                        <el-option label="月" value="month" />
                        <el-option label="年" value="year" />
                    </el-select>
                </div>
            </el-form-item>

            <el-form-item label="解密参数配置">
                <el-input v-model="decryptForm.params" type="textarea" :rows="4" placeholder="请输入解密参数配置，JSON格式" />
            </el-form-item>

            <el-form-item>
                <el-button type="primary" class="!rounded-button whitespace-nowrap"
                    @click="saveDecryptSettings">保存设置</el-button>
                <el-button class="!rounded-button whitespace-nowrap" @click="resetDecryptSettings">重置</el-button>
            </el-form-item>
        </el-form>

    </div>
</template>

<script setup lang="ts">
import { successMsg } from '@/utils/msg';
import { ref } from 'vue';

// Decrypt Settings
const decryptForm = ref({
    algorithm: 'aes256',
    validityPeriod: 30,
    validityUnit: 'day',
    params: '{\n  "iv": "random16bytes",\n  "mode": "CBC"\n}'
});
const saveDecryptSettings = () => {
    successMsg('解密设置已保存');
};

const resetDecryptSettings = () => {
    decryptForm.value = {
        algorithm: 'aes256',
        validityPeriod: 30,
        validityUnit: 'day',
        params: '{\n  "iv": "random16bytes",\n  "mode": "CBC"\n}'
    };
};
</script>

<style scoped></style>