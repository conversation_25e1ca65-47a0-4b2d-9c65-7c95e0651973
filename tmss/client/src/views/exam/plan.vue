<template>
  <div class="flex-1 flex flex-col bg-gray-50 overflow-auto">
    <!-- 头部区域 -->
    <div class="p-6 border-b border-gray-200">
      <el-button type="primary" class="!rounded-button" @click="drawerVisible = true">
        <el-icon class="mr-1">
          <Plus />
        </el-icon>创建考核
      </el-button>
      <div class="flex items-center space-x-4 mt-4">
        <el-input v-model="name" placeholder="请输入考试名称" class="w-64" clearable />
        <el-select v-model="status" placeholder="选择状态" class="w-48" clearable>
          <el-option v-for="item in Object.keys(statusMap)" :key="item" :label="statusMap[item]" :value="item" />
        </el-select>
        <el-button type="primary" @click="getData">搜索</el-button>
        <el-button @click="resetSearch">重置</el-button>
      </div>
    </div>


    <!-- 表格区域 -->
    <div class="flex-1 overflow-auto p-6">
      <el-table :data="tableData" border class="w-full" v-loading="loading">
        <el-table-column v-for="col in cols" :key="col.prop" :label="col.label" :prop="col.prop" show-overflow-tooltip
          align="center" />
        <el-table-column show-overflow-tooltip label="开始时间" prop="start_at" align="center">
          <template #default="{ row: { start_at } }">
            <el-text type="primary">
              {{ formatTime(start_at) }}
            </el-text>
          </template>
        </el-table-column>
        <el-table-column show-overflow-tooltip label="结束时间" prop="end_at" align="center">
          <template #default="{ row: { end_at } }">
            <el-text type="danger">
              {{ formatTime(end_at) }}
            </el-text>
          </template>
        </el-table-column>
        <el-table-column show-overflow-tooltip label="状态" prop="status" align="center" width="100">
          <template #default="{ row: { status } }">
            <el-tag :type="tagMap[status]">
              {{ statusMap[status] }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" align="center">
          <template #default="{ row }">
            <div class="flex justify-start">
              <el-button v-if="checkView(row.status)" size="small" type="default" @click="handleView(row)">
                查看
              </el-button>
              <el-button v-if="row.status == 'draft' || row.status == 'rejected'" size="small" type="warning"
                @click="handleEdit(row)">
                编辑
              </el-button>
              <el-button v-if="row.status == 'draft' || row.status == 'rejected'" size="small" type="danger"
                @click="handleDelete(row.id)">
                删除
              </el-button>
              <ApproveButton approve-code="exams" :data-id="row.id" :data-title="row.name"
                v-if="row.status == 'draft' || row.status == 'rejected'" @success="getData" />
            </div>

          </template>
        </el-table-column>
      </el-table>
    </div>


    <!-- 分页 -->
    <div class="mt-4 flex justify-center">
      <el-pagination v-model:current-page="page" v-model:page-size="pageSize" :total="total" layout=" prev, pager, next"
        @current-change="handleCurrentChange" />
    </div>

    <el-drawer size="100%" v-model="drawerVisible" :title="is_edit ? '编辑考核' : '创建考核'">
      <create-plan @close="drawerVisible = false" :is_edit="is_edit" :is_view="is_view" :plan_id="plan_id" />
    </el-drawer>
  </div>


</template>

<script lang="ts" setup>
import { deleteExamDraft, getExamList } from '@/api/exam';
import { confirmMsg, successMsg } from '@/utils/msg';
import { onMounted, ref, watch } from 'vue';

// page
const page = ref(1)
const pageSize = ref(10)
const total = ref(0)
const handleCurrentChange = () => { }

// table
const cols = [
  { prop: 'name', label: '考试名称' },
  { prop: 'exam_type', label: '考试类型' },
  // { prop: 'total_minutes', label: '考试时长(分钟)' },
]
const typeMap: any = {
  'regular': '平时考试',
  'final': '结业考试',
  'retake': '补考'
}
const statusMap: any = {
  'draft': '草稿',
  'reviewing': '审核中',
  'rejected': '已驳回',
  'published': '已发布',
  'ongoing': '进行中',
  'finished': '已考完',
  'graded': '已打分',
  'archived': '已归档',
  'certified': '已颁发证书'
}

const checkView = (status: string) => status != 'draft' && status != 'rejected'
const tagMap: any = {
  'draft': 'primary',
  'reviewing': 'warning',
  'rejected': 'danger',
  'published': 'success',
}
const tableData: any = ref([])
const loading = ref(false)
// query
const name = ref('')
const status = ref('')
const resetSearch = () => {
  name.value = ''
  status.value = ''
  getData()
}
const getData = async () => {
  loading.value = true
  const params = {
    page: page.value,
    ...(name.value && { name: name.value }),
    ...(status.value && { status: status.value }),
  }
  const res = await getExamList(params)
  loading.value = false
  tableData.value = res.list || []
  tableData.value = tableData.value.map((item: any) => {
    item.exam_type = typeMap[item.exam_type]
    return item
  })
  total.value = res.total || 0
  // console.log(res, 'res')
}
const formatTime = (time: any) => {
  const date = new Date(time * 1000)
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
}

const plan_id = ref(0)


const handleView = (row: any) => {
  is_view.value = true
  plan_id.value = row.id
  drawerVisible.value = true
}
const handleEdit = (row: any) => {
  is_edit.value = true
  plan_id.value = row.id
  drawerVisible.value = true
}
const handleDelete = (id: any) => {
  confirmMsg('确定要删除吗', '提示', async (v) => {
    if (v) {
      const res = await deleteExamDraft(id)
      successMsg(res.message)
      getData()
    }
  })
}
// drawer
const drawerVisible = ref(false)
watch(drawerVisible, (val) => {
  if (!val) {
    is_edit.value = false
    is_view.value = false
    plan_id.value = 0
    getData()
  }
})
const is_edit = ref(false)
const is_view = ref(false)


onMounted(() => {
  getData()
})
</script>

<style scoped>
.el-table__row .el-table__cell:last-child {
  white-space: nowrap;
}

.el-table {
  --el-table-border-color: #e5e7eb;
  --el-table-header-bg-color: #f9fafb;
  --el-table-row-hover-bg-color: #f3f4f6;
}

.el-pagination {
  --el-pagination-button-bg-color: white;
}

:deep(.el-drawer__header) {
  margin-bottom: 0 !important;
}

:deep(.el-drawer__body) {
  display: flex;
  overflow: hidden;
  flex-direction: column;
}
</style>
