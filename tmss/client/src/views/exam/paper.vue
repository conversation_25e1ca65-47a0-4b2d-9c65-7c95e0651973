<template>
  <div class="flex-1 flex overflow-hidden bg-gray-50">

    <!-- 右侧内容区 -->
    <div class="flex-1 p-6 flex flex-col overflow-hidden">
      <!-- <div class="flex flex-wrap gap-3 mb-4">
        <el-button :type="activeMenu === item.key ? 'primary' : ''" v-for="(item, index) in menuItems" :key="index"
          @click="activeMenu = item.key">
          <el-icon class="mr-3">
            <component :is="item.icon" />
          </el-icon>
          {{ item.label }}
        </el-button>
      </div> -->
      <!-- 随机组卷 -->
      <!-- <random-paper v-if="activeMenu === 'random'" :questionTypes="questionTypes" :courses="courses"
        :difficultyLevels="difficultyLevels" /> -->


      <!-- 固定组卷 -->
      <!-- <fixed-paper v-show="activeMenu === 'fixed'" @create="activeMenu = 'manage'" /> -->

      <!-- 试卷管理 -->
      <paper-manage v-if="activeMenu === 'manage'" :paperStatus="paperStatus" />

      <!-- 考试发布 -->
      <paper-publish v-if="activeMenu === 'publish'" />

      <!-- 评分管理 -->
      <paper-grade v-if="activeMenu === 'grade'" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';

const activeMenu = ref('manage');

const menuItems = [
  // { key: 'random', label: '随机组卷', icon: 'Document' },
  // { key: 'fixed', label: '固定组卷', icon: 'Edit' },
  { key: 'manage', label: '试卷管理', icon: 'List' },
  // { key: 'publish', label: '考试发布', icon: 'Timer' },
  // { key: 'grade', label: '阅卷管理', icon: 'Check' }
];

// 试卷状态常量
const paperStatus = {
  'draft': '草稿',
  'reviewing': '审核中',
  'rejected':'已驳回',
  'published': '已发布',
  'ongoing': '考试中',
  'finished': '已考完',
  'graded': '已评分',
  'archived': '已归档'
}



</script>

<style scoped>
.el-input-number.w-full :deep(.el-input__wrapper) {
  width: 100%;
}

.el-select.w-full {
  width: 100%;
}

:deep(.el-input__wrapper) {
  background-color: white;
}

:deep(.el-slider__runway) {
  margin: 0;
}

:deep(.el-checkbox__label) {
  font-weight: normal;
}
</style>
