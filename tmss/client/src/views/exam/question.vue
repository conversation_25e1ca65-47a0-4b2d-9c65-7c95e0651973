<template>
    <div class="flex-1 overflow-auto">
        <div class=" mx-auto px-4 py-6 flex gap-6">
            <!-- 左侧导航 -->
            <div class="w-1/4 bg-white rounded-lg shadow-sm p-4">
                <div class="relative mb-4">
                    <el-input v-model="searchQuery" placeholder="搜索试题" class="!rounded-button">
                        <template #prefix>
                            <el-icon>
                                <Search />
                            </el-icon>
                        </template>
                    </el-input>
                </div>

                <div class="space-y-4">
                    <div v-for="(category, index) in categories" :key="index">
                        <div class="font-medium text-gray-700 mb-2">{{ category.title }}</div>
                        <div class="space-y-2">
                            <div v-for="item in category.items" :key="item.id" :class="{
                                'flex items-center justify-between p-2 rounded-lg cursor-pointer hover:bg-gray-50': true,
                                'bg-blue-50 text-blue-600': selectedCategory === item.id
                            }" @click="selectedCategory = item.id">
                                <span>{{ item.name }}</span>
                                <span class="text-gray-400 text-sm">{{ item.count }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧内容 -->
            <div class="flex-1 bg-white rounded-lg shadow-sm p-6">
                <div class="flex justify-end items-center mb-6">
                    <!-- <div class="space-x-4">
                        <el-button type="primary" class="!rounded-button" @click="showDialog = true">
                            <el-icon class="mr-1">
                                <Plus />
                            </el-icon>新增试题
                        </el-button>
                    </div> -->
                    <div class="flex items-center space-x-2">
                        <el-radio-group v-model="viewMode" class="flex">
                            <el-radio-button label="list">列表视图</el-radio-button>
                            <el-radio-button label="card">卡片视图</el-radio-button>
                        </el-radio-group>
                    </div>
                </div>

                <!-- 试题列表 -->
                <el-table v-if="viewMode === 'list'" :data="questions" style="width: 100%">
                    <el-table-column label="题干" min-width="300">
                        <template #default="{ row }">
                            <div class="flex items-start">
                                <el-tag size="small" class="mr-2">{{ row.type }}</el-tag>
                                <div>{{ row.content }}</div>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column label="难度" width="100">
                        <template #default="{ row }">
                            <el-tag :type="getDifficultyType(row.difficulty)" size="small">
                                {{ row.difficulty }}
                            </el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="分值" width="80">
                        <template #default="{ row }">
                            {{ row.score }}分
                        </template>
                    </el-table-column>
                    <el-table-column label="知识点" width="150">
                        <template #default="{ row }">
                            <el-tag size="small" effect="plain">{{ row.knowledge }}</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" width="200">
                        <template #default="{ row }">
                            <el-button-group>
                                <el-button size="small" type="primary" class="!rounded-button">
                                    编辑
                                </el-button>
                                <el-button size="small" type="danger" class="!rounded-button">
                                    删除
                                </el-button>
                            </el-button-group>
                        </template>
                    </el-table-column>
                </el-table>

                <!-- 卡片视图 -->
                <div v-else class="grid grid-cols-2 gap-4">
                    <div v-for="item in questions" :key="item.id" class="border rounded-lg p-4">
                        <div class="flex items-start justify-between mb-3">
                            <div class="flex items-center">
                                <el-tag size="small" class="mr-2">{{ item.type }}</el-tag>
                                <el-tag :type="getDifficultyType(item.difficulty)" size="small">
                                    {{ item.difficulty }}
                                </el-tag>
                            </div>
                            <span class="text-lg font-medium">{{ item.score }}分</span>
                        </div>
                        <p class="text-gray-700 mb-3">{{ item.content }}</p>
                        <div class="flex items-center justify-between">
                            <el-tag size="small" effect="plain">{{ item.knowledge }}</el-tag>
                            <div class="space-x-2">
                                <el-button size="small" type="primary" class="!rounded-button">
                                    编辑
                                </el-button>
                                <el-button size="small" type="danger" class="!rounded-button">
                                    删除
                                </el-button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 新增/编辑试题对话框 -->
        <el-dialog v-model="showDialog" title="新增试题" width="60%" :before-close="handleClose">
            <el-steps :active="activeStep" finish-status="success" class="mb-8">
                <el-step title="基本信息" />
                <el-step title="试题内容" />
                <el-step title="补充信息" />
            </el-steps>

            <!-- Step 1: 基本信息 -->
            <div v-if="activeStep === 0">
                <el-form :model="questionForm" label-width="100px">
                    <el-form-item label="课程">
                        <el-select v-model="questionForm.courseId" placeholder="请选择课程" class="w-full">
                            <el-option v-for="course in courses" :key="course.id" :label="course.name"
                                :value="course.id" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="题型">
                        <el-select v-model="questionForm.type" placeholder="请选择题型" class="w-full">
                            <el-option label="单选题" value="single_choice" />
                            <el-option label="多选题" value="multiple_choice" />
                            <el-option label="简答题" value="short_answer" />
                            <el-option label="编程题" value="programming" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="难度">
                        <el-select v-model="questionForm.difficulty" placeholder="请选择难度" class="w-full">
                            <el-option label="简单" value="easy" />
                            <el-option label="中等" value="medium" />
                            <el-option label="困难" value="hard" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="分值">
                        <el-input-number v-model="questionForm.score" :min="1" :max="100" class="w-full" />
                    </el-form-item>
                </el-form>
            </div>

            <!-- Step 2: 试题内容 -->
            <div v-if="activeStep === 1">
                <el-form :model="questionForm" label-width="100px">
                    <el-form-item label="题干">
                        <el-input v-model="questionForm.content" type="textarea" :rows="4" placeholder="请输入题干内容" />
                    </el-form-item>
                    <el-form-item label="选项" v-if="['single_choice', 'multiple_choice'].includes(questionForm.type)">
                        <div class="space-y-2">
                            <div v-for="(option, index) in questionForm.options" :key="index"
                                class="flex items-center gap-2">
                                <el-input v-model="questionForm.options[index]" placeholder="请输入选项内容" />
                                <el-button type="danger" circle @click="removeOption(index)">
                                    <el-icon>
                                        <Delete />
                                    </el-icon>
                                </el-button>
                            </div>
                            <el-button type="primary" @click="addOption">添加选项</el-button>
                        </div>
                    </el-form-item>
                    <el-form-item label="正确答案">
                        <el-input v-model="questionForm.correctAnswer" type="textarea" :rows="3"
                            placeholder="请输入正确答案" />
                    </el-form-item>
                </el-form>
            </div>

            <!-- Step 3: 补充信息 -->
            <div v-if="activeStep === 2">
                <el-form :model="questionForm" label-width="100px">
                    <el-form-item label="知识点">
                        <el-select v-model="questionForm.knowledgePoint" placeholder="请选择知识点" class="w-full">
                            <el-option v-for="point in knowledgePoints" :key="point.id" :label="point.name"
                                :value="point.id" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="关联章节">
                        <el-cascader v-model="questionForm.chapterId" :options="chapters" placeholder="请选择关联章节"
                            class="w-full" />
                    </el-form-item>
                    <el-form-item label="解析">
                        <el-input v-model="questionForm.explanation" type="textarea" :rows="4" placeholder="请输入试题解析" />
                    </el-form-item>
                </el-form>
            </div>

            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="handleClose">取消</el-button>
                    <el-button v-if="activeStep > 0" @click="activeStep--">上一步</el-button>
                    <el-button type="primary" @click="activeStep === 2 ? submitForm() : activeStep++">
                        {{ activeStep === 2 ? '提交' : '下一步' }}
                    </el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';

const searchQuery = ref('');
const selectedCategory = ref('');
const viewMode = ref('list');
const showDialog = ref(false);
const activeStep = ref(0);

const categories = ref([
    {
        title: '题型分类',
        items: [
            { id: 'single', name: '单选题', count: 156 },
            { id: 'multiple', name: '多选题', count: 89 },
            { id: 'short', name: '简答题', count: 45 },
            { id: 'programming', name: '编程题', count: 67 }
        ]
    },
    {
        title: '难度分类',
        items: [
            { id: 'easy', name: '简单', count: 98 },
            { id: 'medium', name: '中等', count: 167 },
            { id: 'hard', name: '困难', count: 92 }
        ]
    },
    {
        title: '知识点分类',
        items: [
            { id: 'kp1', name: '条件语句', count: 78 },
            { id: 'kp2', name: '循环结构', count: 65 },
            { id: 'kp3', name: '函数定义', count: 89 },
            { id: 'kp4', name: '类与对象', count: 56 }
        ]
    }
]);

const questions = ref([
    {
        id: 1,
        type: '单选题',
        content: 'Python中if-elif-else语句的执行顺序是什么？',
        difficulty: '简单',
        score: 3,
        knowledge: '条件语句执行逻辑'
    },
    {
        id: 2,
        type: '多选题',
        content: '以下哪些是Python的基本数据类型？',
        difficulty: '中等',
        score: 4,
        knowledge: '数据类型'
    },
    {
        id: 3,
        type: '编程题',
        content: '实现一个冒泡排序算法',
        difficulty: '困难',
        score: 15,
        knowledge: '算法实现'
    }
]);

const questionForm = ref({
    courseId: '',
    type: '',
    difficulty: '',
    score: 3,
    content: '',
    options: [''],
    correctAnswer: '',
    knowledgePoint: '',
    chapterId: '',
    explanation: ''
});

const courses = ref([
    { id: 1, name: 'Python基础编程' },
    { id: 2, name: 'Java程序设计' },
    { id: 3, name: '数据结构' }
]);

const knowledgePoints = ref([
    { id: 1, name: '条件语句' },
    { id: 2, name: '循环结构' },
    { id: 3, name: '函数定义' }
]);

const chapters = ref([
    {
        value: 'ch1',
        label: '第一章',
        children: [
            { value: 'ch1-1', label: '1.1 基础概念' },
            { value: 'ch1-2', label: '1.2 开发环境' }
        ]
    },
    {
        value: 'ch2',
        label: '第二章',
        children: [
            { value: 'ch2-1', label: '2.1 数据类型' },
            { value: 'ch2-2', label: '2.2 运算符' }
        ]
    }
]);

const getDifficultyType = (difficulty: string) => {
    const types: Record<string, string> = {
        '简单': 'success',
        '中等': 'warning',
        '困难': 'danger'
    };
    return types[difficulty] || 'info';
};

const handleClose = () => {
    showDialog.value = false;
    activeStep.value = 0;
    questionForm.value = {
        courseId: '',
        type: '',
        difficulty: '',
        score: 3,
        content: '',
        options: [''],
        correctAnswer: '',
        knowledgePoint: '',
        chapterId: '',
        explanation: ''
    };
};

const addOption = () => {
    questionForm.value.options.push('');
};

const removeOption = (index: number) => {
    questionForm.value.options.splice(index, 1);
};

const submitForm = () => {
    // 提交表单逻辑
    handleClose();
};
</script>

<style scoped>
:deep(.el-dialog__body) {
    padding-top: 20px;
}

:deep(.el-input-number .el-input__wrapper) {
    padding-left: 11px;
}

:deep(.el-steps) {
    padding: 0 100px;
}
</style>
