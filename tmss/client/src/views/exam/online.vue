<template>
    <div class="min-h-screen bg-gray-50 flex flex-col">
        <!-- 顶部考试信息区 -->
        <div class="bg-white shadow-sm py-4 px-6 border-b border-gray-200">
            <div class="max-w-7xl mx-auto flex justify-between items-center">
                <div>
                    <h1 class="text-xl font-semibold text-gray-800">2023年秋季学期期末考试</h1>
                    <div class="flex items-center mt-1 space-x-4 text-sm">
                        <div class="flex items-center">
                            <el-icon class="mr-1">
                                <Clock />
                            </el-icon>
                            <span class="text-red-500 font-medium">剩余时间: {{ formatTime(remainingTime) }}</span>
                        </div>
                        <div class="text-gray-600">总分: 100分</div>
                        <div class="text-gray-600">题量: 25题</div>
                        <div class="text-green-600 font-medium">状态: 进行中</div>
                    </div>
                </div>
                <div class="flex items-center">
                    <el-avatar :size="36"
                        src="https://mastergo.com/ai/api/search-image?query=professional asian male student portrait with white background&width=100&height=100&orientation=squarish&flag=572dfa825b8f245696b14d28d7fa0ff5" />
                    <div class="ml-3">
                        <div class="font-medium">张明远</div>
                        <div class="text-xs text-gray-500">学号: 20231025</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="flex flex-1 overflow-hidden">
            <!-- 左侧题目导航栏 -->
            <div class="w-64 bg-white border-r border-gray-200 flex flex-col"
                :class="{ 'hidden md:flex': !showSidebar }">
                <div class="p-4 border-b border-gray-200">
                    <h3 class="font-medium text-gray-800">题目导航</h3>
                </div>
                <div class="flex-1 overflow-y-auto">
                    <div class="p-2">
                        <el-scrollbar height="100%">
                            <div class="grid grid-cols-3 gap-2">
                                <div v-for="(item, index) in questions" :key="index" @click="currentQuestion = index"
                                    class="h-10 flex items-center justify-center rounded border cursor-pointer transition-colors"
                                    :class="{
                                        'bg-blue-100 border-blue-300': currentQuestion === index,
                                        'bg-green-100 border-green-300': item.answered && currentQuestion !== index,
                                        'bg-white border-gray-300': !item.answered && currentQuestion !== index,
                                        'border-yellow-300': item.marked
                                    }">
                                    {{ index + 1 }}
                                </div>
                            </div>
                        </el-scrollbar>
                    </div>
                </div>
                <!-- <div class="p-3 border-t border-gray-200">
                    <el-button type="primary" class="w-full !rounded-button whitespace-nowrap" @click="toggleSidebar">
                        <el-icon class="mr-1">
                            <ArrowLeft />
                        </el-icon>
                        收起导航
                    </el-button>
                </div> -->
            </div>

            <!-- 主答题区域 -->
            <div class="flex-1 overflow-auto p-6">
                <div class="max-w-4xl mx-auto bg-white rounded-lg shadow-sm p-6">
                    <!-- 题目内容 -->
                    <div class="mb-6">
                        <div class="flex justify-between items-center mb-4">
                            <h2 class="text-lg font-medium text-gray-800">
                                第 {{ currentQuestion + 1 }} 题
                                <span class="text-sm text-gray-500 ml-2">({{
                                    getQuestionType(questions[currentQuestion].type) }})</span>
                            </h2>
                            <el-button size="small" :type="questions[currentQuestion].marked ? 'warning' : 'info'"
                                @click="toggleMark" class="!rounded-button whitespace-nowrap">
                                <el-icon class="mr-1">
                                    <Star />
                                </el-icon>
                                {{ questions[currentQuestion].marked ? '取消标记' : '标记此题' }}
                            </el-button>
                        </div>
                        <div class="text-gray-700 mb-6 leading-relaxed">
                            {{ questions[currentQuestion].content }}
                        </div>

                        <!-- 客观题选项 -->
                        <div
                            v-if="questions[currentQuestion].type === 'single' || questions[currentQuestion].type === 'multiple'">
                            <el-radio-group v-model="questions[currentQuestion].answer"
                                v-if="questions[currentQuestion].type === 'single'" class="flex flex-col space-y-3">
                                <el-radio v-for="(option, idx) in questions[currentQuestion].options" :key="idx"
                                    :label="option.value" class="!items-start">
                                    <div class="ml-2">
                                        <span class="font-medium">{{ option.label }}.</span>
                                        <span class="ml-1">{{ option.text }}</span>
                                    </div>
                                </el-radio>
                            </el-radio-group>

                            <el-checkbox-group v-model="questions[currentQuestion].answer"
                                v-if="questions[currentQuestion].type === 'multiple'" class="flex flex-col space-y-3">
                                <el-checkbox v-for="(option, idx) in questions[currentQuestion].options" :key="idx"
                                    :label="option.value" class="!items-start">
                                    <div class="ml-2">
                                        <span class="font-medium">{{ option.label }}.</span>
                                        <span class="ml-1">{{ option.text }}</span>
                                    </div>
                                </el-checkbox>
                            </el-checkbox-group>
                        </div>

                        <!-- 主观题答题框 -->
                        <div v-if="questions[currentQuestion].type === 'subjective'">
                            <el-input v-model="questions[currentQuestion].answer" type="textarea" :rows="8"
                                placeholder="请输入您的答案..." resize="none" />
                        </div>

                        <!-- 文件上传 -->
                        <div v-if="questions[currentQuestion].type === 'file'">
                            <el-upload class="upload-demo" action="#" :on-change="handleFileChange" :auto-upload="false"
                                :file-list="questions[currentQuestion].files" multiple>
                                <template #trigger>
                                    <el-button type="primary" class="!rounded-button whitespace-nowrap">
                                        <el-icon class="mr-1">
                                            <Upload />
                                        </el-icon>
                                        选择文件
                                    </el-button>
                                </template>
                                <el-button class="ml-3 !rounded-button whitespace-nowrap" type="success"
                                    @click="submitUpload">
                                    <el-icon class="mr-1">
                                        <Check />
                                    </el-icon>
                                    上传文件
                                </el-button>
                                <template #tip>
                                    <div class="el-upload__tip text-gray-500 mt-2">
                                        支持上传图片、PDF、Word等格式文件，单个文件不超过10MB
                                    </div>
                                </template>
                            </el-upload>
                        </div>
                    </div>

                    <!-- 答题控制按钮 -->
                    <div class="flex justify-between pt-4 border-t border-gray-200">
                        <div class="space-x-3">
                            <el-button :disabled="currentQuestion === 0" @click="prevQuestion"
                                class="!rounded-button whitespace-nowrap">
                                <el-icon class="mr-1">
                                    <ArrowLeft />
                                </el-icon>
                                上一题
                            </el-button>
                            <el-button :disabled="currentQuestion === questions.length - 1" @click="nextQuestion"
                                class="!rounded-button whitespace-nowrap">
                                下一题
                                <el-icon class="ml-1">
                                    <ArrowRight />
                                </el-icon>
                            </el-button>
                        </div>
                        <div class="space-x-3">
                            <el-button @click="clearAnswer" class="!rounded-button whitespace-nowrap">
                                <el-icon class="mr-1">
                                    <Delete />
                                </el-icon>
                                清空重答
                            </el-button>
                            <el-button type="primary" @click="saveAnswer" class="!rounded-button whitespace-nowrap">
                                <el-icon class="mr-1">
                                    <DocumentAdd />
                                </el-icon>
                                暂存答案
                            </el-button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部提交控制区 -->
        <div class="bg-white border-t border-gray-200 py-3 px-6">
            <div class="max-w-7xl mx-auto">
                <div class="flex items-center justify-between">
                    <div class="flex-1 mr-4">
                        <el-progress :percentage="answeredPercentage"
                            :color="answeredPercentage === 100 ? '#67C23A' : '#409EFF'" />
                        <div class="flex justify-between text-xs text-gray-500 mt-1">
                            <span>已完成: {{ answeredCount }} / {{ questions.length }}</span>
                            <span v-if="unansweredCount > 0" class="text-red-500">未作答: {{ unansweredCount }}题</span>
                        </div>
                    </div>
                    <div class="space-x-3">
                        <el-button @click="confirmExit" class="!rounded-button whitespace-nowrap">
                            <el-icon class="mr-1">
                                <Close />
                            </el-icon>
                            退出考试
                        </el-button>
                        <el-button type="success" @click="confirmSubmit" :disabled="answeredPercentage < 100"
                            class="!rounded-button whitespace-nowrap">
                            <el-icon class="mr-1">
                                <Finished />
                            </el-icon>
                            提交试卷
                        </el-button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 提交确认对话框 -->
        <el-dialog v-model="submitDialogVisible" title="提交确认" width="30%">
            <span>您确定要提交试卷吗？提交后将无法继续作答。</span>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="submitDialogVisible = false"
                        class="!rounded-button whitespace-nowrap">取消</el-button>
                    <el-button type="primary" @click="submitExam"
                        class="!rounded-button whitespace-nowrap">确定提交</el-button>
                </span>
            </template>
        </el-dialog>

        <!-- 退出确认对话框 -->
        <el-dialog v-model="exitDialogVisible" title="退出确认" width="30%">
            <span>您确定要退出考试吗？未提交的答案将不会被保存。</span>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="exitDialogVisible = false"
                        class="!rounded-button whitespace-nowrap">取消</el-button>
                    <el-button type="primary" @click="exitExam"
                        class="!rounded-button whitespace-nowrap">确定退出</el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, onBeforeUnmount } from 'vue';
import {
    Clock, ArrowLeft, ArrowRight, Star, Upload, Check,
    Delete, DocumentAdd, Close, Finished
} from '@element-plus/icons-vue';
import { successMsg, warningMsg } from '@/utils/msg';

// 考试数据
const questions:any = ref([
    {
        id: 1,
        type: 'single',
        content: '以下哪个是JavaScript的基本数据类型？',
        options: [
            { label: 'A', value: 'A', text: 'Object' },
            { label: 'B', value: 'B', text: 'Array' },
            { label: 'C', value: 'C', text: 'String' },
            { label: 'D', value: 'D', text: 'Function' }
        ],
        answer: '',
        answered: false,
        marked: false
    },
    {
        id: 2,
        type: 'multiple',
        content: '以下哪些是Vue.js的核心特性？（多选）',
        options: [
            { label: 'A', value: 'A', text: '响应式数据绑定' },
            { label: 'B', value: 'B', text: '组件系统' },
            { label: 'C', value: 'C', text: '虚拟DOM' },
            { label: 'D', value: 'D', text: '服务端渲染' }
        ],
        answer: [],
        answered: false,
        marked: false
    },
    {
        id: 3,
        type: 'subjective',
        content: '请简述你对前端工程化的理解，包括但不限于其意义、常用工具和实践方法。',
        answer: '',
        answered: false,
        marked: false
    },
    {
        id: 4,
        type: 'file',
        content: '请上传你的项目设计文档和原型图文件。',
        files: [],
        answer: '',
        answered: false,
        marked: true
    },
    // 更多题目...
    ...Array.from({ length: 21 }, (_, i) => ({
        id: i + 5,
        type: ['single', 'multiple', 'subjective', 'file'][Math.floor(Math.random() * 4)],
        content: `这是第${i + 5}道题目内容，用于测试考试系统的显示效果和功能完整性。`,
        options: ['A', 'B', 'C', 'D'].map((label, idx) => ({
            label,
            value: label,
            text: `选项${label}的内容描述`
        })),
        answer: ['single', 'file'].includes(['single', 'multiple', 'subjective', 'file'][Math.floor(Math.random() * 4)]) ? '' : [],
        files: [],
        answered: Math.random() > 0.5,
        marked: Math.random() > 0.8
    }))
]);

const currentQuestion = ref(0);
const showSidebar = ref(true);
const remainingTime = ref(7200); // 2小时 = 7200秒
const submitDialogVisible = ref(false);
const exitDialogVisible = ref(false);

// 计算属性
const answeredCount = computed(() => {
    return questions.value.filter((q:any) => q.answered).length;
});

const unansweredCount = computed(() => {
    return questions.value.length - answeredCount.value;
});

const answeredPercentage = computed(() => {
    return Math.round((answeredCount.value / questions.value.length) * 100);
});

// 方法
const formatTime = (seconds: number) => {
    const h = Math.floor(seconds / 3600);
    const m = Math.floor((seconds % 3600) / 60);
    const s = seconds % 60;
    return `${h.toString().padStart(2, '0')}:${m.toString().padStart(2, '0')}:${s.toString().padStart(2, '0')}`;
};

const getQuestionType = (type: string) => {
    const types: Record<string, string> = {
        single: '单选题',
        multiple: '多选题',
        subjective: '主观题',
        file: '文件题'
    };
    return types[type] || type;
};

const toggleSidebar = () => {
    showSidebar.value = !showSidebar.value;
};

const toggleMark = () => {
    questions.value[currentQuestion.value].marked = !questions.value[currentQuestion.value].marked;
};

const prevQuestion = () => {
    if (currentQuestion.value > 0) {
        currentQuestion.value--;
    }
};

const nextQuestion = () => {
    if (currentQuestion.value < questions.value.length - 1) {
        currentQuestion.value++;
    }
};

const clearAnswer = () => {
    const q = questions.value[currentQuestion.value];
    if (q.type === 'single') {
        q.answer = '';
    } else if (q.type === 'multiple') {
        q.answer = [];
    } else {
        q.answer = '';
    }
    q.answered = false;
};

const saveAnswer = () => {
    const q:any = questions.value[currentQuestion.value];
    if (
        (q.type === 'single' && q.answer !== '') ||
        (q.type === 'multiple' && q.answer.length > 0) ||
        (q.type === 'subjective' && q.answer.trim() !== '') ||
        (q.type === 'file' && q.files.length > 0)
    ) {
        q.answered = true;
        successMsg('答案已暂存');
    } else {
        warningMsg('请先作答再保存');
    }
};

const handleFileChange = (file: any, fileList: any[]) => {
    questions.value[currentQuestion.value].files = fileList;
    if (fileList.length > 0) {
        questions.value[currentQuestion.value].answered = true;
    }
};

const submitUpload = () => {
    successMsg('文件上传成功');
};

const confirmSubmit = () => {
    submitDialogVisible.value = true;
};

const submitExam = () => {
    submitDialogVisible.value = false;
    successMsg('试卷提交成功');
    // 这里可以添加提交逻辑
};

const confirmExit = () => {
    exitDialogVisible.value = true;
};

const exitExam = () => {
    exitDialogVisible.value = false;
    warningMsg('已退出考试');
    // 这里可以添加退出逻辑
};

// 计时器
let timer: number;
onMounted(() => {
    timer = setInterval(() => {
        if (remainingTime.value > 0) {
            remainingTime.value--;
        } else {
            clearInterval(timer);
            warningMsg('考试时间已结束，系统将自动提交试卷');
            submitExam();
        }
    }, 1000);

    // 防止刷新
    window.addEventListener('beforeunload', (e) => {
        e.preventDefault();
        e.returnValue = '确定要离开吗？未保存的答案可能会丢失。';
    });
});

onBeforeUnmount(() => {
    clearInterval(timer);
    window.removeEventListener('beforeunload', () => { });
});
</script>

<style scoped>
.upload-demo {
    width: 100%;
}

.el-radio,
.el-checkbox {
    align-items: flex-start;
}

.el-radio__label,
.el-checkbox__label {
    white-space: normal;
}
</style>
