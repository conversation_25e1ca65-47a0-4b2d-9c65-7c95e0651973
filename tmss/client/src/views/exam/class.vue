<!-- 代码已包含 CSS：使用 TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 -->

<template>
    <div class="flex-1 flex flex-col bg-gray-50 overflow-auto">

        <!-- 顶部操作栏 -->
        <div class="bg-white px-8 py-6 border-b border-gray-200">
            <div class="flex justify-between items-center mb-6">
                <el-button type="primary" class="!rounded-button" @click="handleCreateClass">
                    <el-icon class="mr-1">
                        <Plus />
                    </el-icon>新建班级
                </el-button>
            </div>

            <div class="flex gap-4">
                <el-input v-model="searchQuery" placeholder="搜索班级名称/班主任" class="w-64">
                    <template #prefix>
                        <el-icon>
                            <Search />
                        </el-icon>
                    </template>
                </el-input>

                <el-select v-model="statusFilter" placeholder="班级状态" class="w-32">
                    <el-option label="全部" value="" />
                    <el-option label="活跃" value="active" />
                    <el-option label="已归档" value="archived" />
                </el-select>
            </div>
        </div>

        <!-- 表格区域 -->
        <div class="flex-1 overflow-auto p-6">
            <el-table :data="filteredClassList" style="width: 100%" border>
                <el-table-column prop="name" label="班级名称">
                    <template #default="{ row }">
                        <div class="flex items-center">
                            <img :src="row.avatar" class="w-8 h-8 rounded-lg mr-2 object-cover" />
                            <span class="text-blue-600 cursor-pointer hover:underline">{{ row.name }}</span>
                        </div>
                    </template>
                </el-table-column>

                <el-table-column prop="teacher" label="班主任">
                    <template #default="{ row }">
                        <div class="flex items-center">
                            <span>{{ row.teacher }}</span>
                        </div>
                    </template>
                </el-table-column>

                <el-table-column prop="studentCount" label="学生人数">
                    <template #default="{ row }">
                        <span>{{ row.studentCount }} 人</span>
                    </template>
                </el-table-column>

                <el-table-column prop="planCount" label="关联计划">
                    <template #default="{ row }">
                        <el-tag type="success" class="!rounded-button">{{ row.planCount }} 个</el-tag>
                    </template>
                </el-table-column>

                <el-table-column prop="questionCount" label="题库使用">
                    <template #default="{ row }">
                        <el-tag type="warning" class="!rounded-button">{{ row.questionCount }} 题</el-tag>
                    </template>
                </el-table-column>

                <el-table-column label="操作" width="280">
                    <template #default="{ row }">
                        <div class="flex gap-2">
                            <el-button type="primary" plain size="small" class="!rounded-button whitespace-nowrap">
                                <el-icon class="mr-1">
                                    <Calendar />
                                </el-icon>考核计划
                            </el-button>
                            <el-button type="success" plain size="small" class="!rounded-button whitespace-nowrap">
                                <el-icon class="mr-1">
                                    <Collection />
                                </el-icon>题库
                            </el-button>
                            <el-button type="warning" plain size="small" class="!rounded-button whitespace-nowrap">
                                <el-icon class="mr-1">
                                    <Document />
                                </el-icon>试卷
                            </el-button>
                            <el-button type="info" plain size="small" class="!rounded-button whitespace-nowrap">
                                <el-icon class="mr-1">
                                    <DataLine />
                                </el-icon>成绩
                            </el-button>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <!-- 分页 -->
        <div class="flex justify-center mt-4">
            <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize"
                :total="filteredClassList.length" layout=" prev, pager, next" />
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue';
import {
    Calendar,
    Collection,
    Document,
    DataLine,
    Search,
    Plus,
    HomeFilled,
    User,
    Reading,
    Notebook,
    Trophy,
    Setting
} from '@element-plus/icons-vue';

const currentMenu = ref('class');
const searchQuery = ref('');
const statusFilter = ref('');
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(100);

const menuItems = [
    { id: 'dashboard', name: '仪表盘', icon: 'HomeFilled' },
    { id: 'class', name: '班级管理', icon: 'User' },
    { id: 'course', name: '课程管理', icon: 'Reading' },
    { id: 'exam', name: '考试管理', icon: 'Notebook' },
    { id: 'achievement', name: '成绩分析', icon: 'Trophy' },
    { id: 'setting', name: '系统设置', icon: 'Setting' }
];

const classList = ref([
    {
        name: '高三一班',
        avatar: 'https://ai-public.mastergo.com/ai/img_res/3fddc0e134bc9a150b3b3ade70dcd15d.jpg',
        teacher: '张晓明老师',
        studentCount: 45,
        planCount: 8,
        questionCount: 2360,
        examCount: 12,
        lastScore: 92,
        status: 'active'
    },
    {
        name: '高三二班',
        avatar: 'https://ai-public.mastergo.com/ai/img_res/18085a3f550924478aad3667346a807c.jpg',
        teacher: '李文华老师',
        studentCount: 42,
        planCount: 6,
        questionCount: 1980,
        examCount: 10,
        lastScore: 88,
        status: 'active'
    },
    {
        name: '高三三班',
        avatar: 'https://ai-public.mastergo.com/ai/img_res/a7dc3bde4641e26df35ac27d1072dd03.jpg',
        teacher: '赵雅芝老师',
        studentCount: 43,
        planCount: 7,
        questionCount: 2150,
        examCount: 11,
        lastScore: 90,
        status: 'active'
    },
    {
        name: '高三四班',
        avatar: 'https://ai-public.mastergo.com/ai/img_res/b279ecd58bc08f7c01bebad8c1421632.jpg',
        teacher: '王建国老师',
        studentCount: 44,
        planCount: 8,
        questionCount: 2280,
        examCount: 12,
        lastScore: 86,
        status: 'archived'
    }
]);

const filteredClassList = computed(() => {
    return classList.value.filter(item => {
        const matchQuery = item.name.includes(searchQuery.value) ||
            item.teacher.includes(searchQuery.value);
        const matchStatus = !statusFilter.value || item.status === statusFilter.value;
        return matchQuery && matchStatus;
    });
});

const getScoreColor = (score: number) => {
    if (score >= 90) return '#67C23A';
    if (score >= 80) return '#E6A23C';
    return '#F56C6C';
};

const handleCreateClass = () => {
    // 处理创建班级逻辑
};
</script>

<style scoped>
:deep(.el-input__wrapper) {
    background-color: #f9fafb;
    border: 1px solid #e5e7eb;
}

:deep(.el-select .el-input__wrapper) {
    background-color: #f9fafb;
    border: 1px solid #e5e7eb;
}

:deep(.el-button) {
    font-weight: 500;
}

:deep(.el-table th) {
    background-color: #f8fafc;
    font-weight: 600;
}

:deep(.el-pagination) {
    justify-content: flex-end;
}
</style>
