<template>
    <div class="flex-1 flex flex-col bg-gray-50 overflow-auto">
        <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-medium mb-4">知识点得分率趋势</h3>
            <div ref="chartRef" class="w-full h-64"></div>
        </div>
        <!-- 统计分析区域 -->
        <div class="grid grid-cols-3 gap-6">
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-medium mb-4">班级统计</h3>
                <div class="grid grid-cols-2 gap-4">
                    <div class="flex justify-between items-center">
                        <span class="text-gray-600">平均分</span>
                        <span class="text-xl font-semibold">{{ classStats.avgScore }}</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-gray-600">最高分</span>
                        <span class="text-green-500">{{ classStats.maxScore }}</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-gray-600">最低分</span>
                        <span class="text-red-500">{{ classStats.minScore }}</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-gray-600">及格率</span>
                        <span>{{ classStats.passRate }}%</span>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-medium mb-4">试题分析</h3>
                <div class="grid grid-cols-2 gap-4">
                    <div v-for="question in classStats.questions" :key="question.id"
                        class="border-b border-gray-100 pb-3">
                        <div class="flex justify-between mb-2">
                            <span class="text-gray-600">试题 {{ question.id }}</span>
                            <span :class="question.errorRate > 50 ? 'text-red-500' : 'text-gray-600'">
                                错误率 {{ question.errorRate }}%
                            </span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-500 text-sm">平均得分</span>
                            <span class="text-sm">{{ question.avgScore }}</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-medium mb-4">知识点分析</h3>
                <div class="grid grid-cols-2 gap-4">
                    <div v-for="point in classStats.knowledgePoints" :key="point.name"
                        class="border-b border-gray-100 pb-3">
                        <div class="flex justify-between items-center">
                            <span class="text-gray-600">{{ point.name }}</span>
                            <div class="flex items-center gap-2">
                                <span :class="point.scoreRate < 60 ? 'text-red-500' : 'text-gray-600'">
                                    得分率 {{ point.scoreRate }}%
                                </span>
                                <el-tag v-if="point.isWeak" type="danger" size="small">薄弱</el-tag>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue';
import * as echarts from 'echarts';

const classStats = ref({
    avgScore: 85.6,
    maxScore: 98,
    minScore: 65,
    passRate: 92,
    questions: [
        { id: 'Q001', errorRate: 25, avgScore: 85 },
        { id: 'Q002', errorRate: 45, avgScore: 72 },
        { id: 'Q003', errorRate: 15, avgScore: 92 },
        { id: 'Q004', errorRate: 35, avgScore: 78 },
        { id: 'Q005', errorRate: 60, avgScore: 65 }
    ],
    knowledgePoints: [
        { name: '变量声明', scoreRate: 75, isWeak: false },
        { name: '循环结构', scoreRate: 82, isWeak: false },
        { name: '函数定义', scoreRate: 68, isWeak: true },
        { name: '数组操作', scoreRate: 90, isWeak: false },
        { name: '异常处理', scoreRate: 58, isWeak: true }
    ]
});
const chartRef = ref<HTMLElement>();
onMounted(() => {
    if (chartRef.value) {
        const chart = echarts.init(chartRef.value);
        const option = {
            animation: false,
            tooltip: {
                trigger: 'axis',
            },
            xAxis: {
                type: 'category',
                data: ['变量声明', '循环结构', '函数定义', '数组操作', '异常处理'],
            },
            yAxis: {
                type: 'value',
                max: 100,
            },
            series: [
                {
                    name: '得分率',
                    type: 'bar',
                    data: [75, 82, 68, 90, 85],
                    itemStyle: {
                        color: '#3b82f6',
                    },
                },
            ],
        };
        chart.setOption(option);
    }
});
</script>

<style scoped></style>