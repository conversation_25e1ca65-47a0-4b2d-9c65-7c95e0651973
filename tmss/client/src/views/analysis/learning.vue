<!-- 代码已包含 CSS：使用 TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 -->

<template>
    <div class="flex-1 bg-gray-50 overflow-auto">
        <!-- 头部导航 -->
        <!-- <div class="bg-white shadow-sm">
            <div class="max-w-7xl mx-auto px-4 py-4 flex justify-between items-center">
                <div class="flex items-center space-x-4">
                    <el-date-picker v-model="dateRange" type="daterange" range-separator="至" start-placeholder="开始日期"
                        end-placeholder="结束日期" class="w-64" />
                    <el-button type="primary" class="!rounded-button whitespace-nowrap">筛选</el-button>
                </div>
            </div>
        </div> -->

        <!-- 数据概览卡片 -->
        <div class="max-w-7xl mx-auto px-4 py-6">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div v-for="(card, index) in overviewCards" :key="index" class="bg-white rounded-lg shadow p-6">
                    <div class="flex justify-between items-start">
                        <div>
                            <p class="text-gray-500 text-sm">{{ card.title }}</p>
                            <p class="text-2xl font-bold mt-2">{{ card.value }}</p>
                        </div>
                        <div
                            :class="`rounded-full p-2 ${card.trend > 0 ? 'bg-green-100 text-green-600' : 'bg-red-100 text-red-600'}`">
                            <i :class="`fas ${card.trend > 0 ? 'fa-arrow-up' : 'fa-arrow-down'}`"></i>
                            <span class="ml-1">{{ Math.abs(card.trend) }}%</span>
                        </div>
                    </div>
                    <p class="text-gray-400 text-xs mt-4">较上期</p>
                </div>
            </div>
        </div>

        <!-- 进度图表 -->
        <div class="max-w-7xl mx-auto px-4 py-6">
            <div class="bg-white rounded-lg shadow p-6">
                <h2 class="text-lg font-semibold text-gray-800 mb-6">学习进度趋势</h2>
                <div ref="chart" class="h-80"></div>
                <div class="flex justify-center space-x-8 mt-4">
                    <div class="flex items-center">
                        <div class="w-4 h-4 bg-blue-500 rounded-full mr-2"></div>
                        <span class="text-sm text-gray-600">完成率</span>
                    </div>
                    <div class="flex items-center">
                        <div class="w-4 h-4 bg-orange-500 rounded-full mr-2"></div>
                        <span class="text-sm text-gray-600">滞后率</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 学员列表和预警模块 -->
        <div class="max-w-7xl mx-auto px-4 py-6">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- 学员列表 -->
                <div class="lg:col-span-2">
                    <div class="bg-white rounded-lg shadow">
                        <div class="p-4 border-b border-gray-200 flex justify-between items-center">
                            <h2 class="text-lg font-semibold text-gray-800">学员进度详情</h2>
                            <div class="flex space-x-2">
                                <el-input v-model="searchQuery" placeholder="搜索学员" class="w-48" clearable>
                                    <template #prefix>
                                        <i class="fas fa-search"></i>
                                    </template>
                                </el-input>
                                <el-select v-model="filterStatus" placeholder="筛选状态" class="w-32">
                                    <el-option label="全部" value="all"></el-option>
                                    <el-option label="正常" value="normal"></el-option>
                                    <el-option label="滞后" value="lagging"></el-option>
                                </el-select>
                            </div>
                        </div>
                        <el-table :data="filteredStudents" class="w-full">
                            <el-table-column prop="name" label="学员" width="180">
                                <template #default="{ row }">
                                    <div class="flex items-center">
                                        <el-avatar icon="UserFilled" size="small" class="mr-2" />
                                        <!-- <img :src="row.avatar" class="w-8 h-8 rounded-full mr-3" /> -->
                                        <span>{{ row.name }}</span>
                                    </div>
                                </template>
                            </el-table-column>
                            <el-table-column prop="progress" label="进度" width="120">
                                <template #default="{ row }">
                                    <el-progress :percentage="row.progress"
                                        :color="row.progress >= 80 ? '#10B981' : row.progress >= 50 ? '#F59E0B' : '#EF4444'"></el-progress>
                                </template>
                            </el-table-column>
                            <el-table-column prop="lastStudyTime" label="最近学习" width="150"></el-table-column>
                            <el-table-column prop="studyHours" label="学习时长" width="120"></el-table-column>
                            <el-table-column prop="status" label="状态" width="100">
                                <template #default="{ row }">
                                    <el-tag :type="row.status === '正常' ? 'success' : 'danger'" size="small">
                                        {{ row.status }}
                                    </el-tag>
                                </template>
                            </el-table-column>
                        </el-table>
                    </div>
                </div>

                <!-- 学习预警 -->
                <div>
                    <div class="bg-white rounded-lg shadow">
                        <div class="p-4 border-b border-gray-200">
                            <h2 class="text-lg font-semibold text-gray-800">学习预警</h2>
                        </div>
                        <div class="p-4">
                            <div v-for="(alert, index) in alerts" :key="index" class="mb-4 last:mb-0">
                                <div class="flex justify-between items-center mb-2">
                                    <div class="flex items-center">
                                        <div
                                            :class="`w-3 h-3 rounded-full mr-2 ${alert.type === 'high' ? 'bg-red-500' : alert.type === 'medium' ? 'bg-yellow-500' : 'bg-blue-500'}`">
                                        </div>
                                        <span class="font-medium">{{ alert.title }}</span>
                                    </div>
                                    <span class="text-xs text-gray-500">{{ alert.count }}人</span>
                                </div>
                                <div class="bg-gray-50 rounded p-3">
                                    <div v-for="student in alert.students" :key="student.id"
                                        class="flex justify-between items-center py-2 border-b border-gray-100 last:border-0">
                                        <div class="flex items-center">
                                            <el-avatar icon="UserFilled" size="small" class="mr-2" />
                                            <!-- <img :src="student.avatar" class="w-6 h-6 rounded-full mr-2" /> -->
                                            <span class="text-sm">{{ student.name }}</span>
                                        </div>
                                        <el-button size="mini" class="!rounded-button whitespace-nowrap">提醒</el-button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue';
import * as echarts from 'echarts';

// 日期范围
const dateRange = ref<[Date, Date]>([new Date(), new Date()]);

// 数据概览卡片
const overviewCards = ref([
    { title: '整体完课率', value: '78%', trend: 5.2 },
    { title: '平均学习时长', value: '3.2h', trend: -1.8 },
    { title: '滞后人数', value: '12人', trend: 2.5 },
    { title: '活跃度指数', value: '86', trend: 3.1 }
]);

// 学员数据
const students = ref([
    { id: 1, name: '张明远', avatar: 'https://mastergo.com/ai/api/search-image?query=professional chinese male portrait with neutral expression wearing business casual attire on light gray background&width=100&height=100&orientation=squarish&flag=047dbb5ac9da70c5aa53d8fdae5b5ba4', progress: 92, lastStudyTime: '2023-06-15 14:30', studyHours: '12.5h', status: '正常' },
    { id: 2, name: '李思琪', avatar: 'https://mastergo.com/ai/api/search-image?query=professional chinese female portrait with friendly smile wearing casual clothes on light gray background&width=100&height=100&orientation=squarish&flag=d5443f0d58a19306ae254542bc93293b', progress: 65, lastStudyTime: '2023-06-14 09:15', studyHours: '8.2h', status: '滞后' },
    { id: 3, name: '王浩然', avatar: 'https://mastergo.com/ai/api/search-image?query=young chinese male portrait with glasses looking confident on light gray background&width=100&height=100&orientation=squarish&flag=58d77c0d72061a2d956b89f4729a743f', progress: 78, lastStudyTime: '2023-06-15 16:45', studyHours: '10.8h', status: '正常' },
    { id: 4, name: '陈雨桐', avatar: 'https://mastergo.com/ai/api/search-image?query=young chinese female portrait with straight hair and minimal makeup on light gray background&width=100&height=100&orientation=squarish&flag=544fd29423721813ed5ce34475f57965', progress: 45, lastStudyTime: '2023-06-10 11:20', studyHours: '6.3h', status: '滞后' },
    { id: 5, name: '刘志强', avatar: 'https://mastergo.com/ai/api/search-image?query=mature chinese male portrait with short hair and professional appearance on light gray background&width=100&height=100&orientation=squarish&flag=a78b99a789c1ec18672a6a7a3cbfb5ff', progress: 88, lastStudyTime: '2023-06-16 10:10', studyHours: '14.2h', status: '正常' },
    { id: 6, name: '赵雅雯', avatar: 'https://mastergo.com/ai/api/search-image?query=elegant chinese female portrait with wavy hair and subtle makeup on light gray background&width=100&height=100&orientation=squarish&flag=642ccac307658fd4496dbd41d9130e6b', progress: 32, lastStudyTime: '2023-06-05 13:40', studyHours: '4.7h', status: '滞后' },
    { id: 7, name: '周宇航', avatar: 'https://mastergo.com/ai/api/search-image?query=young chinese male portrait with short hair and casual style on light gray background&width=100&height=100&orientation=squarish&flag=ff3088acf7dea404e563955d8bc89364', progress: 95, lastStudyTime: '2023-06-16 18:20', studyHours: '15.6h', status: '正常' },
    { id: 8, name: '吴晓琳', avatar: 'https://mastergo.com/ai/api/search-image?query=professional chinese female portrait with neat bun and formal attire on light gray background&width=100&height=100&orientation=squarish&flag=8d6b65f7edee63341e83d772db6fea7a', progress: 50, lastStudyTime: '2023-06-12 15:10', studyHours: '7.9h', status: '滞后' }
]);

// 预警数据
const alerts = ref([
    {
        type: 'high',
        title: '进度严重滞后',
        count: 3,
        students: [
            { id: 4, name: '陈雨桐', avatar: 'https://mastergo.com/ai/api/search-image?query=young chinese female portrait with straight hair and minimal makeup on light gray background&width=100&height=100&orientation=squarish&flag=06a47f29ac860fb46fc899013ca33907' },
            { id: 6, name: '赵雅雯', avatar: 'https://mastergo.com/ai/api/search-image?query=elegant chinese female portrait with wavy hair and subtle makeup on light gray background&width=100&height=100&orientation=squarish&flag=74556f4f4f9087073207f82a3f7083a5' },
            { id: 8, name: '吴晓琳', avatar: 'https://mastergo.com/ai/api/search-image?query=professional chinese female portrait with neat bun and formal attire on light gray background&width=100&height=100&orientation=squarish&flag=e603ea1233c3763925952e20be6de488' }
        ]
    },
    {
        type: 'medium',
        title: '一周未学习',
        count: 2,
        students: [
            { id: 4, name: '陈雨桐', avatar: 'https://mastergo.com/ai/api/search-image?query=young chinese female portrait with straight hair and minimal makeup on light gray background&width=100&height=100&orientation=squarish&flag=fe716eb64425e51ed1c709621f55c2d5' },
            { id: 6, name: '赵雅雯', avatar: 'https://mastergo.com/ai/api/search-image?query=elegant chinese female portrait with wavy hair and subtle makeup on light gray background&width=100&height=100&orientation=squarish&flag=31119b4a333067648424d539c92b4c86' }
        ]
    },
    {
        type: 'low',
        title: '学习时间不足',
        count: 4,
        students: [
            { id: 2, name: '李思琪', avatar: 'https://mastergo.com/ai/api/search-image?query=professional chinese female portrait with friendly smile wearing casual clothes on light gray background&width=100&height=100&orientation=squarish&flag=cc3b539100810cf8e6a48d7a785a1fd6' },
            { id: 4, name: '陈雨桐', avatar: 'https://mastergo.com/ai/api/search-image?query=young chinese female portrait with straight hair and minimal makeup on light gray background&width=100&height=100&orientation=squarish&flag=db1275ae3935c569a574e2c65ef69764' },
            { id: 6, name: '赵雅雯', avatar: 'https://mastergo.com/ai/api/search-image?query=elegant chinese female portrait with wavy hair and subtle makeup on light gray background&width=100&height=100&orientation=squarish&flag=7f09e4d331ab18c2238d24fb4e3757ab' },
            { id: 8, name: '吴晓琳', avatar: 'https://mastergo.com/ai/api/search-image?query=professional chinese female portrait with neat bun and formal attire on light gray background&width=100&height=100&orientation=squarish&flag=33324a06f7eeac001bdcb7333e4b053b' }
        ]
    }
]);

// 搜索和筛选
const searchQuery = ref('');
const filterStatus = ref('all');

const filteredStudents = computed(() => {
    return students.value.filter(student => {
        const matchesSearch = student.name.includes(searchQuery.value);
        const matchesStatus = filterStatus.value === 'all' ||
            (filterStatus.value === 'normal' && student.status === '正常') ||
            (filterStatus.value === 'lagging' && student.status === '滞后');
        return matchesSearch && matchesStatus;
    });
});

// 图表
const chart = ref<HTMLElement | null>(null);

onMounted(() => {
    if (chart.value) {
        const myChart = echarts.init(chart.value);
        const option = {
            animation: false,
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'cross'
                }
            },
            legend: {
                data: ['完成率', '滞后率']
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                boundaryGap: false,
                data: ['6/1', '6/2', '6/3', '6/4', '6/5', '6/6', '6/7', '6/8', '6/9', '6/10', '6/11', '6/12', '6/13', '6/14', '6/15']
            },
            yAxis: [
                {
                    type: 'value',
                    name: '完成率',
                    min: 0,
                    max: 100,
                    axisLabel: {
                        formatter: '{value}%'
                    }
                },
                {
                    type: 'value',
                    name: '滞后率',
                    min: 0,
                    max: 100,
                    axisLabel: {
                        formatter: '{value}%'
                    }
                }
            ],
            series: [
                {
                    name: '完成率',
                    type: 'line',
                    smooth: true,
                    data: [45, 48, 52, 55, 58, 62, 65, 68, 72, 75, 78, 82, 85, 88, 92],
                    itemStyle: {
                        color: '#3B82F6'
                    }
                },
                {
                    name: '滞后率',
                    type: 'line',
                    smooth: true,
                    yAxisIndex: 1,
                    data: [25, 24, 23, 22, 21, 20, 19, 18, 17, 16, 15, 14, 13, 12, 11],
                    itemStyle: {
                        color: '#F59E0B'
                    }
                }
            ]
        };
        myChart.setOption(option);

        window.addEventListener('resize', () => {
            myChart.resize();
        });
    }
});
</script>

<style scoped>
.bg-gray-50 {
    background-color: #f9fafb;
}
</style>
