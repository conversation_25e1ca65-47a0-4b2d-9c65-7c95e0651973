<template>
    <div class="flex-1 flex flex-col bg-gray-50 overflow-auto">
        <!-- 搜索区域 -->
        <div class="p-6 border-b border-gray-200">
            <div class="mb-4 flex justify-start items-center">
                <el-button type="primary" @click="openCreateDialog">新建教案</el-button>
            </div>
            <div class="grid grid-cols-4 gap-4 flex-1">
                <!-- 教案标题 -->
                <div>
                    <el-input v-model="query.title" placeholder="请输入教案标题" clearable class="w-full" />
                </div>

                <!-- 状态 -->
                <div>
                    <el-select v-model="query.status" placeholder="请选择状态" clearable class="w-full">
                        <el-option v-for="(status, key) in statusMap" :key="key" :label="status" :value="key" />
                    </el-select>
                </div>

                <!-- 关联课时计划 -->
                <div>
                    <my-select v-model="query.class_schedule_id" :func="getClassScheduleList" labelKey="title"
                        valueKey="id" placeholder="请选择课时计划" searchKey="title" />
                </div>

                <div>
                    <el-button type="primary" class="mr-2" @click="getData">搜索</el-button>
                    <el-button @click="resetSearch">重置</el-button>
                </div>
            </div>
        </div>

        <!-- 教案表格 -->
        <div class="flex-1 overflow-auto p-6">
            <el-table :data="tableData" style="width: 100%" border v-loading="loading">
                <el-table-column prop="title" label="教案标题" show-overflow-tooltip />
                <el-table-column prop="class_schedule_title" label="关联课时计划" show-overflow-tooltip />
                <el-table-column prop="teacher_name" label="授课教师" width="120" />
                <el-table-column label="状态" width="100" align="center">
                    <template #default="{ row }">
                        <el-tag :type="tagTypeMap[row.status]">{{ statusMap[row.status] }}</el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="操作" width="280" align="center">
                    <template #default="{ row }">
                        <div class="flex justify-start">
                            <el-button size="small" @click="viewDetail(row)">查看</el-button>
                            <el-button size="small" v-if="canEdit(row)" type="primary"
                                @click="editItem(row)">编辑</el-button>
                            <el-button size="small" v-if="canDelete(row)" type="danger"
                                @click="deleteItem(row)">删除</el-button>
                            <ApproveButton v-if="canSubmit(row)" approve-code="teaching_document" :data-id="row.id"
                                :data-title="row.title" @success="getData" />
                        </div>
                    </template>
                </el-table-column>
            </el-table>
        </div>

        <!-- 分页 -->
        <div class="shrink-0 flex justify-center mt-4">
            <el-pagination v-model:current-page="page" v-model:page-size="page_size" :total="total"
                layout="prev, pager, next" @current-change="handleCurrentChange" />
        </div>

        <!-- 创建/编辑弹窗 -->
        <el-dialog v-model="formDialogVisible" :title="formTitle" width="80%" :close-on-click-modal="false"
            :destroy-on-close="true">
            <el-form ref="formRef" :model="form" :rules="rules" label-width="120px" label-position="right">
                <!-- 教案标题 -->
                <el-form-item label="教案标题" prop="title">
                    <el-input v-model="form.title" placeholder="请输入教案标题" />
                </el-form-item>
                <!-- 教案内容 -->
                <el-form-item label="教案内容" prop="content">
                    <Editor v-model="form.content" />
                    <!-- <el-input v-model="form.content" type="textarea" :rows="5" placeholder="请输入教案内容" /> -->
                </el-form-item>
                 <!-- 关联课表 -->
                 <el-form-item label="关联课表" prop="class_schedule_id">
                    <my-select v-model="form.class_schedule_id" :func="getClassScheduleList" labelKey="title"
                        valueKey="id" placeholder="关联课表" searchKey="title" :initData="initScheduleData" />
                </el-form-item>
                <!-- 关联课件 -->
                <el-form-item label="关联课件" prop="courseware_id">
                <my-select v-model="form.courseware_id" placeholder="请选择关联课件" :func="getCoursewareList"
                    labelKey="title" valueKey="id" searchKey="name" :initData="initCoursewareData" />
            </el-form-item>
            </el-form>

            <template #footer>
                <el-button @click="formDialogVisible = false">取消</el-button>
                <el-button type="primary" @click="submitForm">保存</el-button>
            </template>
        </el-dialog>

        <!-- 查看详情弹窗 -->
        <el-dialog v-model="detailDialogVisible" title="教案详情" width="800px">
            <el-descriptions v-if="currentItem" :column="1" border>
                <!-- <el-descriptions-item label="教案ID">{{ currentItem.id }}</el-descriptions-item> -->
                <el-descriptions-item label="教案标题">{{ currentItem.title }}</el-descriptions-item>
                <el-descriptions-item label="关联课表">{{ currentItem.class_schedule_title || '无'
                }}</el-descriptions-item>
                <el-descriptions-item label="关联课件">{{ currentItem.courseware_title || '无' }}</el-descriptions-item>
                <el-descriptions-item label="授课教师">{{ currentItem.teacher_name }}</el-descriptions-item>
                <el-descriptions-item label="状态">
                    <el-tag :type="tagTypeMap[currentItem.status]">{{ statusMap[currentItem.status] }}</el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="教案内容">
                    <!-- 安全提示：确保内容来源可信 -->
                    <div class="bg-gray-100 p-3 rounded" v-html="currentItem.content || '无'"></div>
                </el-descriptions-item>

                <!-- <el-descriptions-item label="创建时间">
                    {{ formatDate(currentItem.created_at) }}
                </el-descriptions-item> -->
                <el-descriptions-item label="最后更新时间">
                    {{ formatDate(currentItem.updated_at) }}
                </el-descriptions-item>
            </el-descriptions>
            <template #footer>
                <el-button @click="detailDialogVisible = false">关闭</el-button>
            </template>
        </el-dialog>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import {
    createTeachingDocument,
    updateTeachingDocument,
    deleteTeachingDocument,
    getTeachingDocumentsList,
    getTeachingDocumentDetail
} from '@/api/document';
import { getClassScheduleList } from '@/api/schedule';
import { getCoursewareList } from '@/api/courseware';
import { formatDate } from '@/utils/date.ts';

// 表格数据
const tableData = ref<any[]>([]);
const loading = ref(false);
const page = ref(1);
const page_size = ref(10);
const total = ref(0);
const formRef = ref<any>(null);

// 查询条件
const query = ref({
    title: '',
    status: '',
    class_schedule_id: ''
});

// 状态映射
const statusMap: Record<string, string> = {
    draft: '草稿',
    reviewing: '审核中',
    published: '已发布',
    rejected: '已驳回'
};

const tagTypeMap: Record<string, string> = {
    draft: 'primary',
    reviewing: 'warning',
    published: 'success',
    rejected: 'danger'
};

// 弹窗控制
const formDialogVisible = ref(false);
const detailDialogVisible = ref(false);
const currentItem = ref<any>(null);
const formTitle = ref('新建教案');
const initScheduleData = ref<any[]>([]);
const initCoursewareData = ref<any[]>([]);
// 表单数据
const form: any = ref({
    id: '',
    title: '',
    content: '',
    class_schedule_id: '',
    courseware_id: ''
});

// 表单验证规则
const rules = ref({
    title: [{ required: true, message: '请输入教案标题', trigger: 'blur' }],
    content: [{ required: true, message: '请输入教案内容', trigger: 'blur' }]
});

// 获取教案列表
const getData = async () => {
    loading.value = true;
    try {
        const params = {
            page: page.value,
            page_size: page_size.value,
            ...query.value
        };

        const res = await getTeachingDocumentsList(params);
        tableData.value = res.list || [];
        total.value = res.total || 0;
    } catch (error) {
        ElMessage.error('获取数据失败');
    } finally {
        loading.value = false;
    }
};

// 分页切换
const handleCurrentChange = (val: number) => {
    page.value = val;
    getData();
};

// 重置查询
const resetSearch = () => {
    query.value = { title: '', status: '', class_schedule_id: '' };
    getData();
};

// 打开创建弹窗
const openCreateDialog = () => {
    form.value = { id: '', title: '', content: '', class_schedule_id: '', courseware_id: '' };
    initScheduleData.value = [];
    initCoursewareData.value = [];
    formTitle.value = '新建教案';
    formDialogVisible.value = true;
};

// 编辑教案
const editItem = async (row: any) => {
    try {
        const detail = await getTeachingDocumentDetail(row.id);
        form.value = {
            id: detail.id * 1,
            title: detail.title,
            content: detail.content,
            class_schedule_id: detail.class_schedule_id,
            courseware_id: detail.courseware_id
        };

        // 设置初始化的课时计划数据
        if (detail.class_schedule_id) {
            initScheduleData.value = [{
                id: detail.class_schedule_id,
                title: detail.class_schedule_title
            }];
        }
        // 设置初始化的课件数据
        if (detail.courseware_id) {
            initCoursewareData.value = [{ id: detail.courseware_id, title: detail.courseware_title }];
        }


        formTitle.value = '编辑教案';
        formDialogVisible.value = true;
    } catch (error) {
        ElMessage.error('获取教案详情失败');
    }
};

// 提交表单
const submitForm = async () => {
    try {
        await formRef.value.validate();
        form.value.class_schedule_id = form.value.class_schedule_id * 1;
        form.value.courseware_id = form.value.courseware_id * 1;
        form.value.id = form.value.id * 1;
        if (form.value.id) {
            await updateTeachingDocument(form.value);
            ElMessage.success('教案更新成功');
        } else {

            await createTeachingDocument(form.value);
            ElMessage.success('教案创建成功');
        }

        formDialogVisible.value = false;
        getData();
    } catch (error) {
        // 验证失败或API错误
    }
};

// 删除教案
const deleteItem = (row: any) => {
    ElMessageBox.confirm('确定要删除该教案吗？', '提示', {
        type: 'warning'
    }).then(async () => {
        try {
            await deleteTeachingDocument(row.id);
            ElMessage.success('删除成功');
            getData();
        } catch (error) {
            ElMessage.error('删除失败');
        }
    });
};


// 查看详情
const viewDetail = async (row: any) => {
    try {
        const detail = await getTeachingDocumentDetail(row.id);
        currentItem.value = detail;
        detailDialogVisible.value = true;
    } catch (error) {
        ElMessage.error('获取教案详情失败');
    }
};

// 权限判断
const canEdit = (row: any) => ['draft', 'rejected'].includes(row.status);
const canDelete = (row: any) => ['draft', 'rejected'].includes(row.status);
const canSubmit = (row: any) => ['draft', 'rejected'].includes(row.status);

// 初始化
onMounted(() => {
    getData();
});
</script>

<style scoped>
.el-table {
    margin-top: 20px;
}

.el-form-item {
    margin-bottom: 20px;
}

.el-dialog__body {
    padding: 20px;
}
</style>