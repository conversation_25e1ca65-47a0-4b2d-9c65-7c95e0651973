<template>
    <div class="min-h-screen">
      <!-- 顶部操作区 -->
      <div class="mb-6 flex items-center justify-between">
        <div class="flex gap-4">
          <el-button type="primary" class="!rounded-button" @click="handleAdd">
            <el-icon class="mr-1"><Plus /></el-icon>新增菜单
          </el-button>
          <el-button class="!rounded-button" @click="toggleExpand">
            <el-icon class="mr-1"><CaretBottom /></el-icon>{{ isExpandAll ? '全部收起' : '全部展开' }}
          </el-button>
          <el-button class="!rounded-button" @click="refreshTable">
            <el-icon class="mr-1"><Refresh /></el-icon>刷新
          </el-button>
        </div>
      </div>
  
      <!-- 树形表格 -->
      <el-table
        ref="tableRef"
        :data="tableData"
        row-key="id"
        default-expand-all
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        class="w-full"
      >
        <el-table-column prop="name" label="菜单名称" min-width="200">
          <template #default="{ row }">
            <span class="flex items-center">
              <el-icon v-if="row.icon" class="mr-2"><component :is="row.icon" /></el-icon>
              {{ row.name }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="type" label="菜单类型" width="120">
          <template #default="{ row }">
            <el-tag :type="getTypeTag(row.type)">{{ row.type }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="path" label="路由地址" min-width="180" />
        <el-table-column prop="sort" label="排序号" width="100" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-switch v-model="row.status" :active-value="1" :inactive-value="0" @change="handleStatusChange(row)" />
          </template>
        </el-table-column>
        <el-table-column label="操作" width="280">
          <template #default="{ row }">
            <el-button-group class="mr-2">
              <el-button type="primary" link @click="handleEdit(row)">
                <el-icon><Edit /></el-icon>
              </el-button>
              <el-button type="primary" link @click="handleAddSub(row)">
                <el-icon><Plus /></el-icon>
              </el-button>
              <el-button type="primary" link @click="handleDelete(row)">
                <el-icon><Delete /></el-icon>
              </el-button>
            </el-button-group>
            <el-button-group>
              <el-button type="primary" link @click="handleMoveUp(row)">
                <el-icon><ArrowUp /></el-icon>
              </el-button>
              <el-button type="primary" link @click="handleMoveDown(row)">
                <el-icon><ArrowDown /></el-icon>
              </el-button>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>
  
      <!-- 编辑弹窗 -->
      <el-dialog
        v-model="dialogVisible"
        :title="dialogType === 'add' ? '新增菜单' : '编辑菜单'"
        width="500px"
      >
        <el-form ref="formRef" :model="formData" label-width="100px">
          <el-form-item label="上级菜单">
            <el-tree-select
              v-model="formData.parentId"
              :data="menuOptions"
              placeholder="请选择上级菜单"
              class="w-full"
            />
          </el-form-item>
          <el-form-item label="菜单名称" required>
            <el-input v-model="formData.name" placeholder="请输入菜单名称" />
          </el-form-item>
          <el-form-item label="菜单类型" required>
            <el-radio-group v-model="formData.type">
              <el-radio label="管理员">管理员</el-radio>
              <el-radio label="教员">教员</el-radio>
              <el-radio label="学员">学员</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="菜单图标">
            <el-select v-model="formData.icon" placeholder="请选择图标" class="w-full">
              <el-option label="Menu" value="Menu" />
              <el-option label="Document" value="Document" />
              <el-option label="Setting" value="Setting" />
            </el-select>
          </el-form-item>
          <el-form-item label="路由地址" required>
            <el-input v-model="formData.path" placeholder="请输入路由地址" />
          </el-form-item>
          <el-form-item label="排序号">
            <el-input-number v-model="formData.sort" :min="0" :max="999" />
          </el-form-item>
          <el-form-item label="状态">
            <el-switch v-model="formData.status" :active-value="1" :inactive-value="0" />
          </el-form-item>
        </el-form>
        <template #footer>
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit">确定</el-button>
        </template>
      </el-dialog>
    </div>
  </template>
  
  <script lang="ts" setup>
  import { ref } from 'vue';
  import { ElMessage, ElMessageBox } from 'element-plus';
  import { Plus, CaretBottom, Refresh, Edit, Delete, ArrowUp, ArrowDown, Menu, Document, Setting } from '@element-plus/icons-vue';
  
  const isExpandAll = ref(true);
  const dialogVisible = ref(false);
  const dialogType = ref<'add' | 'edit'>('add');
  const tableRef = ref();
  const formRef = ref();
  
  const tableData = ref([
    {
      id: 1,
      name: '系统管理',
      type: '管理员',
      icon: 'Setting',
      path: '/system',
      sort: 1,
      status: 1,
      children: [
        {
          id: 11,
          name: '用户管理',
          type: '管理员',
          icon: 'Document',
          path: '/system/user',
          sort: 1,
          status: 1
        },
        {
          id: 12,
          name: '角色管理',
          type: '管理员',
          icon: 'Document',
          path: '/system/role',
          sort: 2,
          status: 1
        }
      ]
    },
    {
      id: 2,
      name: '课程中心',
      type: '教员',
      icon: 'Menu',
      path: '/course',
      sort: 2,
      status: 1,
      children: [
        {
          id: 21,
          name: '课程管理',
          type: '教员',
          icon: 'Document',
          path: '/course/manage',
          sort: 1,
          status: 1
        }
      ]
    }
  ]);
  
  const formData = ref({
    parentId: null,
    name: '',
    type: '管理员',
    icon: '',
    path: '',
    sort: 0,
    status: 1
  });
  
  const menuOptions = ref([
    {
      value: 0,
      label: '顶级菜单'
    }
  ]);
  
  const getTypeTag = (type: string) => {
    switch (type) {
      case '管理员':
        return 'danger';
      case '教员':
        return 'warning';
      case '学员':
        return 'success';
      default:
        return '';
    }
  };
  
  const toggleExpand = () => {
    isExpandAll.value = !isExpandAll.value;
    const table = tableRef.value;
    if (isExpandAll.value) {
      table.expandAll();
    } else {
      table.collapseAll();
    }
  };
  
  const refreshTable = () => {
    ElMessage.success('刷新成功');
  };
  
  const handleAdd = () => {
    dialogType.value = 'add';
    formData.value = {
      parentId: null,
      name: '',
      type: '管理员',
      icon: '',
      path: '',
      sort: 0,
      status: 1
    };
    dialogVisible.value = true;
  };
  
  const handleEdit = (row: any) => {
    dialogType.value = 'edit';
    formData.value = { ...row };
    dialogVisible.value = true;
  };
  
  const handleAddSub = (row: any) => {
    dialogType.value = 'add';
    formData.value = {
      parentId: row.id,
      name: '',
      type: row.type,
      icon: '',
      path: '',
      sort: 0,
      status: 1
    };
    dialogVisible.value = true;
  };
  
  const handleDelete = (row: any) => {
    ElMessageBox.confirm('确认删除该菜单吗？', '提示', {
      type: 'warning'
    }).then(() => {
      ElMessage.success('删除成功');
    });
  };
  
  const handleMoveUp = (row: any) => {
    ElMessage.success('上移成功');
  };
  
  const handleMoveDown = (row: any) => {
    ElMessage.success('下移成功');
  };
  
  const handleStatusChange = (row: any) => {
    ElMessage.success(`${row.status === 1 ? '启用' : '禁用'}成功`);
  };
  
  const handleSubmit = () => {
    ElMessage.success(`${dialogType.value === 'add' ? '新增' : '编辑'}成功`);
    dialogVisible.value = false;
  };
  </script>
  
  <style scoped>
  .el-button-group .el-button--primary.is-link:first-child:not(:last-child) {
    border-right: 1px solid var(--el-button-border-color);
  }
  .el-button-group .el-button--primary.is-link:last-child:not(:first-child) {
    border-left: 1px solid var(--el-button-border-color);
  }
  </style>
  
  