<template>
  <div class="flex-1 flex flex-col bg-gray-50 overflow-auto">
    <!-- 筛选区域 -->
    <div class="p-6 border-b border-gray-200">
      <div class="flex justify-start mb-4">
        <el-button type="primary" class="!rounded-button" @click="createPlan">
          新增教学计划
        </el-button>
        <el-button type="success" class="mb-4 !rounded-button whitespace-nowrap ml-auto"
          @click="reviseListDrawerVisible = true">
          <el-icon class="mr-1">
            <View />
          </el-icon>
          修订列表
        </el-button>
      </div>
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div class="flex items-center gap-4">
          <el-input placeholder="搜索计划名称" v-model="name" clearable></el-input>
          <el-select v-model="statusFilter" placeholder="请选择状态">
            <el-option v-for="status in statusOptions" :key="status.value" :label="status.label"
              :value="status.value" />
          </el-select>
        </div>

        <div class="flex items-center">
          <el-button class="!rounded-button ml-2" type="primary" @click="getData">搜索</el-button>
          <el-button class="!rounded-button ml-2" @click="resetFilters">重置</el-button>
        </div>
      </div>
    </div>

    <!-- 教学计划列表 -->
    <div class="flex-1 overflow-auto p-6">
      <el-table :data="tableData" style="width: 100%" border>
        <el-table-column align="center" v-for="item in cols" :key="item.prop" :prop="item.prop"
          :label="item.label" show-overflow-tooltip />

        <el-table-column prop="status" label="状态" width="120" align="center">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.plan.status)" effect="light" class="!rounded-button">
              {{ getStatusLabel(row.plan.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="classes" label="关联班级">
          <template #default="{ row }">
            {{row.plan.classes.map((c: any) => c.name).join(', ') || '无'}}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="190">
          <template #default="{ row }">
            <div class="flex justify-center">
              <el-button v-if="row.plan.status == 'published' || row.plan.status == 'reviewing' || row.plan.status == 'deleted'"
                size="small" @click="viewPlan(row.plan)">查看</el-button>
              <el-button v-if="row.plan.status == 'draft' || row.plan.status == 'rejected'" size="small"
                type="primary" @click="editPlan(row.plan)">
                编辑
              </el-button>
              <el-button v-if="row.plan.status == 'draft' || row.status == 'rejected'" size="small"
                type="danger" @click="deletePlan(row.plan)">
                删除
              </el-button>
              <ApproveButton :approve-code="approveCode" :data-id="row.plan.id"
                :data-title="row.plan.name"
                v-if="row.plan.status == 'draft' || row.plan.status == 'rejected'" @success="getData" />
            </div>
          </template>
        </el-table-column>
        <!-- 修订 -->
        <el-table-column label="修订" width="190" align="center">
          <template #default="{ row }">
            <RevisionActions :approve-code="approveCode" :data="row.plan" :rev="row.rev"
              :show-create-button="row.plan.status === 'published' && !row.rev"
              @create="showReviseCreateDialog(row.plan)" @edit="handleReviseEdit(row.rev)"
              @delete="handleReviseDelete(row.rev)" @view="handleReviseView(row.rev)"
              @refresh="getData" />
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class=" flex justify-center mt-4">
      <el-pagination v-model:current-page="page" v-model:page-size="pageSize" :total="total"
        layout="prev, pager, next" @current-change="handleCurrentChange" />
    </div>

    <!-- 新增/编辑教学计划对话框 -->
    <TeachingPlanDialogForm
      v-model="dialogVisible"
      :action-type="actionType"
      :dialog-title="dialogTitle"
      :row="selectedRow"
      @success="getData"
    />

    <!-- 修订列表 -->
    <el-drawer title="计划修订列表" v-model="reviseListDrawerVisible" size="90%" body-class="my-drawer">
      <RevisonList :approve-code="approveCode" />
    </el-drawer>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import { createTeachingPlan, deleteTeachingPlan, getTeachingPlanList } from '@/api/teaching-plan';
import { confirmMsg, errMsg, successMsg } from '@/utils/msg';
import { ElMessageBox } from 'element-plus';
import { deleteRevision } from '@/api/revision'
import { statusOptions, getStatusLabel, getStatusTagType } from '@/api/status'


// 筛选条件
const approveCode = ref('teaching_plan')
const reviseListDrawerVisible = ref(false)
const dialogVisible = ref(false)
const actionType = ref('create')
const dialogTitle = ref('新增教学计划')
const selectedRow = ref(null) // 用于传递 row

const statusFilter = ref('');
const name = ref('')
const page = ref(1);
const pageSize = ref(10);
const total = ref(0)
const tableData: any = ref([])
const cols = [
  { label: '计划名称', prop: 'plan.name' },
  { label: '开始时间', prop: 'plan.start_at' },
  { label: '结束时间', prop: 'plan.end_at' },
  { label: '关联大纲', prop: 'plan.syllabus_name' },
]

const getData = async () => {
  const params = {
    page: page.value,
    ...(name.value && { name: name.value }),
    ...(statusFilter.value && { status: statusFilter.value }),
  }
  const res = await getTeachingPlanList(params)
  tableData.value = res.list || []
  total.value = res.total || 0
}

const handleCurrentChange = (val: any) => {
  page.value = val
  getData()
}

/** 新增教学计划 */
const createPlan = () => {
  dialogTitle.value = '新增教学计划';
  actionType.value = 'create';
  selectedRow.value = null;
  dialogVisible.value = true;
}

const viewPlan = (plan: any) => {
  actionType.value = 'view'
  dialogTitle.value = '查看教学计划';
  selectedRow.value = plan
  dialogVisible.value = true;
}

const editPlan = (plan: any) => {
  actionType.value = 'edit'
  dialogTitle.value = '编辑教学计划';
  selectedRow.value = plan
  dialogVisible.value = true;
}

const deletePlan = (plan: any) => {
  confirmMsg('确定删除吗', '提示', async (action) => {
    if (action) {
      const res = await deleteTeachingPlan(plan.id)
      successMsg(res.message)
      getData()
    }
  })
}

// 修订相关方法
const showReviseCreateDialog = async (row: any) => {
  dialogTitle.value = '创建修订';
  actionType.value = 'revise-create';
  selectedRow.value = row
  dialogVisible.value = true
}

const handleReviseEdit = async (row: any) => {
  dialogTitle.value = '编辑修订';
  actionType.value = 'revise-edit';
  selectedRow.value = row
  dialogVisible.value = true
}

const handleReviseView = async (row: any) => {
  dialogTitle.value = '查看修订';
  actionType.value = 'revise-view';
  selectedRow.value = row
  dialogVisible.value = true
}

const handleReviseDelete = async (row: any) => {
  ElMessageBox.confirm(`确定要删除修订吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    const res = await deleteRevision(row.id)
    getData()
    successMsg('删除成功')
    if (!res.success) {
      errMsg(res.message)
    }
  })
}

onMounted(() => {
  getData()
});

/** 重置搜索 */
const resetFilters = () => {
  name.value = ''
  statusFilter.value = "";
  getData()
};
</script>

<style scoped>
.el-table {
  margin-top: 16px;
}

.el-form-item {
  margin-bottom: 16px;
}

.el-button {
  white-space: nowrap;
}
</style>