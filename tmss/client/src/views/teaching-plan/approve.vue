<template>
    <div class="flex-1 flex flex-col bg-gray-50 overflow-auto">
        <!-- 列表区域 -->
        <div class="flex-1 overflow-auto p-6">
            <el-table :data="tableData" v-loading="loading" style="width: 100%" border>
                <el-table-column align="center" v-for="item in cols" :key="item.prop" :prop="item.prop"
                    :label="item.label" show-overflow-tooltip />
                <el-table-column prop="created_at" label="创建时间">
                    <template #default="{ row }">
                        {{ formatDate(row.created_at) }}
                    </template>
                </el-table-column>
                <el-table-column prop="classes" label="关联班级">
                    <template #default="{ row }">
                        {{row.classes.map((c: any) => c.name).join(', ') || '无'}}
                    </template>
                </el-table-column>
                <el-table-column label="操作" align="center" width="240">
                    <template #default="{ row }">
                        <div class="flex justify-center">
                            <el-button plain @click="view(row)">查看</el-button>
                            <el-button type="primary" plain @click="review(row.id)">通过</el-button>
                            <el-button type="danger" plain @click="reject(row.id)">驳回</el-button>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
        </div>

        <!-- 分页区域 -->
        <div class="shrink-0 flex justify-center mt-4 pb-2">
            <el-pagination background v-model:current-page="page" v-model:page-size="pageSize" :total="total"
                layout=" prev, pager, next" @current-change="handleCurrentChange" />
        </div>

        <el-dialog v-model="showCreateDialog" title="查看详情" width="60%" top="5vh">
            <el-form v-if="showCreateDialog" :model="planForm" label-width="120px" label-position="top" disabled>
                <el-form-item label="教学大纲">
                    <my-select v-model="planForm.teaching_plan.syllabus_id" :func="getSyllabusList"
                        :extraParams="{ status: 'published' }" placeholder="请选择关联的教学大纲" labelKey="syllabus.name"
                        valueKey="syllabus.id" searchKey="name" :initData="initSyllabus" />
                </el-form-item>
                <el-form-item label="计划名称">
                    <el-input v-model="planForm.teaching_plan.name" placeholder="请输入计划名称" />
                </el-form-item>
                <el-form-item label="学期">
                    <el-input v-model="planForm.teaching_plan.term" placeholder="学期格式建议为 YYYY-MM-DD~YYYY-MM-DD" />
                </el-form-item>
                <el-form-item label="教室需求">
                    <el-input v-model="planForm.teaching_plan.classroom_requirements" placeholder="描述教室需求"
                        type="textarea" :rows="3" />
                </el-form-item>
                <el-form-item label="关联班级">
                    <my-select v-model="planForm.class_ids" multiple placeholder="请选择关联的班级" :func="getClassList"
                        labelKey="name" valueKey="id" searchKey="name" :initData="initClass" />
                </el-form-item>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="开始时间">
                            <el-date-picker v-model="planForm.start_at" type="date" placeholder="选择开始时间"
                                value-format="YYYY-MM-DD"></el-date-picker>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">

                        <el-form-item label="结束时间">
                            <el-date-picker value-format="YYYY-MM-DD" v-model="planForm.end_at" type="date"
                                placeholder="选择开始时间"></el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
        </el-dialog>
    </div>
</template>

<script setup lang="ts">
import { getClassList } from '@/api/class';
import { getSyllabusList } from '@/api/syllabus';
import { getTeachingPlanReviewingList, rejectTeachingPlan, reviewTeachingPlan } from '@/api/teaching-plan';
import { confirmMsg, successMsg } from '@/utils/msg';
import { computed, onMounted, ref, watch } from 'vue';

const page = ref(1)
const pageSize = ref(10)
const total = ref(0)
const handleCurrentChange = (val: number) => {
    page.value = val
    getData()
}
const loading = ref(false)
const tableData = ref([])
const cols = [
    { label: '计划名称', prop: 'name' },
    { label: '开始时间', prop: 'start_at' },
    { label: '结束时间', prop: 'end_at' },
    { label: '关联大纲', prop: 'syllabus_name' },
    { label: '学期', prop: 'term' },
]

const formatDate = (date: any) => {
    return new Date(date * 1000).toLocaleDateString().replace(/\//g, '-').split('-').map((item: any) => item.padStart(2, '0')).join('-');
};
const getData = async () => {
    loading.value = true

    const res = await getTeachingPlanReviewingList({ page: page.value })
    loading.value = false

    console.log(res, '```')
    tableData.value = res.list || []
    total.value = res.total || 0
}

const reject = (id: any) => {
    confirmMsg('确定要驳回吗？', '提示', async (action) => {
        console.log(action)
        if (action) {
            const res = await rejectTeachingPlan(id)
            successMsg(res.message)
            getData()
        }
    })
}
const review = (id: any) => {
    confirmMsg('确定要通过吗？', '提示', async (action) => {
        console.log(action)
        if (action) {
            const res = await reviewTeachingPlan(id)
            successMsg(res.message)
            getData()
        }
    })
}
// dialog
const showCreateDialog = ref(false);
watch(showCreateDialog, (val) => {
    if (!val) {
        planForm.value = initForm();
    }
})

//教学计划表单数据
const initForm = () => {
    return {
        "teaching_plan": {
            "syllabus_id": '',
            "name": "",
            "term": "",
            "classroom_requirements": ""
        },
        "class_ids": [],
        "start_at": "",
        "end_at": ""
    }
}
const planForm: any = ref(initForm());




const setPlanForm = (plan: any) => {
    initClass.value = plan.classes
    initSyllabus.value = [{
        syllabus: {
            id: plan.syllabus_id,
            name: plan.syllabus_name
        }
    }]
    return {
        teaching_plan: {
            id: plan.id,
            syllabus_id: plan.syllabus_id,
            name: plan.name,
            term: plan.term,
            classroom_requirements: plan.classroom_requirements

        },
        class_ids: plan.classes.map((item: any) => item.id),
        start_at: plan.start_at,
        end_at: plan.end_at
    }
};
// 回显大纲
const initSyllabus: any = ref([])
// 回显班级
const initClass: any = ref([])
const view = (plan: any) => {
    planForm.value = setPlanForm(plan)
    showCreateDialog.value = true;
};



onMounted(() => {
    getData()
})
</script>