<!-- src/views/schedule/index.vue (更新后的版本) -->
<template>
  <div class="flex-1 flex flex-col bg-gray-50 overflow-auto">
    <!-- Search Section -->
    <div class="p-6 border-b border-gray-200">
      <div class="mb-4 flex justify-start items-center">
        <el-button type="primary" @click="addSchedule()">新建课表</el-button>
        <BatchApprove approve-code="class_schedule" :data-list="selectedSchedules" @success="getData" />
      </div>
      <div class="grid grid-cols-4 gap-4 flex-1">
        <!-- 课表名称 -->
        <div>
          <el-input v-model="query.title" placeholder="请输入课表名称" clearable class="w-full" />
        </div>

        <!-- 教学计划 -->
        <div>
            <TeachingPlanSelect v-model="query.teaching_plan_id"  />
        </div>

        <!-- 状态 -->
        <div>
          <el-select v-model="query.status" placeholder="请选择状态" clearable class="w-full">
            <el-option v-for="status in statusOptions" :key="status.value" :label="status.label"
              :value="status.value" />
          </el-select>
        </div>

        <div>
          <el-button type="primary" class="mr-2" @click="getData">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </div>
      </div>
    </div>

    <!-- Class Schedule Table -->
    <div class="flex-1 overflow-auto p-6">
      <el-table 
        :data="tableData" 
        style="width: 100%" 
        border 
        v-loading="loading"
        @selection-change="handleSelectionChange"
      >
        <el-table-column 
          type="selection" 
          width="55" 
          align="center"
          :selectable="(row: any) => ['draft', 'rejected'].includes(row.status)" 
        />
        <el-table-column 
          align="center" 
          v-for="col in cols" 
          :key="col.prop" 
          :prop="col.prop" 
          :label="col.label"
          show-overflow-tooltip 
        />
        <el-table-column prop="status" align="center" label="状态" width="100">
          <template #default="{ row: { status } }">
            <el-tag :type="getStatusTagType(status)" effect="light" class="!rounded-button">
              {{ getStatusLabel(status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="280" align="center">
          <template #default="{ row }">
            <div class="flex justify-start">
              <el-button 
                size="small" 
                v-if="row.status === 'published'"
                @click="viewClassSchedule(row)"
              >
                查看
              </el-button>
              <el-button 
                size="small" 
                v-if="['draft', 'rejected'].includes(row.status)" 
                type="primary"
                @click="editClassSchedule(row)"
              >
                编辑
              </el-button>
              <el-button 
                size="small" 
                v-if="['draft', 'rejected'].includes(row.status)" 
                type="danger"
                @click="deleteClassSchedule(row)"
              >
                删除
              </el-button>
              <ApproveButton 
                v-if="['draft', 'rejected'].includes(row.status)"
                approve-code="class_schedule" 
                :data-id="row.id" 
                :data-title="row.title"
                @success="getData" 
              />
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- Pagination -->
    <div class="shrink-0 flex justify-center mt-4">
      <el-pagination 
        v-model:current-page="page" 
        v-model:page-size="page_size" 
        :total="total"
        layout="prev, pager, next" 
        @current-change="handleCurrentChange" 
      />
    </div>

    <!-- 添加课表弹窗 -->
    <ScheduleCreateForm 
      v-model="createDialogVisible" 
      @success="getData" 
    />

    <!-- 编辑课表弹窗 -->
    <ScheduleEditForm 
      v-model="editDialogVisible" 
      :row-data="selectedRow"
      @success="getData" 
    />

    <!-- 查看详情弹窗 -->
    <ScheduleViewForm 
      v-model="viewDialogVisible" 
      :row-data="selectedRow"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import {
  delClassSchedule,
  getClassScheduleList
} from '@/api/schedule';
import { statusOptions, getStatusLabel, getStatusTagType } from '@/api/status'

// 表格数据
const tableData = ref([]);
const loading = ref(false);
const page = ref(1);
const page_size = ref(10);
const total = ref(0);

// 查询条件
const query = ref({
  title: '',
  teaching_plan_id: '',
  status: '',
});

// 弹窗状态
const createDialogVisible = ref(false)
const editDialogVisible = ref(false)
const viewDialogVisible = ref(false)

// 选中行数据
const selectedRow = ref<any>(null)
const selectedSchedules = ref<any[]>([]);

// 表格列定义
const cols = [
  { prop: 'title', label: '课表名称' },
  { prop: 'teaching_plan_name', label: '教学计划' },
  { prop: 'course_name', label: '课程' },
  { prop: 'classroom_name', label: '教室' },
  { prop: 'teacher_name', label: '授课教师' },
];



// 获取数据
const getData = async () => {
  loading.value = true;
  const params = {
    page: page.value,
    ...(query.value.title && { title: query.value.title }),
    ...(query.value.teaching_plan_id && { teaching_plan_id: query.value.teaching_plan_id }),
    ...(query.value.status && { status: query.value.status }),
  };
  try {
    const res = await getClassScheduleList(params);
    tableData.value = res.list || [];
    total.value = res.total || 0;
  } finally {
    loading.value = false;
  }
};

// 分页切换
const handleCurrentChange = (val: number) => {
  page.value = val;
  getData();
};

// 重置查询
const resetSearch = () => {
  query.value = { title: '', teaching_plan_id: '', status: '' };
  getData();
};

// 打开添加弹窗
const addSchedule = () => {
  createDialogVisible.value = true;
};

// 删除课表
const deleteClassSchedule = async (row: any) => {
  ElMessageBox.confirm('确定要删除该课表吗？', '提示', {
    type: 'warning',
  }).then(async () => {
    try {
      await delClassSchedule(row.id);
      ElMessage.success('删除成功');
      getData();
    } catch (error) {
      ElMessage.error('删除失败');
    }
  });
};

// 查看课表
const viewClassSchedule = async (row: any) => {
  selectedRow.value = row
  viewDialogVisible.value = true;
};

// 编辑课表
const editClassSchedule = async (row: any) => {
  selectedRow.value = row
  editDialogVisible.value = true
}

const handleSelectionChange = (selection: any[]) => {
  selectedSchedules.value = selection.map(item => {
    return { id: item.id, title: item.title }
  });
};

// 初始化
onMounted(() => {
  getData();
});
</script>

<style scoped>
.el-table {
  margin-top: 20px;
}

.el-form-item {
  margin-bottom: 20px;
}

.el-dialog__body {
  padding: 20px;
}

.el-select {
  width: 100%;
}

.el-input__inner {
  height: 36px;
}
</style>