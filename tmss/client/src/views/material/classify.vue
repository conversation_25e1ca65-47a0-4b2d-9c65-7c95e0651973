<template>
    <div class="flex-1 flex overflow-hidden bg-gray-50 p-6">

        <!-- 主体内容 -->
        <div class="flex flex-1 overflow-hidden gap-4">
            <!-- 分类树形结构 -->
            <div class="w-full overflow-auto flex flex-col">
                <div class="bg-white shadow-sm rounded-lg flex-1">
                    <div class="flex items-center justify-between p-4 border-b border-gray-200">
                        <h2 class="text-lg font-medium text-gray-900">资料分类</h2>
                        <el-button type="primary" @click="showAddCategoryDialog">
                            <el-icon class="mr-1">
                                <Plus />
                            </el-icon>
                            新增分类
                        </el-button>
                    </div>
                    <div class="p-4">
                        <el-tree :data="categories" node-key="id" :props="defaultProps" :expand-on-click-node="false"
                            default-expand-all @node-click="handleCategorySelect" class="border-none">
                            <template #default="{ node, data }">
                                <div class="group flex items-center">
                                    <span>{{ node.label }}</span>
                                    <div class="opacity-0 group-hover:opacity-100 flex">
                                        <el-button class="!p-0 !ml-4 !h-full" text @click.stop="handleEdit(data)">
                                            <el-icon :size="20" color="#333">
                                                <Edit />
                                            </el-icon>
                                        </el-button>
                                        <el-button class="!p-0 !ml-4 !h-full" text @click.stop="handleDelete(data)">
                                            <el-icon :size="20" color="#333">
                                                <Delete />
                                            </el-icon>
                                        </el-button>
                                    </div>
                                </div>
                            </template>
                        </el-tree>
                    </div>
                </div>
            </div>

            <!-- 新增/编辑分类弹窗 -->
            <el-dialog v-model="showCategoryDialog" :title="isEdit ? '编辑分类' : '新增分类'" width="500px"
                :destroy-on-close="true">
                <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
                    <el-form-item label="分类名称" prop="name">
                        <el-input v-model="form.name" placeholder="请输入分类名称" />
                    </el-form-item>
                    <el-form-item label="父级分类" prop="parent_id">
                        <el-tree-select placeholder="父级分类" v-model="form.parent_id" :data="categories"
                            :props="{ value: 'id', label: 'name' }" check-strictly clearable class="w-full" />
                    </el-form-item>
                    <el-form-item label="关联章节" prop="chapter_id">
                        <el-tree-select placeholder="选择章节" v-model="form.chapter_id" :data="chapterOptions"
                            :props="chapterProps" clearable check-strictly class="w-full" />
                    </el-form-item>
                </el-form>
                <template #footer>
                    <el-button @click="showCategoryDialog = false">取消</el-button>
                    <el-button type="primary" @click="saveCategory">保存</el-button>
                </template>
            </el-dialog>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import { msg, confirmMsg as confirm } from "@/utils/msg";
import { chapterList } from "@/api/chapter";
import { materialCateList, createMaterialCate, updateMaterialCate, deleteMaterialCate } from "@/api/material";
import type { CreateMaterialParams, UpdateMaterialParams } from "@/api/material";

// 分类数据
const categories = ref([]);
const defaultProps = {
    children: "children",
    label: "name",
};
// 当前选中的分类
const selectedCategory = ref<any>(null);
// 显示分类对话框相关
const showCategoryDialog = ref(false);
//编辑状态
const isEdit = ref<boolean>(false);
//新建表单实例
const formRef = ref();
//新建表单数据
const form = ref({
    id: 0,
    name: "",
    parent_id: "",
    chapter_id: ""
});
// 表单验证规则
const rules = {
    name: [
        { required: true, message: "分类名称不能为空", trigger: "blur" }
    ]
};
//章节选择器prop映射
const chapterProps = {
    label: "name",
    value: "id"
}
//章节选择列表
const chapterOptions = ref([]);
//是否提交
let inSubmit = false;

/** 点击编辑分类 */
const handleEdit = (cate: any) => {
    isEdit.value = true;
    console.log(cate);
    form.value = {
        id: cate.id,
        name: cate.name,
        parent_id: cate.parent_id,
        chapter_id: cate.chapter_id
    };
    showCategoryDialog.value = true;
}

/** 分类选择 */
const handleCategorySelect = (node: any) => {
    selectedCategory.value = node;
    showAddCategoryDialog();
};

/** 新增分类 */
const showAddCategoryDialog = () => {
    isEdit.value = false;
    form.value = {
        id: 0,
        name: "",
        parent_id: selectedCategory.value ? selectedCategory.value.id : "",
        chapter_id: ""
    };
    selectedCategory.value = null;
    showCategoryDialog.value = true;
};

/** 保存分类 */
const saveCategory = () => {
    if (inSubmit) {
        msg("warning", "请勿重复提交");
        return;
    }
    formRef.value?.validate(async (valid: any) => {
        if (valid) {
            if (!form.value.chapter_id) {
                msg("warning", "请选择关联章节");
                return;
            }
            let res: any;
            inSubmit = true;
            try {
                if (isEdit.value) {
                    const data: UpdateMaterialParams = {
                        ...form.value,
                        parent_id: +form.value.parent_id || 0,
                        chapter_id: +form.value.chapter_id
                    }
                    res = await updateMaterialCate(data);
                } else {
                    const data: CreateMaterialParams = {
                        name: form.value.name,
                        parent_id: +form.value.parent_id || 0,
                        chapter_id: +form.value.chapter_id
                    }
                    res = await createMaterialCate(data);
                }
                if (res.code == 0) {
                    msg("success", isEdit.value ? "编辑成功" : "新增成功");
                    showCategoryDialog.value = false;
                    getMaterialCateList();
                }
                inSubmit = false;
            } catch (e) {
                inSubmit = false;
            }
        }
    });
};

/** 删除分类 */
const handleDelete = (cate: any) => {
    confirm("确定删除分类吗？", "删除资料分类", async action => {
        if (action) {
            try {
                const res = await deleteMaterialCate(cate.id);
                if (res.code == 0) {
                    msg("success", "删除成功");
                    getMaterialCateList();
                } else {
                    msg("error", res.message);
                }
            } catch (e) { }
        }
    });
}

/** 获取章节列表 */
const getChapterList = async () => {
    try {
        const res = await chapterList();
        if (res.code == 0) {
            chapterOptions.value = res.data || [];
        }
    } catch (e) { }
}

/** 获取分类列表 */
const getMaterialCateList = async () => {
    try {
        const res = await materialCateList();
        if (res.code == 0) {
            categories.value = res.data || [];
        }
    } catch { }
}

// 初始化默认选择第一个分类
if (categories.value.length > 0) {
    selectedCategory.value = categories.value[0];
}

getMaterialCateList();
getChapterList();
</script>

<style scoped>
.material-card:hover {
    transform: translateY(-2px);
    transition: transform 0.2s ease-in-out;
}
</style>