<!-- src/views/material/upload.vue -->
<template>
  <div class="flex-1 flex flex-col bg-gray-50 overflow-auto p-6">
    <!-- 搜索和操作区域 -->
    <div class=" mb-6 flex justify-between items-center">
      <div class="flex-1 max-w-md">
        <el-input v-model="searchQuery" placeholder="搜索资料名称、关键词..." class="!rounded-[6px]">
          <template #prefix>
            <el-icon><i class="fas fa-search"></i></el-icon>
          </template>
        </el-input>
      </div>
      <div class="flex space-x-3">
        <el-button type="primary" class="!rounded-[6px] whitespace-nowrap" @click="addMaterial">
          <el-icon class="mr-1" color="#fff">
            <Plus />
          </el-icon>
          新建资料
        </el-button>
        <el-button type="success" class="mb-4 !rounded-button whitespace-nowrap ml-auto"
          @click="reviseListDrawerVisible = true">
          <el-icon class="mr-1">
            <View />
          </el-icon>
          修订列表
        </el-button>
      </div>
    </div>

    <!-- 高级搜索 -->
    <div class="mb-6 bg-white p-4 rounded-lg shadow-sm">
      <div class="flex items-center justify-between">
        <h3 class="text-sm font-medium text-gray-700">高级搜索</h3>
        <el-button type="link" @click="toggleAdvancedSearch">
          {{ showAdvancedSearch ? '收起' : '展开' }}
        </el-button>
      </div>
      <el-collapse-transition>
        <div v-show="showAdvancedSearch" class="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4">
          <!-- <el-tree-select v-model="searchCategory" :data="categories" :props="defaultProps" placeholder="选择分类"
            check-strictly clearable class="w-full" />
           -->
          <ResourceCategorySelect v-model="searchCategory" placeholder="选择分类" />
          <el-select v-model="searchStatus" placeholder="资料状态" clearable class="w-full">
            <el-option v-for="status in statusOptions" :key="status.value" :label="status.label"
              :value="status.value" />
          </el-select>
        </div>
      </el-collapse-transition>
    </div>

    <!-- 资料列表 -->
    <div class="bg-white shadow-sm rounded-lg flex-1 p-4 overflow-auto ">
      <el-table :data="materials" style="width: 100%" empty-text="暂无数据">
        <el-table-column prop="name" show-overflow-tooltip label="资料名称">
        </el-table-column>
        <el-table-column prop="category_name" label="分类"></el-table-column>
        <el-table-column prop="chapter_name" label="章节"></el-table-column>
        <el-table-column prop="ext" label="类型"></el-table-column>
        <el-table-column label="大小">
          <template #default="{ row }">{{ formatFileSize(row.size) }}</template>
        </el-table-column>
        <el-table-column show-overflow-tooltip label="创建时间">
          <template #default="{ row }">{{ new Date(row.created_at * 1000).toLocaleString() }}</template>
        </el-table-column>
        <el-table-column label="状态">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)" effect="light" class="!rounded-button">
              {{ getStatusLabel(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200">
          <template #default="{ row }">
            <div class="flex justify-center items-center">
              <el-button v-if="row.status != 'draft' && row.status != 'rejected'" size="small" type="primary"
                @click="viewMaterial(row)">
                查看
              </el-button>
              <el-button v-if="row.status == 'draft' || row.status == 'rejected'" size="small" type="primary"
                @click="editMaterial(row)">
                编辑
              </el-button>
              <el-button v-if="row.status == 'draft' || row.status == 'rejected'" size="small" type="danger"
                class="text-red-500" @click="delMaterial(row)">
                删除
              </el-button>
              <ApproveButton approve-code="resources" :data-id="row.id" :data-title="row.name"
                v-if="row.status == 'draft' || row.status == 'rejected'" @success="refreshMaterialList" />
            </div>
          </template>
        </el-table-column>
        <!-- 修订 -->
        <el-table-column label="修订" width="190" align="center">
          <template #default="{ row }">
            <RevisionActions :approve-code="approveCode" :data="row" :rev="row.rev"
              :show-create-button="row.status === 'published' && !row.rev" @create="showReviseCreateDialog(row)"
              @edit="handleReviseEdit(row)" @delete="handleReviseDelete(row.rev)" @view="handleReviseView(row)"
              @refresh="refreshMaterialList" />
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <div class="mt-4 flex justify-center ">
      <el-pagination v-model:current-page="pages.page" v-model:page-size="pages.limit" layout="prev, pager, next"
        :total="pages.total" @current-change="currentChange" />
    </div>

    <!-- 批量添加资料弹窗 -->
    <ResourceCreateForm v-model="showCreateDialog" @success="handleCreateSuccess" />

    <!-- 编辑资料弹窗 -->
    <ResourceEditForm v-model="dialogVisible" :row-data="selectedRow" :dialog-title="dialogTitle"
      @success="handleEditSuccess" :approve-code="approveCode" :action-type="actionType" />
    <!-- 修订列表 -->
    <el-drawer title="资料修订列表" v-model="reviseListDrawerVisible" size="90%" body-class="my-drawer">
      <RevisonList :approve-code="approveCode" />
    </el-drawer>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, watch } from "vue"
import { msg, confirmMsg as confirm, successMsg, errMsg } from "@/utils/msg"
import {
  materialList,
  deleteMaterial
} from "@/api/material"
import type { MaterialListParams } from "@/api/material"

import { statusOptions, getStatusLabel, getStatusTagType } from '@/api/status'
import { ElMessageBox } from "element-plus"
import { deleteRevision } from "@/api/revision"

// 资料数据
const materials = ref<any[]>([])
const approveCode = ref('resources')
const reviseListDrawerVisible = ref(false)
const dialogVisible = ref(false)
const actionType = ref('create')
const dialogTitle = ref('编辑资料')
const selectedRow = ref<any>(null)
// 搜索相关
const searchQuery = ref<string>("")
const searchCategory = ref<string | number>("")
const searchStatus = ref<string>("")
const showAdvancedSearch = ref<boolean>(true)

// 分页相关
const pages = reactive({
  page: 1,
  limit: 10,
  total: 0
})

// 弹窗相关
const showCreateDialog = ref(false)

// 搜索状态
let inSearch = false
// 原始页数
let lastPages = { ...pages }

/** 筛选课件列表 */
watch([searchQuery, searchCategory, searchStatus], newVal => {
  // 有筛选条件
  if (newVal.some(s => s)) {
    if (!inSearch) {
      inSearch = true
    }
    pages.page = 1
    getMaterialList({
      page: pages.page,
      name: newVal[0],
      category_id: +newVal[1] || 0,
      status: newVal[2] || ""
    })
  } else {
    // 重置分页
    pages.page = lastPages.page
    getMaterialList({ page: pages.page })
    inSearch = false
  }
})

/** 格式化文件大小 */
const formatFileSize = (bytes: number) => {
  if (bytes === 0) return "0 Bytes"
  const k = 1024
  const sizes = ["Bytes", "KB", "MB", "GB"]
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i]
}

/** 切换高级搜索 */
const toggleAdvancedSearch = () => {
  showAdvancedSearch.value = !showAdvancedSearch.value
}

/** 新建资料 */
const addMaterial = () => {
  showCreateDialog.value = true
}

/** 编辑资料 */
const editMaterial = (row: any) => {
  selectedRow.value = row
  dialogVisible.value = true
  actionType.value = 'edit'
  dialogTitle.value = '编辑资料'
}
const viewMaterial = (row: any) => {
  selectedRow.value = row
  dialogVisible.value = true
  actionType.value = 'view'
  dialogTitle.value = '查看资料'
}


/** 新建成功回调 */
const handleCreateSuccess = () => {
  refreshMaterialList()
}

/** 编辑成功回调 */
const handleEditSuccess = () => {
  refreshMaterialList()
}

/** 刷新资料列表 */
const refreshMaterialList = () => {
  getMaterialList({
    page: pages.page,
    name: searchQuery.value,
    category_id: +searchCategory.value || 0,
    status: searchStatus.value || ""
  })
}

/** 删除资料 */
const delMaterial = (material: any) => {
  confirm(
    `确定要删除资料 "${material.name}" 吗?`,
    "删除资料",
    async action => {
      if (action) {
        try {
          const res = await deleteMaterial(material.id)
          if (res.code == 0) {
            msg("success", "删除成功")
            refreshMaterialList()
          }
        } catch (error) {
          console.error('删除资料失败:', error)
          msg("error", "删除资料失败")
        }
      }
    }
  )
}



/** 获取资料列表 */
const getMaterialList = async (params?: MaterialListParams) => {
  params = params || {}
  // 记录原分页
  if (!inSearch) {
    lastPages.page = pages.page
  }
  try {
    const res = await materialList(params)
    if (res.code == 0) {
      const data = res.data || {}
      materials.value = data.list || []
      pages.total = data.total || 0
    }
  } catch (error) {
    console.error('获取资料列表失败:', error)
  }
}

/** 分页改变 */
const currentChange = (page: number) => {
  pages.page = page
  getMaterialList({
    page,
    name: searchQuery.value,
    category_id: +searchCategory.value || 0,
    status: searchStatus.value || ""
  })
}
// 修订相关方法
const showReviseCreateDialog = async (row: any) => {
  dialogTitle.value = '创建修订';
  actionType.value = 'revise-create';
  selectedRow.value = row
  dialogVisible.value = true
}

const handleReviseEdit = async (row: any) => {
  dialogTitle.value = '编辑修订';
  actionType.value = 'revise-edit';
  selectedRow.value = row
  dialogVisible.value = true
}

const handleReviseView = async (row: any) => {
  dialogTitle.value = '查看修订';
  actionType.value = 'revise-view';
  selectedRow.value = row
  dialogVisible.value = true
}

const handleReviseDelete = async (row: any) => {
  ElMessageBox.confirm(`确定要删除修订吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    const res = await deleteRevision(row.id)
    getMaterialList()
    successMsg('删除成功')
    if (!res.success) {
      errMsg(res.message)
    }
  })
}
// 初始化数据
getMaterialList()
</script>

<style scoped>
.upload-area {
  border: 1px dashed #dcdfe6;
  border-radius: 6px;
  padding: 20px;
  text-align: center;
  background-color: #f5f7fa;
}

.upload-area:hover {
  border-color: #409eff;
}

.el-icon--upload {
  font-size: 67px;
  color: #c0c4cc;
  margin-bottom: 16px;
}

.el-upload__text {
  color: #606266;
  font-size: 14px;
}

.el-upload__tip {
  margin-top: 7px;
}
</style>