package model

const (
	CoursesStatusDraft     = "draft"     // 草稿
	CoursesStatusReviewing = "reviewing" // 审核中
	CoursesStatusPublished = "published" // 已发布
	CoursesStatusRejected  = "rejected"  // 审核拒绝
)

// 课程管理
type Courses struct {
	ID          int64  `gorm:"primary_key;AUTO_INCREMENT;column:id;type:bigint;" json:"id"`
	PlanID      int64  `gorm:"column:plan_id;type:bigint;" json:"plan_id"`                       // 关联的教学计划ID
	MajorID     int64  `gorm:"column:majors_id;type:bigint;" json:"majors_id"`                   // 专业ID
	ChapterID   int64  `gorm:"column:chapter_id;type:bigint;" json:"chapter_id"`                 // 关联总章节ID
	UserID      int64  `gorm:"column:user_id;type:bigint;" json:"user_id"`                       // 用户ID
	CourseCode  string `gorm:"column:course_code;type:varchar;size:50;" json:"course_code"`      // 课程编码，唯一标识一门课程
	Name        string `gorm:"column:name;type:varchar;size:255;" json:"name"`                   // 课程名称（中文）
	Description string `gorm:"column:description;type:text;" json:"description"`                 // 课程描述信息（可包含课程目标、内容简介等）
	TotalHours  int64  `gorm:"column:total_hours;type:int;" json:"total_hours"`                  // 总课时数（单位：小时）
	Status      string `gorm:"column:status;varchar;size:50;" json:"status"`                     // 课程状态（draft: 草稿, published: 已发布, reviewing: 审核中）
	CreatedAt   int64  `gorm:"column:created_at;autoCreateTime" json:"created_at"`               // 创建时间（Unix 时间戳）
	UpdatedAt   int64  `gorm:"column:updated_at;autoUpdateTime" json:"updated_at"`               // 创建时间（Unix 时间戳）
	SubmitAt    int64  `gorm:"column:submit_at;" json:"submit_at"`                               // 提交时间（审核提交时间）
	PublishedAt int64  `gorm:"column:published_at;" json:"published_at"`                         // 发布时间
	Version     string `gorm:"column:version;type:varchar;size:20;default:v1.0;" json:"version"` // 版本号

}

// TableName sets the insert table name for this struct type
func (c *Courses) TableName() string {
	return "courses"
}

type ReqCoursesUpdate struct {
	Course     Courses `json:"course"`
	TeacherIDs []int64 `json:"teacher_ids"` // 多个老师ID
}
type ReqCoursesSearch struct {
	Page     int    `form:"page,default=1" json:"page"`
	PageSize int    `form:"page_size,default=10" json:"page_size"`
	Name     string `form:"name" json:"name"`       // 按名称模糊搜索
	Status   string `form:"status" json:"status"`   // 状态筛选 draft, published, reviewing
	UserID   int64  `form:"user_id" json:"user_id"` // 创建人ID
	PlanID   int64  `form:"plan_id" json:"plan_id"` // 关联教学计划ID
}
type ReqGetCoursesByClassAndMajor struct {
	ClassID  int64 `json:"class_id" binding:"required"` // 班级ID
	MajorID  int64 `json:"major_id" binding:"required"` // 专业ID
	Page     int   `json:"page" default:"1"`
	PageSize int   `json:"page_size" default:"10"`
}
