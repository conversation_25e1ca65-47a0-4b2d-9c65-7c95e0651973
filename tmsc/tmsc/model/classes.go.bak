package model

// Class 表示班级模型
type Class struct {
	ID          int64  `gorm:"primary_key;AUTO_INCREMENT;column:id;type:bigint;" json:"id"`      // 班级ID
	UserID      int64  `gorm:"column:user_id;type:bigint;" json:"user_id"`                       // 用户ID
	ClassID     string `gorm:"column:class_id;type:varchar(50);unique;not null" json:"class_id"` // 班级编号（唯一）
	MajorID     int64  `gorm:"column:major_id;type:bigint;" json:"major_id"`                     // 专业ID
	Name        string `gorm:"column:name;type:varchar(100);not null" json:"name"`               // 班级名称
	Description string `gorm:"column:description;type:text" json:"description"`                  // 班级描述
	CreatedAt   int64  `gorm:"column:created_at;autoCreateTime" json:"created_at"`               // 创建时间（秒级时间戳）
	UpdatedAt   int64  `gorm:"column:updated_at;autoUpdateTime" json:"updated_at"`               // 更新时间（秒级时间戳）
}

// TableName 设置数据库表名
func (Class) TableName() string {
	return "classes"
}

func (Class) Columns() []string {
	return []string{"id", "user_id", "class_id", "major_id", "name", "description", "created_at", "updated_at"}
}

func (Class) UpdateColumns(mode string) []string {
	if mode == "exclude_primary_key" {
		return []string{"user_id", "class_id", "major_id", "name", "description", "updated_at"}
	}
	return Class{}.Columns()
}

type ReqClassSearch struct {
	Page     int    `form:"page,default=1" json:"page"`
	PageSize int    `form:"page_size,default=10" json:"page_size"`
	Name     string `form:"name" json:"name"` // 按名称模糊搜索
	ClassID  string `form:"class_id" json:"class_id"`
	MajorID  int64  `form:"major_id" json:"major_id"`
}
type RespClassList struct {
	List  []Class `json:"list"`
	Total int64   `json:"total"`
}
