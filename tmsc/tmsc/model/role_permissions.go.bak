package model

// RolePermissions struct is a row record of the role_permissions table in the tms database
type RolePermissions struct {
	// RoleID       int64  `gorm:"primary_key;column:role_id;type:bigint;" json:"role_id"`
	// PermissionID string `gorm:"primary_key;column:permission_id;type:text;" json:"permission_id"`
	RoleCode string `gorm:"primary_key;column:role_code;varchar;size:50;" json:"role_code"`
	Path     string `gorm:"primary_key;column:path;type:varchar;size:250;" json:"path"`
	Method   string `gorm:"primary_key;column:method;type:varchar;size:50;" json:"method"`
}

// TableName sets the insert table name for this struct type
func (r *RolePermissions) TableName() string {
	return "role_permissions"
}
