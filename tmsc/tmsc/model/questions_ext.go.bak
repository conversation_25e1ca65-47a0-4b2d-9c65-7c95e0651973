package model

type QuestionsExt struct {
	ID         int64  `gorm:"primary_key;AUTO_INCREMENT;column:id;type:bigint;" json:"id"`
	ExtKey     string `gorm:"column:ext_key;type:varchar;" json:"ext_key"` // 扩展键，例如：'courseware','chapter' 等
	ExtValue   int64  `gorm:"column:ext_value;type:bigint;" json:"ext_value"`
	QuestionID int64  `gorm:"column:question_id;type:bigint;" json:"question_id"`
}

func (QuestionsExt) TableName() string {
	return "questions_ext"
}
