package model

// UserRoles struct is a row record of the user_roles table in the tms database
type UserRoles struct {
	// UserID int64 `gorm:"primary_key;column:user_id;type:bigint;" json:"user_id"`
	// RoleID int64 `gorm:"primary_key;column:role_id;type:bigint;" json:"role_id"`

	UserID   int64  `gorm:"primary_key;column:user_id;type:bigint;" json:"user_id"`
	RoleCode string `gorm:"primary_key;column:role_code;type:string;" json:"role_code"`
}

// TableName sets the insert table name for this struct type
func (u *UserRoles) TableName() string {
	return "user_roles"
}
