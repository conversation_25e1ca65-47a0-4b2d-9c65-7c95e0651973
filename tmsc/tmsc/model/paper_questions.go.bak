package model

// QuestionCategory 定义题目类型常量
const (
	QuestionCategoryTheoryCourseware  = "theory_courseware"  // 理论课件
	QuestionCategoryVirtualCourseware = "virtual_courseware" // 虚拟课件
	QuestionCategoryTheoryQuestion    = "theory_question"    // 理论试题
)

type PaperQuestions struct {
	ID               int64  `gorm:"primary_key;AUTO_INCREMENT;column:id;type:bigint;" json:"id"`
	PaperID          int64  `gorm:"column:paper_id;type:bigint;" json:"paper_id"`                       // 关联的试卷ID
	QuestionCategory string `gorm:"column:question_category;varchar;size:50;" json:"question_category"` // 题目类型
	QuestionID       int64  `gorm:"column:question_id;type:bigint;" json:"question_id"`                 // 题目ID
	Content          string `gorm:"column:content;type:text;" json:"content"`                           // 题目内容
	Answer           string `gorm:"column:answer;type:text;" json:"answer"`                             // 答案
	OrderNnm         int    `gorm:"column:order_num;type:int;" json:"order_num"`                        // 顺序
	Score            int    `gorm:"column:score;type:int;" json:"score"`                                // 分值
	CreatedAt        int64  `gorm:"column:created_at;autoCreateTime" json:"created_at"`
}

func (p *PaperQuestions) TableName() string {
	return "paper_questions"
}
