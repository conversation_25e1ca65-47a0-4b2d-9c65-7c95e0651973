package model

import "sort"

// Menus struct is a row record of the menus table in the tms database
type Menus struct {
	ID        int64  `gorm:"primary_key;AUTO_INCREMENT;column:id;type:bigint;" json:"id"`
	ParentID  int64  `gorm:"column:parent_id;type:bigint;default:0;index;" json:"parent_id"` // 父菜单ID
	MenuName  string `gorm:"column:menu_name;type:varchar;size:50;" json:"menu_name"`        // 菜单名称
	Path      string `gorm:"column:path;type:varchar;size:255;index;" json:"path"`           // 路由路径
	Icon      string `gorm:"column:icon;type:varchar;size:150;" json:"icon"`                 // 图标名称
	OrderNum  int64  `gorm:"column:order_num;type:int;" json:"order_num"`                    // 排序字段，用于前端菜单的顺序排列
	SysCode   string `gorm:"column:sys_code;type:varchar;size:50;" json:"sys_code"`          // 角色编码，用于权限控制，只做系统标记不做强关联，方便后续扩展
	CreatedAt int64  `gorm:"column:created_at" json:"created_at"`                            // 记录创建时间，默认当前时间戳
	UpdatedAt int64  `gorm:"column:updated_at" json:"updated_at"`                            // 记录更新时间
}

// TableName sets the insert table name for this struct type
func (m *Menus) TableName() string {
	return "menus"
}

func (m Menus) Columns() []string {
	return []string{"id", "parent_id", "menu_name", "path", "icon", "sys_code", "order_num", "created_at", "updated_at"}
}

func (m Menus) UpdateColumns(mode string) []string {
	if mode == "exclude_primary_key" {
		return []string{"parent_id", "menu_name", "path", "icon", "sys_code", "order_num", "updated_at"}
	}
	return m.Columns()
}

type MenuNode struct {
	*Menus
	Children []*MenuNode `json:"children"`
}

func BuildMenuTree(menus []*Menus) []*MenuNode {
	menuMap := make(map[int64]*MenuNode)
	var roots []*MenuNode

	for _, menu := range menus {
		node := &MenuNode{
			Menus:    menu,
			Children: []*MenuNode{}, // 确保Children字段初始化为空数组
		}
		menuMap[menu.ID] = node

		if menu.ParentID == 0 {
			roots = append(roots, node)
		} else {
			if parent, ok := menuMap[menu.ParentID]; ok {
				parent.Children = append(parent.Children, node)
			} else {
				// 如果父节点尚未处理，则将其视为根节点，这通常不应该发生，除非数据有问题
				roots = append(roots, node)
			}
		}
	}

	// 对每个节点的子菜单进行排序
	for _, node := range menuMap {
		sort.Slice(node.Children, func(i, j int) bool {
			return node.Children[i].Menus.OrderNum < node.Children[j].Menus.OrderNum
		})
	}

	// 对根菜单进行排序
	sort.Slice(roots, func(i, j int) bool {
		return roots[i].Menus.OrderNum < roots[j].Menus.OrderNum
	})

	return roots
}

func findMenuTree(menuTrees []*MenuNode, parentID int64) *MenuNode {
	for _, menuTree := range menuTrees {
		if menuTree.Menus.ID == parentID {
			return menuTree
		}
		if len(menuTree.Children) > 0 {
			childMenuTree := findMenuTree(menuTree.Children, parentID)
			if childMenuTree != nil {
				return childMenuTree
			}
		}
	}
	return nil
}

// ConvertMenuNodesToMenus converts a slice of MenuNode to a slice of Menu
func ConvertMenuNodesToMenus(menuNodes []*MenuNode) []*Menus {
	menus := make([]*Menus, 0)
	for _, node := range menuNodes {
		// 增加空值检查，处理潜在的无效节点
		if node == nil || node.Menus == nil {
			continue // 跳过空节点或其内部Menus为nil的节点
		}
		// 直接追加嵌入的*Menus，避免不必要的内存分配和字段复制
		menus = append(menus, node.Menus)
		if len(node.Children) > 0 {
			childrenMenus := ConvertMenuNodesToMenus(node.Children)
			menus = append(menus, childrenMenus...)
		}
	}
	return menus
}
