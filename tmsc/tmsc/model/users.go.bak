package model

type UpdateUser struct {
	Users
	RoleIds []int64 `json:"role_ids"`
}

// Users struct is a row record of the users table in the tms database
type Users struct {
	ID        int64  `gorm:"primary_key;AUTO_INCREMENT;column:id;" json:"id"`                 // 用户唯一标识符，主键，自增
	Account   string `gorm:"column:account;type:varchar;size:50;uniqueIndex;" json:"account"` // 用户登录账号，需唯一，可考虑添加索引以优化查询
	Username  string `gorm:"column:username;type:varchar;size:50;" json:"username"`           // 用户昵称或显示名称
	Password  string `gorm:"column:password;type:varchar;size:100;" json:"password"`          // BCrypt加密后的密码哈希值，确保密码安全存储
	Email     string `gorm:"column:email;type:varchar;size:100;" json:"email"`                // 用户邮箱，可用于找回密码或通知
	Phone     string `gorm:"column:phone;type:varchar;size:20;" json:"phone"`
	Status    string `gorm:"column:status;type:char;size:6;default:1;" json:"status"` // 用户状态，例如：1-正常，0-禁用
	CreatedAt int64  `gorm:"column:created_at" json:"created_at"`                     // 记录创建时间，默认当前时间戳
	UpdatedAt int64  `gorm:"column:updated_at" json:"updated_at"`                     // 记录更新时间
	Salt      string `gorm:"column:salt;type:varchar;size:100;" json:"salt"`          // 密码加盐，增加安全性
}

// TableName sets the insert table name for this struct type
func (u *Users) TableName() string {
	return "users"
}

func (u Users) Columns() []string {
	return []string{
		"id",
		"account",
		"username",
		"password",
		"email",
		"phone",
		"status",
		"created_at",
		"updated_at",
		"salt"}
}

func (u Users) UpdateColumns(mode string) []string {
	columns := []string{
		"account",
		"username",
		"email",
		"phone",
		"password",
		"status",
		"salt",
		"updated_at",
	}
	if mode == "exclude_primary_key" {
		return columns
	}
	return u.Columns()
}
