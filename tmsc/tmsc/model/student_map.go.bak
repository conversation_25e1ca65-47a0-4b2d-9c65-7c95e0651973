package model

type StudentMap struct {
	ID      int64 `gorm:"primary_key;AUTO_INCREMENT;" json:"id"`
	UserID  int64 `gorm:"column:user_id;type:bigint;" json:"user_id"`
	ClassID int64 `gorm:"column:class_id;type:bigint;" json:"class_id"`
	MajorID int64 `gorm:"column:major_id;type:bigint;" json:"major_id"`
}

func (StudentMap) TableName() string {
	return "student_map"
}

type StudentRefPOJO struct {
	UserRef  UserRefPOJO  `json:"user_ref"`
	ClassRef ClassRefPOJO `json:"class_ref"`
}
type UserRefPOJO struct {
	UserID  int64
	ClassID int64
	MajorID int64
}
type ClassRefPOJO struct {
	ClassID int64
	PlanIDS []int64
}
