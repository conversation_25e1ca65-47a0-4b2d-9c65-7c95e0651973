package model

// Chapter struct is a row record of the chapters table in the tms database
type ResourceCategory struct {
	ID          int64  `gorm:"primary_key;AUTO_INCREMENT;column:id;type:bigint;" json:"id"`
	UserID      int64  `gorm:"column:user_id;type:bigint;" json:"user_id"`               // 创建人ID
	Name        string `gorm:"column:name;type:varchar;size:255;" json:"name"`           // 分类名称
	ParentID    int64  `gorm:"column:parent_id;type:bigint;default:0;" json:"parent_id"` // 上级分类ID（默认为0表示顶级分类）
	ChapterID   int64  `gorm:"column:chapter_id;type:bigint;" json:"chapter_id"`         // 章节ID
	Description string `gorm:"column:description;type:text;" json:"description"`         // 分类描述
	CreatedAt   int64  `gorm:"column:created_at;autoCreateTime" json:"created_at"`       // 创建时间（Unix 时间戳）
	UpdatedAt   int64  `gorm:"column:updated_at;autoUpdateTime" json:"updated_at"`       // 更新时间（Unix 时间戳）
}

// TableName sets the insert table name for this struct type
func (c *ResourceCategory) TableName() string {
	return "resource_category"
}

type ReqResourceCategorySearch struct {
	Name     string `form:"name" json:"name"`           // 按名称模糊搜索
	ParentID int64  `form:"parent_id" json:"parent_id"` // 上级分类ID
}

// ResourceCategoryTreeNode 树形结构节点
type ResourceCategoryTreeNode struct {
	ID          int64                       `json:"id"`
	Name        string                      `json:"name"`
	Description string                      `json:"description"`
	ParentID    int64                       `json:"parent_id"`
	ChapterID   int64                       `json:"chapter_id"`
	Children    []*ResourceCategoryTreeNode `json:"children"` // 子分类列表
}
