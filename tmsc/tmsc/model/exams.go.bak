package model

const (
	ExamStatusDraft     = "draft"     // 未发布
	ExamStatusReviewing = "reviewing" // 审核中
	ExamStatusPublished = "published" // 已发布
	ExamStatusRejected  = "rejected"  // 审核拒绝
	ExamStatusOngoing   = "ongoing"   // 考试中
	ExamStatusFinished  = "finished"  // 已考完
	ExamStatusGraded    = "graded"    // 已打分
	ExamStatusArchived  = "archived"  // 已归档
	ExamStatusCertified = "certified" // 已颁发证书
)

// ExamType 定义考试类型常量
const (
	ExamTypeRegular = "regular" // 平时考试
	ExamTypeFinal   = "final"   // 结业考试
	ExamTypeRetake  = "retake"  // 补考
)

// Exams struct is a row record of the exams table in the tms database
type Exams struct {
	ID            int64  `gorm:"primary_key;AUTO_INCREMENT;column:id;type:bigint;" json:"id"`
	Name          string `gorm:"column:name;type:varchar;size:255;" json:"name"`     // 考试名称
	ExamType      string `gorm:"column:exam_type;varchar;size:50;" json:"exam_type"` // 考试类型
	PaperID       int64  `gorm:"column:paper_id;type:bigint;" json:"paper_id"`       // 试卷ID
	UserID        int64  `gorm:"column:user_id;type:bigint;" json:"user_id"`
	TotalMinutes  int64  `gorm:"column:total_minutes;type:int;" json:"total_minutes"`    // 总时数（单位：分钟）
	StartAt       int64  `gorm:"column:start_at;" json:"start_at"`                       // 开始时间（考试开始时间）
	EndAt         int64  `gorm:"column:end_at;" json:"end_at"`                           // 结束时间（考试截止时间）
	Location      string `gorm:"column:location;type:varchar;size:100;" json:"location"` // 考核地点
	Status        string `gorm:"column:status;varchar;size:50;" json:"status"`           // 状态
	Iscertificate bool   `gorm:"column:is_certificate;type:bool;" json:"is_certificate"` // 是否颁发证书
	CreatedAt     int64  `gorm:"column:created_at;autoCreateTime" json:"created_at"`
	UpdatedAt     int64  `gorm:"column:updated_at;autoUpdateTime" json:"updated_at"`
	SubmitAt      int64  `gorm:"column:submit_at;" json:"submit_at"`       // 提交时间（审核提交时间）
	PublishedAt   int64  `gorm:"column:published_at;" json:"published_at"` // 发布时间（审核通过时间）
}

// TableName sets the insert table name for this struct type
func (e *Exams) TableName() string {
	return "exams"
}

// ReqCreateExam 创建考试请求结构体
type ReqCreateExam struct {
	Exams         Exams   `json:"exams"`
	ClassIDs      []int64 `json:"class_ids"`      // 关联班级ID列表
	TeacherIDs    []int64 `json:"teacher_ids"`    // 阅卷老师ID列表
	SupervisorIDs []int64 `json:"supervisor_ids"` // 监考老师ID列表
	StartAtStr    string  `json:"start_at"`       // 开始时间
}

// RespExamList 获取考试列表响应结构体
type RespExamList struct {
	List  []Exams `json:"list"`
	Total int64   `json:"total"`
}
type ReqExamSearch struct {
	Page     int    `form:"page,default=1" json:"page"`
	PageSize int    `form:"page_size,default=10" json:"page_size"`
	Name     string `form:"name" json:"name"`       // 按名称搜索
	Status   string `form:"status" json:"status"`   // 状态筛选 draft/reviewing/published
	UserID   int64  `form:"user_id" json:"user_id"` // 创建人ID
}

type RespExamDetail struct {
	Exams       Exams        `json:"exams"`
	Students    []StudentMap `json:"students"`    // 学生列表
	Teachers    []Users      `json:"teachers"`    // 阅卷老师
	Supervisors []Users      `json:"supervisors"` // 监考老师
	Paper       Papers       `json:"paper"`       // 试卷详情
}

// ReqGetExams 获取考试列表的请求参数
type ReqGetExams struct {
	UserID   int64  `form:"user_id" json:"user_id"`     // 用户ID
	Status   string `form:"status" json:"status"`       // 考试状态，默认 published
	Role     string `form:"role" json:"role"`           // 角色，默认 student
	ExamType string `form:"exam_type" json:"exam_type"` // 考试类型（regular/final/retake）
	StartAt  int64  `form:"start_at" json:"start_at"`   // 查询起始时间（Unix 时间戳）
	EndAt    int64  `form:"end_at" json:"end_at"`       // 查询结束时间（Unix 时间戳）
	Page     int    `form:"page" json:"page"`           // 当前页码，默认 1
	PageSize int    `form:"page_size" json:"page_size"` // 每页数量，默认 10，最大 100
}

type ExamsWithExt struct {
	Exams Exams      `json:"exams"`
	Exts  []ExamsExt `json:"exts"`
}
