package model

// ScormSessions 存储 SCORM 学习记录
type ScormSessions struct {
	ID                uint    `json:"id" gorm:"primary_key"`
	CourseID          int64   `json:"course_id" gorm:"comment:课程ID"`
	ChapterID         int64   `json:"chapter_id" gorm:"comment:章节ID"`
	LearnerID         string  `json:"learner_id" gorm:"type:varchar(255);comment:学员ID"`
	LearnerName       string  `json:"learner_name" gorm:"type:varchar(255);comment:学员姓名"`
	ScoID             uint    `json:"sco_id" gorm:"index;comment:SCO 唯一标识"`
	AttemptNumber     int     `json:"attempt_number" gorm:"comment:尝试次数"`
	CompletionStatus  string  `json:"completion_status" gorm:"type:varchar(50);comment:完成状态"` // incomplete, completed, not attempted, unknown
	SuccessStatus     string  `json:"success_status" gorm:"type:varchar(50);comment:成功状态"`    // passed, failed, unknown
	LessonStatus      string  `json:"lesson_status" gorm:"type:varchar(50);comment:课程状态"`     // not attempted, completed, browed, failed, incomplete, passed, pto, aborted, unknown
	Entry             string  `json:"entry" gorm:"type:varchar(50);comment:入口"`
	Location          string  `json:"location" gorm:"type:varchar(255);comment:学习位置"`
	ScoreRaw          int     `json:"score_raw" gorm:"comment:原始分数"`
	ScoreMin          int     `json:"score_min" gorm:"comment:最小分数"`
	ScoreMax          int     `json:"score_max" gorm:"comment:最大分数"`
	ScoreScaled       float64 `json:"score_scaled" gorm:"comment:缩放分数"` // 缩放得分（通常 0-1）
	TotalTime         string  `json:"total_time" gorm:"type:varchar(50);comment:总学习时长"`
	SessionTime       string  `json:"session_time" gorm:"type:varchar(50);comment:本次会话时长"`
	SuspendData       string  `json:"suspend_data" gorm:"type:text;comment:挂起数据"`
	Exit              string  `json:"exit" gorm:"type:varchar(50);comment:退出方式"`
	Credit            string  `json:"credit" gorm:"type:varchar(50);comment:学分"`
	Mode              string  `json:"mode" gorm:"type:varchar(50);comment:模式"`
	CMI               string  `json:"cmi" gorm:"type:json;comment:SCORM CMI 数据"` // 存储所有 CMI 数据的 JSON 字符串
	InitialAccessTime int64   `json:"initial_access_time" gorm:"comment:初始访问时间戳"`
	LastAccessTime    int64   `json:"last_access_time" gorm:"comment:最后访问时间戳"`
	CreatedAt         int64   `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt         int64   `json:"updated_at" gorm:"autoUpdateTime"`
}

func (ScormSessions) TableName() string {
	return "scorm_sessions"
}

// 理解 completion_status、success_status 和 lesson_status 这三个 SCORM 状态对于准确记录学员学习进度至关重要。
// 它们在 SCORM 规范中有明确的定义和用途，并且通常由 SCORM 课件（SCO）在与学习管理系统（LMS）通信时设置。

// 下面我们来逐一解释这三个状态在什么情况下会被设置以及背后的原因。
// 1. CompletionStatus (完成状态)
// 可能的值： completed (已完成), incomplete (未完成), not attempted (未尝试), unknown (未知)。

// 何时设置及原因：
// completed: 当学员已经浏览或达到了 SCO 内容的既定结束点时设置。这表示学员已经通过了课件的所有必要部分。
// 例如，一个视频观看完毕，或者阅读材料已滚动到最后。completion_status 主要关注学员是否已接触并走完整个内容，而不是其表现好坏。
// incomplete: 当学员开始学习 SCO 但尚未达到其预设的完成条件时设置。学员可能中途退出，或者没有完成所有必需的活动。
// not attempted: 当学员从未开始学习某个 SCO 时设置。这是初始状态。
// unknown: 当 SCO 没有明确报告其完成状态时设置。这种情况较少见，通常是由于课件本身的问题或 SCORM 通信不规范。

// 2. SuccessStatus (成功状态)
// 可能的值： passed (通过), failed (未通过), unknown (未知)。

// 何时设置及原因：
// passed: 当学员在 SCO 中达到了预定义的成功标准时设置。这通常与学员的成绩（例如通过考试、达到某个分数线）或表现相关。
// 例如，一个测验得分超过及格线。success_status 主要关注学员的学习成果或表现是否达标。
// failed: 当学员未能达到 SCO 中预定义的成功标准时设置。例如，测验得分低于及格线。
// unknown: 当 SCO 没有明确报告其成功状态时设置。这可能是因为 SCO 不包含评估内容，或者课件本身没有实现此状态的报告。

// 3. LessonStatus (课程状态)
// 可能的值： not attempted (未尝试), completed (已完成), browsed (已浏览), failed (未通过), incomplete (未完成),
//  passed (通过), pto (待定), aborted (中断), unknown (未知)。

// 何时设置及原因：
// lesson_status 是 SCORM 2004 之前的版本（如 SCORM 1.2）中用来表示学员整体学习情况的字段。
// 在 SCORM 2004 中，这个字段被拆分并替换为 completion_status 和 success_status。
// 因此，如果您主要使用 SCORM 2004 或更高版本，lesson_status 的作用就大大减弱，甚至可能不会被使用或仅作为历史兼容字段存在。

// 为什么在 SCORM 2004 中不推荐直接依赖它：
// 含义模糊： lesson_status 的值包含了“完成”和“通过”的双重含义，这在实践中容易混淆。
// 例如，一个 SCO 可能被“完成”但未“通过”（例如，看完视频但考试没及格），或者“通过”了但未完全“完成”（例如，只做了核心部分就达到通过标准）。
// 粒度不够： SCORM 2004 通过 completion_status 专注于学员是否走完内容，success_status 专注于学员的表现结果，提供了更清晰、更细致的进度追踪能力。

// 如果您仍然需要处理 lesson_status：
// 通常，当您处理 SCORM 1.2 或其他旧版本的课件时，这个字段会比较重要。
// 其值会根据课件的内部逻辑和学员与课件的交互来设置，例如：
// not attempted: 尚未开始。
// completed: 已学习完所有内容。
// passed: 已达到通过标准。
// failed: 未达到通过标准。
// incomplete: 已开始但未完成。
// browsed: 仅仅浏览过，没有进行任何实质性交互。
// aborted: 学习会话异常中断。
// 其他状态通常较少用到。

// 总结：
// 在现代的 SCORM 实现中（尤其是 SCORM 2004 及更高版本），completion_status 和 success_status 是您应该重点关注的两个状态。
// completion_status 回答的是“学员是否完成了内容？”
// success_status 回答的是“学员是否通过了评估？”
// lesson_status 更多是用于向后兼容旧版 SCORM 规范的字段，其含义在现代 SCORM 中被拆分得更明确。
// 在设计系统时，建议优先使用 completion_status 和 success_status 来判断学员的学习进度和结果。

// TODO: 待加入课程id 章节id
// 计算学习进度，返回百分比
// 1. 章节进度
// ==> 1.1 获取章节中的所有 SCO
// ==> 1.2 获取学员在这些 SCO 的会话数据。
// 对于步骤 1 中的每个 ScoID，查询 ScormSessions 表，
// 获取 LearnerID 的记录以及最新的 AttemptNumber（或者如果允许多次尝试并且只计算最佳/最新完成的尝试，则获取导致完成的尝试）
// ==> 1.3 统计已完成的 SCO。 遍历学员在这些 SCO 的会话数据。计算其中有多少符合您定义的“已完成”标准（例如，CompletionStatus = 'completed'）
// ==> 1.4 计算百分比：已完成的 SCO / 所有 SCO

// 2. 课程进度
// ==> 2.1 获取课程中的所有 SCO
// ==> 2.2 获取学员在这些 SCO 的会话数据。 对于步骤 1 中的每个 ScoID，查询 ScormSessions 表，获取 LearnerID 的记录以及相关的尝试
// ==> 2.3 统计已完成的 SCO。 计算其中有多少 SCO 符合您的“已完成”标准
// ==> 2.4 计算百分比：已完成的 SCO / 所有 SCO
