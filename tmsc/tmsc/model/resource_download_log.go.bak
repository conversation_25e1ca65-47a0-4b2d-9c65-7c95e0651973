package model

// ResourceDownloadLog struct is a row record of the resource_download_log table in the tms database
type ResourceDownloadLog struct {
	ID           int64  `gorm:"primary_key;column:id;type:integer" json:"id"`
	ResourceID   int64  `gorm:"column:resource_id;type:bigint;" json:"resource_id"`     // 资料ID
	UserID       int64  `gorm:"column:user_id;type:bigint;" json:"user_id"`             // 下载用户ID
	Path         string `gorm:"column:path;type:varchar;size:255;" json:"path"`         // 下载资料路径
	DownloadTime int64  `gorm:"column:download_time;type:bigint;" json:"download_time"` // 下载时间
}

// TableName sets the insert table name for this struct type
func (r *ResourceDownloadLog) TableName() string {
	return "resource_download_log"
}

type ResourceDownloadQuery struct {
	ResourceID int64 `json:"resource_id"` // 资料ID
	UserID     int64 `json:"user_id"`     // 下载用户ID
	StartAt    int64 `json:"start_at"`    // 开始时间
	EndAt      int64 `json:"end_at"`      // 结束时间

	Page     int `json:"page"`
	PageSize int `json:"page_size"`
}

type ResourceDownloadResponse struct {
	ResourceDownloadLog
	Username     string `json:"username"`
	ResourceName string `josn:"resource_name"`
	ResourceSize int64  `json:"resource_size"`
	ResourceExt  string `json:"resource_ext"`
}
