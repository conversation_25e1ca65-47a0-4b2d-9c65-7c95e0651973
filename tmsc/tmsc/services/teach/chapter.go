package teach

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"tms/model"
	"tmsc/pkg/cache"
	"tmsc/pkg/db"
	"tmsc/pkg/logger"
	"tmsc/services/adapter"

	"github.com/spf13/cast"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// buildChapterTree 将平级数据构建成树状结构
func buildChapterTree(list []model.Chapter) []*model.ChapterTreeNode {
	chapterMap := make(map[int64]*model.ChapterTreeNode)

	// 第一步：初始化所有节点
	for _, chapter := range list {
		chapterMap[chapter.ID] = &model.ChapterTreeNode{
			ID:                     chapter.ID,
			Name:                   chapter.Name,
			Description:            chapter.Description,
			ParentID:               chapter.ParentID,
			Children:               make([]*model.ChapterTreeNode, 0),
			AttachedAttributeValue: chapter.AttachedAttributeValue,
		}
	}

	// 第二步：构建树结构
	var tree []*model.ChapterTreeNode
	for _, chapter := range list {
		node := chapterMap[chapter.ID]

		if chapter.ParentID == 0 {
			tree = append(tree, node)
		} else {
			if parent, ok := chapterMap[chapter.ParentID]; ok {
				parent.Children = append(parent.Children, node)
			} else {
				// 如果父节点不存在，添加到根节点
				tree = append(tree, node)
			}
		}
	}

	return tree
}

func (s *ChapterService) GrabChapters(userID int64, CourseID ...int64) ([]model.Chapter, error) {
	logger.Logger.Info("GrabChapters", zap.Int64s("CourseID", CourseID))
	key := fmt.Sprintf("%d-token", userID)
	token, err := cache.FreeCache.Get([]byte(key))
	if err != nil || token == nil {
		logger.Logger.Info("Token expired or not found for user, stopping EventFetcher", zap.Int64("userID", userID))
		return nil, err
	}
	params := adapter.RPCQueryParam{
		Method: "ListChapter",
		Params: map[string]any{
			"course_ids": CourseID,
		},
	}

	queryData, err := adapter.GetAdapterInstance().QueryData(context.Background(), string(token), params)
	if err != nil {
		logger.Logger.Error("Failed to query data", zap.Error(err))
		return nil, err
	}

	chapters := make([]model.Chapter, 0)
	if err := json.Unmarshal(queryData, &chapters); err != nil {
		logger.Logger.Error("Failed to unmarshal data", zap.Error(err))
		return nil, err
	}

	return chapters, nil
}

func (s *ChapterService) GrabCoursewares(userID int64, chapterID ...int64) ([]model.CoursewareWithExt, error) {
	logger.Logger.Info("GrabCoursewares", zap.Int64s("chapterID", chapterID))
	key := fmt.Sprintf("%d-token", userID)
	token, err := cache.FreeCache.Get([]byte(key))
	if err != nil || token == nil {
		logger.Logger.Info("Token expired or not found for user, stopping EventFetcher", zap.Int64("userID", userID))
		return nil, err
	}
	params := adapter.RPCQueryParam{
		Method: "ListCourseware",
		Params: map[string]any{
			"chapter_ids": chapterID,
		},
	}

	queryData, err := adapter.GetAdapterInstance().QueryData(context.Background(), string(token), params)
	if err != nil {
		logger.Logger.Error("Failed to query data", zap.Error(err))
		return nil, err
	}

	coursewareWithExts := make([]model.CoursewareWithExt, 0)
	if err := json.Unmarshal(queryData, &coursewareWithExts); err != nil {
		logger.Logger.Error("Failed to unmarshal data", zap.Error(err))
		return nil, err
	}

	return coursewareWithExts, nil
}

// GetChapterListByCourseID 根据课程ID获取章节列表并构建树状结构
func (s *ChapterService) GetChapterListByCourseID(courseID int64) ([]*model.ChapterTreeNode, error) {
	var courses model.Courses
	err := db.DB.Where("id = ?", courseID).First(&courses).Error
	if err != nil {
		return nil, errors.New("未找到对应课程")
	}

	chapterIDS := []int64{courses.ChapterID}
	// 收集所有需要查询的章节ID
	finalChapterIDMap := make(map[int64]struct{})
	queue := make([]int64, 0)

	// 初始化队列，将所有初始章节ID加入队列
	for _, initialID := range chapterIDS {
		if _, ok := finalChapterIDMap[initialID]; !ok {
			finalChapterIDMap[initialID] = struct{}{}
			queue = append(queue, initialID)
		}
	}

	// TODO:bfs适合于大数据量的情况
	// 若不想查询链接过多，可以将全部的id和parent_id 加载进内存
	// 然后进行 通过id，查询出需要构建的id 和 parent_id
	// 一次性查询出需要的数据，进行构建
	// BFS 遍历：只向下查找所有子章节
	for len(queue) > 0 {
		currentID := queue[0]
		queue = queue[1:]

		// 查找子节点
		var children []model.Chapter
		if err := db.DB.Where("parent_id = ?", currentID).Find(&children).Error; err != nil {
			log.Printf("获取章节子节点失败: %v", err)
			continue
		}
		for _, child := range children {
			if _, ok := finalChapterIDMap[child.ID]; !ok {
				finalChapterIDMap[child.ID] = struct{}{}
				queue = append(queue, child.ID)
			}
		}
	}

	// 将 map 的 key 转换为 slice
	var idsToQuery []int64
	for id := range finalChapterIDMap {
		idsToQuery = append(idsToQuery, id)
	}

	var chapters []model.Chapter
	if err := db.DB.Where("id IN (?)", idsToQuery).Order("id ASC").Find(&chapters).Error; err != nil {
		return nil, errors.New("获取数据失败: " + err.Error())
	}

	chapterIDs := make([]int64, len(chapters))
	for i, chapter := range chapters {
		chapterIDs[i] = chapter.ID
	}

	// 批量查询课件
	var coursewares []model.Courseware
	coursewareCounts := make(map[int64]int64) // 修改为存储数量
	if len(chapterIDs) > 0 {
		if err := db.DB.Where("chapter_id IN (?)", chapterIDs).Find(&coursewares).Error; err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			logger.Logger.Error("批量查询课件失败", zap.Error(err))
			return nil, errors.New("获取课件数据失败: " + err.Error())
		}
		for _, cw := range coursewares {
			coursewareCounts[cw.ChapterID]++ // 统计数量
		}
	}

	// 批量查询问题
	var questionsExt []model.QuestionsExt
	questionCounts := make(map[int64]int64) // 修改为存储数量
	if len(chapterIDs) > 0 {
		if err := db.DB.Where("ext_key = ? AND ext_value IN (?)", "chapter", chapterIDs).Find(&questionsExt).Error; err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			logger.Logger.Error("批量查询问题失败", zap.Error(err))
			return nil, errors.New("获取问题数据失败: " + err.Error())
		}
		for _, qe := range questionsExt {
			// ext_value 是 string 类型，需要转换为 int64
			chapterID := cast.ToInt64(qe.ExtValue)
			questionCounts[chapterID]++ // 统计数量
		}
	}

	// 批量查询章节下的资料
	resourceCounts := make(map[int64]int64) // 修改为存储数量
	if len(chapterIDs) > 0 {
		var resources []model.Resources
		if err := db.DB.Where("chapter_id IN (?)", chapterIDs).Find(&resources).Error; err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			logger.Logger.Error("批量查询资料失败", zap.Error(err))
			return nil, errors.New("获取资料数据失败: " + err.Error())
		}
		for _, r := range resources {
			resourceCounts[r.ChapterID]++ // 统计数量
		}
	}

	for i := range chapters {
		attachedAttributes := make([]model.AttachedAttribute, 0) // 每次循环初始化
		if count := coursewareCounts[chapters[i].ID]; count > 0 {
			attachedAttributes = append(attachedAttributes, model.AttachedAttribute{Key: "courseware_count", Value: cast.ToString(count)}) // 使用实际数量
		}

		if count := questionCounts[chapters[i].ID]; count > 0 {
			attachedAttributes = append(attachedAttributes, model.AttachedAttribute{Key: "question_count", Value: cast.ToString(count)}) // 使用实际数量
		}

		if count := resourceCounts[chapters[i].ID]; count > 0 {
			attachedAttributes = append(attachedAttributes, model.AttachedAttribute{Key: "resource_count", Value: cast.ToString(count)}) // 使用实际数量
		}

		if len(attachedAttributes) > 0 {
			attributesStr, _ := json.Marshal(&attachedAttributes)
			chapters[i].AttachedAttributeValue = string(attributesStr)
		}
	}

	return buildChapterTree(chapters), nil
}

// 根据章节id获取课件列表
func (s *ChapterService) GetCoursewareListByChapterID(chapterID int64) ([]*model.Courseware, error) {
	var coursewares []*model.Courseware
	if err := db.DB.Where("chapter_id = ?", chapterID).Find(&coursewares).Error; err != nil {
		return nil, errors.New("获取数据失败: " + err.Error())
	}
	return coursewares, nil
}

func (s *ChapterService) ClearCourseCache(courseID int64) error {
	// TODO: 删除 下载的包 和 解压的包
	return nil
}
