package teach

type TeachSerivce struct{}

func NewTeachService() *TeachSerivce {
	return &TeachSerivce{}
}

type CourseSerivce struct{}

func NewCourseSerivce() *CourseSerivce {
	return &CourseSerivce{}
}

type CoursewareService struct{}

func NewCoursewareService() *CoursewareService {
	return &CoursewareService{}
}

// ChapterService 章节相关服务
type ChapterService struct{}

// NewChapterService 创建服务实例
func NewChapterService() *ChapterService {
	return &ChapterService{}
}

type QuestionService struct {
}

func NewQuestionService() *QuestionService {
	return &QuestionService{}
}
