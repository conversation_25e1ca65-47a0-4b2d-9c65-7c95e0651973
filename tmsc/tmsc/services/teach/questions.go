package teach

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"tms/model"
	"tmsc/pkg/cache"
	"tmsc/pkg/db"
	"tmsc/pkg/logger"
	"tmsc/services/adapter"

	"go.uber.org/zap"
)

func (s *QuestionService) GrabQuestions(userID int64, chapterID ...int64) ([]model.Questions, error) {
	key := fmt.Sprintf("%d-token", userID)
	token, err := cache.FreeCache.Get([]byte(key))
	if err != nil || token == nil {
		logger.Logger.Info("Token expired or not found for user, stopping EventFetcher", zap.Int64("userID", userID))
		return nil, err
	}
	params := adapter.RPCQueryParam{
		Method: "ListQuestionsOfChapter",
		Params: map[string]any{
			"chapter_ids": chapterID,
		},
	}

	queryData, err := adapter.GetAdapterInstance().QueryData(context.Background(), string(token), params)
	if err != nil {
		logger.Logger.Error("Failed to query data", zap.Error(err))
		return nil, err
	}

	questions := make([]model.Questions, 0)
	if err := json.Unmarshal(queryData, &questions); err != nil {
		logger.Logger.Error("Failed to unmarshal data", zap.Error(err))
		return nil, err
	}

	return questions, nil
}

func (s *QuestionService) GrabQuestionExts(userID int64, chapterIDS []int64, questions ...model.Questions) ([]model.QuestionsExt, error) {
	key := fmt.Sprintf("%d-token", userID)
	token, err := cache.FreeCache.Get([]byte(key))
	if err != nil || token == nil {
		logger.Logger.Info("Token expired or not found for user, stopping EventFetcher", zap.Int64("userID", userID))
		return nil, err
	}
	var ids []int64
	for _, v := range questions {
		ids = append(ids, v.ID)
	}

	params := adapter.RPCQueryParam{
		Method: "ListQuestionExts",
		Params: map[string]any{
			"question_ids": ids,
			"user_id":      userID,
			"chapter_ids":  chapterIDS,
		},
	}

	queryData, err := adapter.GetAdapterInstance().QueryData(context.Background(), string(token), params)
	if err != nil {
		logger.Logger.Error("Failed to query data", zap.Error(err))
		return nil, err
	}
	var questionsExt []model.QuestionsExt
	if err := json.Unmarshal(queryData, &questionsExt); err != nil {
		logger.Logger.Error("Failed to unmarshal data", zap.Error(err))
		return nil, err
	}
	return questionsExt, nil
}

// GetQuestionsByChapterID 根据章节ID获取关联的试题列表（不分页）
func (s *QuestionService) GetQuestionsByChapterID(chapterID int64) ([]model.Questions, error) {
	if chapterID == 0 {
		return nil, errors.New("无效的章节ID")
	}

	var questionIDs []int64
	// 查询该章节下所有关联的试题ID（来自 questions_ext 表）
	err := db.DB.Model(&model.QuestionsExt{}).
		Where("ext_key = ? AND ext_value = ?", "chapter", chapterID).
		Distinct("question_id").
		Pluck("question_id", &questionIDs).Error

	if err != nil {
		return nil, errors.New("查询试题ID失败: " + err.Error())
	}

	if len(questionIDs) == 0 {
		return []model.Questions{}, nil
	}

	// 查询试题详情
	var questions []model.Questions
	err = db.DB.Model(&model.Questions{}).
		Where("id IN (?)", questionIDs).
		Find(&questions).Error

	if err != nil {
		return nil, errors.New("查询试题失败: " + err.Error())
	}

	return questions, nil
}
