package teach

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"tms/model"
	"tmsc/pkg/cache"
	"tmsc/pkg/db"
	"tmsc/pkg/logger"
	"tmsc/services/adapter"

	"go.uber.org/zap"
)

func (cs *CourseSerivce) GrabCourses(userID int64, plans []model.TeachingPlan) ([]model.Courses, error) {
	key := fmt.Sprintf("%d-token", userID)
	token, err := cache.FreeCache.Get([]byte(key))
	if err != nil || token == nil {
		logger.Logger.Info("Token expired or not found for user, stopping EventFetcher", zap.Int64("userID", userID))
		return nil, err
	}
	var planID []int64
	for _, plan := range plans {
		planID = append(planID, plan.ID)
	}

	params := adapter.RPCQueryParam{
		Method: "ListCourse",
		Params: map[string]any{
			"plan_ids": planID,
		},
	}

	queryData, err := adapter.GetAdapterInstance().QueryData(context.Background(), string(token), params)
	if err != nil {
		logger.Logger.Error("Failed to query data", zap.Error(err))
		return nil, err
	}

	courses := make([]model.Courses, 0)
	if err := json.Unmarshal(queryData, &courses); err != nil {
		logger.Logger.Error("Failed to unmarshal data", zap.Error(err))
		return nil, err
	}

	return courses, nil
}

func (cs *CourseSerivce) GetTechCourses(req model.ReqCoursesSearch, userID int64) ([]model.Courses, int64, error) {
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 || req.PageSize > 100 {
		req.PageSize = 10
	}

	dbQuery := db.DB.Model(&model.Courses{})

	if req.Name != "" {
		dbQuery = dbQuery.Where("name LIKE ?", "%"+req.Name+"%")
	}
	if req.Status != "" {
		dbQuery = dbQuery.Where("status = ?", req.Status)
	}
	if req.UserID > 0 {
		dbQuery = dbQuery.Where("user_id = ?", req.UserID)
	}
	if req.PlanID > 0 {
		dbQuery = dbQuery.Where("plan_id = ?", req.PlanID)
	}

	var total int64
	if err := dbQuery.Count(&total).Error; err != nil {
		return nil, 0, errors.New("查询总数失败")
	}

	var list []model.Courses
	offset := (req.Page - 1) * req.PageSize
	if err := dbQuery.Offset(offset).Limit(req.PageSize).Order("created_at DESC").Find(&list).Error; err != nil {
		return nil, 0, errors.New("查询数据失败: " + err.Error())
	}

	return list, total, nil
}

func (cs *CourseSerivce) GetCourseList(req model.ReqCoursesSearch, userID int64) ([]model.Courses, int64, error) {
	var userRef model.StudentMap
	if err := db.DB.Where("user_id = ?", userID).First(&userRef).Error; err != nil {
		logger.Logger.Error("获取用户关联信息失败", zap.Error(err))
		return nil, 0, nil
	}

	var planIDS []int64
	if err := db.DB.Model(&model.TeachingPlanExt{}).Where("ext_key = ? AND ext_value = ?", "class_id", userRef.ClassID).Pluck("plan_id", &planIDS).Error; err != nil {
		logger.Logger.Error("获取教学计划关联信息失败", zap.Error(err))
		return nil, 0, nil
	}

	if len(planIDS) == 0 {
		return []model.Courses{}, 0, nil
	}

	dbQuery := db.DB.Model(&model.Courses{}).Where("plan_id IN (?)", planIDS)

	if req.Name != "" {
		dbQuery = dbQuery.Where("name LIKE ?", "%"+req.Name+"%")
	}

	var total int64
	if err := dbQuery.Count(&total).Error; err != nil {
		return nil, 0, errors.New("查询总数失败")
	}

	var list []model.Courses
	offset := (req.Page - 1) * req.PageSize
	if err := dbQuery.Offset(offset).Limit(req.PageSize).Order("id DESC").Find(&list).Error; err != nil {
		return nil, 0, errors.New("查询数据失败: " + err.Error())
	}

	return list, total, nil
}

func (cs *CourseSerivce) GetAttachedAttributes(courseID int64) ([]model.Courseware, []model.Resources, []model.Questions, error) {
	var course model.Courses
	if err := db.DB.First(&course, courseID).Error; err != nil {
		return nil, nil, nil, errors.New("未找到对应课程")
	}

	// 收集所有需要查询的章节ID
	finalChapterIDMap := make(map[int64]struct{})
	queue := make([]int64, 0)

	// 初始化队列，将所有初始章节ID加入队列
	if _, ok := finalChapterIDMap[course.ChapterID]; !ok {
		finalChapterIDMap[course.ChapterID] = struct{}{}
		queue = append(queue, course.ChapterID)
	}

	// TODO:bfs适合于大数据量的情况
	// 若不想查询链接过多，可以将全部的id和parent_id 加载进内存
	// 然后进行 通过id，查询出需要构建的id 和 parent_id
	// 一次性查询出需要的数据，进行构建
	// BFS 遍历：只向下查找所有子章节
	for len(queue) > 0 {
		currentID := queue[0]
		queue = queue[1:]

		// 查找子节点
		var children []model.Chapter
		if err := db.DB.Where("parent_id = ?", currentID).Find(&children).Error; err != nil {
			logger.Logger.Error("获取章节子节点失败", zap.Error(err))
			continue
		}
		for _, child := range children {
			if _, ok := finalChapterIDMap[child.ID]; !ok {
				finalChapterIDMap[child.ID] = struct{}{}
				queue = append(queue, child.ID)
			}
		}
	}

	// 将 map 的 key 转换为 slice
	var idsToQuery []int64
	for id := range finalChapterIDMap {
		idsToQuery = append(idsToQuery, id)
	}

	// 课件是挂载在章节上的
	var coursewares []model.Courseware
	if err := db.DB.Where("chapter_id IN (?)", idsToQuery).Find(&coursewares).Error; err != nil {
		return nil, nil, nil, errors.New("查询课件失败: " + err.Error())
	}

	// 资源是挂载在章节上的
	// 需要先找到课程下的所有子章节
	var resources []model.Resources
	if err := db.DB.Where("chapter_id IN (?)", idsToQuery).Find(&resources).Error; err != nil {
		return nil, nil, nil, errors.New("查询资料失败: " + err.Error())
	}

	// 试题是挂载在章节上的
	// 需要先找到课程下的所有子章节
	var questionIDS []int64
	// 查询该章节下所有关联的试题ID（来自 questions_ext 表）
	err := db.DB.Model(&model.QuestionsExt{}).
		Where("ext_key = ? AND ext_value IN (?)", "chapter", idsToQuery).
		Distinct("question_id").
		Pluck("question_id", &questionIDS).Error
	if err != nil {
		return nil, nil, nil, errors.New("查询试题ID失败: " + err.Error())
	}
	var questions []model.Questions
	if err := db.DB.Where("id IN (?)", questionIDS).Find(&questions).Error; err != nil {
		return nil, nil, nil, errors.New("查询试题失败: " + err.Error())
	}

	return coursewares, resources, questions, nil
}
