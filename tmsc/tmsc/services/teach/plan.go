package teach

import (
	"context"
	"encoding/json"
	"fmt"
	"tms/model"
	"tmsc/pkg/cache"
	"tmsc/pkg/db"
	"tmsc/pkg/logger"
	"tmsc/services/adapter"
	"tmsc/utils"

	"go.uber.org/zap"
)

func (t *TeachSerivce) GrabTechPlan(userID int64) ([]model.TeachPlanWithExts, error) {
	key := fmt.Sprintf("%d-token", userID)
	token, err := cache.FreeCache.Get([]byte(key))
	if err != nil || token == nil {
		logger.Logger.Info("Token expired or not found for user, stopping EventFetcher", zap.Int64("userID", userID))
		return nil, err
	}
	params := adapter.RPCQueryParam{
		Method: "ListTeachPlans",
		Params: map[string]any{
			"user_id": userID,
		},
	}

	queryData, err := adapter.GetAdapterInstance().QueryData(context.Background(), string(token), params)
	if err != nil {
		logger.Logger.Error("Failed to query data", zap.Error(err))
		return nil, err
	}
	var plansWithExt []model.TeachPlanWithExts
	if err := json.Unmarshal(queryData, &plansWithExt); err != nil {
		logger.Logger.Error("Failed to unmarshal data", zap.Error(err))
		return nil, err
	}

	return plansWithExt, nil
}

func (t *TeachSerivce) GetTechPlanList(req model.ReqTeachingPlanSearch, userID int64) (*model.RespTeachingPlanList, error) {
	var userRef model.StudentMap
	if err := db.DB.Where("user_id = ?", userID).First(&userRef).Error; err != nil {
		logger.Logger.Error("获取用户关联信息失败", zap.Error(err))
		return nil, err
	}

	var planIDS []int64
	if err := db.DB.Model(&model.TeachingPlanExt{}).Where("ext_key = ? AND ext_value = ?", "class_id", userRef.ClassID).Pluck("plan_id", &planIDS).Error; err != nil {
		logger.Logger.Error("获取教学计划关联信息失败", zap.Error(err))
		return nil, err
	}

	plansQuery := db.DB.Where("id IN (?)", planIDS)
	if req.Status != "" {
		plansQuery = plansQuery.Where("status = ?", req.Status)
	}
	if req.Name != "" {
		plansQuery = plansQuery.Where("name LIKE ?", "%"+req.Name+"%")
	}

	var total int64
	if err := plansQuery.Count(&total).Error; err != nil {
		logger.Logger.Error("获取教学计划总数失败", zap.Error(err))
		return nil, err
	}

	offset := (req.Page - 1) * req.PageSize
	var plans []model.TeachingPlan
	if err := plansQuery.Offset(offset).Limit(req.PageSize).Find(&plans).Error; err != nil {
		logger.Logger.Error("获取教学计划失败", zap.Error(err))
		return nil, err
	}

	var list []model.PlanWithFormattedTime
	for _, plan := range plans {
		list = append(list, model.PlanWithFormattedTime{
			TeachingPlan: plan,
			StartAtStr:   utils.FormatUnixTime(plan.StartAt),
			EndAtStr:     utils.FormatUnixTime(plan.EndAt),
		})
	}

	return &model.RespTeachingPlanList{
		List:  list,
		Total: total,
	}, nil
}

func (t *TeachSerivce) GetTechPlanContent(planID int64) (*model.PlanWithFormattedTime, error) {
	var plan model.TeachingPlan
	if err := db.DB.Where("id = ?", planID).First(&plan).Error; err != nil {
		logger.Logger.Error("获取教学计划失败", zap.Error(err))
		return nil, err
	}
	return &model.PlanWithFormattedTime{
			TeachingPlan: plan,
			StartAtStr:   utils.FormatUnixTime(plan.StartAt),
			EndAtStr:     utils.FormatUnixTime(plan.EndAt)},
		nil
}

func (t *TeachSerivce) DeleteTechPlan() error {
	return nil
}
