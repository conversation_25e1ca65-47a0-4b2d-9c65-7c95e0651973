package teach

import (
	"tms/model"
	"tmsc/pkg/logger"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

func (t *TeachSerivce) UpsertPlans(tx *gorm.DB, plans []model.TeachingPlan) error {
	if len(plans) == 0 {
		return nil
	}
	var planIDs []int64
	for _, plan := range plans {
		planIDs = append(planIDs, plan.ID)
	}
	if err := tx.Where("id IN (?)", planIDs).Delete(&model.TeachingPlan{}).Error; err != nil {
		logger.Logger.Error("删除教学计划失败", zap.Error(err))
		return err
	}
	if err := tx.CreateInBatches(&plans, 100).Error; err != nil {
		logger.Logger.Error("保存教学计划失败", zap.Error(err))
		return err
	}
	return nil
}

func (t *TeachSerivce) UpsertPlanExts(tx *gorm.DB, plans []model.TeachingPlan, planExts []model.TeachingPlanExt) error {
	if len(planExts) == 0 {
		return nil
	}
	var planIDs, planExtIDs []int64
	for _, plan := range plans {
		planIDs = append(planIDs, plan.ID)
	}
	for _, ext := range planExts {
		planExtIDs = append(planExtIDs, ext.ExtID)
	}
	if err := tx.Where("plan_id IN (?) OR ext_id IN (?)", planIDs, planExtIDs).Delete(&model.TeachingPlanExt{}).Error; err != nil {
		logger.Logger.Error("删除教学计划扩展失败", zap.Error(err))
		return err
	}
	if err := tx.CreateInBatches(&planExts, 100).Error; err != nil {
		logger.Logger.Error("保存教学计划扩展失败", zap.Error(err))
		return err
	}
	return nil
}

func (t *TeachSerivce) UpsertCourses(tx *gorm.DB, plans []model.TeachingPlan, courses []model.Courses) error {
	if len(courses) == 0 {
		return nil
	}
	var planIDs []int64
	for _, plan := range plans {
		planIDs = append(planIDs, plan.ID)
	}
	if err := tx.Where("plan_id IN (?)", planIDs).Delete(&model.Courses{}).Error; err != nil {
		logger.Logger.Error("删除课程失败", zap.Error(err))
		return err
	}
	if err := tx.CreateInBatches(&courses, 100).Error; err != nil {
		logger.Logger.Error("保存课程失败", zap.Error(err))
		return err
	}
	return nil
}

func (t *TeachSerivce) UpsertChapters(tx *gorm.DB, courses []model.Courses, chapters []model.Chapter) error {
	if len(chapters) == 0 {
		return nil
	}
	var chapterIDs []int64
	for _, ch := range chapters {
		chapterIDs = append(chapterIDs, ch.ID)
	}
	for _, course := range courses {
		chapterIDs = append(chapterIDs, course.ChapterID)
	}
	if err := tx.Where("id IN (?)", chapterIDs).Delete(&model.Chapter{}).Error; err != nil {
		logger.Logger.Error("删除章节失败", zap.Error(err))
		return err
	}
	if err := tx.CreateInBatches(&chapters, 100).Error; err != nil {
		logger.Logger.Error("保存章节失败", zap.Error(err))
		return err
	}
	return nil
}

func (t *TeachSerivce) UpsertCoursewares(tx *gorm.DB, chapters []model.Chapter, coursewaresWithExt []model.CoursewareWithExt) error {
	if len(coursewaresWithExt) == 0 {
		return nil
	}
	var coursewareIDs, chapterIDs []int64
	var coursewares []model.Courseware
	var coursewaresExt []model.CoursewareExt

	for _, ch := range chapters {
		chapterIDs = append(chapterIDs, ch.ID)
	}
	for _, c := range coursewaresWithExt {
		coursewareIDs = append(coursewareIDs, c.Courseware.ID)
		coursewares = append(coursewares, c.Courseware)
		coursewaresExt = append(coursewaresExt, c.Exts...)
	}
	coursewareIDs = append(coursewareIDs, chapterIDs...)

	if err := tx.Where("id IN (?)", coursewareIDs).Delete(&model.Courseware{}).Error; err != nil {
		logger.Logger.Error("删除课件失败", zap.Error(err))
		return err
	}
	if err := tx.CreateInBatches(&coursewares, 100).Error; err != nil {
		logger.Logger.Error("保存课件失败", zap.Error(err))
		return err
	}
	if err := tx.Where("courseware_id IN (?)", coursewareIDs).Delete(&model.CoursewareExt{}).Error; err != nil {
		logger.Logger.Error("删除课件扩展失败", zap.Error(err))
		return err
	}
	if err := tx.CreateInBatches(&coursewaresExt, 100).Error; err != nil {
		logger.Logger.Error("保存课件扩展失败", zap.Error(err))
		return err
	}
	return nil
}

func (t *TeachSerivce) UpsertQuestions(tx *gorm.DB, chapters []model.Chapter, questions []model.Questions, questionExts []model.QuestionsExt) error {
	if len(questions) == 0 {
		return nil
	}
	var questionIDs, chapterIDs []int64
	for _, q := range questions {
		questionIDs = append(questionIDs, q.ID)
	}
	for _, ch := range chapters {
		chapterIDs = append(chapterIDs, ch.ID)
	}

	if err := tx.Where("ext_key = ? AND ext_value IN (?)", "chapter", chapterIDs).Delete(&model.QuestionsExt{}).Error; err != nil {
		logger.Logger.Error("删除试题关联失败", zap.Error(err))
		return err
	}
	if err := tx.Where("id IN (?)", questionIDs).Delete(&model.Questions{}).Error; err != nil {
		logger.Logger.Error("删除题库失败", zap.Error(err))
		return err
	}
	if err := tx.CreateInBatches(&questions, 100).Error; err != nil {
		logger.Logger.Error("保存题库失败", zap.Error(err))
		return err
	}
	if len(questionExts) > 0 {
		var extIDs []int64
		for _, ext := range questionExts {
			extIDs = append(extIDs, ext.ID)
		}
		if err := tx.Where("id IN (?)", extIDs).Delete(&model.QuestionsExt{}).Error; err != nil {
			logger.Logger.Error("删除试题扩展失败", zap.Error(err))
			return err
		}
		if err := tx.CreateInBatches(&questionExts, 100).Error; err != nil {
			logger.Logger.Error("保存试题扩展失败", zap.Error(err))
			return err
		}
	}
	return nil
}
