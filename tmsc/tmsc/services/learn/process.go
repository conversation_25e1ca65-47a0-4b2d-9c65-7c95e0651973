package learn

import (
	"fmt"
	"time"
	"tms/model"
	"tmsc/pkg/db"
	"tmsc/utils"

	"github.com/spf13/cast"
)

type LearningProgress struct {
	TotalSCOs       int    `json:"total_scos"`
	CompletedSCOs   int    `json:"completed_scos"`
	LearningPercent string `json:"learning_percent"`
}

// WeeklyLearningStats 用于返回周学习统计数据
type WeeklyLearningStats struct {
	CurrentWeekHours float64 `json:"current_week_hours"`
	LastWeekHours    float64 `json:"last_week_hours"`
	Improvement      float64 `json:"improvement"`
}

// DailyLearningStats 用于返回每日学习时长
type DailyLearningStats struct {
	Date         string  `json:"date"`
	LearningTime float64 `json:"learning_time"`
}

func (s *LearnService) GetCourseLearningProgress(courseID int64, learnerID int64) (*LearningProgress, error) {
	totalSCOs := 100
	completedSCOs := 85
	learningPercent := fmt.Sprintf("%.2f", float64(completedSCOs)/float64(totalSCOs))
	return &LearningProgress{
		TotalSCOs:       totalSCOs,
		CompletedSCOs:   completedSCOs,
		LearningPercent: learningPercent,
	}, nil
}

func (s *LearnService) GetChapterLearningProgress(chapterID int64, learnerID int64) (*LearningProgress, error) {
	totalSCOs := 100
	completedSCOs := 85
	learningPercent := fmt.Sprintf("%.2f", float64(completedSCOs)/float64(totalSCOs))
	return &LearningProgress{
		TotalSCOs:       totalSCOs,
		CompletedSCOs:   completedSCOs,
		LearningPercent: learningPercent,
	}, nil
}

// GetWeeklyLearningStats 计算每周学习统计
func (s *LearnService) GetWeeklyLearningStats(userID int64) (*WeeklyLearningStats, error) {
	now := time.Now()
	// 计算本周和上周的时间范围
	startOfWeek := now.AddDate(0, 0, -int(now.Weekday())+1)
	startOfLastWeek := startOfWeek.AddDate(0, 0, -7)
	endOfWeek := startOfWeek.AddDate(0, 0, 7)

	// 获取本周的学习时长
	currentWeekSeconds, err := s.calculateTotalTime(userID, startOfWeek, endOfWeek)
	if err != nil {
		return nil, err
	}

	// 获取上周的学习时长
	lastWeekSeconds, err := s.calculateTotalTime(userID, startOfLastWeek, startOfWeek)
	if err != nil {
		return nil, err
	}

	currentWeekHours := float64(currentWeekSeconds) / 3600
	lastWeekHours := float64(lastWeekSeconds) / 3600

	var improvement float64
	if lastWeekHours > 0 {
		improvement = ((currentWeekHours - lastWeekHours) / lastWeekHours) * 100
	} else if currentWeekHours > 0 {
		improvement = 100.0 // 如果上周没有学习，本周有学习，则提升100%
	}

	return &WeeklyLearningStats{
		CurrentWeekHours: currentWeekHours,
		LastWeekHours:    lastWeekHours,
		Improvement:      improvement,
	}, nil
}

// GetDailyLearningStats 获取每日学习时长
func (s *LearnService) GetDailyLearningStats(userID int64, start, end time.Time) ([]DailyLearningStats, error) {
	var dailyStats []DailyLearningStats

	for d := start; d.Before(end.AddDate(0, 0, 1)); d = d.AddDate(0, 0, 1) {
		dayStart := time.Date(d.Year(), d.Month(), d.Day(), 0, 0, 0, 0, d.Location())
		dayEnd := dayStart.AddDate(0, 0, 1)

		totalSeconds, err := s.calculateTotalTime(userID, dayStart, dayEnd)
		if err != nil {
			return nil, err
		}

		dailyStats = append(dailyStats, DailyLearningStats{
			Date:         d.Format("2006-01-02"),
			LearningTime: float64(totalSeconds) / 3600,
		})
	}

	return dailyStats, nil
}

// calculateTotalTime 计算指定时间范围内的总学习时长（秒）
func (s *LearnService) calculateTotalTime(userID int64, start, end time.Time) (int64, error) {
	var totalSeconds int64

	// 从 ScormSessions 获取学习时长
	var scormSessions []model.ScormSessions
	err := db.DB.Where("learner_id = ? AND last_access_time >= ? AND last_access_time < ?", cast.ToString(userID), start.Unix(), end.Unix()).Find(&scormSessions).Error
	if err != nil {
		return 0, err
	}

	for _, session := range scormSessions {
		seconds, err := utils.ParseISODuration(session.TotalTime)
		if err == nil {
			totalSeconds += seconds
		}
	}

	// 从 VirtualCoursewareSubmitHistory 获取学习时长
	var history []model.VirtualCoursewareSubmitHistory
	err = db.DB.Where("user_id = ? AND completed_at >= ? AND completed_at < ?", userID, start.Unix(), end.Unix()).Find(&history).Error
	if err != nil {
		return 0, err
	}

	for _, h := range history {
		totalSeconds += h.TotalTime
	}

	return totalSeconds, nil
}
