package resource

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"
	"tms/model"
	"tmsc/pkg/cache"
	"tmsc/pkg/config"
	"tmsc/pkg/db"
	"tmsc/pkg/logger"
	"tmsc/services/adapter"
	"tmsc/utils"

	"github.com/spf13/cast"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

type ResourceService struct{}

func NewResourceService() *ResourceService {
	return &ResourceService{}
}

func (s *ResourceService) GrabResourceCategory(userID int64, param map[string]any) ([]model.ResourceCategory, error) {
	key := fmt.Sprintf("%d-token", userID)
	token, err := cache.FreeCache.Get([]byte(key))
	if err != nil || token == nil {
		logger.Logger.Info("Token expired or not found for user, stopping EventFetcher", zap.Int64("userID", userID))
		return nil, err
	}
	params := adapter.RPCQueryParam{
		Method: "ListResourceCategory",
		Params: map[string]any{
			"user_id": userID,
		},
	}
	for k, v := range param {
		params.Params[k] = v
	}

	queryData, err := adapter.GetAdapterInstance().QueryData(context.Background(), string(token), params)
	if err != nil {
		logger.Logger.Error("Failed to query data", zap.Error(err))
		return nil, err
	}

	var resourceCategories []model.ResourceCategory
	if err := json.Unmarshal(queryData, &resourceCategories); err != nil {
		logger.Logger.Error("Failed to unmarshal data", zap.Error(err))
		return nil, err
	}

	return resourceCategories, nil
}

func (s *ResourceService) GrabResource(userID int64, param map[string]any) ([]model.Resources, error) {
	key := fmt.Sprintf("%d-token", userID)
	token, err := cache.FreeCache.Get([]byte(key))
	if err != nil || token == nil {
		logger.Logger.Info("Token expired or not found for user, stopping EventFetcher", zap.Int64("userID", userID))
		return nil, err
	}
	params := adapter.RPCQueryParam{
		Method: "ListResource",
		Params: map[string]any{
			"user_id": userID,
		},
	}
	for k, v := range param {
		params.Params[k] = v
	}

	queryData, err := adapter.GetAdapterInstance().QueryData(context.Background(), string(token), params)
	if err != nil {
		logger.Logger.Error("Failed to query data", zap.Error(err))
		return nil, err
	}

	var resources []model.Resources
	if err := json.Unmarshal(queryData, &resources); err != nil {
		logger.Logger.Error("Failed to unmarshal data", zap.Error(err))
		return nil, err
	}

	return resources, nil
}

func (s *ResourceService) UpsertData(tx *gorm.DB, userID int64, categories []model.ResourceCategory, resources []model.Resources) error {
	categoryIDs := make([]int64, 0)
	if len(categories) != 0 {
		var chapterIDs []int64
		if err := tx.Model(&model.Chapter{}).Pluck("id", &chapterIDs).Error; err != nil {
			return err
		}
		if err := tx.Where("chapter_id IN (?)", chapterIDs).Delete(&model.ResourceCategory{}).Error; err != nil {
			return err
		}
		if err := tx.CreateInBatches(&categories, 100).Error; err != nil {
			return err
		}
	}
	for _, v := range categories {
		categoryIDs = append(categoryIDs, v.ID)
	}

	if len(resources) != 0 {
		if err := tx.Where("category_id IN (?)", categoryIDs).Delete(&model.Resources{}).Error; err != nil {
			return err
		}
		if err := tx.CreateInBatches(&resources, 100).Error; err != nil {
			return err
		}
	}

	return nil
}

// GetResourceCategoryList 获取资料分类列表（支持搜索和树状结构）
func (s *ResourceService) GetResourceCategory(req model.ReqResourceCategorySearch) ([]*model.ResourceCategoryTreeNode, error) {
	dbQuery := db.DB.Model(&model.ResourceCategory{})
	if req.Name != "" {
		dbQuery = dbQuery.Where("name LIKE ?", "%"+req.Name+"%")
	}
	if req.ParentID > 0 {
		dbQuery = dbQuery.Where("parent_id = ?", req.ParentID)
	}

	var categories []model.ResourceCategory
	if err := dbQuery.Order("id ASC").Find(&categories).Error; err != nil {
		return nil, errors.New("获取数据失败")
	}

	return buildResourceCategoryTree(categories), nil
}

// buildResourceCategoryTree 将平级数据构建成树状结构
func buildResourceCategoryTree(list []model.ResourceCategory) []*model.ResourceCategoryTreeNode {
	categoryMap := make(map[int64]*model.ResourceCategoryTreeNode)

	// 第一步：初始化所有节点为指针，并存入 map
	for _, category := range list {
		categoryMap[category.ID] = &model.ResourceCategoryTreeNode{
			ID:          category.ID,
			Name:        category.Name,
			Description: category.Description,
			ParentID:    category.ParentID,
			ChapterID:   category.ChapterID,
			Children:    make([]*model.ResourceCategoryTreeNode, 0),
		}
	}
	var tree []*model.ResourceCategoryTreeNode
	for _, m := range list {
		node := categoryMap[m.ID]

		if m.ParentID == 0 {
			tree = append(tree, node)
		} else {
			if parent, ok := categoryMap[m.ParentID]; ok {
				parent.Children = append(parent.Children, node)
			}
		}
	}

	return tree
}

func (s *ResourceService) GetResourceList(req model.ReqResourceSearch) (model.RespResourceList, error) {
	var res model.RespResourceList
	dbQuery := db.DB.Model(&model.Resources{})
	if req.Name != "" {
		dbQuery = dbQuery.Where("name LIKE ?", "%"+req.Name+"%")
	}
	if req.Status != "" {
		dbQuery = dbQuery.Where("status = ?", req.Status)
	}
	if req.UserID > 0 {
		dbQuery = dbQuery.Where("user_id = ?", req.UserID)
	}
	if req.CategoryID > 0 {
		dbQuery = dbQuery.Where("category_id = ?", req.CategoryID)
	}
	if req.ChapterID > 0 {
		dbQuery = dbQuery.Where("chapter_id = ?", req.ChapterID)
	}

	if err := dbQuery.Count(&res.Total).Error; err != nil {
		return res, errors.New("查询总数失败")
	}

	var resources []model.Resources
	offset := (req.Page - 1) * req.PageSize
	if err := dbQuery.Offset(offset).Limit(req.PageSize).Order("created_at DESC").Find(&resources).Error; err != nil {
		return res, errors.New("查询数据失败: " + err.Error())
	}
	resourcesData := make([]*model.RespResourceData, 0)
	for _, v := range resources {
		var downloadLog model.ResourceDownloadLog
		if err := db.DB.Where("resource_id = ?", v.ID).Order("download_time DESC").First(&downloadLog).Error; err != nil {
			if !errors.Is(err, gorm.ErrRecordNotFound) {
				return res, errors.New("查询数据失败: " + err.Error())
			}
		}

		resourcesData = append(resourcesData, &model.RespResourceData{
			Resources:   v,
			DownloadURL: strings.TrimPrefix(downloadLog.Path, "data/resources/"),
		})
	}
	res.List = resourcesData

	return res, nil
}

func (s *ResourceService) GetResourceContent(id int64) (model.Resources, error) {
	var res model.Resources
	if err := db.DB.Where("id = ?", id).First(&res).Error; err != nil {
		return res, errors.New("查询数据失败: " + err.Error())
	}
	return res, nil
}

func (s *ResourceService) DownloadResource(userID int64, ids []int64) error {
	if len(ids) == 0 {
		return errors.New("请至少选择一个文件")
	}

	downLoadPathFolder := filepath.Join("data", "resources")
	if err := os.MkdirAll(downLoadPathFolder, os.ModePerm); err != nil {
		return errors.New("创建文件夹失败: " + err.Error())
	}

	// 使用 goroutine 并发处理下载，并使用 WaitGroup 等待所有下载完成
	var wg sync.WaitGroup
	errCh := make(chan error, len(ids)) // 用于收集下载过程中的错误

	// 创建一个速率限制器，每秒允许5个请求
	rateLimiter := make(chan struct{}, 5)
	ticker := time.NewTicker(200 * time.Millisecond) // 每200毫秒发送一个令牌

	for _, id := range ids {
		<-ticker.C                // 等待令牌
		rateLimiter <- struct{}{} // 获取一个许可

		wg.Add(1)
		go func(resourceID int64) {
			defer wg.Done()
			defer func() { <-rateLimiter }() // 释放许可

			var res model.Resources
			if err := db.DB.Where("id = ?", resourceID).First(&res).Error; err != nil {
				errCh <- fmt.Errorf("查询数据失败: %w", err)
				return
			}

			remote := strings.TrimSuffix(config.Config.Remote.URL, "api")
			remoteFilePath := strings.TrimPrefix(res.Path, "./")
			remoteFilePath = remote + remoteFilePath
			var downloadFilePath string
			// 确定下载文件的保存路径
			now := time.Now()
			year := now.Year()
			month := cast.ToInt(now.Month())
			day := now.Day()
			downloadDir := filepath.Join("data", "resources", cast.ToString(year), cast.ToString(month), cast.ToString(day))
			if err := os.MkdirAll(downloadDir, os.ModePerm); err != nil {
				errCh <- fmt.Errorf("创建文件夹失败: %w", err)
				return
			}

			downloadFilePath = filepath.Join(downloadDir, filepath.Base(res.Path))
			logger.Logger.Info("开始下载文件", zap.Int64("user_id", userID), zap.String("name", res.Name), zap.String("source_path", remoteFilePath), zap.String("destination_path", downloadFilePath))
			if err := utils.DownloadFile(remoteFilePath, downloadFilePath); err != nil {
				errCh <- fmt.Errorf("下载文件失败: %w", err)
				return
			}

			// 生成下载记录
			if err := db.DB.Create(&model.ResourceDownloadLog{
				ResourceID:   resourceID,
				UserID:       userID,
				DownloadTime: time.Now().Unix(),
				Path:         downloadFilePath,
			}).Error; err != nil {
				errCh <- fmt.Errorf("生成下载记录失败: %w", err)
				return
			}

			logger.Logger.Info("Downloaded resource", zap.Int64("user_id", userID), zap.String("name", res.Name), zap.String("source_path", remoteFilePath), zap.String("destination_path", downloadFilePath))
		}(id)
	}

	wg.Wait()
	close(errCh) // 关闭错误通道

	// 检查是否有错误发生
	var allErrors []error
	for err := range errCh {
		logger.Logger.Error("下载过程中发生错误", zap.Error(err))
		allErrors = append(allErrors, err)
	}

	if len(allErrors) > 0 {
		return fmt.Errorf("下载过程中发生错误")
	}

	return nil
}

func (s *ResourceService) DownloadResourceHistory(req model.ResourceDownloadQuery) ([]model.ResourceDownloadResponse, int64, error) {
	dbQuery := db.DB.Model(&model.ResourceDownloadLog{}).
		Joins("LEFT JOIN users ON users.id = resource_download_log.user_id").
		Joins("LEFT JOIN resources ON resources.id = resource_download_log.resource_id").
		Select("resource_download_log.*, users.username, resources.name as resource_name, resources.size as resource_size, resources.ext as resource_ext")

	if req.ResourceID != 0 {
		dbQuery = dbQuery.Where("resource_download_log.resource_id = ?", req.ResourceID)
	}
	if req.UserID != 0 {
		dbQuery = dbQuery.Where("resource_download_log.user_id = ?", req.UserID)
	}
	if req.StartAt != 0 {
		dbQuery = dbQuery.Where("download_time >= ?", req.StartAt)
	}
	if req.EndAt != 0 {
		dbQuery = dbQuery.Where("download_time <= ?", req.EndAt)
	}

	result := make([]model.ResourceDownloadResponse, 0)
	var total int64
	if err := dbQuery.Count(&total).Error; err != nil {
		return result, 0, errors.New("查询总数失败")
	}

	if req.Page > 0 && req.PageSize > 0 {
		offset := (req.Page - 1) * req.PageSize
		dbQuery = dbQuery.Offset(offset).Limit(req.PageSize)
	}

	if err := dbQuery.Find(&result).Error; err != nil {
		return result, 0, errors.New("查询数据失败: " + err.Error())
	}

	return result, total, nil
}

func (s *ResourceService) CheckResourceExists(id int64) (bool, error) {
	var history model.ResourceDownloadLog
	err := db.DB.Where("resource_id = ?", id).Order("download_time DESC").First(&history).Error
	if err != nil {
		return false, err
	}
	if _, err := os.Stat(history.Path); os.IsNotExist(err) {
		return false, nil
	}

	return true, nil
}

func (s *ResourceService) ClearResourceCache(id int64) error {
	var resource model.Resources
	if err := db.DB.First(&resource, id).Error; err != nil {
		return errors.New("未找到对应资料")
	}

	var downlog model.ResourceDownloadLog
	if err := db.DB.Where("resource_id = ?", id).Order("download_time DESC").First(&downlog).Error; err == nil {
		// 删除文件
		if _, err := os.Stat(downlog.Path); err == nil {
			if err := os.Remove(downlog.Path); err != nil {
				return errors.New("删除文件失败: " + err.Error())
			}
		} else if !os.IsNotExist(err) {
			return errors.New("检查文件状态失败: " + err.Error())
		}
	}

	return nil
}
