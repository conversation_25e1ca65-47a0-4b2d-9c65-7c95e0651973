package system

import (
	"sort"
	"tms/model"
)

type MenuNode struct {
	*model.Menus
	Children []*MenuNode `json:"children"`
}

func BuildMenuTree(menus []*model.Menus) []*MenuNode {
	menuMap := make(map[int64]*MenuNode)
	var roots []*MenuNode

	for _, menu := range menus {
		node := &MenuNode{
			Menus:    menu,
			Children: []*MenuNode{}, // 确保Children字段初始化为空数组
		}
		menuMap[menu.ID] = node

		if menu.ParentID == 0 {
			roots = append(roots, node)
		} else {
			if parent, ok := menuMap[menu.ParentID]; ok {
				parent.Children = append(parent.Children, node)
			} else {
				// 如果父节点尚未处理，则将其视为根节点，这通常不应该发生，除非数据有问题
				roots = append(roots, node)
			}
		}
	}

	// 对每个节点的子菜单进行排序
	for _, node := range menuMap {
		sort.Slice(node.Children, func(i, j int) bool {
			return node.Children[i].Menus.OrderNum < node.Children[j].Menus.OrderNum
		})
	}

	// 对根菜单进行排序
	sort.Slice(roots, func(i, j int) bool {
		return roots[i].Menus.OrderNum < roots[j].Menus.OrderNum
	})

	return roots
}

func findMenuTree(menuTrees []*MenuNode, parentID int64) *MenuNode {
	for _, menuTree := range menuTrees {
		if menuTree.Menus.ID == parentID {
			return menuTree
		}
		if len(menuTree.Children) > 0 {
			childMenuTree := findMenuTree(menuTree.Children, parentID)
			if childMenuTree != nil {
				return childMenuTree
			}
		}
	}
	return nil
}

// ConvertMenuNodesToMenus converts a slice of MenuNode to a slice of Menu
func ConvertMenuNodesToMenus(menuNodes []*MenuNode) []*model.Menus {
	menus := make([]*model.Menus, 0)
	for _, node := range menuNodes {
		// 增加空值检查，处理潜在的无效节点
		if node == nil || node.Menus == nil {
			continue // 跳过空节点或其内部Menus为nil的节点
		}
		// 直接追加嵌入的*Menus，避免不必要的内存分配和字段复制
		menus = append(menus, node.Menus)
		if len(node.Children) > 0 {
			childrenMenus := ConvertMenuNodesToMenus(node.Children)
			menus = append(menus, childrenMenus...)
		}
	}
	return menus
}
