package cron

import (
	"context"
	"fmt"
	"sync"

	"time"

	"tms/model"
	"tmsc/pkg/cache"
	"tmsc/pkg/logger"
	"tmsc/services/adapter"
	usync "tmsc/services/sync"

	"go.uber.org/zap"
)

const (
	minInterval = 1 * time.Second
	maxInterval = 8 * time.Second
)

type EventFetcher struct {
	userID          int64 // Add userID
	context         context.Context
	cancel          context.CancelFunc
	currentInterval time.Duration
	timer           *time.Timer
}

var (
	// activeFetchers stores active EventFetcher instances by user ID
	activeFetchers = make(map[int64]*EventFetcher)
	fetcherMutex   sync.Mutex // To protect activeFetchers map
)

func NewEventFetcher(userID int64) *EventFetcher { // Remove ctx parameter
	// Create a cancellable context from the incoming context
	childCtx, cancel := context.WithCancel(context.Background()) // Use context.Background()
	return &EventFetcher{
		userID:          userID, // Store userID
		context:         childCtx,
		cancel:          cancel,
		currentInterval: minInterval,
	}
}

func (f *EventFetcher) Start() {
	f.timer = time.NewTimer(f.currentInterval)
	go func() {
		for {
			select {
			case <-f.timer.C:
				f.fetch()
				f.processLocalEvent()
				f.processServerEvent()
			case <-f.context.Done(): // Listen for context cancellation
				logger.Logger.Info("EventFetcher stopped due to context cancellation.")
				f.timer.Stop() // Stop the timer
				return
			}
		}
	}()
}

// Stop method to cancel the fetcher's context
func (f *EventFetcher) Stop() {
	if f.cancel != nil {
		f.cancel()
	}
}

func (f *EventFetcher) fetch() {
	// Check token validity
	key := fmt.Sprintf("%d-token", f.userID)
	token, err := cache.FreeCache.Get([]byte(key))
	if err != nil || token == nil {
		logger.Logger.Info("Token expired or not found for user, stopping EventFetcher", zap.Int64("userID", f.userID))
		StopEventFetcher(f.userID) // Stop this fetcher
		return
	}

	logger.Logger.Info("Fetching events...", zap.Duration("interval", f.currentInterval))

	var events []model.SyncRecords // Declare events here
	events, err = adapter.GetAdapterInstance().FetchEvent(f.context, string(token))
	if err != nil {
		logger.Logger.Error("Failed to fetch events", zap.Error(err))
		// Network error, back off
		f.backoff()
		f.timer.Reset(f.currentInterval)
		return
	}

	if len(events) > 0 {
		logger.Logger.Info("Successfully fetched new events")
		for _, e := range events {
			if err = usync.SaveToDB(e); err != nil {
				logger.Logger.Error("Failed to process event", zap.Error(err))
			}
		}
		// Reset interval on success
		f.resetInterval()
	} else {
		logger.Logger.Info("No new events")
		// No new events, back off
		f.backoff()
	}

	f.timer.Reset(f.currentInterval)
}

func (f *EventFetcher) backoff() {
	f.currentInterval *= 2
	if f.currentInterval > maxInterval {
		f.currentInterval = maxInterval
	}
}

func (f *EventFetcher) resetInterval() {
	f.currentInterval = minInterval
}

// StartEventFetcher starts or restarts an event fetcher for a given user.
func StartEventFetcher(userID int64) { // Remove c parameter
	fetcherMutex.Lock()
	defer fetcherMutex.Unlock()

	// If an old fetcher exists for this user, stop it first
	if oldFetcher, ok := activeFetchers[userID]; ok {
		logger.Logger.Info("Stopping existing EventFetcher for user", zap.Int64("userID", userID))
		oldFetcher.Stop()
		delete(activeFetchers, userID)
	}

	fetcher := NewEventFetcher(userID) // No c parameter
	activeFetchers[userID] = fetcher
	fetcher.Start()
	logger.Logger.Info("Started new EventFetcher for user", zap.Int64("userID", userID))
}

// StopEventFetcher stops the event fetcher for a given user.
func StopEventFetcher(userID int64) {
	fetcherMutex.Lock()
	defer fetcherMutex.Unlock()

	if fetcher, ok := activeFetchers[userID]; ok {
		logger.Logger.Info("Stopping EventFetcher for user", zap.Int64("userID", userID))
		fetcher.Stop()
		delete(activeFetchers, userID)
	} else {
		logger.Logger.Warn("No active EventFetcher found for user", zap.Int64("userID", userID))
	}
}

func (f *EventFetcher) processLocalEvent() {
	key := fmt.Sprintf("%d-token", f.userID)
	token, err := cache.FreeCache.Get([]byte(key))
	if err != nil || token == nil {
		logger.Logger.Info("Token expired or not found for user, stopping EventFetcher", zap.Int64("userID", f.userID))
		return
	}

	syncEvents := usync.FindClentSyncEvent(f.userID)
	if len(syncEvents) == 0 {
		f.backoff()
		f.timer.Reset(f.currentInterval)
		logger.Logger.Info("No local events to process")
		return
	}

	_, err = usync.ProcessLocalEvent(syncEvents)
	if err != nil {
		logger.Logger.Error("Failed to process local events", zap.Error(err))
		return
	}

	logger.Logger.Info("Successfully processed local events")
}

func (f *EventFetcher) processServerEvent() {
	key := fmt.Sprintf("%d-token", f.userID)
	token, err := cache.FreeCache.Get([]byte(key))
	if err != nil || token == nil {
		logger.Logger.Info("Token expired or not found for user, stopping EventFetcher", zap.Int64("userID", f.userID))
		return
	}

	syncEvents := usync.FindServerSyncEvent(f.userID)
	if len(syncEvents) == 0 {
		f.backoff()
		f.timer.Reset(f.currentInterval)
		logger.Logger.Info("No server events to process")
		return
	}

	for _, e := range syncEvents {
		err = adapter.GetAdapterInstance().PushEvent(f.context, e, string(token))
		if err != nil {
			logger.Logger.Error("Failed to process server events", zap.Error(err))
			continue
		}
		// TODO: 待新增同步回执机制，类似三次握手
		if err = usync.MarkEventAsSynced(e.EventID); err != nil {
			logger.Logger.Error("Failed to mark event as synced", zap.Error(err))
			continue
		}
	}

	// 本地修改完，推送给服务端,推送完后，若已经成功同步（服务端生成反馈数据），客户端抓取反馈数据，标记为已同步

	logger.Logger.Info("Successfully processed server events")
}
