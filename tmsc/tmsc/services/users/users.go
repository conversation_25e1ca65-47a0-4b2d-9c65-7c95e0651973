package users

import (
	"errors"
	"time"
	"tms/model"
	"tmsc/pkg/db"
	"tmsc/pkg/logger"
	"tmsc/utils"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

type UserQuery struct {
	Account  string
	Username string
	Phone    string
	Email    string

	Page     int
	PageSize int
}

type UserResponse struct {
	model.Users
	Roles []model.Roles `json:"roles"`
}

type UpdateUserParam struct {
	User      model.Users `json:"user_info"`
	RoleCodes []string    `json:"role_codes"`
}

func GetUsersByCondition(scopes func(db *gorm.DB) *gorm.DB) []model.Users {
	var users []model.Users
	if err := db.DB.Scopes(scopes).Order("id desc").Find(&users).Error; err != nil {
		logger.Logger.Error("获取用户失败", zap.Error(err))
		return nil
	}

	return users
}

func GetUserList(userQuery UserQuery) ([]UserResponse, int64, error) {
	// 构建查询范围
	scopes := func(db *gorm.DB) *gorm.DB {
		if userQuery.Account != "" {
			db = db.Where("account LIKE ?", "%"+userQuery.Account+"%")
		}
		if userQuery.Username != "" {
			db = db.Where("username LIKE ?", "%"+userQuery.Username+"%")
		}
		if userQuery.Phone != "" {
			db = db.Where("phone LIKE ?", "%"+userQuery.Phone+"%")
		}
		if userQuery.Email != "" {
			db = db.Where("email LIKE ?", "%"+userQuery.Email+"%")
		}

		return db
	}

	var count int64
	if err := db.DB.Model(&model.Users{}).Scopes(scopes).Count(&count).Error; err != nil {
		logger.Logger.Error("获取用户数量失败", zap.Error(err))
		return nil, 0, err
	}

	var users []model.Users
	userListQuery := db.DB.Scopes(scopes).Order("id desc")
	if userQuery.Page > 0 && userQuery.PageSize > 0 {
		offset := (userQuery.Page - 1) * userQuery.PageSize
		userListQuery = userListQuery.Offset(offset).Limit(userQuery.PageSize)
	}
	if err := userListQuery.Omit("password").Find(&users).Error; err != nil {
		logger.Logger.Error("获取用户失败", zap.Error(err))
		return nil, 0, err
	}

	if len(users) == 0 {
		return []UserResponse{}, count, nil
	}

	userIDs := make([]int64, len(users))
	for i, user := range users {
		userIDs[i] = user.ID
	}

	var userRoles []struct {
		UserID   int64       `gorm:"column:user_id"`
		RoleCode string      `gorm:"column:role_code"`
		Role     model.Roles `gorm:"embedded"`
	}

	if err := db.DB.Model(&model.UserRoles{}).
		Select("user_roles.user_id, user_roles.role_code, roles.*").
		Joins("left join roles on user_roles.role_code = roles.role_code").
		Where("user_roles.user_id IN (?)", userIDs).
		Find(&userRoles).Error; err != nil {
		logger.Logger.Error("批量获取用户角色失败", zap.Error(err))
		return nil, 0, err
	}

	userRolesMap := make(map[int64][]model.Roles)
	for _, ur := range userRoles {
		if ur.Role.ID != 0 || ur.Role.RoleCode != "" {
			userRolesMap[ur.UserID] = append(userRolesMap[ur.UserID], ur.Role)
		}
	}

	var userResponses []UserResponse
	for _, user := range users {
		roles, ok := userRolesMap[user.ID]
		if !ok {
			roles = []model.Roles{}
		}
		user.Salt = ""
		userResponses = append(userResponses, UserResponse{
			Users: user,
			Roles: roles,
		})
	}

	return userResponses, count, nil
}

// 更新自己的信息
func UpdateUser(user model.Users) error {
	if user.ID == 0 {
		return errors.New("用户ID不能为空")
	}

	updates := make(map[string]interface{})
	if user.Username != "" {
		updates["username"] = user.Username
	}
	if user.Phone != "" {
		updates["phone"] = user.Phone
	}
	if user.Email != "" {
		updates["email"] = user.Email
	}

	if user.Password != "" {
		password := utils.HashPassword(user.Password, user.Salt)
		updates["password"] = password
	}

	updates["updated_at"] = time.Now().UnixMilli()

	if err := db.DB.Model(&model.Users{}).Where("id = ?", user.ID).Updates(updates).Error; err != nil {
		logger.Logger.Error("更新用户失败", zap.Error(err))
		return err
	}

	return nil
}

// 通过远程端信息更新用户
func UpdateUserInfo(updateObj UpdateUserParam) error {
	logger.Logger.Info("UpdateUserInfo", zap.Any("updateObj", updateObj))
	if updateObj.User.ID == 0 {
		return errors.New("用户ID不能为空")
	}
	updates := make(map[string]interface{})

	var existingUser model.Users
	if err := db.DB.Where("id = ?", updateObj.User.ID).First(&existingUser).Error; err != nil {
		logger.Logger.Error("获取用户失败", zap.Error(err))
		return err
	}

	if updateObj.User.Username != "" {
		updates["username"] = updateObj.User.Username
	}
	if updateObj.User.Phone != "" {
		updates["phone"] = updateObj.User.Phone
	}
	if updateObj.User.Email != "" {
		updates["email"] = updateObj.User.Email
	}
	if updateObj.User.Password != "" {
		updates["salt"] = updateObj.User.Salt
		updates["password"] = utils.HashPassword(updateObj.User.Password, updateObj.User.Salt)
	}
	updates["updated_at"] = updateObj.User.UpdatedAt
	if err := db.DB.Model(&model.Users{}).Where("id = ?", updateObj.User.ID).Updates(updates).Error; err != nil {
		return err
	}

	if updateObj.RoleCodes != nil {
		err := db.DB.Transaction(func(tx *gorm.DB) error {
			if err := tx.Where("user_id = ?", updateObj.User.ID).Delete(&model.UserRoles{}).Error; err != nil {
				return err
			}
			for _, role := range updateObj.RoleCodes {
				if err := tx.Create(&model.UserRoles{
					UserID:   updateObj.User.ID,
					RoleCode: role,
				}).Error; err != nil {
					return err
				}
			}
			return nil
		})
		if err != nil {
			return err
		}
	}

	return nil
}

func DeleteLocalUser(userID int64) error {
	if err := db.DB.Transaction(func(tx *gorm.DB) error {
		var existingUser model.Users
		if err := tx.Where("id = ?", userID).First(&existingUser).Error; err != nil {
			return err
		}

		if err := tx.Where("id = ?", userID).Delete(&model.Users{}).Error; err != nil {
			return err
		}
		if err := tx.Where("user_id = ?", userID).Delete(&model.UserRoles{}).Error; err != nil {
			return err
		}
		if err := tx.Where("user_id = ?", userID).Delete(&model.UserCodes{}).Error; err != nil {
			return err
		}

		return nil
	}); err != nil {
		return err
	}
	return nil
}

func GetUserSysCodes(roleCodes []string) ([]string, error) {
	var sysCodes []string
	err := db.DB.Model(&model.Roles{}).Where("role_code IN ?", roleCodes).Pluck("sys_code", &sysCodes).Error
	if err != nil {
		logger.Logger.Error("获取系统代码失败", zap.Error(err))
		return nil, errors.New("获取系统代码失败")
	}
	// 手动去重
	unique := make(map[string]bool)
	var result []string
	for _, code := range sysCodes {
		if !unique[code] {
			unique[code] = true
			result = append(result, code)
		}
	}

	return result, nil
}

func SaveUserCodes(userId int64, classId int64, roleCodes []string) error {
	sysCodes, err := GetUserSysCodes(roleCodes)
	if err != nil {
		return err
	}
	err = db.DB.Model(&model.UserCodes{}).Where("user_id = ?", userId).Delete(&model.UserCodes{}).Error
	if err != nil {
		logger.Logger.Error("删除用户权限失败", zap.Error(err))
		return errors.New("删除用户权限失败")
	}
	var saveData []model.UserCodes
	// var isStudent bool
	for _, code := range sysCodes {
		saveData = append(saveData, model.UserCodes{UserID: userId, SysCode: code})
		if code == model.AuthUser {
			// isStudent = true
		}
	}
	if err := db.DB.Create(&saveData).Error; err != nil {
		return errors.New("更新用户权限失败")
	}
	// if isStudent && classId > 0 {
	// 	var classData model.Class
	// 	err := db.DB.Model(&model.Class{}).Where("id = ?", classId).First(&classData).Error
	// 	if err != nil {
	// 		logger.Logger.Error("获取班级失败", zap.Error(err))
	// 		return errors.New("获取班级失败")
	// 	}
	// 	// err = db.DB.Model(&model.StudentMap{}).Where("user_id = ?", userId).Delete(&model.StudentMap{}).Error
	// 	// if err != nil {
	// 	// 	logger.Logger.Error("清空用户班级关系失败", zap.Error(err))
	// 	// 	return errors.New("清空用户班级关系失败")
	// 	// }
	// 	// studentMap := model.StudentMap{ClassID: classId, MajorID: classData.MajorID, UserID: userId}
	// 	// if err := db.DB.Create(&studentMap).Error; err != nil {
	// 	// 	logger.Logger.Error("添加用户班级关系失败", zap.Error(err))
	// 	// 	return errors.New("添加用户班级关系失败")
	// 	// }
	// }
	return nil
}

func SaveStudetRef(ref model.StudentMap) error {
	err := utils.Transactional(db.DB, func(tx *gorm.DB) error {
		if ref.UserID == 0 {
			return nil
		}
		if ref.ClassID == 0 && ref.MajorID == 0 {
			return nil
		}

		if err := db.DB.Model(&model.StudentMap{}).Where("user_id = ?", ref.UserID).Delete(&model.StudentMap{}).Error; err != nil {
			logger.Logger.Error("清空用户班级关系失败", zap.Error(err))
			return err
		}

		if err := db.DB.Create(&ref).Error; err != nil {
			logger.Logger.Error("添加用户班级关系失败", zap.Error(err))
			return err
		}

		return nil
	})

	return err
}

func DeleteUser(userID int64) error {
	if err := db.DB.Transaction(func(tx *gorm.DB) error {
		if err := tx.Where("id = ?", userID).Delete(&model.Users{}).Error; err != nil {
			return err
		}
		if err := tx.Where("user_id = ?", userID).Delete(&model.UserRoles{}).Error; err != nil {
			return err
		}
		if err := tx.Where("user_id = ?", userID).Delete(&model.UserCodes{}).Error; err != nil {
			return err
		}
		if err := tx.Where("user_id = ?", userID).Delete(&model.StudentMap{}).Error; err != nil {
			return err
		}
		return nil
	}); err != nil {
		return err
	}
	return nil
}
