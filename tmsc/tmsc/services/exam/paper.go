package exam

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"tms/model"
	"tmsc/pkg/cache"
	"tmsc/pkg/db"
	"tmsc/pkg/logger"
	"tmsc/services/adapter"

	"go.uber.org/zap"
)

func (s *ExamService) GrabPapers(userID int64, exams ...model.Exams) ([]model.Papers, error) {
	key := fmt.Sprintf("%d-token", userID)
	token, err := cache.FreeCache.Get([]byte(key))
	if err != nil || token == nil {
		logger.Logger.Info("Token expired or not found for user, stopping EventFetcher", zap.Int64("userID", userID))
		return nil, err
	}
	ids := make([]int64, 0)
	for _, v := range exams {
		ids = append(ids, v.PaperID)
	}

	params := adapter.RPCQueryParam{
		Method: "ListPaper",
		Params: map[string]any{
			"paper_ids": ids,
			"user_id":   userID,
		},
	}

	queryData, err := adapter.GetAdapterInstance().QueryData(context.Background(), string(token), params)
	if err != nil {
		logger.Logger.Error("Failed to query data", zap.Error(err))
		return nil, err
	}

	var papers []model.Papers
	if err := json.Unmarshal(queryData, &papers); err != nil {
		logger.Logger.Error("Failed to unmarshal data", zap.Error(err))
		return nil, err
	}

	return papers, nil
}

func (s *ExamService) GetPaperOfExam(paperID int64) (model.RespPaperDetail, error) {
	var resp model.RespPaperDetail

	// 1. 获取试卷基本信息
	var paper model.Papers
	if err := db.DB.First(&paper, paperID).Error; err != nil {
		return resp, errors.New("未找到对应试卷")
	}
	resp.Papers = paper

	// 2. 获取试卷关联的题目列表
	var paperQuestions []model.PaperQuestions
	err := db.DB.Where("paper_id = ?", paperID).Find(&paperQuestions).Error

	if err != nil {
		return resp, errors.New("获取题目列表失败: " + err.Error())
	}
	resp.PaperQuestions = paperQuestions

	// 3. 获取课程信息
	if paper.CourseID > 0 {
		err := db.DB.Where("id = ?", paper.CourseID).First(&resp.Courses).Error
		if err != nil {
			return resp, errors.New("获取课程信息失败: " + err.Error())
		}
	}

	return resp, nil
}
