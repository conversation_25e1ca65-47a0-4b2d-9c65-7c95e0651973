package exam

import (
	"sort"
	"tms/model"
	"tmsc/pkg/db"
)

// 考试记录
func (s *ExamProgressService) GetExamHistory(userID int64) ([]model.ExamHistory, error) {
	var progresses []model.ExamProgress
	if err := db.DB.Where("status NOT IN (?) AND user_id = ?", []string{model.ExamProgressStatusNotStarted, model.ExamProgressStatusOngoing}, userID).Find(&progresses).Error; err != nil {
		return nil, err
	}
	var examIds []int64
	for _, p := range progresses {
		examIds = append(examIds, p.ExamID)
	}
	if len(examIds) == 0 {
		return []model.ExamHistory{}, nil
	}

	var joinExams []model.Exams
	if err := db.DB.Where("id IN (?)", examIds).Find(&joinExams).Error; err != nil {
		return nil, err
	}
	joinExamsMap := make(map[int64]model.Exams)
	examFinishMap := make(map[int64]struct{})
	for _, e := range joinExams {
		joinExamsMap[e.ID] = e
		if e.Status == model.ExamStatusGraded || e.Status == model.ExamStatusArchived || e.Status == model.ExamStatusCertified {
			examFinishMap[e.ID] = struct{}{}
		}
	}

	ranks, err := CalculateRanks(progresses, userID)
	if err != nil {
		return nil, err
	}
	rankMap := make(map[int64]int)
	for _, rank := range ranks {
		rankMap[rank.ExamID] = rank.Rank
	}

	var results []model.ExamHistory
	for _, p := range progresses {
		r := model.ExamHistory{
			UserID:         p.UserID,
			ExamName:       joinExamsMap[p.ExamID].Name,
			ExamID:         p.ExamID,
			ProgressdID:    p.ID,
			ProgressStatus: p.Status,
			ExamStatus:     joinExamsMap[p.ExamID].Status,
			SubmitAt:       p.EndTime,
			CostTime:       p.EndTime - p.StartTime,
		}
		if _, ok := examFinishMap[r.ExamID]; ok {
			r.Score = p.Score
			r.Rank = rankMap[r.ExamID]
		}
		results = append(results, r)
	}

	return results, nil
}

func CalculateRanks(progresses []model.ExamProgress, userID int64) ([]model.ExamRank, error) {
	// 1. 按 exam_id 分组
	grouped := make(map[int64][]model.ExamProgress)
	for _, p := range progresses {
		grouped[p.ExamID] = append(grouped[p.ExamID], p)
	}

	// 2. 排序 + 排名
	var results []model.ExamRank
	for examID, group := range grouped {
		// 排序（按 score 降序）
		sort.Slice(group, func(i, j int) bool {
			return group[i].Score > group[j].Score
		})

		// 排名计算（支持并列）
		rank := 1
		for i := 0; i < len(group); i++ {
			if i > 0 && group[i].Score < group[i-1].Score {
				rank = i + 1
			}
			results = append(results, model.ExamRank{
				UserID: group[i].UserID,
				ExamID: examID,
				Score:  group[i].Score,
				Rank:   rank,
			})
		}
	}

	return results, nil
}
