package exam

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"
	"tms/model"
	"tmsc/pkg/cache"
	"tmsc/pkg/db"
	"tmsc/pkg/logger"
	"tmsc/services/adapter"
	"tmsc/utils"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

func (s *ExamService) GrabExams(userID int64) ([]model.ExamsWithExt, error) {
	key := fmt.Sprintf("%d-token", userID)
	token, err := cache.FreeCache.Get([]byte(key))
	if err != nil || token == nil {
		logger.Logger.Info("Token expired or not found for user, stopping EventFetcher", zap.Int64("userID", userID))
		return nil, err
	}
	params := adapter.RPCQueryParam{
		Method: "ListExam",
		Params: map[string]any{
			"user_id": userID,
		},
	}

	queryData, err := adapter.GetAdapterInstance().QueryData(context.Background(), string(token), params)
	if err != nil {
		logger.Logger.Error("Failed to query data", zap.Error(err))
		return nil, err
	}
	var examsWithext []model.ExamsWithExt
	if err := json.Unmarshal(queryData, &examsWithext); err != nil {
		logger.Logger.Error("Failed to unmarshal data", zap.Error(err))
		return nil, err
	}

	return examsWithext, nil
}

func (s *ExamService) GetExamList(userID int64, req model.ReqExamSearch) ([]model.StudentExamEntry, int64, error) {
	var res []model.StudentExamEntry
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 || req.PageSize > 100 {
		req.PageSize = 10
	}
	if req.StartAt == 0 {
		req.StartAt = time.Now().Unix()
	}

	var examIDs []int64
	if err := db.DB.Model(&model.ExamsExt{}).Where("ext_key = ? AND ext_value = ?", "student", userID).
		Pluck("exam_id", &examIDs).Error; err != nil {
		logger.Logger.Error("获取考试关联信息失败", zap.Error(err))
		return nil, 0, err
	}

	dbQuery := db.DB.Model(&model.Exams{}).Where("exams.id IN (?)", examIDs).
		Joins("LEFT JOIN exam_progress ON exams.id = exam_progress.exam_id AND exam_progress.user_id = ?", userID).
		Select("exams.*, exam_progress.status as progress_status")
	if req.Name != "" {
		dbQuery = dbQuery.Where("exams.name LIKE ?", "%"+req.Name+"%")
	}
	if req.Status != "" {
		dbQuery = dbQuery.Where("exams.status = ?", req.Status)
	}
	if req.ExamType != "" {
		dbQuery = dbQuery.Where("exams.exam_type = ?", req.ExamType)
	}
	if req.StartAt > 0 && req.EndAt > 0 {
		// 查询时间范围内的考试（考试时间与查询时间范围有重叠）
		dbQuery = dbQuery.Where("exams.start_at <= ? AND exams.end_at >= ?", req.EndAt, req.StartAt)
	} else if req.StartAt > 0 {
		// 只指定开始时间，查找在该时间点有效的考试和未来的考试
		dbQuery = dbQuery.Where("((exams.start_at <= ? AND exams.end_at >= ?) OR (exams.start_at > ?))", req.StartAt, req.StartAt, req.StartAt)
	} else if req.EndAt > 0 {
		// 只指定结束时间，查找在该时间点之前开始的考试
		dbQuery = dbQuery.Where("exams.start_at <= ?", req.EndAt)
	}

	var total int64
	if err := dbQuery.Count(&total).Error; err != nil {
		logger.Logger.Error("exams count fail", zap.Error(err))
		return res, 0, errors.New("查询总数失败")
	}

	offset := (req.Page - 1) * req.PageSize
	if err := dbQuery.Offset(offset).Limit(req.PageSize).Order("exams.created_at DESC").Find(&res).Error; err != nil {
		return res, 0, errors.New("查询数据失败: " + err.Error())
	}

	return res, total, nil
}

func (s *ExamService) UpsertData(userID int64,
	exams []model.Exams,
	papers []model.Papers,
	paperQuestions []model.PaperQuestions,
	questions []model.Questions,
	examExts []model.ExamsExt,
	questionExts []model.QuestionsExt) error {

	err := utils.Transactional(db.DB, func(tx *gorm.DB) error {
		// Exams + ExamsExt
		if len(exams) > 0 {
			examIDs := make([]int64, len(exams))
			for i, e := range exams {
				examIDs[i] = e.ID
			}
			if err := db.DB.Where("id IN (?)", examIDs).Delete(&model.Exams{}).Error; err != nil {
				return err
			}
			if err := db.DB.CreateInBatches(exams, 100).Error; err != nil {
				return err
			}
			if err := db.DB.Where("exam_id IN (?)", examIDs).Delete(&model.ExamsExt{}).Error; err != nil {
				return err
			}
			if err := db.DB.CreateInBatches(examExts, 100).Error; err != nil {
				return err
			}
		}

		// Papers
		if len(papers) > 0 {
			paperIDs := make([]int64, len(papers))
			for i, p := range papers {
				paperIDs[i] = p.ID
			}
			if err := db.DB.Where("id IN (?)", paperIDs).Delete(&model.Papers{}).Error; err != nil {
				return err
			}
			if err := db.DB.CreateInBatches(papers, 100).Error; err != nil {
				return err
			}

			// PaperQuestions
			if len(paperQuestions) > 0 {
				pqIDs := make([]int64, len(paperQuestions))
				for i, pq := range paperQuestions {
					pqIDs[i] = pq.ID
				}
				if err := db.DB.Where("id IN (?) OR paper_id IN (?)", pqIDs, paperIDs).Delete(&model.PaperQuestions{}).Error; err != nil {
					return err
				}
				if err := db.DB.CreateInBatches(paperQuestions, 100).Error; err != nil {
					return err
				}
			}
		}

		// Questions + QuestionsExt
		if len(questions) > 0 {
			questionIDs := make([]int64, len(questions))
			for i, q := range questions {
				questionIDs[i] = q.ID
			}
			if err := db.DB.Where("id IN (?)", questionIDs).Delete(&model.Questions{}).Error; err != nil {
				return err
			}
			if err := db.DB.CreateInBatches(questions, 100).Error; err != nil {
				return err
			}
			qeIDS := make([]int64, len(questionExts))
			for i, qe := range questionExts {
				qeIDS[i] = qe.ID
			}
			if err := db.DB.Where("id IN (?) OR question_id IN (?)", qeIDS, questionIDs).Delete(&model.QuestionsExt{}).Error; err != nil {
				return err
			}
			if err := db.DB.CreateInBatches(questionExts, 100).Error; err != nil {
				return err
			}
		}

		return nil
	})

	if err != nil {
		logger.Logger.Error("Failed to upsert data transactionally", zap.Error(err))
	}
	return err
}
