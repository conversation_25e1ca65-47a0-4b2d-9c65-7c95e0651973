package exam

import (
	"context"
	"encoding/json"
	"fmt"
	"tms/model"
	"tmsc/pkg/cache"
	"tmsc/pkg/logger"
	"tmsc/services/adapter"

	"go.uber.org/zap"
)

func (s *ExamService) GrabPaperQuestions(userID int64, papers ...model.Papers) ([]model.PaperQuestions, error) {
	key := fmt.Sprintf("%d-token", userID)
	token, err := cache.FreeCache.Get([]byte(key))
	if err != nil || token == nil {
		logger.Logger.Info("Token expired or not found for user, stopping EventFetcher", zap.Int64("userID", userID))
		return nil, err
	}
	ids := make([]int64, 0)
	for _, v := range papers {
		ids = append(ids, v.ID)
	}

	params := adapter.RPCQueryParam{
		Method: "ListPaperQuestion",
		Params: map[string]any{
			"paper_ids": ids,
			"user_id":   userID,
		},
	}

	queryData, err := adapter.GetAdapterInstance().QueryData(context.Background(), string(token), params)
	if err != nil {
		logger.Logger.Error("Failed to query data", zap.Error(err))
		return nil, err
	}
	var paperQuestions []model.PaperQuestions
	if err := json.Unmarshal(queryData, &paperQuestions); err != nil {
		logger.Logger.Error("Failed to unmarshal data", zap.Error(err))
		return nil, err
	}

	return paperQuestions, nil
}

func (s *ExamService) GrabQuestions(userID int64, paperQuestions ...model.PaperQuestions) ([]model.Questions, error) {
	key := fmt.Sprintf("%d-token", userID)
	token, err := cache.FreeCache.Get([]byte(key))
	if err != nil || token == nil {
		logger.Logger.Info("Token expired or not found for user, stopping EventFetcher", zap.Int64("userID", userID))
		return nil, err
	}
	var ids []int64
	for _, v := range paperQuestions {
		ids = append(ids, v.QuestionID)
	}

	params := adapter.RPCQueryParam{
		Method: "ListQuestionOfPaper",
		Params: map[string]any{
			"question_ids": ids,
			"user_id":      userID,
		},
	}

	queryData, err := adapter.GetAdapterInstance().QueryData(context.Background(), string(token), params)
	if err != nil {
		logger.Logger.Error("Failed to query data", zap.Error(err))
		return nil, err
	}
	var questions []model.Questions
	if err := json.Unmarshal(queryData, &questions); err != nil {
		logger.Logger.Error("Failed to unmarshal data", zap.Error(err))
		return nil, err
	}

	return questions, nil
}

func (s *ExamService) GrabQuestionExts(userID int64, questions ...model.Questions) ([]model.QuestionsExt, error) {
	key := fmt.Sprintf("%d-token", userID)
	token, err := cache.FreeCache.Get([]byte(key))
	if err != nil || token == nil {
		logger.Logger.Info("Token expired or not found for user, stopping EventFetcher", zap.Int64("userID", userID))
		return nil, err
	}
	var ids []int64
	for _, v := range questions {
		ids = append(ids, v.ID)
	}

	params := adapter.RPCQueryParam{
		Method: "ListQuestionExts",
		Params: map[string]any{
			"question_ids": ids,
			"user_id":      userID,
		},
	}

	queryData, err := adapter.GetAdapterInstance().QueryData(context.Background(), string(token), params)
	if err != nil {
		logger.Logger.Error("Failed to query data", zap.Error(err))
		return nil, err
	}
	var questionsExt []model.QuestionsExt
	if err := json.Unmarshal(queryData, &questionsExt); err != nil {
		logger.Logger.Error("Failed to unmarshal data", zap.Error(err))
		return nil, err
	}

	return questionsExt, nil
}
