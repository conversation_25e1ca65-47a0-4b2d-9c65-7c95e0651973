Content authors are able to specify sequencing rules via XML in the the course’s manifest. These rules (known as the “sequencing definition model”) can be broken down into the following categories:
- Sequencing Control Modes – Determine what type of navigation is allowed by the user. Usually whether free navigation via a table of contents is used or whether linear navigation via previous/next buttons is used.
- Constrain Choice Controls – Restrict the activities that the user may select from the table of contents.
- Sequencing Rules – Specify if-then conditions that determine which activities are available for delivery and which activity should be delivered next.
- Limit Conditions – Provide limits on the number of times activities can be attempted.
- Rollup Rules – Specify if-then conditions that determine how status is rolled up through the hierarchy of aggregations.
- Rollup Controls – Determine which activities participate in status rollup and how their status is weighted in relation to other activities.
- Rollup Consideration Controls – Provide more precise control over status rollup than do the rollup controls.
- Objectives – Provide a way to track the status of individual learning objectives and share this status across activities. Objectives are often -overloaded and used as variables to control sequencing actions.
- Selection Controls – Provide a way to specify that only a random subset of the available activities should be delivered.
- Randomization Controls – Shuffle the order of the activities to be delivered.
- Delivery Controls – Allow for non-communicative content to be delivered and sequenced.
- Navigation Controls – Control which navigational UI elements should be presented by the LMS.

序列控制模式 - 确定用户允许的导航类型。通常是指是否使用目录进行自由导航，或者是否使用上一页/下一页按钮进行线性导航。
选择限制控制 - 限制用户可以从目录中选择的活动。
序列规则 - 指定如果-那么条件，以确定哪些活动可供交付以及下一个应交付的活动。
限制条件 - 提供对活动尝试次数的限制。
汇总规则 - 指定如果-那么条件，以确定状态如何通过聚合的层次结构汇总。
汇总控制 - 确定哪些活动参与状态汇总以及它们的状态如何相对于其他活动进行加权。
汇总考虑控制 - 提供比汇总控制更精确的状态汇总控制。
目标 - 提供跟踪单个学习目标状态并跨活动共享此状态的方法。目标通常被过度使用，用作控制序列操作的变量。
选择控制 - 提供一种方式，指定仅应交付可用的活动的一个随机子集。
随机化控制 - 打乱要交付的活动顺序。
交付控制 - 允许交付和序列化非交流内容。
导航控制 - 控制LMS应呈现哪些导航UI元素。

序列化操作基于一个“跟踪模型”，该模型与内容和学习管理系统在运行时交换的数据模型元素的一个子集紧密相关。当一个SCO退出时，当前SCO的运行时数据将被转移到序列化跟踪数据中。然后，LMS调用“序列化循环”。序列化循环是一组定义明确的算法，将这些序列化规则应用于当前的跟踪数据集，以确定下一个应交付的活动。这些算法在序列化规范中通过一组伪代码定义，LMS的行为必须模仿这些伪代码。

下面的XML提供了一个简单的排序示例。在这个例子中，模块1是一个包含三个子活动（每个子活动由一个项目表示）的活动。在模块1的排序中，限制条件将此活动限制为两次尝试。汇总规则表示，如果至少有一个子活动已收到满意状态，则模块1应被标记为完成。随机化控制表示，在每次尝试模块1时，应随机选择其三个子项目中的一个进行交付。

``` xml
<item identifier=”ITEM-1″ identifierref=”RES-1″>
<title>Module 1</title>
<item identifier=”ITEM-2″ identifierref=”RES-2″>…
<item identifier=”ITEM-3″ identifierref=”RES-3″>…
<item identifier=”ITEM-4″ identifierref=”RES-4″>…
<imsss:sequencing>
<imsss:limitConditions attemptLimit=”2″/>
<imsss:rollupRules>
<imsss:rollupRule minimumCount=”1″>
<imsss:rollupConditions>
<imsss:rollupCondition condition=”satisfied”/>
</imsss:rollupConditions>
<imsss:rollupAction action=”completed”/>
</imsss:rollupRule>
</imsss:rollupRules>
<imsss:randomizationControls selectCount=”1″ randomizationTiming=”onEachNewAttempt”/>
</imsss:sequencing>
</item>
```