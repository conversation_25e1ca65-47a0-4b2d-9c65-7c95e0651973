package lms

import (
	"encoding/json"
	"errors"
	"tms/model"
	"tmsc/pkg/db"
	"tmsc/pkg/logger"

	"go.uber.org/zap"
)

// ActivityTree 代表内存中针对特定学习者的整个课程结构。
// 它是课程的实时、有状态的表示，结合了清单（manifest）数据和学习者特定的跟踪数据。
type ActivityTree struct {
	Root      *ActivityNode          `json:"root"`
	Manifest  *Manifest              `json:"manifest"`
	LearnerID string                 `json:"learner_id"`
	CMIData   map[string]interface{} `json:"cmi"` // 存储所有 CMI key-value 数据
}

// ActivityNode 代表活动树中的单个节点。
// 它对应于 imsmanifest.xml 中的一个 <item>。
type ActivityNode struct {
	Identifier string          `json:"identifier"`
	Title      string          `json:"title"`
	Visible    bool            `json:"visible"`
	Parent     *ActivityNode   `json:"-"` // 从 JSON 序列化中排除，以防止循环引用
	Children   []*ActivityNode `json:"children"`
	Resource   *Resource       `json:"resource,omitempty"` // 链接到实际的 SCO 资源（如果适用）

	// 排序规则（从清单中复制）
	SequencingRules *Sequencing `json:"sequencing_rules,omitempty"`

	// 跟踪数据（学习者的实时状态）
	AttemptCount     int                          `json:"attempt_count"`
	CompletionStatus string                       `json:"completion_status"` // 完成状态: completed, incomplete, not attempted, unknown
	SuccessStatus    string                       `json:"success_status"`    // 成功状态: passed, failed, unknown
	Objectives       map[string]*TrackedObjective `json:"objectives"`

	// 用于排序和导航（S&N）过程的内部状态标志
	isActive               bool
	isSuspended            bool
	isDisabled             bool // 新增：活动被排序规则禁用
	isHiddenFromChoice     bool // 新增：活动被排序规则从选项中隐藏
	isStopForwardTraversal bool // 新增：活动停止向前遍历
}

// TrackedObjective 保存特定学习目标的实时跟踪数据。
type TrackedObjective struct {
	ObjectiveID       string  `json:"objective_id"`
	Satisfied         bool    `json:"satisfied"`          // 对应于 cmi.objectives.n.success_status
	NormalizedMeasure float64 `json:"normalized_measure"` // 对应于 cmi.objectives.n.score.scaled
}

// BuildActivityTree 根据课程清单为学习者创建一个新的活动树。
//   - 该函数首先根据课程清单（Manifest）构建一个全新的、无状态的活动树。这个过程是递归的，
//     能够正确反映 imsmanifest.xml中定义的层级关系。
//   - 然后，它会尝试从数据库中加载该学习者之前的学习记录 (ScormSessions)。
//   - 如果找到记录，它会反序列化之前保存的活动树状态，并调用 applyTrackingData函数将学习进度（如完成状态、尝试次数等）应用到新创建的树上。
//   - 这种“先从清单构建再应用状态”的逻辑是正确的。它确保了即使课程结构（Manifest）发生变化，系统也能以最新的结构为基础，同时恢复学习者之前的进度。
func (s *ScormService) BuildActivityTree(manifest *Manifest, learnerID string, scoID uint) (*ActivityTree, error) {
	orgIdentifier := manifest.Organizations.Default
	var rootOrganization *Organization
	for i := range manifest.Organizations.Organization {
		if manifest.Organizations.Organization[i].Identifier == orgIdentifier {
			rootOrganization = &manifest.Organizations.Organization[i]
			break
		}
	}

	if rootOrganization == nil {
		return nil, errors.New("default organization not found in manifest")
	}

	tree := &ActivityTree{
		Manifest:  manifest,
		LearnerID: learnerID,
		CMIData:   make(map[string]interface{}),
	}

	// 初始化基本 CMI 值
	tree.CMIData["cmi.completion_status"] = "not attempted"
	tree.CMIData["cmi.success_status"] = "unknown"
	tree.CMIData["cmi.entry"] = "ab-initio"

	tree.Root = &ActivityNode{
		Identifier:      rootOrganization.Identifier,
		Title:           rootOrganization.Title,
		Visible:         true,
		SequencingRules: &rootOrganization.Sequencing,
		Children:        []*ActivityNode{},
		Objectives:      make(map[string]*TrackedObjective),
	}

	for _, item := range rootOrganization.Items {
		childNode, err := s.buildNodeRecursive(item, tree.Root, manifest)
		if err != nil {
			return nil, err
		}
		tree.Root.Children = append(tree.Root.Children, childNode)
	}

	// 从数据库加载学习者之前的跟踪数据，并将其应用于树节点。
	var existingRecord model.ScormSessions
	result := db.DB.Where("learner_id = ? AND sco_id = ?", learnerID, scoID).First(&existingRecord)

	if result.Error == nil {
		// 找到记录，反序列化存储的活动树
		var loadedTree ActivityTree
		if err := json.Unmarshal([]byte(existingRecord.CMI), &loadedTree); err != nil {
			logger.Logger.Error("反序列化现有活动树失败", zap.Error(err))
			// 如果反序列化失败，则继续使用一个全新的树
		} else {
			// 将加载的跟踪数据应用于新构建的树
			s.applyTrackingData(tree.Root, loadedTree.Root)
		}
	}

	return tree, nil
}

// applyTrackingData 递归地将从已加载树（loadedNode）中的跟踪数据（如尝试次数、完成状态、目标等）复制到新构建的树（newNode）的相应节点上。
// 它通过节点的 Identifier 来匹配新旧树中的节点。
func (s *ScormService) applyTrackingData(newNode *ActivityNode, loadedNode *ActivityNode) {
	newNode.AttemptCount = loadedNode.AttemptCount
	newNode.CompletionStatus = loadedNode.CompletionStatus
	newNode.SuccessStatus = loadedNode.SuccessStatus
	newNode.isActive = loadedNode.isActive
	newNode.isSuspended = loadedNode.isSuspended

	// 复制目标
	for objID, loadedObj := range loadedNode.Objectives {
		if _, ok := newNode.Objectives[objID]; !ok {
			newNode.Objectives[objID] = &TrackedObjective{}
		}
		newNode.Objectives[objID].Satisfied = loadedObj.Satisfied
		newNode.Objectives[objID].NormalizedMeasure = loadedObj.NormalizedMeasure
	}

	// 递归地应用于子节点
	for _, newChild := range newNode.Children {
		for _, loadedChild := range loadedNode.Children {
			if newChild.Identifier == loadedChild.Identifier {
				s.applyTrackingData(newChild, loadedChild)
				break
			}
		}
	}
}

// buildNodeRecursive 是一个递归构建活动树的辅助函数。
func (s *ScormService) buildNodeRecursive(item Item, parent *ActivityNode, manifest *Manifest) (*ActivityNode, error) {
	node := &ActivityNode{
		Identifier:      item.Identifier,
		Title:           item.Title,
		Visible:         item.IsVisible,
		Parent:          parent,
		SequencingRules: &item.Sequencing,
		Children:        []*ActivityNode{},
		Objectives:      make(map[string]*TrackedObjective),
		// 初始化跟踪数据
		AttemptCount:     0,
		CompletionStatus: "not attempted",
		SuccessStatus:    "unknown",
	}

	// 查找关联的资源
	if item.IdentifierRef != "" {
		for i, res := range manifest.Resources.Resource {
			if res.Identifier == item.IdentifierRef {
				node.Resource = &manifest.Resources.Resource[i]
				break
			}
		}
	}

	for _, subItem := range item.Items {
		childNode, err := s.buildNodeRecursive(subItem, node, manifest)
		if err != nil {
			return nil, err
		}
		node.Children = append(node.Children, childNode)
	}

	return node, nil
}
