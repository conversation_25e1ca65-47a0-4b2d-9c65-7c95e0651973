package lms

import (
	"encoding/json"
	"errors"
	"fmt"
	"tms/model"
	"tmsc/pkg/logger"

	"go.uber.org/zap"
)

// --- Sequencing and Navigation Engine ---

// Navigate 是 S&N 引擎的主要入口点。
// 它接收一个导航请求（例如 "start", "continue"）和一个 SCORM 会话，
// 并返回下一个要交付给学习者的活动。
func (s *ScormService) Navigate(request string, session *model.ScormSessions) (*ActivityNode, error) {
	// 这是一个整体排序过程 (SSP.1)

	// 从 CMI JSON 中反序列化活动树
	var tree ActivityTree
	if err := json.Unmarshal([]byte(session.CMI), &tree); err != nil {
		logger.Logger.Error("导航时反序列化活动树失败", zap.Error(err))
		return nil, errors.New("无法加载活动树")
	}

	// 1. 终止过程 (Termination Process)
	// 如果当前有活动正在进行，我们需要处理其退出。
	activeNode := findActiveNode(tree.Root)
	if activeNode != nil {
		// "exit" 是一个隐式的终止请求
		if err := s.terminationProcess("exit", activeNode, &tree); err != nil {
			return nil, fmt.Errorf("终止过程出错: %w", err)
		}
	}

	// 2. 选择和流转过程 (Selection and Flow Process)
	// 这个过程找到下一个要交付的活动。
	nextActivity, err := s.selectionAndFlowProcess(request, &tree)
	if err != nil {
		return nil, fmt.Errorf("选择和流转过程出错: %w", err)
	}

	// 3. 交付过程 (Delivery Process)
	// 准备已识别的活动以进行交付。
	if nextActivity != nil {
		// 将活动标记为 active
		nextActivity.isActive = true
		// TODO: 增加交付逻辑 (例如, 设置 cmi.entry)
	}

	// 将可能已更新的活动树序列化回 CMI 字段
	updatedTreeJSON, err := json.Marshal(tree)
	if err != nil {
		logger.Logger.Error("导航时重新序列化活动树失败", zap.Error(err))
		return nil, errors.New("无法保存活动树状态")
	}
	session.CMI = string(updatedTreeJSON)
	// 注意：这里只更新了 session 对象的 CMI 字段，
	// 调用此函数的上层逻辑需要负责将 session 对象保存到数据库。

	// 如果没有活动被选中，课程就结束了。
	return nextActivity, nil
}

// terminationProcess 处理结束活动尝试的逻辑。
// 这实现了 SCORM S&N 手册中的 TP.1, TP.2, 和 TP.3 过程。
func (s *ScormService) terminationProcess(request string, node *ActivityNode, tree *ActivityTree) error {
	logger.Logger.Info("开始终止过程", zap.String("node", node.Identifier), zap.String("request", request))

	// TP.1: 退出动作规则 (Exit Action Rules)
	exitConditions := make([]SequencingRuleInterface, 0, len(node.SequencingRules.SequencingRules.ExitCondition))
	for i := range node.SequencingRules.SequencingRules.ExitCondition {
		exitConditions = append(exitConditions, &node.SequencingRules.SequencingRules.ExitCondition[i])
	}
	s.evaluateSequencingRules(exitConditions, node, tree, "exit")

	// TP.2: 后置条件规则 (Post Condition Rules)
	postConditions := make([]SequencingRuleInterface, 0, len(node.SequencingRules.SequencingRules.PostCondition))
	for i := range node.SequencingRules.SequencingRules.PostCondition {
		postConditions = append(postConditions, &node.SequencingRules.SequencingRules.PostCondition[i])
	}
	s.evaluateSequencingRules(postConditions, node, tree, "post")

	// TP.3: 结束尝试过程 (End Attempt Process)
	node.isActive = false
	node.isSuspended = (request == "suspend") // 如果退出类型是 suspend，则标记为挂起

	// TP.4: 汇总过程 (Rollup Process)
	if node.Parent != nil {
		s.rollupProcess(node, tree) // 从当前节点开始，向上汇总到其父节点
	}

	logger.Logger.Info("终止过程完成", zap.String("node", node.Identifier))
	return nil
}

// evaluateSequencingRules 遍历一组规则并进行评估。
func (s *ScormService) evaluateSequencingRules(rules []SequencingRuleInterface, node *ActivityNode, tree *ActivityTree, ruleType string) {
	for _, rule := range rules {
		if s.evaluateRuleConditions(rule.GetConditions(), node, tree) {
			// 规则条件满足，应用规则动作。
			s.applySequencingRuleAction(rule.GetRuleAction(), node)
			logger.Logger.Info("规则条件满足，动作已应用",
				zap.String("node", node.Identifier),
				zap.String("ruleType", ruleType),
				zap.String("action", rule.GetRuleAction().Action))
		}
	}
}

// applySequencingRuleAction 应用在排序规则中定义的动作。
func (s *ScormService) applySequencingRuleAction(action RuleAction, node *ActivityNode) {
	logger.Logger.Info("应用排序规则动作", zap.String("action", action.Action), zap.String("node", node.Identifier))
	switch action.Action {
	case "satisfied":
		node.SuccessStatus = "passed"
	case "notSatisfied":
		node.SuccessStatus = "failed"
	case "completed":
		node.CompletionStatus = "completed"
	case "notCompleted":
		node.CompletionStatus = "incomplete"
	case "disabled":
		node.isDisabled = true
	case "hiddenFromChoice":
		node.isHiddenFromChoice = true
	case "stopForwardTraversal":
		node.isStopForwardTraversal = true
	default:
		logger.Logger.Warn("不支持的排序规则动作", zap.String("action", action.Action), zap.String("node", node.Identifier))
	}
}

// evaluateRuleConditions 评估单个排序规则的条件。
func (s *ScormService) evaluateRuleConditions(conditions RuleConditions, node *ActivityNode, tree *ActivityTree) bool {
	// `conditionCombination` 属性可以是 "all" 或 "any"。
	if len(conditions.RuleCondition) == 0 {
		return true // 没有条件，总是评估为 true
	}

	if conditions.ConditionCombination == "all" {
		// 所有条件必须为 true
		for _, cond := range conditions.RuleCondition {
			if !s.evaluateSingleCondition(cond, node, tree) {
				return false // 一个 false 条件意味着组合为 false
			}
		}
		return true
	} else { // "any"
		// 至少一个条件必须为 true
		for _, cond := range conditions.RuleCondition {
			if s.evaluateSingleCondition(cond, node, tree) {
				return true // 一个 true 条件意味着组合为 true
			}
		}
		return false
	}
}

// evaluateSingleCondition 评估单个 <ruleCondition>。
func (s *ScormService) evaluateSingleCondition(cond RuleCondition, node *ActivityNode, tree *ActivityTree) bool {
	result := false
	switch cond.Condition {
	case "satisfied":
		result = (node.SuccessStatus == "passed")
	case "completed":
		result = (node.CompletionStatus == "completed")
	case "attempted":
		result = node.AttemptCount > 0
	case "attemptLimitExceeded":
		if node.SequencingRules != nil && node.SequencingRules.LimitConditions.AttemptLimit != nil {
			result = node.AttemptCount >= *node.SequencingRules.LimitConditions.AttemptLimit
		} else {
			result = false // 未定义尝试次数限制
		}
	case "always":
		result = true
	case "never":
		result = false
	// TODO: 实现其他 SCORM 条件
	default:
		logger.Logger.Warn("不支持的规则条件", zap.String("condition", cond.Condition))
		result = false // 不支持的条件默认为 false
	}

	// 如果存在 "not" 操作符，则反转结果
	if cond.Operator == "not" {
		result = !result
	}

	logger.Logger.Debug("评估单个条件",
		zap.String("condition", cond.Condition),
		zap.String("node", node.Identifier),
		zap.Bool("result", result))
	return result
}

func (s *ScormService) rollupProcess(node *ActivityNode, tree *ActivityTree) error {
	if node.Parent == nil {
		return nil // 已经到达树的根节点
	}

	logger.Logger.Info("开始汇总过程", zap.String("from_node", node.Identifier), zap.String("to_node", node.Parent.Identifier))

	parent := node.Parent
	parentStatusChanged := false

	// TODO: 实现完整的汇总过程 (RB.1.1 到 RB.1.5)
	// 这是一个简化的实现，仅用于演示目的。

	// RB.1.2: 汇总完成状态 (Rollup Completion)
	oldCompletionStatus := parent.CompletionStatus
	allChildrenCompleted := true
	for _, child := range parent.Children {
		if !s.isActivityIncludedInRollup(child) {
			continue
		}
		if child.CompletionStatus != "completed" {
			allChildrenCompleted = false
			break
		}
	}

	if allChildrenCompleted {
		parent.CompletionStatus = "completed"
	} else {
		parent.CompletionStatus = "incomplete"
	}
	if oldCompletionStatus != parent.CompletionStatus {
		parentStatusChanged = true
	}

	// 如果父节点状态改变，则递归汇总
	if parentStatusChanged {
		logger.Logger.Info("父节点状态已改变，递归汇总", zap.String("parent", parent.Identifier))
		return s.rollupProcess(parent, tree)
	}

	logger.Logger.Info("汇总过程完成", zap.String("for_node", node.Parent.Identifier))
	return nil
}

// isActivityIncludedInRollup 检查一个活动是否应该被包含在汇总计算中。
func (s *ScormService) isActivityIncludedInRollup(node *ActivityNode) bool {
	// 检查 DeliveryControls 中的 tracked 属性
	if node.SequencingRules != nil {
		return node.SequencingRules.DeliveryControls.Tracked
	}
	// 默认情况下，所有活动都参与汇总
	return true
}

// selectionAndFlowProcess 是导航逻辑的核心。
// 这实现了 SCORM S&N 手册中的 SSP.2 过程。
func (s *ScormService) selectionAndFlowProcess(request string, tree *ActivityTree) (*ActivityNode, error) {
	logger.Logger.Info("开始选择和流转", zap.String("request", request))

	// 添加空指针检查
	if tree == nil {
		return nil, errors.New("活动树为空")
	}
	if tree.Root == nil {
		return nil, errors.New("活动树根节点为空")
	}

	var targetActivity *ActivityNode
	var err error

	switch request {
	case "start":
		targetActivity = s.findNextAvailableActivity(tree.Root, true)
	case "continue":
		activeNode := findActiveNode(tree.Root)
		if activeNode != nil {
			targetActivity = s.findNextActivityInFlow(activeNode)
		} else {
			// 如果没有活动节点，行为类似于 'start'
			targetActivity = s.findNextAvailableActivity(tree.Root, true)
		}
	case "previous":
		activeNode := findActiveNode(tree.Root)
		if activeNode != nil {
			targetActivity = s.findPreviousActivityInFlow(activeNode)
		} else {
			err = errors.New("没有活动节点，无法执行 'previous' 操作")
		}
	case "choice":
		// TODO: 实现选择导航 (SSP.2.2)
		err = errors.New("选择导航尚未实现")
	default:
		err = fmt.Errorf("不支持的导航请求: %s", request)
	}

	if err != nil {
		return nil, err
	}

	// 如果找到了目标活动，需要确保它是可用的。
	if targetActivity != nil {
		if !s.isActivityAvailable(targetActivity, tree) {
			logger.Logger.Warn("选择的活动不可用", zap.String("activity", targetActivity.Identifier))
			// TODO: 应该有更复杂的逻辑来处理这种情况，例如寻找下一个可用的活动
			return nil, errors.New("选择的活动当前不可用")
		}
	}

	logger.Logger.Info("选择和流转完成", zap.String("next_activity", func() string {
		if targetActivity != nil {
			return targetActivity.Identifier
		}
		return "none"
	}()))
	return targetActivity, nil
}

// findNextAvailableActivity 递归查找树中下一个可用的活动。
// fromTheStart: true 表示从头开始搜索，false 表示从当前节点之后搜索。
func (s *ScormService) findNextAvailableActivity(node *ActivityNode, fromTheStart bool) *ActivityNode {
	if node == nil {
		logger.Logger.Warn("findNextAvailableActivity: node is nil")
		return nil
	}
	// 这是一个简化的深度优先遍历
	if s.isActivityAvailable(node, nil) && node.Resource != nil && node.Resource.ScormType == "sco" {
		return node
	}

	for _, child := range node.Children {
		if availableChild := s.findNextAvailableActivity(child, fromTheStart); availableChild != nil {
			return availableChild
		}
	}

	return nil
}

// findNextActivityInFlow 从当前活动节点查找流中的下一个活动。
func (s *ScormService) findNextActivityInFlow(activeNode *ActivityNode) *ActivityNode {
	// 这是一个简化的实现，它只查找下一个同级节点或父节点的下一个同级节点。
	// 真正的 SCORM 流转要复杂得多 (SSP.2.1.1)。

	// 1. 尝试流转到子节点
	if len(activeNode.Children) > 0 {
		// 检查 flow 控制模式
		if activeNode.SequencingRules.ControlMode.Flow {
			return s.findNextAvailableActivity(activeNode.Children[0], true)
		}
	}

	// 2. 尝试流转到下一个同级节点
	if nextSibling := findNextSibling(activeNode); nextSibling != nil {
		return s.findNextAvailableActivity(nextSibling, true)
	}

	// 3. 向上回溯，尝试流转到父节点的下一个同级节点
	current := activeNode
	for current.Parent != nil {
		if nextSibling := findNextSibling(current.Parent); nextSibling != nil {
			return s.findNextAvailableActivity(nextSibling, true)
		}
		current = current.Parent
	}

	return nil
}

// findPreviousActivityInFlow 从当前活动节点查找流中的上一个活动。
func (s *ScormService) findPreviousActivityInFlow(activeNode *ActivityNode) *ActivityNode {
	// TODO: 实现复杂的流转导航规则 (SSP.2.1.2)
	// 这是一个简化的实现，只查找上一个同级节点。
	if activeNode.Parent != nil {
		for i, sibling := range activeNode.Parent.Children {
			if sibling == activeNode && i-1 >= 0 {
				// 返回上一个同级节点中最后一个可用的叶子节点
				return findLastAvailableLeaf(activeNode.Parent.Children[i-1], s)
			}
		}
	}
	return nil
}

// isActivityAvailable 检查一个活动是否可用于交付。
func (s *ScormService) isActivityAvailable(node *ActivityNode, tree *ActivityTree) bool {
	if node == nil {
		logger.Logger.Warn("isActivityAvailable: node is nil")
		return false
	}

	// 这是一个简化的可用性检查。
	// 完整的检查需要评估前置条件规则、限制条件等 (SCORM SN-3-10)。
	if node.isDisabled {
		return false
	}

	// 检查 SequencingRules 是否为空
	if node.SequencingRules == nil {
		logger.Logger.Warn("isActivityAvailable: SequencingRules is nil")
		return true // 如果没有排序规则，默认可用
	}

	// 评估前置条件规则
	preConditions := make([]SequencingRuleInterface, 0, len(node.SequencingRules.SequencingRules.PreConditionRule))
	for i := range node.SequencingRules.SequencingRules.PreConditionRule {
		preConditions = append(preConditions, &node.SequencingRules.SequencingRules.PreConditionRule[i])
	}
	// 注意：这里需要一个机制来处理规则动作，如果一个前置条件规则为真，它可能会禁用该活动。
	// 为简单起见，我们假设如果任何 "disable" 规则为真，活动就不可用。
	for _, rule := range preConditions {
		if s.evaluateRuleConditions(rule.GetConditions(), node, tree) && rule.GetRuleAction().Action == "disabled" {
			return false
		}
	}

	return true
}

// findActiveNode 是一个辅助函数，用于在树中查找当前活动的节点。
func findActiveNode(node *ActivityNode) *ActivityNode {
	if node == nil {
		return nil
	}
	if node.isActive {
		return node
	}
	for _, child := range node.Children {
		if activeChild := findActiveNode(child); activeChild != nil {
			return activeChild
		}
	}
	return nil
}

// findNextSibling 查找一个节点的下一个同级节点。
func findNextSibling(node *ActivityNode) *ActivityNode {
	if node.Parent == nil {
		return nil
	}
	for i, sibling := range node.Parent.Children {
		if sibling.Identifier == node.Identifier && i+1 < len(node.Parent.Children) {
			return node.Parent.Children[i+1]
		}
	}
	return nil
}

// findLastAvailableLeaf 查找给定节点下的最后一个可用叶子节点。
func findLastAvailableLeaf(node *ActivityNode, s *ScormService) *ActivityNode {
	if len(node.Children) > 0 {
		// 从最后一个子节点开始向前搜索
		for i := len(node.Children) - 1; i >= 0; i-- {
			if leaf := findLastAvailableLeaf(node.Children[i], s); leaf != nil {
				return leaf
			}
		}
	}
	// 如果没有子节点，或者子节点都不可用，则检查自身
	if s.isActivityAvailable(node, nil) && node.Resource != nil && node.Resource.ScormType == "sco" {
		return node
	}
	return nil
}
