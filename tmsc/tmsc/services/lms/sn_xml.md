# SCORM 2004 Sequencing Definition XML Binding

SCORM 2004 的 **Sequencing Definition Structure** 详细定义了学习内容如何进行排序、导航和追踪。以下是其主要组成部分及其功能的归纳：

## Sequencing 元素 (`<sequencing>`)

`<sequencing>` 元素是所有排序数据的主容器。如果它是 `sequencingCollection` 的一部分，则必须指定一个 `ID`。

## 控制模式 (`<controlMode>`)

此部分定义了学习者如何与内容互动：

* **choice**：控制学习者是否可以选择要访问的活动。
* **choiceExit**：控制学习者是否可以退出当前活动并选择其他活动。
* **flow**：控制活动是否按照预定义顺序进行。
* **forwardOnly**：限制学习者只能向前浏览，不能向后。
* **useCurrentAttemptObjectiveInfo**：指示在当前尝试中使用目标信息。
* **useCurrentAttemptProgressInfo**：指示在当前尝试中使用进度信息。

## 排序规则 (`<sequencingRules>`)

这部分定义了基于特定条件执行的操作：

### 前置条件规则 (`<preConditionRule>`)

包含在活动开始前评估的规则。
* `<ruleCoditions>`[1]Container for conditions to be evaluated for this rule
  * conditionCombination [0:1]Bound to Condition Combination
  * `<ruleCondition>` [1-many]Container for defined a rule condition
    * **referencedObjective**：引用的目标。
    * **measureThreshold**：测量阈值。
    * **operator**：操作符。
    * **condition**：具体的条件。
* ：定义满足条件时执行的动作。

### 退出条件规则 (`<exitConditionRule>`)

包含在活动退出时评估的规则。其 `<ruleConditions>` 结构与 `<preConditionRule>` 相同。

### 后置条件规则 (`<postConditionRule>`)

包含在活动完成或终止后评估的规则。其 `<ruleConditions>` 状态与 `<preConditionRule>` 相同。

## 限制条件 (`<limitConditions>`)

定义对活动的限制：

* **attemptLimit**：限制学习者尝试活动的次数。
* **attemptAbsoluteDurationLimit**：限制学习者尝试活动的总持续时间（SCORM 2004 中对基于时长的排序支持有限）。

## 汇总规则 (`<rollupRules>`)

定义了子活动的完成或目标状态如何影响父活动的汇总：

* **rollupObjectiveSatisfied**：定义目标满足状态的汇总方式。
* **rollupProgressCompletion**：定义进度完成状态的汇总方式。
* **objectiveMeasureWeight**：定义目标测量对汇总的权重。

### 汇总规则 (`<rollupRule>`)

定义单个汇总规则：

* **childActivitySet**：指定受此规则影响的子活动集。
* **minimumCount**：完成或满足的子活动所需的最小数量。
* **minimumPercent**：完成或满足的子活动所需的最小百分比。
* `<rollupConditions> [1]`包含适用于此汇总规则的条件，可以组合 (`conditionCombination`)。
  * **conditionCombination**：定义特定的汇总条件。
  * `<<rollupCondition> [1:many]>` Container for defining a specific rollup condition
    1. **operator**：操作符。
    2. condition**：具体的条件。
* `<rollupAction> [1]`Container for specifying a rollup action
  * `<action> [1]`Bound to Rollup Actions.


## 目标 (`<objectives>`)

定义了活动的学习目标：

### 主要目标 (`<primaryObjective>`)

定义活动的主要目标（指示目标贡献到汇总）。

* **satisfiedByMeasure**：目标是否通过测量值来满足。
* **objectiveId**：目标的唯一标识符。
* ：目标满足的最小标准化测量值。

### 映射信息 (`<mapInfo>`)

定义了活动目标如何映射到全局目标：

* **targetObjectiveId**：目标全局目标的ID。
* **readSatisfiedStatus**：读取目标满足状态。
* **readNormalizedMeasure**：读取目标标准化测量值。
* **writeSatisfiedStatus**：写入目标满足状态。
* **writeNormalizedMeasure**：写入目标标准化测量值。

### 其他目标 (`<objectives>`)

定义活动的非主要目标（指示目标不贡献到汇总）。其结构与主要目标类似，但 `objectiveId` 为必填项。

## 随机化控制 (`<randomizationControls>`)

控制活动的随机化和选择行为：

* **randomizationTiming**：随机化的时机。
* **selectCount**：选择子活动的数量。
* **reorderChildren**：是否重新排序子活动。
* **selectionTiming**：选择的时机。

## 交付控制 (`<deliveryControls>`)

定义内容交付的控制：

* **tracked**：活动是否被追踪。
* **completionSetByContent**：完成状态是否由内容设置。
* **objectiveSetByContent**：目标是否由内容设置。

## ADL 扩展 (`<adlseq:...>`)

这些是 SCORM 2004 4th Edition 中引入的 ADL 扩展：

### 受限选择考量 (`<adlseq:constrainedChoiceConsiderations>`)

控制受限选择：

* **preventActivation**：是否阻止激活。
* **constrainChoice**：是否限制选择。

### 汇总考量 (`<adlseq:rollupConsiderations>`)

定义汇总的额外考量：

* **requiredForSatisfied**：满足目标所需。
* **requiredForNotSatisfied**：不满足目标所需。
* **requiredForCompleted**：完成所需。
* **requiredForIncomplete**：未完成所需。
* **measureSatisfactionIfActive**：如果活动，是否测量满足度。

### ADL 目标扩展 (`<adlseq:objectives>`)

此容器用于指定 ADL 对目标的扩展。它包含一个或多个 `<objective>` 元素，这些元素必须与上面定义的目标相对应。

* ：定义活动的目标（必须与上面定义的目标相对应）。

  - **objectiveId**：目标的ID。

  定义映射到全局目标的信息 (`<mapInfo>`)

  * **targetObjectiveId**：目标全局目标的ID。
  * **readRawScore**：读取原始分数。
  * **readMinScore**：读取最小分数。
  * **readMaxScore**：读取最大分数。
  * **readCompletionStatus**：读取完成状态。
  * **readProgressMeasure**：读取进度测量。
  * **writeRawScore**：写入原始分数。
  * **writeMinScore**：写入最小分数。
  * **writeMaxScore**：写入最大分数。
  * **writeCompletionStatus**：写入完成状态。
  * **writeProgressMeasure**：写入进度测量。




下面是一个 SCORM 2004 Sequencing Definition XML Binding 的完整示例。这个例子涵盖了常见的排序控制、规则、目标和汇总设置。请注意，为了简洁和清晰，某些可选属性或更复杂的规则可能没有包含在内，但这个例子展示了核心结构和功能。

-----

## SCORM 2004 Sequencing Definition XML 示例

这个 XML 片段通常会嵌套在 SCORM 清单文件 (imsmanifest.xml) 中的 `<item>` 元素内部，位于 `<imsss:sequencing>` 标签下。

```xml
<?xml version="1.0" encoding="UTF-8"?>
<imsmanifest identifier="MANIFEST-ABCD-12345" version="1.0"
             xmlns="http://www.imsglobal.org/xsd/imscp_v1p1"
             xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
             xmlns:adlcp="http://www.adlnet.org/xsd/adlcp_v1p3"
             xmlns:adlseq="http://www.adlnet.org/xsd/adlseq_v1p3"
             xmlns:imsss="http://www.imsglobal.org/xsd/imsss"
             xsi:schemaLocation="http://www.imsglobal.org/xsd/imscp_v1p1 imscp_v1p1.xsd
                                 http://www.adlnet.org/xsd/adlcp_v1p3 adlcp_v1p3.xsd
                                 http://www.adlnet.org/xsd/adlseq_v1p3 adlseq_v1p3.xsd
                                 http://www.imsglobal.org/xsd/imsss imsss_v1p0sequencing.xsd">
  <metadata>
    <schema>ADL SCORM</schema>
    <schemaversion>2004 4th Edition</schemaversion>
  </metadata>
  <organizations default="ORG-001">
    <organization identifier="ORG-001">
      <title>SCORM Sequencing Example Course</title>
      <item identifier="ITEM-MODULE-1" identifierref="RES-MODULE-1" isvisible="true">
        <title>Module 1: Introduction to SCORM</title>
        <imsss:sequencing>
          <imsss:controlMode choice="true" flow="true" forwardOnly="true"/>
          <imsss:sequencingRules>
            <imsss:preConditionRule>
              <imsss:ruleConditions conditionCombination="all">
                <imsss:ruleCondition condition="completed" operator="not"/>
              </imsss:ruleConditions>
              <imsss:ruleAction action="disabled"/>
            </imsss:preConditionRule>
            <imsss:exitConditionRule>
              <imsss:ruleConditions>
                <imsss:ruleCondition condition="completed"/>
              </imsss:ruleConditions>
              <imsss:ruleAction action="exit"/>
            </imsss:exitConditionRule>
            <imsss:postConditionRule>
              <imsss:ruleConditions>
                <imsss:ruleCondition condition="satisfied" operator="not"/>
              </imsss:ruleConditions>
              <imsss:ruleAction action="setComplete"/>
            </imsss:postConditionRule>
          </imsss:sequencingRules>

          <imsss:limitConditions attemptLimit="3"/>

          <imsss:rollupRules rollupObjectiveSatisfied="true" rollupProgressCompletion="true" objectiveMeasureWeight="0.8">
            <imsss:rollupRule minimumPercent="0.5">
              <imsss:rollupConditions>
                <imsss:rollupCondition condition="completed"/>
              </imsss:rollupConditions>
              <imsss:rollupAction action="satisfied"/>
            </imsss:rollupRule>
          </imsss:rollupRules>

          <imsss:objectives>
            <imsss:primaryObjective satisfiedByMeasure="true">
              <imsss:minNormalizedMeasure>0.7</imsss:minNormalizedMeasure>
              <imsss:mapInfo targetObjectiveId="global_course_completion" readSatisfiedStatus="true" writeSatisfiedStatus="true"/>
            </imsss:primaryObjective>
            <imsss:objective objectiveId="quiz_score" satisfiedByMeasure="true">
              <imsss:minNormalizedMeasure>0.6</imsss:minNormalizedMeasure>
              <imsss:mapInfo targetObjectiveId="global_quiz_average" readNormalizedMeasure="true" writeNormalizedMeasure="true"/>
            </imsss:objective>
          </imsss:objectives>

          <imsss:randomizationControls selectCount="1" reorderChildren="true" randomizationTiming="never"/>

          <imsss:deliveryControls tracked="true" completionSetByContent="true" objectiveSetByContent="true"/>

          <adlseq:constrainedChoiceConsiderations preventActivation="true" constrainChoice="true"/>
          <adlseq:rollupConsiderations requiredForSatisfied="true" requiredForCompleted="true" measureSatisfactionIfActive="true"/>
          
          <adlseq:objectives>
            <adlseq:objective objectiveId="quiz_score">
              <adlseq:mapInfo targetObjectiveId="global_quiz_average"
                              readRawScore="true" writeRawScore="true"
                              readMinScore="true" writeMinScore="true"
                              readMaxScore="true" writeMaxScore="true"
                              readCompletionStatus="true" writeCompletionStatus="true"
                              readProgressMeasure="true" writeProgressMeasure="true"/>
            </adlseq:objective>
          </adlseq:objectives>

        </imsss:sequencing>
      </item>
      </organization>
  </organizations>
  <resources>
    <resource identifier="RES-MODULE-1" type="webcontent" adlcp:scormType="sco" href="module1/index.html">
      <file href="module1/index.html"/>
      </resource>
    </resources>
</imsmanifest>
```

-----

### 示例解释：

1.  **`<imsss:controlMode>`**:

      * `choice="true"`：允许学习者自由选择活动（如果其父活动允许）。
      * `flow="true"`：允许学习者按预定顺序流式访问活动。
      * `forwardOnly="true"`：限制学习者只能向前导航，不能返回。

2.  **`<imsss:sequencingRules>`**:

      * **`<imsss:preConditionRule>`**: 如果活动未完成 (`condition="completed" operator="not"`)，则禁用 (`action="disabled"`) 该活动，防止学习者进入。
      * **`<imsss:exitConditionRule>`**: 如果活动已完成 (`condition="completed"`)，则在尝试再次进入时会触发退出操作 (`action="exit"`)。
      * **`<imsss:postConditionRule>`**: 如果活动未满足其目标 (`condition="satisfied" operator="not"`)，则将其完成状态设置为完成 (`action="setComplete"`)。

3.  **`<imsss:limitConditions>`**:

      * `attemptLimit="3"`：学习者最多只能尝试此活动 3 次。

4.  **`<imsss:rollupRules>`**:

      * `rollupObjectiveSatisfied="true"` 和 `rollupProgressCompletion="true"`：指示父活动的汇总将考虑子活动的目标满足状态和进度完成状态。
      * `objectiveMeasureWeight="0.8"`：在汇总时，目标测量的权重为 0.8。
      * **`<imsss:rollupRule>`**: 如果此活动至少有 50% 的子活动已完成 (`minimumPercent="0.5" condition="completed"`)，则此活动的目标被视为已满足 (`action="satisfied"`)。

5.  **`<imsss:objectives>`**:

      * **`<imsss:primaryObjective>`**: 定义了活动的主要目标。
          * `satisfiedByMeasure="true"`：目标的满足度由测量值决定。
          * `<imsss:minNormalizedMeasure>0.7</imsss:minNormalizedMeasure>`：标准化测量值至少需要达到 0.7 才能满足目标。
          * `<imsss:mapInfo targetObjectiveId="global_course_completion" .../>`：将此活动的满足状态映射到名为 "global\_course\_completion" 的全局目标。
      * **`<imsss:objective>`**: 定义了一个名为 "quiz\_score" 的非主要目标，并将其测量值映射到 "global\_quiz\_average"。

6.  **`<imsss:randomizationControls>`**:

      * `selectCount="1"`：如果此活动有多个子活动，系统会随机选择其中一个供学习者访问。
      * `reorderChildren="true"`：如果此活动有多个子活动，它们的顺序可能会被随机打乱。
      * `randomizationTiming="never"`：表示随机化只发生一次，而不是每次都发生。

7.  **`<imsss:deliveryControls>`**:

      * `tracked="true"`：此活动的进度和状态会被 LMS 追踪。
      * `completionSetByContent="true"`：此活动的完成状态由内容本身（通过 SCORM API）设置。
      * `objectiveSetByContent="true"`：此活动的目标状态由内容本身设置。

8.  **`<adlseq:constrainedChoiceConsiderations>`**:

      * `preventActivation="true"`：在某些情况下，可以阻止活动被激活。
      * `constrainChoice="true"`：进一步限制学习者的选择自由。

9.  **`<adlseq:rollupConsiderations>`**:

      * `requiredForSatisfied="true"`：表示要使父活动满足，此活动必须满足。
      * `requiredForCompleted="true"`：表示要使父活动完成，此活动必须完成。
      * `measureSatisfactionIfActive="true"`：如果此活动处于活动状态，则测量其满足度。

10. **`<adlseq:objectives>`**:

      * 此扩展允许更细粒度地控制目标信息的读写，例如读取和写入原始分数 (`readRawScore`, `writeRawScore`)、最小/最大分数、完成状态和进度测量。
