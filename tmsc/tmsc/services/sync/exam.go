package sync

import (
	"encoding/json"
	"tms/model"
	"tmsc/pkg/db"
	"tmsc/pkg/logger"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

func processExamSync(event model.SyncRecords) error {
	logger.Logger.Debug("Processing exam sync event", zap.Any("event", event))
	var data model.UserExamEntry
	if err := json.Unmarshal([]byte(event.SyncData), &data); err != nil {
		return err
	}

	if data.Exam != nil {
		if err := handleExamData(data.Exam); err != nil {
			return err
		}
	}

	if data.ExamProgress != nil {
		if err := handleExamProgressData(data.ExamProgress); err != nil {
			return err
		}
	}

	if data.ExamAnswers != nil {
		if err := handleExamAnswersData(data.ExamAnswers); err != nil {
			return err
		}
	}

	return nil
}

func handleExamData(exam *model.Exams) error {
	// 处理考试数据
	logger.Logger.Debug("Handling exam data", zap.Any("exam", exam))
	if err := db.DB.Model(&model.Exams{}).
		Where("id = ?", exam.ID).
		Updates(map[string]interface{}{
			"status": exam.Status,
		}).Error; err != nil {
		logger.Logger.Error("Failed to handle exam data", zap.Error(err))
		return err
	}

	return nil
}

func handleExamProgressData(progress *model.ExamProgress) error {
	// 处理考试进度数据
	logger.Logger.Debug("Handling exam progress data", zap.Any("progress", progress))
	if err := db.DB.Model(&model.ExamProgress{}).
		Where("id = ?", progress.ID).
		Save(progress).Error; err != nil {
		logger.Logger.Error("Failed to handle exam progress data", zap.Error(err))
		return err
	}

	return nil
}

func handleExamAnswersData(answers []model.ExamAnswer) error {
	// 处理考试答案数据
	logger.Logger.Debug("Handling exam answers data", zap.Any("answers", answers))
	err := db.DB.Transaction(func(tx *gorm.DB) error {
		if err := tx.Model(&model.ExamAnswer{}).
			Where("exam_progress_id = ?", answers[0].ExamProgressID).
			Delete(&model.ExamAnswer{}).Error; err != nil {
			return err
		}

		if err := tx.Model(&model.ExamAnswer{}).
			Create(&answers).Error; err != nil {
			return err
		}

		return nil
	})
	if err != nil {
		logger.Logger.Error("Failed to handle exam answers data", zap.Error(err))
		return err
	}

	return nil
}
