package sync

import (
	"encoding/json"
	"fmt"
	"tms/model"
	"tmsc/pkg/cache"
	"tmsc/services/users"
)

// processUpdateUserInfo handles user info updates
func processUpdateUserInfo(event model.SyncRecords) error {
	var updateObj users.UpdateUserParam
	if err := json.Unmarshal([]byte(event.SyncData), &updateObj); err != nil {
		return fmt.Errorf("failed to unmarshal update object: %w", err)
	}

	if err := users.UpdateUserInfo(updateObj); err != nil {
		return fmt.Errorf("failed to update user info: %w", err)
	}

	return nil
}

// processDeleteUserInfo handles user deletion
func processDeleteUserInfo(event model.SyncRecords) error {
	if err := users.DeleteLocalUser(event.UserID); err != nil {
		return fmt.Errorf("failed to delete user: %w", err)
	}

	// Clear user token from cache
	key := fmt.Sprintf("%d-token", event.UserID)
	cache.FreeCache.Del([]byte(key))

	return nil
}
