package sync

import (
	"tms/model"
	"tmsc/pkg/db"
	"tmsc/pkg/logger"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

func SaveToDB(e model.SyncRecords) error {
	logger.Logger.Info("Processing event", zap.Any("event", e))

	var event model.SyncRecords
	result := db.DB.Where("event_id = ?", e.EventID).First(&event)
	if result.Error == nil {
		logger.Logger.Info("Event already exists, skipping", zap.Any("event", e))
		return nil
	}
	if result.Error != gorm.ErrRecordNotFound {
		return result.Error
	}

	db.DB.Create(&model.SyncRecords{
		EventID:    e.EventID,
		UserID:     e.UserID,
		SyncType:   e.SyncType,
		SyncObject: e.SyncObject,
		SyncData:   e.SyncData,
		IsSynced:   e.IsSynced,
		CreatedAt:  e.CreatedAt,
		UpdatedAt:  e.UpdatedAt,
	})

	return nil
}

func FindClentSyncEvent(userID int64) []model.SyncRecords {
	var events []model.SyncRecords
	db.DB.Where("user_id = ? and sync_object = ? and is_synced = ?", userID, ClientSyncType, false).Find(&events)

	return events
}

func FindServerSyncEvent(userID int64) []model.SyncRecords {
	var events []model.SyncRecords
	db.DB.Where("user_id = ? and sync_object = ? and is_synced = ?", userID, ServerSyncType, false).Find(&events)

	return events
}

func MarkEventAsSynced(eventID string) error {
	return db.DB.Model(&model.SyncRecords{}).Where("event_id = ?", eventID).Update("is_synced", true).Error
}

// LocalEventProcessor defines the interface for processing local events
type LocalEventProcessor interface {
	Process(event model.SyncRecords) error
}

// LocalEventProcessorFunc is a function type that implements LocalEventProcessor
type LocalEventProcessorFunc func(event model.SyncRecords) error

func (f LocalEventProcessorFunc) Process(event model.SyncRecords) error {
	return f(event)
}

// LocalEventProcessors maps sync types to their processors
var LocalEventProcessors = map[string]LocalEventProcessor{
	UpdateUserInfoSyncType: LocalEventProcessorFunc(processUpdateUserInfo),
	DeleteUserInfoSyncType: LocalEventProcessorFunc(processDeleteUserInfo),
	ExamSyncType:           LocalEventProcessorFunc(processExamSync),
}

// ProcessLocalEvent processes local events using registered processors
func ProcessLocalEvent(es []model.SyncRecords) (map[string]struct{}, error) {
	eventMap := make(map[string]struct{})

	for _, e := range es {
		processor, exists := LocalEventProcessors[e.SyncType]
		if !exists {
			logger.Logger.Warn("No processor found for sync type", zap.String("syncType", e.SyncType))
			continue
		}

		if err := processor.Process(e); err != nil {
			logger.Logger.Error("Failed to process event",
				zap.String("eventID", e.EventID),
				zap.String("syncType", e.SyncType),
				zap.Error(err))
			continue
		}

		if err := MarkEventAsSynced(e.EventID); err != nil {
			logger.Logger.Error("Failed to mark event as synced", zap.Error(err))
			continue
		}

		eventMap[e.EventID] = struct{}{}
	}

	return eventMap, nil
}
