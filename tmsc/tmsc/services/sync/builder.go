package sync

import (
	"encoding/json"
	"errors"
	"time"
	"tms/model"
)

// EventSyncBuilder 同步数据构建器
type EventSyncBuilder struct {
	syncUser   int64
	operator   int64  // 操作人 （谁触发的事件）
	syncObject string // client or server
	eventType  string // 事件类型
	syncData   any
}

// NewEventSyncBuilder 创建同步构建器
func NewEventSyncBuilder(syncUser int64) *EventSyncBuilder {
	return &EventSyncBuilder{
		syncUser: syncUser,
	}
}

func (s *EventSyncBuilder) Operator(operator int64) *EventSyncBuilder {
	s.operator = operator
	return s
}

func (s *EventSyncBuilder) SyncServer() *EventSyncBuilder {
	s.syncObject = ServerSyncType
	return s
}

func (s *EventSyncBuilder) SyncClient() *EventSyncBuilder {
	s.syncObject = ClientSyncType
	return s
}

func (s *EventSyncBuilder) EventType(eventType string) *EventSyncBuilder {
	s.eventType = eventType
	return s
}

// Data 设置同步数据
func (s *EventSyncBuilder) Data(data interface{}) *EventSyncBuilder {
	s.syncData = data
	return s
}

// Save 保存同步记录
func (s *EventSyncBuilder) Save() error {
	if err := s.check(); err != nil {
		return err
	}

	var syncData string
	if s.syncData != nil {
		data, err := json.Marshal(s.syncData)
		if err != nil {
			return err
		}
		syncData = string(data)
	}

	eventID := model.GenerateEventID(s.operator, s.eventType, s.syncObject, syncData, time.Now())
	// return HandleWriteSync(s.eventType, s.syncObject, syncData, s.syncUser, eventID)
	return SaveToDB(model.SyncRecords{
		EventID:    eventID,
		UserID:     s.syncUser,
		SyncType:   s.eventType,
		SyncObject: s.syncObject,
		SyncData:   syncData,
		IsSynced:   false,
		CreatedAt:  time.Now().Unix(),
		UpdatedAt:  time.Now().Unix(),
	})
}

func (s *EventSyncBuilder) check() error {
	if s.syncUser == 0 {
		return errors.New("sync user is required")
	}
	if s.operator == 0 {
		return errors.New("operator is required")
	}
	if s.syncObject == "" {
		return errors.New("sync object is required")
	}
	if s.eventType != UpdateUserInfoSyncType &&
		s.eventType != DeleteUserInfoSyncType &&
		s.eventType != LearningSyncType &&
		s.eventType != ExamSyncType &&
		s.eventType != StudySyncType {
		return nil
	}
	return nil
}
