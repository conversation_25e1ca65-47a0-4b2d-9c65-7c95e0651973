package adapter

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"time"
	"tmsc/libs"
	"tms/model"
)

var longPollClient = &http.Client{
	Timeout: 60 * time.Second,
}

func (a Adapter) PushEvent(c context.Context, reqBody model.SyncRecords, token string) error {
	remoteURL := a.buildRequestURL(PushEventAPI)
	method := "POST"

	signatureHeaders, err := a.getSigner().Sign(method, remoteURL, reqBody)
	if err != nil {
		return fmt.Errorf("error signing request: %w", err)
	}

	if token == "" {
		return errors.New("err not found token")
	}

	signatureHeaders["Authorization"] = token

	err = libs.Post(remoteURL, signatureHeaders, reqBody, nil, longPollClient)
	if err != nil {
		return fmt.Errorf("error making remote request: %w", err)
	}

	return nil
}

func (a Adapter) FetchEvent(c context.Context, token string) ([]model.SyncRecords, error) {
	remoteURL := a.buildRequestURL(FetchEventAPI)
	method := "GET"

	signatureHeaders, err := a.getSigner().Sign(method, remoteURL, nil)
	if err != nil {
		return nil, fmt.Errorf("error signing request: %w", err)
	}

	if token == "" {
		return nil, errors.New("err invalid or empty token")
	}

	signatureHeaders["Authorization"] = token

	var resp []model.SyncRecords
	err = libs.Get(remoteURL, signatureHeaders, &resp, longPollClient)
	if err != nil {
		return nil, fmt.Errorf("error making remote request: %w", err)
	}

	return resp, nil
}
