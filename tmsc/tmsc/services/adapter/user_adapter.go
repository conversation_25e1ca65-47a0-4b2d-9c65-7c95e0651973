package adapter

import (
	"fmt"
	"tms/model"
	"tmsc/libs"
	"tmsc/services/system"

	"github.com/gin-gonic/gin"
)

type UserLoginResp struct {
	User            model.Users             `json:"user"`
	Menus           []*system.MenuNode      `json:"menus"`
	Roles           []*model.Roles          `json:"roles"`
	RolePermissions []model.RolePermissions `json:"role_permissions,omitempty"`
	Token           string                  `json:"token"`
	Class           *model.Class            `json:"class"`
	Major           *model.Majors           `json:"major"`
	UserCode        string                  `json:"user_code"`
	SysCode         string                  `json:"sys_code"`
	UserRef         *model.StudentMap       `json:"user_ref,omitempty"`
}

type UserLoginReq struct {
	Account  string `json:"account" binding:"required"`
	Password string `json:"password" binding:"required"`
	UserCode string `json:"user_code"`
}

// UserLogin demonstrates how to make a signed API call to a remote server.
// This function simulates fetching a user profile.
func (a Adapter) UserLogin(req UserLoginReq) (*UserLoginResp, error) {
	remoteURL := a.buildRequestURL(AuthLoginAPI)
	method := "POST"

	// The body of the request.
	reqBody := gin.H{
		"account":   req.Account,
		"password":  req.Password,
		"user_code": req.UserCode,
	}

	signatureHeaders, err := a.getSigner().Sign(method, remoteURL, reqBody)
	if err != nil {
		return nil, fmt.Errorf("error signing request: %w", err)
	}

	// Optional: If the endpoint requires user authentication, add the JWT here.
	// signatureHeaders["Authorization"] = "Bearer your_jwt_token"

	var userProfileResponse UserLoginResp
	fmt.Println("Sending signed request to:", remoteURL)
	err = libs.Post(remoteURL, signatureHeaders, reqBody, &userProfileResponse)
	if err != nil {
		return nil, fmt.Errorf("error making remote request: %w", err)
	}

	fmt.Printf("Successfully fetched remote user profile: %+v\n", userProfileResponse)

	return &userProfileResponse, nil
}
