package adapter

import (
	"strings"
	"sync"
	"tmsc/libs"
	"tmsc/pkg/config"
)

var (
	once            sync.Once
	adapterInstance *Adapter
)

type Adapter struct {
	signer *libs.Signer
}

func NewAdapter() *Adapter {
	once.Do(func() {
		adapterInstance = &Adapter{
			signer: libs.NewSigner(config.Config.Remote.AppKey, config.Config.Remote.AppSecret),
		}
	})
	return adapterInstance
}

func GetAdapterInstance() *Adapter {
	if adapterInstance == nil {
		return NewAdapter()
	}
	return adapterInstance
}

func (a *Adapter) getSigner() *libs.Signer {
	return a.signer
}

// 构建请求地址
func (a Adapter) buildRequestURL(endpoint string) string {
	var baseURL string
	if config.Config.Remote.URL != "" {
		baseURL = config.Config.Remote.URL
	}

	if !strings.HasSuffix(baseURL, "/") {
		baseURL += "/"
	}

	return baseURL + endpoint
}
