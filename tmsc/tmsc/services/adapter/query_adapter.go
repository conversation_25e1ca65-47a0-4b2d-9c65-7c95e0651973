package adapter

import (
	"context"
	"errors"
	"fmt"
	"tmsc/libs"
	"tmsc/pkg/logger"

	"go.uber.org/zap"
)

type RPCQueryParam struct {
	Method string         `json:"method"`
	Params map[string]any `json:"params"`
}

func (a Adapter) QueryData(c context.Context, token string, reqBody RPCQueryParam) ([]byte, error) {
	remoteURL := a.buildRequestURL(QueryDataAPI)
	method := "POST"

	signatureHeaders, err := a.getSigner().Sign(method, remoteURL, reqBody)
	if err != nil {
		return nil, fmt.Errorf("error signing request: %w", err)
	}

	if token == "" {
		return nil, errors.New("err not found token")
	}

	logger.Logger.Debug("[QueryData]", zap.Any("param", reqBody))

	signatureHeaders["Authorization"] = token

	var resp []byte
	err = libs.Post(remoteURL, signatureHeaders, reqBody, &resp, longPollClient)
	if err != nil {
		return nil, fmt.<PERSON><PERSON><PERSON>("error making remote request: %w", err)
	}

	return resp, nil
}
