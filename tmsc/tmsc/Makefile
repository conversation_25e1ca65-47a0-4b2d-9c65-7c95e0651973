.PHONY: all windows linux mac clean

APP_NAME := tmsc
MAIN_GO := main.go

all: windows linux mac

windows:
	CGO_ENABLED=1 GOOS=windows GOARCH=amd64 CC=x86_64-w64-mingw32-gcc CXX=x86_64-w64-mingw32-g++ go build -tags sqlite_modernc -o $(APP_NAME)_windows_amd64.exe $(MAIN_GO)

linux:
	CGO_ENABLED=1 GOOS=linux GOARCH=amd64 go build -tags sqlite_modernc -o $(APP_NAME)_linux_amd64 $(MAIN_GO)

mac:
	CGO_ENABLED=1 GOOS=darwin GOARCH=amd64 go build -tags sqlite_modernc -o $(APP_NAME)_darwin_amd64 $(MAIN_GO)

run:
	go run -tags sqlite_modernc $(MAIN_GO)

clean:
	rm -f $(APP_NAME)_windows_amd64.exe $(APP_NAME)_linux_amd64 $(APP_NAME)_darwin_amd64