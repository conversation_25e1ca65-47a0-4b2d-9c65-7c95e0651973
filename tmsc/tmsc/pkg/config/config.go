package config

import (
	"encoding/json"
	"os"
)

// Config 是全局的系统配置实例。
var Config SystemEnv

// SystemEnv 结构体定义了系统、日志和缓存的配置信息。
type SystemEnv struct {
	System System       `json:"system"`
	Log    Log          `json:"log"`
	Cache  CacheConfig  `json:"cache"`
	Remote RemoteServer `json:"remote"`
}

// System 结构体定义了系统相关的配置。
type System struct {
	Debug         bool     `json:"debug"`
	Port          int      `json:"port"`
	Host          string   `json:"host"`
	Scheme        string   `json:"scheme"`
	SessionType   string   `json:"sessionType"`
	SessionSecret string   `json:"sessionSecret"`
	IpAccess      []string `json:"ipAccess"`
}

// CacheConfig 结构体定义了缓存相关的配置。
type CacheConfig struct {
	Type    string `json:"type"`    // 缓存类型: memory/file
	FileDir string `json:"fileDir"` // 文件缓存目录(仅文件缓存需要)
}

// Log 结构体定义了日志相关的配置。
type Log struct {
	WriteFile  bool   `json:"writeFile"`
	Path       string `json:"path"`
	Filename   string `json:"filename"`
	MaxSize    int    `json:"maxSize"`
	MaxBackups int    `json:"maxBackups"`
	MaxAge     int    `json:"maxAge"`
}

// RemoteServer 结构体定义了远程服务器的配置。
type RemoteServer struct {
	URL       string `json:"url"`
	AppKey    string `json:"appKey"`
	AppSecret string `json:"appSecret"`
}

// LoginConf 是全局的登录配置实例。
var LoginConf struct {
	Email struct {
		From     string `json:"from"`
		Username string `json:"username"`
		Password string `json:"password"`
		Host     string `json:"host"`
		Port     int    `json:"port"`
		IsSsl    bool   `json:"isSsl"`
	} `json:"email"`
}

// LoadSystemConfig 加载系统和登录配置
func LoadSystemConfig() error {
	data, err := os.ReadFile("config/system.json")
	if err != nil {
		return err
	}
	err = json.Unmarshal(data, &Config)
	if err != nil {
		return err
	}

	return nil
}
