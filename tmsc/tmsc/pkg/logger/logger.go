package logger

import (
	"os"
	"path/filepath"
	"tmsc/pkg/config"

	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"gopkg.in/natefinch/lumberjack.v2"
)

// 全局 logger 对象
var Logger *zap.Logger

// InitLogger 初始化日志系统
func InitLogger() error {
	// 获取日志核心
	core := getZapCore()
	// 创建一个 zap.Logger
	Logger = zap.New(core, zap.AddCaller()) // zap.AddCaller() 添加调用者信息，以便于追踪错误位置
	// 替换 zap 的全局 logger
	zap.ReplaceGlobals(Logger)
	return nil
}

// getZapCore 获取 zapcore.Core
func getZapCore() zapcore.Core {
	// 初始化一个空的 zapcore.Core 列表
	var cores []zapcore.Core

	// 添加控制台输出核心
	cores = append(cores, getConsoleCore())

	// 如果配置中启用了文件写入，则添加文件输出核心
	if config.Config.Log.WriteFile {
		cores = append(cores, getFileCore())
	}

	// 使用 zapcore.NewTee 将多个核心合并为一个
	return zapcore.NewTee(cores...)
}

// getEncoderConfig 获取编码器配置
func getEncoderConfig() zapcore.EncoderConfig {
	return zapcore.EncoderConfig{
		MessageKey:     "message",
		LevelKey:       "level",
		TimeKey:        "time",
		NameKey:        "logger",
		CallerKey:      "caller",
		StacktraceKey:  "stacktrace",
		LineEnding:     zapcore.DefaultLineEnding,
		EncodeLevel:    zapcore.LowercaseLevelEncoder,
		EncodeTime:     zapcore.ISO8601TimeEncoder,
		EncodeDuration: zapcore.SecondsDurationEncoder,
		EncodeCaller:   zapcore.ShortCallerEncoder,
	}
}

// getConsoleCore 获取控制台输出核心
func getConsoleCore() zapcore.Core {
	// 使用 zapcore.NewConsoleEncoder 创建一个编码器
	encoder := zapcore.NewConsoleEncoder(getEncoderConfig())
	// 创建一个写入器，将日志输出到标准输出
	writer := zapcore.AddSync(os.Stdout)
	// 设置日志级别
	level := zapcore.DebugLevel
	// 创建并返回一个 zapcore.Core
	return zapcore.NewCore(encoder, writer, level)
}

// getFileCore 获取文件输出核心
func getFileCore() zapcore.Core {
	// 使用 zapcore.NewJSONEncoder 创建一个编码器
	encoder := zapcore.NewJSONEncoder(getEncoderConfig())
	// 配置 lumberjack 进行日志切割和归档
	logFilePath := filepath.Join("data", config.Config.Log.Path, config.Config.Log.Filename)
	lumberjackLogger := &lumberjack.Logger{
		Filename:   logFilePath,
		MaxSize:    config.Config.Log.MaxSize,
		MaxBackups: config.Config.Log.MaxBackups,
		MaxAge:     config.Config.Log.MaxAge,
		Compress:   false, // 是否压缩
	}
	// 创建一个写入器
	writer := zapcore.AddSync(lumberjackLogger)
	// 设置日志级别
	level := zapcore.InfoLevel
	// 创建并返回一个 zapcore.Core
	return zapcore.NewCore(encoder, writer, level)
}