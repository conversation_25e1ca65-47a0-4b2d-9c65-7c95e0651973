package casbin

import (
	"log"
	"sync"
	"tmsc/pkg/db"

	"github.com/casbin/casbin/v2"
	gormadapter "github.com/casbin/gorm-adapter/v3"
	"gorm.io/gorm"
)

var (
	enforcer *casbin.Enforcer
	once     sync.Once
)

// InitCasbin 初始化 Casbin Enforcer
func InitCasbin() {
	once.Do(func() {
		// 使用 Gorm 适配器
		var casbinDB *gorm.DB
		if db.DB != nil {
			casbinDB = db.DB
		} else {
			log.Fatalf("No database enabled for Casbin")
		}

		if casbinDB == nil {
			log.Fatalf("Casbin database connection is nil")
		}
		adapter, err := gormadapter.NewAdapterByDB(casbinDB)
		if err != nil {
			log.Fatalf("Failed to initialize Casbin Gorm adapter: %v", err)
		}

		// 从配置文件和适配器创建 Enforcer
		enforcer, err = casbin.NewEnforcer("config/rbac_model.conf", adapter)
		if err != nil {
			log.Fatalf("Failed to create Casbin enforcer: %v", err)
		}

		// 从 DB 加载策略
		err = enforcer.LoadPolicy()
		if err != nil {
			log.Fatalf("Failed to load Casbin policy: %v", err)
		}

		// 启用日志
		enforcer.EnableLog(true)
	})
}

// GetEnforcer 获取 Casbin Enforcer 实例
func GetEnforcer() *casbin.Enforcer {
	if enforcer == nil {
		InitCasbin()
	}
	return enforcer
}
