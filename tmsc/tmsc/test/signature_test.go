package libs_test

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"net/url"
	"sort"
	"strings"
	"testing"
	"tmsc/libs"
)

const (
	TestAppKey    = "test_app_key"
	TestAppSecret = "test_app_secret_that_is_very_long_and_secure"
)

// TestSigner_Sign is the main test function for the Signer.
func TestSigner_Sign(t *testing.T) {
	signer := libs.NewSigner(TestAppKey, TestAppSecret)

	// Sub-test for a POST request with a body and query parameters.
	t.Run("POST with body and query", func(t *testing.T) {
		// 1. Define request parameters.
		method := "POST"
		requestURL := "https://api.example.com/v1/users?name=gopher&role=admin"
		body := struct {
			Action string `json:"action"`
			ID     int    `json:"id"`
		}{
			Action: "create",
			ID:     12345,
		}

		// 2. Generate the signature headers.
		headers, err := signer.Sign(method, requestURL, body)
		if err != nil {
			t.Fatalf("Sign() error = %v", err)
		}

		// 3. Manually reconstruct the signature for verification.
		u, _ := url.Parse(requestURL)
		bodyBytes, _ := json.Marshal(body)

		// Manually sort query parameters.
		q := u.Query()
		keys := make([]string, 0, len(q))
		for k := range q {
			keys = append(keys, k)
		}
		sort.Strings(keys)
		var sortedParts []string
		for _, k := range keys {
			for _, v := range q[k] {
				sortedParts = append(sortedParts, k+"="+v)
			}
		}
		sortedQuery := strings.Join(sortedParts, "&")

		stringToSign := fmt.Sprintf("%s\n%s\n%s\n%s\n%s\n%s",
			method,
			u.Path,
			sortedQuery,
			headers["X-Timestamp"],
			headers["X-Nonce"],
			string(bodyBytes),
		)

		h := hmac.New(sha256.New, []byte(TestAppSecret))
		h.Write([]byte(stringToSign))
		expectedSignature := base64.StdEncoding.EncodeToString(h.Sum(nil))

		// 4. Assert that the generated signature is correct.
		if headers["X-Signature"] != expectedSignature {
			t.Errorf("Signature mismatch. Got %v, want %v", headers["X-Signature"], expectedSignature)
		}

		// 5. Assert that other headers are present.
		if headers["X-App-Key"] != TestAppKey {
			t.Errorf("AppKey mismatch. Got %v, want %v", headers["X-App-Key"], TestAppKey)
		}
	})

	// Sub-test for a GET request without a body.
	t.Run("GET without body", func(t *testing.T) {
		// 1. Define request parameters.
		method := "GET"
		requestURL := "https://api.example.com/v1/status"

		// 2. Generate the signature headers.
		headers, err := signer.Sign(method, requestURL, nil)
		if err != nil {
			t.Fatalf("Sign() error = %v", err)
		}

		// 3. Manually reconstruct the signature for verification.
		u, _ := url.Parse(requestURL)

		stringToSign := fmt.Sprintf("%s\n%s\n%s\n%s\n%s\n%s",
			method,
			u.Path,
			"", // No query parameters
			headers["X-Timestamp"],
			headers["X-Nonce"],
			"", // No body
		)

		h := hmac.New(sha256.New, []byte(TestAppSecret))
		h.Write([]byte(stringToSign))
		expectedSignature := base64.StdEncoding.EncodeToString(h.Sum(nil))

		// 4. Assert that the generated signature is correct.
		if headers["X-Signature"] != expectedSignature {
			t.Errorf("Signature mismatch. Got %v, want %v", headers["X-Signature"], expectedSignature)
		}
	})
}
