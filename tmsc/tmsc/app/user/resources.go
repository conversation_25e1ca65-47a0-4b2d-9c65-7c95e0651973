package user

import (
	"tms/model"
	"tmsc/libs"
	"tmsc/middleware"
	"tmsc/pkg/db"
	"tmsc/pkg/logger"
	"tmsc/services/auth"
	"tmsc/services/resource"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

type ResoursesAPI struct{}

var resourceService = resource.NewResourceService()

func init() {
	resourceAPI := &ResoursesAPI{}

	v1 := middleware.NewGroup("v1", "API V1 版本")
	usersV1 := v1.Group("user", "客户端用户接口")
	resourceV1 := usersV1.Group("resources", "资料接口")
	{
		resourceV1.Register("POST", "/grab", resourceAPI.GrabResource, "user", "获取远程相关资料")
		resourceV1.Register("GET", "/category", resourceAPI.GetResourceCategory, "user", "资料分类")
		resourceV1.Register("GET", "/list", resourceAPI.GetResourceList, "user", "资料列表")
		resourceV1.Register("GET", "/content/:id", resourceAPI.GetResourceContent, "user", "资料内容")
		resourceV1.Register("POST", "/download", resourceAPI.DownloadResource, "user", "下载资料")
		resourceV1.Register("GET", "/download/history", resourceAPI.DownloadResourceHistory, "user", "下载记录")
	}
}

func (api *ResoursesAPI) GrabResource(c *gin.Context) {
	claims := auth.GetUserClaims(c)
	categories, err := resourceService.GrabResourceCategory(claims.ID, nil)
	if err != nil {
		logger.Logger.Error("获取资料分类失败", zap.Error(err))
		libs.Error(c, "获取资料分类失败")
		return
	}

	resources, err := resourceService.GrabResource(claims.ID, nil)
	if err != nil {
		logger.Logger.Error("获取资料列表失败", zap.Error(err))
		libs.Error(c, "获取资料列表失败")
		return
	}
	err = db.DB.Transaction(func(tx *gorm.DB) error {
		if err := resourceService.UpsertData(tx, claims.ID, categories, resources); err != nil {
			logger.Logger.Error("同步资料失败", zap.Error(err))
			return err
		}
		return nil
	})

	if err != nil {
		libs.Error(c, "同步资料失败")
		return
	}

	libs.Success(c, "同步资料成功", nil)
}

func (api *ResoursesAPI) GetResourceCategory(c *gin.Context) {
	reqParam := model.ReqResourceCategorySearch{
		Name:     c.Query("name"),
		ParentID: cast.ToInt64(c.Query("parent_id")),
	}
	resp, err := resourceService.GetResourceCategory(reqParam)
	if err != nil {
		logger.Logger.Error("获取资料分类失败", zap.Error(err))
		libs.Error(c, "获取资料分类失败")
		return
	}
	libs.Success(c, "success", resp)
}

func (api *ResoursesAPI) GetResourceList(c *gin.Context) {
	ps := libs.GetPageParam(c)
	reqParam := model.ReqResourceSearch{
		Page:       ps.Page,
		PageSize:   ps.PageSize,
		Name:       c.Query("name"),
		Status:     c.Query("status"),
		CategoryID: cast.ToInt64(c.Query("category_id")),
		ChapterID:  cast.ToInt64(c.Query("chapter_id")),
	}
	resp, err := resourceService.GetResourceList(reqParam)
	if err != nil {
		logger.Logger.Error("获取资料列表失败", zap.Error(err))
		libs.Error(c, "获取资料列表失败")
		return
	}

	result := libs.GeneratePageResult(resp.List, resp.Total, ps)
	libs.Success(c, "success", result)
}

func (api *ResoursesAPI) GetResourceContent(c *gin.Context) {
	id := cast.ToInt64(c.Param("id"))
	if id == 0 {
		libs.Error(c, "资料ID不能为空")
		return
	}

	resp, err := resourceService.GetResourceContent(id)
	if err != nil {
		logger.Logger.Error("获取资料内容失败", zap.Error(err))
		libs.Error(c, "获取资料内容失败")
		return
	}
	libs.Success(c, "success", resp)
}

func (api *ResoursesAPI) DownloadResource(c *gin.Context) {
	var req struct {
		ResourceIDS []int64 `json:"resources_ids" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Logger.Error("获取资料内容失败", zap.Error(err))
		libs.Error(c, "获取资料内容失败")
		return
	}
	claims := auth.GetUserClaims(c)
	if claims.ID == 0 {
		libs.Error(c, "用户ID不能为空")
		return
	}

	if err := resourceService.DownloadResource(claims.ID, req.ResourceIDS); err != nil {
		logger.Logger.Error("下载资料失败", zap.Error(err))
		libs.Error(c, "下载资料失败")
		return
	}

	libs.Success(c, "success", nil)
}

func (api *ResoursesAPI) DownloadResourceHistory(c *gin.Context) {
	claims := auth.GetUserClaims(c)
	if claims.ID == 0 {
		libs.Error(c, "用户ID不能为空")
		return
	}
	ps := libs.GetPageParam(c)
	req := model.ResourceDownloadQuery{
		Page:       ps.Page,
		PageSize:   ps.PageSize,
		ResourceID: cast.ToInt64(c.Query("resource_id")),
		UserID:     claims.ID,
		StartAt:    cast.ToInt64(c.Query("start_at")),
		EndAt:      cast.ToInt64(c.Query("end_at")),
	}

	resp, total, err := resourceService.DownloadResourceHistory(req)
	if err != nil {
		logger.Logger.Error("获取下载记录失败", zap.Error(err))
		libs.Error(c, "获取下载记录失败")
		return
	}

	result := libs.GeneratePageResult(resp, total, ps)
	libs.Success(c, "success", result)
}
