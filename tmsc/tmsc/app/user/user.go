package user

import (
	"log/slog"
	"tms/model"
	"tmsc/libs"
	"tmsc/middleware"
	"tmsc/pkg/logger"
	"tmsc/services/auth"
	eventSync "tmsc/services/sync"
	"tmsc/services/users"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"go.uber.org/zap"
)

func init() {
	v1 := middleware.NewGroup("v1", "API V1 版本")
	usersV1 := v1.Group("user", "客户端用户接口")
	userGroup := usersV1.Group("users", "用户接口")
	{
		userGroup.Register("GET", "/info", GetUserInfo, "user", "获取用户信息")
		userGroup.Register("GET", "/list", GetUserList, "user", "本地用户列表")
		userGroup.Register("POST", "/update", UpdateUser, "user", "更新用户信息")
		userGroup.Register("POST", "/delete/:id", DeleteUser, "user", "删除用户")
	}
}

func GetUserInfo(c *gin.Context) {
	libs.Success(c, "success", nil)
}

func GetUserList(c *gin.Context) {
	pp := libs.GetPageParam(c)
	resp, total, err := users.GetUserList(users.UserQuery{
		Page:     pp.Page,
		PageSize: pp.PageSize,
	})
	if err != nil {
		logger.Logger.Error("获取用户列表失败", zap.Error(err))
		libs.Error(c, "获取用户列表失败")
		return
	}

	result := libs.GeneratePageResult(resp, total, pp)

	libs.Success(c, "success", result)
}

func UpdateUser(c *gin.Context) {
	type UpdateUser struct {
		User      model.Users `json:"user_info"`
		RoleCodes []string    `json:"role_codes"`
	}
	var req UpdateUser
	if err := c.ShouldBindJSON(&req); err != nil {
		slog.Error("参数错误", "error", err)
		libs.Error(c, "参数错误")
		return
	}
	if err := users.UpdateUser(req.User); err != nil {
		logger.Logger.Error("更新用户失败", zap.Error(err))
		libs.Error(c, "更新用户失败")
		return
	}
	claims := auth.GetUserClaims(c)
	if claims.ID != req.User.ID {
		libs.Error(c, "用户不匹配")
		return
	}

	UpdateObject := users.UpdateUserParam{
		User: req.User,
	}
	err := eventSync.NewEventSyncBuilder(req.User.ID).
		Operator(claims.ID).
		SyncServer().
		EventType(eventSync.UpdateUserInfoSyncType).
		Data(UpdateObject).
		Save()
	if err != nil {
		logger.Logger.Error("插入修改用户信息同步数据失败", zap.Error(err))
		libs.Error(c, "更新用户失败")
		return
	}

	libs.Success(c, "success", nil)
}

func DeleteUser(c *gin.Context) {
	id := cast.ToInt64(c.Param("id"))
	if id == 0 {
		libs.Error(c, "用户ID不能为空")
		return
	}

	if err := users.DeleteUser(id); err != nil {
		logger.Logger.Error("删除用户失败", zap.Error(err))
		libs.Error(c, "删除用户失败")
		return
	}

	libs.Success(c, "success", nil)
}
