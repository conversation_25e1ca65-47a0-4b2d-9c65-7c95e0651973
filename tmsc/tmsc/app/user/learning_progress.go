package user

import (
	"time"
	"tmsc/libs"
	"tmsc/middleware"
	"tmsc/services/auth"
	"tmsc/services/learn"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

type LearningProgressAPI struct{}

var learnService = learn.NewLearnService()

func init() {
	api := &LearningProgressAPI{}

	v1 := middleware.NewGroup("v1", "API V1 版本")
	usersV1 := v1.Group("user", "客户端用户接口")
	learningProgressV1 := usersV1.Group("learning_progress", "学习进度接口")
	{
		learningProgressV1.Register("GET", "/course/:course_id/list", api.GetCourseLearningProgress, "user", "课程学习进度")
		learningProgressV1.Register("GET", "/chapter/:chapter_id/list", api.GetChapterLearningProgress, "user", "章节学习进度")
		learningProgressV1.Register("GET", "/weekly_stats", api.GetWeeklyLearningStats, "user", "每周学习统计")
		learningProgressV1.Register("GET", "/daily_stats", api.GetDailyLearningStats, "user", "每日学习统计")
	}
}

func (api *LearningProgressAPI) GetCourseLearningProgress(c *gin.Context) {
	claims := auth.GetUserClaims(c)
	courseID := c.Param("course_id")
	if courseID == "" {
		libs.Error(c, "无效的课程ID")
		return
	}

	resp, err := learnService.GetCourseLearningProgress(cast.ToInt64(courseID), claims.ID)
	if err != nil {
		libs.Error(c, err.Error())
		return
	}
	libs.Success(c, "查询成功", resp)
}

func (api *LearningProgressAPI) GetChapterLearningProgress(c *gin.Context) {
	claims := auth.GetUserClaims(c)
	chapterID := c.Param("chapter_id")
	if chapterID == "" {
		libs.Error(c, "无效的章节ID")
		return
	}

	resp, err := learnService.GetChapterLearningProgress(cast.ToInt64(chapterID), claims.ID)
	if err != nil {
		libs.Error(c, err.Error())
		return
	}

	libs.Success(c, "查询成功", resp)
}

func (api *LearningProgressAPI) GetWeeklyLearningStats(c *gin.Context) {
	claims := auth.GetUserClaims(c)

	resp, err := learnService.GetWeeklyLearningStats(claims.ID)
	if err != nil {
		libs.Error(c, err.Error())
		return
	}
	libs.Success(c, "查询成功", resp)
}

func (api *LearningProgressAPI) GetDailyLearningStats(c *gin.Context) {
	claims := auth.GetUserClaims(c)
	startTimeStr := c.Query("start_time")
	endTimeStr := c.Query("end_time")

	if startTimeStr == "" || endTimeStr == "" {
		libs.Error(c, "必须提供开始和结束时间")
		return
	}

	startTime, err := time.Parse("2006-01-02", startTimeStr)
	if err != nil {
		libs.Error(c, "无效的开始时间格式")
		return
	}

	endTime, err := time.Parse("2006-01-02", endTimeStr)
	if err != nil {
		libs.Error(c, "无效的结束时间格式")
		return
	}

	resp, err := learnService.GetDailyLearningStats(claims.ID, startTime, endTime)
	if err != nil {
		libs.Error(c, err.Error())
		return
	}

	libs.Success(c, "查询成功", resp)
}
