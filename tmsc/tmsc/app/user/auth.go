package user

import (
	"errors"
	"fmt"
	"strings"

	"tms/model"
	"tmsc/libs"
	"tmsc/middleware"
	"tmsc/pkg/cache"
	"tmsc/pkg/db"
	"tmsc/pkg/logger"
	"tmsc/services/adapter"
	"tmsc/services/auth"
	"tmsc/services/cron"
	"tmsc/services/system"
	"tmsc/services/users"
	"tmsc/utils"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// 用户端 分联网 和 离线单机 两种
// 登录时 校验 先校验本地数据 ，若失败 再校验 网络数据；
func init() {
	v1 := middleware.NewGroup("v1", "API V1 版本")
	usersV1 := v1.Group("user", "客户端用户接口")
	authV1 := usersV1.Group("auth", "认证接口")
	{
		authV1.Register("POST", "/login", UserLogin, "guest", "用户登录")
		authV1.Register("POST", "/logout", UserLogout, "user", "用户登出")
	}
}

type LoginResponse struct {
	User     *model.Users       `json:"user"`
	Menu     []*system.MenuNode `json:"menu"`
	Role     []*model.Roles     `json:"role"`
	Token    string             `json:"token"`
	SysCode  string             `json:"sys_code"`
	UserCode string             `json:"user_code"`
}

// checkLocalUser 校验本地用户
func checkLocalUser(loginReq adapter.UserLoginReq) (*LoginResponse, error) {
	var user model.Users
	if err := db.DB.Where("account = ?", loginReq.Account).First(&user).Error; err != nil {
		return nil, err
	}

	if utils.HashPassword(loginReq.Password, user.Salt) != user.Password {
		return nil, errors.New("密码错误")
	}

	var userRoleCodes []string
	if err := db.DB.Model(&model.UserRoles{}).Where("user_id = ?", user.ID).Pluck("role_code", &userRoleCodes).Error; err != nil {
		logger.Logger.Error("Failed to get user roles", zap.Error(err))
		return nil, errors.New("获取用户角色失败")
	}

	var roles []*model.Roles
	if err := db.DB.Model(&model.Roles{}).Where("role_code in (?)", userRoleCodes).Find(&roles).Error; err != nil {
		logger.Logger.Error("Failed to get user roles", zap.Error(err))
		return nil, errors.New("获取用户角色失败")
	}

	if len(roles) == 0 {
		return nil, errors.New("用户角色为空")
	}

	if loginReq.UserCode == "" {
		for _, v := range userRoleCodes {
			if v == model.AuthUser {
				loginReq.UserCode = model.AuthUser
				break
			}
		}
	}
	if loginReq.UserCode == "" {
		loginReq.UserCode = userRoleCodes[0]
	}
	var sysCode string
	for _, role := range roles {
		if role.RoleCode == loginReq.UserCode {
			sysCode = role.SysCode
			break
		}
	}

	hasAllRole := false
	for _, roleCode := range userRoleCodes {
		if roleCode == "all" {
			hasAllRole = true
			break
		}
	}

	var menuIDS []int64
	if hasAllRole {
		// 获取所有菜单 ID
		if err := db.DB.Model(&model.Menus{}).Pluck("id", &menuIDS).Error; err != nil {
			logger.Logger.Error("Failed to get menu", zap.Error(err))
			return nil, errors.New("获取菜单失败")
		}
	} else {
		// 否则只获取用户角色对应的菜单
		if err := db.DB.Model(&model.RoleMenu{}).Where("role_code = ?", loginReq.UserCode).Pluck("menu_id", &menuIDS).Error; err != nil {
			logger.Logger.Error("Failed to get menu", zap.Error(err))
			return nil, errors.New("获取菜单失败")
		}
	}

	menus := make([]*model.Menus, 0)
	if len(menuIDS) > 0 {
		if err := db.DB.Where("id in (?)", menuIDS).Find(&menus).Error; err != nil {
			logger.Logger.Error("Failed to get menu", zap.Error(err))
			return nil, errors.New("获取菜单失败")
		}
	}
	menuTree := system.BuildMenuTree(menus)

	claims := utils.UserClaims{
		ID:        user.ID,
		RoleCodes: userRoleCodes,
		UserCode:  loginReq.UserCode,
	}
	token, err := utils.GenerateToken(&claims)
	if err != nil {
		logger.Logger.Error("Failed to generate token", zap.Error(err))
		return nil, errors.New("生成令牌失败")
	}

	resp := &LoginResponse{
		User:     &user,
		Menu:     menuTree,
		Role:     roles,
		Token:    token,
		UserCode: loginReq.UserCode,
		SysCode:  sysCode,
	}

	return resp, nil
}

// syncRemoteUser 远程用户登录并同步数据
func syncRemoteUser(loginReq adapter.UserLoginReq) (*LoginResponse, error) {
	resp, err := adapter.GetAdapterInstance().UserLogin(loginReq)
	if err != nil {
		errMsg := err.Error()
		if strings.Contains(errMsg, "failed to send request") || strings.Contains(errMsg, "failed to read response body") {
			logger.Logger.Error("远程登录网络错误", zap.Error(err))
			return nil, errors.New("网络连接失败，请检查网络或稍后重试")
		} else if strings.Contains(errMsg, "api error: 用户不存在") {
			logger.Logger.Error("远程用户不存在", zap.Error(err))
			return nil, errors.New("用户不存在")
		}
		logger.Logger.Error("远程登录未知错误", zap.Error(err))
		return nil, fmt.Errorf("远程登录失败: %w", err)
	}

	user := resp.User
	password := utils.HashPassword(loginReq.Password, user.Salt)
	resp.User.Password = password
	user.Password = password
	token := resp.Token
	roles := resp.Roles
	menuTree := resp.Menus

	go syncUserData(resp)

	result := &LoginResponse{
		User:     &user,
		Menu:     menuTree,
		Role:     roles,
		Token:    token,
		UserCode: resp.UserCode,
		SysCode:  resp.SysCode,
	}

	return result, nil
}

func UserLogin(c *gin.Context) {
	var loginReq adapter.UserLoginReq
	if err := c.ShouldBindJSON(&loginReq); err != nil {
		logger.Logger.Error("参数错误", zap.Error(err))
		libs.Error(c, "参数错误")
		return
	}

	var loginResp *LoginResponse
	var err error

	// 尝试从本地校验
	loginResp, err = checkLocalUser(loginReq)
	if err != nil {
		// 如果本地校验失败，尝试远程登录并同步
		if errors.Is(err, gorm.ErrRecordNotFound) {
			if loginReq.UserCode == "" {
				loginReq.UserCode = model.AuthUser
			}
			loginResp, err = syncRemoteUser(loginReq)
			if err != nil {
				logger.Logger.Error("远程用户登录失败", zap.Error(err))
				libs.Error(c, "登录失败")
				return
			}
		} else {
			// 其他本地校验错误
			logger.Logger.Error("本地用户校验失败", zap.Error(err))
			libs.Error(c, "登录失败")
			return
		}
	}

	defer func() {
		key := fmt.Sprintf("%d-token", loginResp.User.ID)
		cache.FreeCache.Set([]byte(key), []byte(loginResp.Token), 3600*24)
		go cron.StartEventFetcher(loginResp.User.ID)
	}()

	loginResp.User.Salt = ""
	loginResp.User.Password = ""
	libs.Success(c, "success", gin.H{
		"token":     loginResp.Token,
		"user":      loginResp.User,
		"roles":     loginResp.Role,
		"menus":     loginResp.Menu,
		"user_code": loginResp.UserCode,
		"sys_code":  loginResp.SysCode,
	})
}

func syncUserData(resp *adapter.UserLoginResp) {
	// 保存到本地
	// 使用 Upsert 优化用户保存，避免重复创建
	if err := db.DB.Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "id"}},
		DoUpdates: clause.AssignmentColumns(resp.User.UpdateColumns("exclude_primary_key")),
	}).Create(&resp.User).Error; err != nil {
		logger.Logger.Error("保存或更新用户失败", zap.Error(err))
		return
	}
	// 保存用户角色和菜单
	// 批量保存或更新角色信息
	if len(resp.Roles) > 0 {
		if err := db.DB.Clauses(clause.OnConflict{
			Columns:   []clause.Column{{Name: "id"}},
			DoUpdates: clause.AssignmentColumns(model.Roles{}.UpdateColumns("exclude_primary_key")),
		}).CreateInBatches(resp.Roles, 100).Error; err != nil {
			logger.Logger.Error("批量保存或更新角色失败", zap.Error(err))
		}
	}

	// 同步用户角色关联关系
	var newUserRoles []model.UserRoles
	var currentRoleCodes []string
	for _, role := range resp.Roles {
		newUserRoles = append(newUserRoles, model.UserRoles{
			UserID:   resp.User.ID,
			RoleCode: role.RoleCode,
		})
		currentRoleCodes = append(currentRoleCodes, role.RoleCode)
	}

	// 删除旧的用户角色关联
	if err := db.DB.Where("user_id = ?", resp.User.ID).Not("role_code IN (?)", currentRoleCodes).Delete(&model.UserRoles{}).Error; err != nil {
		logger.Logger.Error("删除旧的用户角色关联失败", zap.Error(err))
	}
	// 批量创建新的用户角色关联，忽略冲突
	if len(newUserRoles) > 0 {
		if err := db.DB.Clauses(clause.OnConflict{DoNothing: true}).CreateInBatches(newUserRoles, 100).Error; err != nil {
			logger.Logger.Error("批量保存用户角色关联失败", zap.Error(err))
		}
	}

	menus := system.ConvertMenuNodesToMenus(resp.Menus)
	// 批量保存或更新菜单信息
	if len(menus) > 0 {
		if err := db.DB.Clauses(clause.OnConflict{
			Columns:   []clause.Column{{Name: "id"}},
			DoUpdates: clause.AssignmentColumns(model.Menus{}.UpdateColumns("exclude_primary_key")),
		}).CreateInBatches(menus, 100).Error; err != nil {
			logger.Logger.Error("批量保存或更新菜单失败", zap.Error(err))
		}
	}

	// 同步角色菜单关联关系
	var newRoleMenus []model.RoleMenu
	var menuRoleCode string
	for _, menu := range menus {
		newRoleMenus = append(newRoleMenus, model.RoleMenu{
			RoleCode: menu.SysCode,
			MenuID:   menu.ID,
		})
	}
	if len(newRoleMenus) > 0 {
		menuRoleCode = menus[0].SysCode
	}

	// 删除旧的角色菜单关联
	if err := db.DB.Where("role_code IN (?)", menuRoleCode).Delete(&model.RoleMenu{}).Error; err != nil {
		logger.Logger.Error("删除旧的角色菜单关联失败", zap.Error(err))
	}
	// 批量创建新的角色菜单关联，忽略冲突
	if len(newRoleMenus) > 0 {
		if err := db.DB.Clauses(clause.OnConflict{DoNothing: true}).CreateInBatches(newRoleMenus, 100).Error; err != nil {
			logger.Logger.Error("批量保存角色菜单关联失败", zap.Error(err))
		}
	}

	// 批量保存角色权限，遇到唯一键冲突时忽略
	if len(resp.RolePermissions) > 0 {
		if err := db.DB.Clauses(clause.OnConflict{DoNothing: true}).CreateInBatches(resp.RolePermissions, 100).Error; err != nil {
			logger.Logger.Error("批量保存角色权限失败", zap.Error(err))
		}
	}

	// 优化专业数据保存
	if resp.Major != nil {
		if err := db.DB.Clauses(clause.OnConflict{
			Columns:   []clause.Column{{Name: "id"}},
			DoUpdates: clause.AssignmentColumns(resp.Major.UpdateColumns("exclude_primary_key")),
		}).Create(&resp.Major).Error; err != nil {
			logger.Logger.Error("保存或更新专业失败", zap.Error(err))
		} else {
			logger.Logger.Info("专业保存或更新成功", zap.Any("major", resp.Major))
		}
	}

	// 优化班级数据保存
	if resp.Class != nil {
		if err := db.DB.Clauses(clause.OnConflict{
			Columns:   []clause.Column{{Name: "id"}},
			DoUpdates: clause.AssignmentColumns(resp.Class.UpdateColumns("exclude_primary_key")),
		}).Create(&resp.Class).Error; err != nil {
			logger.Logger.Error("保存或更新班级失败", zap.Error(err))
		} else {
			logger.Logger.Info("班级保存或更新成功", zap.Any("class", resp.Class))
		}

		var sysRoleCodes []string
		for _, v := range resp.Roles {
			if v.IsSystem {
				sysRoleCodes = append(sysRoleCodes, v.SysCode)
			}
		}

		if err := users.SaveUserCodes(resp.User.ID, resp.Class.ID, utils.Unique(sysRoleCodes)); err != nil {
			logger.Logger.Error("保存用户权限失败", zap.Error(err))
		}
	}

	if resp.UserRef != nil {
		// 保存学员的关联关系
		if err := users.SaveStudetRef(*resp.UserRef); err != nil {
			logger.Logger.Error("保存学员关联关系失败", zap.Error(err))
		}
	}
}

func UserLogout(c *gin.Context) {
	claims := auth.GetUserClaims(c)
	key := fmt.Sprintf("%d-token", claims.ID)
	cache.FreeCache.Del([]byte(key))
	logger.Logger.Info("User logged out", zap.Int64("userID", claims.ID))

	cron.StopEventFetcher(claims.ID) // Stop the event fetcher for the logged out user

	libs.Success(c, "success", nil)
}
