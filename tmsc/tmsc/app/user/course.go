package user

import (
	"tms/model"
	"tmsc/libs"
	"tmsc/middleware"
	"tmsc/pkg/logger"
	"tmsc/services/auth"
	"tmsc/services/lms"
	"tmsc/services/teach"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"go.uber.org/zap"
)

type CoursesAPI struct{}

var chapterService = teach.NewChapterService()
var questionService = teach.NewQuestionService()

var lmsService *lms.ScormService

func init() {
	api := &CoursesAPI{}
	lmsService = &lms.ScormService{}

	v1 := middleware.NewGroup("v1", "API V1 版本")
	usersV1 := v1.Group("user", "客户端用户接口")
	courseGroup := usersV1.Group("courses", "课程接口")
	{
		courseGroup.Register("GET", "/list", api.GetCourseList, model.AuthUser, "课程列表")
		courseGroup.Register("GET", "/chapter_list/:id", api.GetCourseContent, model.AuthUser, "课程内容(章节)")
		courseGroup.Register("GET", "/courseware_list/:id", api.GetCoursewareList, model.AuthUser, "获取课件列表")
		courseGroup.Register("GET", "/question_list/:id", api.GetQuestionList, model.AuthUser, "获取理论试题列表")
		courseGroup.Register("POST", "/clear_cache/:id", api.ClearCoursesCache, model.AuthUser, "清除课程缓存")
	}
}

func (api *CoursesAPI) GetCourseList(c *gin.Context) {
	// 获取当前用户该班级下的所有的教学计划下的课程
	claims := auth.GetUserClaims(c)
	po := libs.GetPageParam(c)
	req := model.ReqCoursesSearch{
		PageSize: po.PageSize,
		Page:     po.Page,
		Name:     c.Query("name"),
	}
	data, total, err := courseService.GetCourseList(req, claims.ID)
	if err != nil {
		libs.Error(c, "获取课程列表失败")
		return
	}
	result := libs.GeneratePageResult(data, total, po)
	libs.Success(c, "获取课程列表成功", result)
}

func (api *CoursesAPI) GetCourseContent(c *gin.Context) {
	id := cast.ToInt64(c.Param("id"))
	if id == 0 {
		libs.Error(c, "无效的课程ID")
		return
	}
	resp, err := chapterService.GetChapterListByCourseID(id)
	if err != nil {
		libs.Error(c, err.Error())
		return
	}
	libs.Success(c, "查询成功", resp)
}

func (api *CoursesAPI) GetCoursewareList(c *gin.Context) {
	id := cast.ToInt64(c.Param("id"))
	if id == 0 {
		libs.Error(c, "无效的章节ID")
		return
	}
	resp, err := chapterService.GetCoursewareListByChapterID(id)
	if err != nil {
		libs.Error(c, err.Error())
		return
	}

	var coursewareIDS []int64
	for _, courseware := range resp {
		coursewareIDS = append(coursewareIDS, courseware.ID)
	}
	if err := coursewareService.DownloadAndUnzipCourseware(coursewareIDS); err != nil {
		logger.Logger.Error("解压课件失败", zap.Error(err))
	}

	libs.Success(c, "查询成功", resp)
}

func (api *CoursesAPI) GetQuestionList(c *gin.Context) {
	id := cast.ToInt64(c.Param("id"))
	if id == 0 {
		libs.Error(c, "无效的章节ID")
		return
	}
	resp, err := questionService.GetQuestionsByChapterID(id)
	if err != nil {
		libs.Error(c, err.Error())
		return
	}
	libs.Success(c, "查询成功", resp)
}

func (api *CoursesAPI) ClearCoursesCache(c *gin.Context) {
	id := cast.ToInt64(c.Param("id")) // course_id
	if id == 0 {
		libs.Error(c, "无效的章节ID")
		return
	}

	coursewares, resources, _, err := courseService.GetAttachedAttributes(id)
	if err != nil {
		logger.Logger.Error("获取课程关联数据失败", zap.Error(err))
		libs.Error(c, "清除缓存失败")
		return
	}

	for _, v := range coursewares {
		if err := coursewareService.ClearCourseCache(v.ID); err != nil {
			libs.Error(c, "清除缓存失败")
			return
		}
	}
	for _, v := range resources {
		if err := resourceService.ClearResourceCache(v.ID); err != nil {
			libs.Error(c, "清除缓存失败")
			return
		}
	}

	libs.Success(c, "清除成功", nil)
}
