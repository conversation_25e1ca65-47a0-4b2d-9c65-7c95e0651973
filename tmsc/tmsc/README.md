# tmsc - Go Web应用开发框架

![Go Version](https://img.shields.io/badge/go-1.16+-blue.svg)
[![License](https://img.shields.io/badge/license-MIT-green.svg)](LICENSE)

## 功能特性

- 🚀 **快速开发** - 基于Gin框架的高性能路由引擎
- 🔐 **认证授权** - 支持JWT、Session、OAuth2等多种认证方式
- 🗄️ **多数据库支持** - MySQL/PostgreSQL/SQLite/MongoDB统一接口
- ⚡ **缓存系统** - 内存/文件/Redis多级缓存
- 📦 **模块化设计** - 按功能划分的清晰项目结构
- ?? **监控统计** - 内置请求日志和性能监控

## 快速开始

### 系统要求

- Go 1.16+
- 数据库(任选其一):
  - MySQL 5.7+
  - PostgreSQL 10+
  - SQLite 3
  - MongoDB 4.2+

### 安装步骤

1. 克隆项目仓库:
```bash
git clone https://gitee.com/godoos/tmsc.git
cd tmsc
```

2. 安装依赖:
```bash
go mod tidy
```

3. 配置环境:
```bash
cp config/system.example.json config/system.json
```

### 配置说明

编辑 `config/system.json`:

```json
{
  "system": {
    "debug": true,
    "port": 8816,
    "host": "0.0.0.0",
    "sessionType": "memory|redis|file",
    "sessionSecret": "your-secret-key-here"
  },
  "database": {
    "type": "mysql|postgres|sqlite|mongodb",
    "host": "localhost",
    "port": 3306,
    "user": "root",
    "password": "",
    "dbname": "tmsc"
  }
}
```

### 启动服务

开发模式:
```bash
go run main.go
```

生产模式:
```bash
go build -o tmsc main.go
./tmsc
```

## 项目结构

```
.
├── app/                # 应用模块
│   ├── admin/          # 管理后台
│   ├── user/           # 用户系统
│   └── app.go         # 应用入口
├── cmd/                # CLI命令
├── config/             # 配置文件
├── libs/               # 公共库
├── middleware/         # 中间件
├── model/              # 数据模型
├── pkg/                # 核心组件
│   ├── cache/          # 缓存系统
│   ├── db/             # 数据库抽象
│   └── sms/            # 短信服务
└── service/            # 业务服务
```

## API示例

### 用户认证

**登录接口**:
```http
POST /api/v1/user/login
Content-Type: application/json

{
  "username": "admin",
  "password": "123456"
}
```

响应示例:
```json
{
  "code": 200,
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expire": 3600
  }
}
```

### 数据查询

使用数据库工厂:
```go
// 获取数据库实例
db := pkg.db.GetDB()

// 查询用户列表
var users []model.User
if err := db.Where("status = ?", 1).Find(&users).Error; err != nil {
    log.Error("查询失败: ", err)
}
```

## 开发指南

### 创建新模块

1. 在`app/`下创建模块目录
2. 实现路由和控制器
3. 注册路由到主应用:

```go
// app/new_module/router.go
func InitRouter() {
    middleware.RegisterRouter("GET", "/api/new", handler, 0, "new-api")
}
```

### 使用缓存

```go
// 设置缓存(1小时过期)
err := pkg.cache.Set("user:1", userData, time.Hour)

// 获取缓存
val, err := pkg.cache.Get("user:1")
```

## 贡献指南

我们欢迎任何形式的贡献!

1. 提交Issue报告问题或建议
2. Fork仓库并创建分支(`feat/xxx`或`fix/xxx`)
3. 提交Pull Request

### 开发流程

1. 设置开发环境:
```bash
go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest
```

2. 运行测试:
```bash
go test ./...
```

3. 代码规范检查:
```bash
golangci-lint run
```

## 许可证

Apache License © 2004 tmsc Team