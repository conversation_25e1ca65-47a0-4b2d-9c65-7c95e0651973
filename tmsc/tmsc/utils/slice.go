package utils

import "fmt"

// Unique 范型,可比较类型的 切片 去重
func Unique[T comparable](slice []T) []T {
	uniqueMap := make(map[T]struct{})
	for _, item := range slice {
		uniqueMap[item] = struct{}{}
	}

	uniqueSlice := make([]T, 0, len(uniqueMap))
	for item := range uniqueMap {
		uniqueSlice = append(uniqueSlice, item)
	}

	return uniqueSlice
}

// 范型：可比较类型切片 转成字符串切片
func SliceToStringArray[T comparable](slice []T) []string {
	slice = Unique(slice)

	stringSlice := make([]string, len(slice))
	for i, v := range slice {
		stringSlice[i] = fmt.Sprintf("%v", v)
	}
	return stringSlice
}
