package utils

import (
	"fmt"
	"strconv"
)

// PageNumbers 生成页码字符串数组
func PageNumbers(currentPage int, totalPages int) []string {
	var pages []string

	// 如果总页数小于等于5，则直接生成所有页码
	if totalPages <= 5 {
		for i := 1; i <= totalPages; i++ {
			pages = append(pages, strconv.Itoa(i))
		}
	} else {
		// 如果当前页小于等于3，则显示第1到第5页
		if currentPage <= 3 {
			for i := 1; i <= 5; i++ {
				pages = append(pages, strconv.Itoa(i))
			}
		} else if currentPage >= totalPages-2 { // 如果当前页接近最后两页，则显示最后5页
			for i := totalPages - 4; i <= totalPages; i++ {
				pages = append(pages, strconv.Itoa(i))
			}
		} else { // 否则，显示当前页及其前后各两页
			startPage := currentPage - 2
			endPage := currentPage + 2
			for i := startPage; i <= endPage; i++ {
				pages = append(pages, strconv.Itoa(i))
			}
		}
	}

	return pages
}

// 计算上一页和下一页的 URL
func PrevNextURL(page, totalPages int) (prevURL, nextURL string) {
	if page > 1 {
		prevURL = fmt.Sprintf("?page=%d", page-1)
	}
	if page < totalPages {
		nextURL = fmt.Sprintf("?page=%d", page+1)
	}
	return
}
func Paginate[T any](data []T, page, pageSize int) ([]T, error) {
	if len(data) == 0 {
		return data, nil
	}
	if page < 1 || pageSize < 1 {
		return nil, fmt.Errorf("页码和每页大小必须大于0")
	}

	start := (page - 1) * pageSize
	if start >= len(data) {
		return nil, fmt.Errorf("页码超出范围")
	}

	end := start + pageSize
	if end > len(data) {
		end = len(data)
	}

	return data[start:end], nil
}
