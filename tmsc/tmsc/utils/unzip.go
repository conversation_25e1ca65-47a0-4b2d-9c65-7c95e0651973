package utils

import (
	"archive/zip"
	"io"
	"os"
	"path/filepath"
	"strings"
)

// UnzipScorm 解压 SCORM 包到指定目录
// filePath: SCORM 包的路径 (通常是 .zip 文件)
// destFolder: 解压目标文件夹
// 返回解压后的根目录路径和错误
func UnzipScorm(filePath, destFolder string) (string, error) {
	reader, err := zip.OpenReader(filePath)
	if err != nil {
		return "", err
	}
	defer reader.Close()

	// 确保目标文件夹存在
	if err := os.MkdirAll(destFolder, 0755); err != nil {
		return "", err
	}

	var unzipPath string
	for _, file := range reader.File {
		path := filepath.Join(destFolder, file.Name)

		// 检查路径是否在目标文件夹内，防止路径遍历漏洞
		if !strings.HasPrefix(path, filepath.Clean(destFolder)+string(os.PathSeparator)) {
			return "", os.ErrInvalid
		}

		if file.FileInfo().IsDir() {
			if err := os.MkdirAll(path, file.Mode()); err != nil {
				return "", err
			}
			continue
		}

		// 创建文件所在的目录
		if err := os.MkdirAll(filepath.Dir(path), 0755); err != nil {
			return "", err
		}

		// 如果文件已存在，先删除
		if _, err := os.Stat(path); err == nil {
			if err := os.Remove(path); err != nil {
				return "", err
			}
		}
		outFile, err := os.OpenFile(path, os.O_WRONLY|os.O_CREATE|os.O_TRUNC, file.Mode())
		if err != nil {
			return "", err
		}

		rc, err := file.Open()
		if err != nil {
			outFile.Close()
			return "", err
		}

		_, err = io.Copy(outFile, rc)
		outFile.Close()
		rc.Close()
		if err != nil {
			return "", err
		}

		// 假设 SCORM 包的根目录是解压后的第一个文件夹
		if unzipPath == "" && strings.Contains(file.Name, "/") {
			parts := strings.Split(file.Name, "/")
			if len(parts) > 0 {
				unzipPath = filepath.Join(destFolder, parts[0])
			}
		}
	}

	// 如果没有找到子目录，则认为解压路径就是目标文件夹
	if unzipPath == "" {
		unzipPath = destFolder
	}

	return unzipPath, nil
}
