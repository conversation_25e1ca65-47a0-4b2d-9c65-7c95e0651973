package utils

import (
	"fmt"
	"io"
	"net/http"
	"os"
	"path/filepath"
)

// DownloadFile 从指定的URL下载文件并保存到本地路径
func DownloadFile(url string, filePath string) error {
	// 创建目标文件夹（如果不存在）
	dir := filepath.Dir(filePath)
	if err := os.MkdirAll(dir, os.ModePerm); err != nil {
		return fmt.Errorf("创建目录失败: %w", err)
	}

	// 创建文件
	out, err := os.Create(filePath)
	if err != nil {
		return fmt.Errorf("创建文件失败: %w", err)
	}
	defer out.Close()

	// 发送HTTP GET请求
	resp, err := http.Get(url)
	if err != nil {
		return fmt.Errorf("下载请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("下载失败，HTTP状态码: %d %s", resp.StatusCode, resp.Status)
	}

	// 将响应体写入文件
	n, err := io.Copy(out, resp.Body)
	if err != nil {
		return fmt.Errorf("写入文件失败: %w", err)
	}

	// 检查Content-Length
	if resp.ContentLength != -1 && n != resp.ContentLength {
		return fmt.Errorf("下载文件大小不匹配，预期: %d, 实际: %d", resp.ContentLength, n)
	}

	return nil
}
