package middleware

import (
	"log"
	"net/http"
	"strings"
	"tms/model"
	"tmsc/pkg/db"

	"github.com/gin-gonic/gin"
)

// Route 结构体用于存储路由信息
type Route struct {
	Method           string            `json:"method"`
	Path             string            `json:"path"`
	Name             string            `json:"name"`
	Handler          gin.HandlerFunc   `json:"-"`
	RouteMiddlewares []gin.HandlerFunc `json:"-"`        // 路由特定的中间件
	GroupMiddlewares []gin.HandlerFunc `json:"-"`        // 组中间件
	AuthType         string            `json:"authType"` // 表示认证类型 guest不认证user用户teacher教师admin管理员all所有用户
}

// routes 存储所有需要注册的路由
var Routes = make(map[string]Route)

var APIRootGroup *RouteGroup // 全局 API 根路由组

func init() {
	APIRootGroup = &RouteGroup{
		Prefix:      "api",
		ID:          model.GeneratePermissionID("GROUP", "/api"), // 为根分组生成ID
		Description: "API Root",
		Routes:      make([]Route, 0),
		Children:    make([]*RouteGroup, 0),
	}
}

// RouteGroup 表示一个路由分组，支持嵌套
type RouteGroup struct {
	ID            string            // 分组的唯一ID，用于权限同步
	Parent        *RouteGroup       // 父分组，用于构建完整路径
	Prefix        string            // 当前分组路径前缀
	Description   string            // 分组描述
	GroupHandlers []gin.HandlerFunc // 组中间件列表
	Routes        []Route           `json:"routes"`
	Children      []*RouteGroup     // 子分组列表
}

// NewGroup 创建根分组
func NewGroup(prefix, description string) *RouteGroup {
	trimmedPrefix := strings.Trim(prefix, "/")

	// 检查 APIRootGroup 的子分组中是否已存在该前缀的分组
	for _, child := range APIRootGroup.Children {
		if child.Prefix == trimmedPrefix {
			return child // 如果存在，则返回已存在的子分组
		}
	}

	// 如果不存在，则创建新的分组
	child := &RouteGroup{
		Parent:      APIRootGroup,                                                                   // 将其父分组设置为 APIRootGroup
		ID:          model.GeneratePermissionID("GROUP", APIRootGroup.buildFullPath(trimmedPrefix)), // 为子分组生成ID
		Prefix:      trimmedPrefix,
		Description: description,
		Routes:      make([]Route, 0),
		Children:    make([]*RouteGroup, 0),
	}
	// 将新创建的分组添加到 APIRootGroup 的子分组列表中
	APIRootGroup.Children = append(APIRootGroup.Children, child)
	return child
}

// Group 创建当前分组下的子分组
func (rg *RouteGroup) Group(prefix, description string) *RouteGroup {
	trimmedPrefix := strings.Trim(prefix, "/")

	// 检查当前分组的子分组中是否已存在该前缀的分组
	for _, child := range rg.Children {
		if child.Prefix == trimmedPrefix {
			return child // 如果存在，则返回已存在的子分组
		}
	}

	// 如果不存在，则创建新的子分组
	child := &RouteGroup{
		Parent:      rg,
		ID:          model.GeneratePermissionID("GROUP", rg.buildFullPath(trimmedPrefix)), // 为子分组生成ID
		Prefix:      trimmedPrefix,
		Description: description,
		Routes:      make([]Route, 0),
		Children:    make([]*RouteGroup, 0),
	}
	rg.Children = append(rg.Children, child)
	return child
}

// Use 添加中间件到当前路由组
func (rg *RouteGroup) Use(middlewares ...gin.HandlerFunc) {
	rg.GroupHandlers = append(rg.GroupHandlers, middlewares...)
}

// Register 向该分组中注册一个路由
func (rg *RouteGroup) Register(method, path string, handler gin.HandlerFunc, authType string, name string, routeMiddlewares ...gin.HandlerFunc) {
	fullPath := rg.buildFullPath(path)

	route := Route{
		Method:           method,
		Path:             fullPath,
		Name:             name,
		Handler:          handler,
		RouteMiddlewares: routeMiddlewares, // 存储路由特定中间件
		GroupMiddlewares: rg.GroupHandlers, // 存储组中间件
		AuthType:         authType,
	}
	RegisterRouter(method, fullPath, handler, authType, name, route.GroupMiddlewares, route.RouteMiddlewares)
	rg.Routes = append(rg.Routes, route)
}

// 构建完整路径
func (rg *RouteGroup) buildFullPath(path string) string {
	var parentPath string
	if rg.Parent != nil {
		parentPath = rg.Parent.buildFullPath("")
	}

	currentPath := "/" + rg.Prefix
	if path == "/" || path == "" {
		return parentPath + currentPath
	}

	return parentPath + currentPath + "/" + strings.Trim(path, "/")
}

// GetGroupRoutes 获取当前分组下所有路由（不包括子分组）
func (rg *RouteGroup) GetGroupRoutes() []Route {
	return rg.Routes
}

// GetAllRoutes 递归获取当前分组及其所有子分组下的路由
func GetAllRoutes(group *RouteGroup) []Route {
	var allRoutes []Route
	allRoutes = append(allRoutes, group.Routes...)

	for _, child := range group.Children {
		allRoutes = append(allRoutes, GetAllRoutes(child)...)
	}

	return allRoutes
}

// RegisterRouter 注册控制器中的路由
func RegisterRouter(method string, path string, handler gin.HandlerFunc, authType string, name string, groupMiddlewares []gin.HandlerFunc, routeMiddlewares []gin.HandlerFunc) {
	path = strings.TrimPrefix(path, "/")
	path = strings.TrimSuffix(path, "/")

	// 拼接路由地址
	if path == "index" {
		path = "/"
	} else {
		path = "/" + strings.ToLower(path)
	}

	// 构造键值
	//key := method + ":" + path

	// 添加路由
	Routes[path] = Route{
		Path:             path,
		Method:           method,
		Handler:          handler,
		AuthType:         authType,
		Name:             name,
		GroupMiddlewares: groupMiddlewares, // 存储组中间件
		RouteMiddlewares: routeMiddlewares, // 存储路由特定中间件
	}

	//slog.Info("Register route", "method", method, "path", path)
}

// BindRouter 绑定所有注册的路由到 Gin 引擎
func BindRouter(e *gin.Engine) {
	// 将 data/courses 目录设置为静态文件服务
	e.StaticFS("/course-assets", http.Dir("data/courses"))
	e.StaticFS("/courseware-assets", http.Dir("data/scorm"))
	e.StaticFS("/resource-assets", http.Dir("data/resources"))

	for _, route := range Routes {
		var allHandlers []gin.HandlerFunc
		allHandlers = append(allHandlers, route.GroupMiddlewares...)
		allHandlers = append(allHandlers, route.RouteMiddlewares...)
		allHandlers = append(allHandlers, route.Handler)

		switch route.Method {
		case "GET":
			e.GET(route.Path, allHandlers...)
		case "POST":
			e.POST(route.Path, allHandlers...)
		case "DELETE":
			e.DELETE(route.Path, allHandlers...)
		case "PUT":
			e.PUT(route.Path, allHandlers...)
		}
	}
}

// PrintRouteTreeDFS 深度优先遍历并打印路由树
func PrintRouteTreeDFS(group *RouteGroup, indent int) {
	if group == nil {
		return
	}

	// 打印当前分组信息
	log.Printf("%sGroup: %s (%s)", strings.Repeat("  ", indent), group.Prefix, group.Description)

	// 打印当前分组下的路由
	for _, route := range group.Routes {
		log.Printf("%s  Route: %s %s (%s)", strings.Repeat("  ", indent), route.Method, route.Path, route.Name)
	}

	// 递归遍历子分组
	for _, child := range group.Children {
		PrintRouteTreeDFS(child, indent+1)
	}
}

// SyncPermissionsToDB 同步路由权限到数据库
func SyncPermissionsToDB() {
	dbPermissions := make(map[string]model.Permissions)

	// 获取数据库中所有现有权限
	var existingPermissions []model.Permissions
	if err := db.DB.Find(&existingPermissions).Error; err != nil {
		log.Printf("Failed to fetch existing permissions: %v", err)
		return
	}
	for _, p := range existingPermissions {
		dbPermissions[p.ID] = p
	}

	// 递归同步路由分组和路由权限
	syncRouteGroupAndPermissionsToDB(APIRootGroup, "0", dbPermissions, "", "") // 初始调用，apiVersion和module为空

	// 删除数据库中不再存在的权限
	for _, p := range dbPermissions {
		if err := db.DB.Delete(&p).Error; err != nil {
			log.Printf("Failed to delete old permission %s (Type: %s, Method: %s, Path: %s): %v", p.ID, p.Type, p.Method, p.Path, err)
		} else {
			log.Printf("Deleted old permission: Type: %s, Method: %s, Path: %s", p.Type, p.Method, p.Path)
		}
	}

	log.Println("Permissions synchronization completed.")
}

// syncRouteGroupAndPermissionsToDB 递归同步路由分组和路由权限到数据库
func syncRouteGroupAndPermissionsToDB(group *RouteGroup, parentID string, dbPermissions map[string]model.Permissions, apiVersion, module string) {
	if group == nil {
		return
	}

	// 处理当前分组作为权限
	groupPermissionID := group.ID
	groupPermission := model.Permissions{
		ID:         groupPermissionID,
		ParentID:   parentID,
		Name:       group.Description,
		Type:       "group", // 权限类型为分组
		Method:     "GROUP", // 特殊标记
		Path:       group.buildFullPath(""),
		Module:     module,
		APIVersion: apiVersion,
	}
	syncPermission(groupPermission, dbPermissions)

	// 处理当前分组下的路由
	for _, route := range group.Routes {
		permissionID := model.GeneratePermissionID(route.Method, route.Path)
		newPermission := model.Permissions{
			ID:         permissionID,
			ParentID:   groupPermissionID, // 父权限ID为当前分组的ID
			Name:       route.Name,
			Type:       "route", // 权限类型为路由
			Method:     route.Method,
			Path:       route.Path,
			Module:     module,
			APIVersion: apiVersion,
		}
		syncPermission(newPermission, dbPermissions)
	}

	// 递归处理子分组
	for _, child := range group.Children {
		currentAPIVersion := apiVersion
		currentModule := module

		// 如果当前分组是 APIRootGroup 的直接子分组，则其 Prefix 是 APIVersion
		if group == APIRootGroup {
			currentAPIVersion = child.Prefix
		} else if group.Parent == APIRootGroup {
			// 如果当前分组是 APIVersion 分组的子分组，则其 Prefix 是 Module
			currentModule = child.Prefix
		}

		syncRouteGroupAndPermissionsToDB(child, groupPermissionID, dbPermissions, currentAPIVersion, currentModule)
	}
}

// syncPermission 辅助函数，用于同步单个权限到数据库
func syncPermission(newPermission model.Permissions, dbPermissions map[string]model.Permissions) {
	if existingP, ok := dbPermissions[newPermission.ID]; ok {
		// 权限已存在，检查是否需要更新
		if existingP.Name != newPermission.Name ||
			existingP.Type != newPermission.Type || // 检查 Type 是否变更
			existingP.Method != newPermission.Method ||
			existingP.Path != newPermission.Path ||
			existingP.Module != newPermission.Module ||
			existingP.APIVersion != newPermission.APIVersion ||
			existingP.ParentID != newPermission.ParentID {
			if err := db.DB.Save(&newPermission).Error; err != nil {
				log.Printf("Failed to update permission %s (Type: %s, Method: %s, Path: %s): %v", newPermission.ID, newPermission.Type, newPermission.Method, newPermission.Path, err)
			} else {
				log.Printf("Updated permission: Type: %s, Method: %s, Path: %s", newPermission.Type, newPermission.Method, newPermission.Path)
			}
		}
		delete(dbPermissions, newPermission.ID)
	} else {
		// 权限不存在，插入新权限
		if err := db.DB.Create(&newPermission).Error; err != nil {
			log.Printf("Failed to create permission %s (Type: %s, Method: %s, Path: %s): %v", newPermission.ID, newPermission.Type, newPermission.Method, newPermission.Path, err)
		} else {
			log.Printf("Created new permission: Type: %s, Method: %s, Path: %s", newPermission.Type, newPermission.Method, newPermission.Path)
		}
	}
}
