package middleware

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

func Cors() gin.HandlerFunc {
	return func(c *gin.Context) {

		origin := c.Request.Header.Get("Origin")
		if origin != "" {
			c.Writer.Header().Set("Access-Control-Allow-Origin", origin)
			c.<PERSON>.Header().Set("Access-Control-Allow-Credentials", "true")
		}

		c.<PERSON>.Header().Set("Access-Control-Allow-Headers", "Origin, Content-Length, Content-Type, Authorization, ClientID, AuthorizationAdmin,pwd")
		c.Writer.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.<PERSON>.Header().Set("Access-Control-Expose-Headers", "New-Token, New-Expires-In, Content-Disposition")
		c.<PERSON>.Header().Set("Access-Control-Max-Age", "7200")

		if c.Request.Method == http.MethodOptions {
			c.AbortWithStatus(http.StatusNoContent)
			return
		}

		c.Next()
	}
}
