package middleware

import (
	"fmt"
	"log"
	"net"
	"net/http"
	"strings"
	"tms/model"
	"tmsc/libs"
	"tmsc/pkg/cache"
	"tmsc/pkg/config"

	"github.com/gin-gonic/gin"
)

// 定义角色检查常量
const (
	RoleAll    = "*"  // 所有角色
	RoleBypass = "-1" // 绕过角色检查
)

func JwtVerify() gin.HandlerFunc {
	return func(c *gin.Context) {
		url := c.Request.URL.Path

		// 检查并跳过静态文件
		if isStaticFile(url) {
			c.Next()
			return
		}

		routeData, ok := getRouteData(url)
		if !ok {
			handleUnregisteredRoute(c, url)
			return
		}

		if routeData.AuthType == model.AuthNone {
			handleNoAuthRoute(c)
			return
		}

		token := extractToken(c)
		if token == "" {
			libs.ErrorLogin(c, "token is empty")
			c.Abort()
			return
		}

		user, err := verifyTokenAndSetUser(c, token)
		if err != nil {
			libs.ErrorLogin(c, "token is invalid")
			c.Abort()
			return
		}

		key := fmt.Sprintf("%d-token", user.ID)
		tokenBytes, _ := cache.FreeCache.Get([]byte(key))
		if string(tokenBytes) != token {
			libs.ErrorLogin(c, "token is invalid")
			c.Abort()
			return
		}
		uid := user.ID
		c.Set("userId", uid)

		c.Next()
	}
}

// isStaticFile 检查 URL 是否是静态文件
func isStaticFile(url string) bool {
	return strings.HasPrefix(url, "/static/") || strings.HasPrefix(url, "/upload/") ||
		strings.HasPrefix(url, "/views/") || strings.HasPrefix(url, "/os/") ||
		strings.HasPrefix(url, "/course-assets/") || strings.HasPrefix(url, "/courseware-assets/") ||
		strings.HasPrefix(url, "/resource-assets/")
}

// handleUnregisteredRoute 处理未注册路由
func handleUnregisteredRoute(c *gin.Context, url string) {
	ip := libs.GetIpAddress(c)
	log.Printf("未注册路由:%v, access ip: %v", url, ip)
	libs.Error(c, "未注册路由")
	c.Abort()
}

// handleNoAuthRoute 处理无需认证的路由
func handleNoAuthRoute(c *gin.Context) {
	c.Next() // 对于无需认证的路由，直接放行
}

// extractToken 从请求头或查询参数中提取 token
func extractToken(c *gin.Context) string {
	token := c.GetHeader("Authorization")
	if token == "" {
		token = c.Query("token")
	}
	return token
}

// verifyTokenAndSetUser 验证 token 并将用户信息设置到 context
func verifyTokenAndSetUser(c *gin.Context, token string) (*libs.UserClaims, error) {
	user, err := libs.ParseToken(token)
	if err != nil {
		return nil, err
	}
	c.Set("user", user) // 将解析出的用户信息存储在 context 中
	return user, nil
}

func getRouteData(url string) (Route, bool) {
	for _, route := range Routes {
		// 检查 URL 是否匹配动态路由
		matched, _ := MatchRoute(route.Path, url)
		// fmt.Printf("Pattern: %-30s Path: %-30s => Matched: %-5v Params: %v\n",
		// 	route.Path, url, matched, params)
		if matched {
			return route, true
		}
	}
	return Route{}, false
}

// MatchRoute 判断 path 是否匹配 pattern，同时返回参数映射
func MatchRoute(pattern, path string) (bool, map[string]string) {
	params := make(map[string]string)

	// 清理首尾 /
	pattern = strings.Trim(pattern, "/")
	path = strings.Trim(path, "/")

	// 分割成路径段
	patternParts := strings.Split(pattern, "/")
	pathParts := strings.Split(path, "/")

	// 长度不一致直接不匹配
	if len(patternParts) != len(pathParts) {
		return false, nil
	}

	// 遍历比对
	for i := 0; i < len(patternParts); i++ {
		pp := patternParts[i]
		pv := pathParts[i]

		if strings.HasPrefix(pp, ":") {
			// 动态参数
			paramName := pp[1:]
			params[paramName] = pv
		} else if pp != pv {
			// 静态不一致
			return false, nil
		}
	}

	return true, params
}

// CheckIp 验证客户端 IP 是否允许访问
func CheckIp(r *http.Request) bool {
	if strings.HasPrefix(r.URL.Scheme, "wails") {
		return true
	}

	clientIP := r.RemoteAddr
	host := r.Host
	ip, _, err := net.SplitHostPort(clientIP)
	if err != nil {
		//fmt.Printf("Error splitting host and port: %v\n", err)
		return false
	}

	// 解析 IP 地址
	ipAddress := net.ParseIP(ip)
	//fmt.Printf("clientIP:%v, host:%v\n", clientIP, host)

	// 允许本机访问
	if ipAddress.IsLoopback() || host == "localhost" {
		return true
	}

	// 获取允许的 IP 和域名列表
	ipAndDomainList := config.Config.System.IpAccess
	//fmt.Printf("ipAndDomainList:%v\n", ipAndDomainList)
	if len(ipAndDomainList) == 0 {
		return true
	}
	// 检查 IP 地址
	for _, allowed := range ipAndDomainList {
		if ipAddress.String() == allowed {
			return true
		}
		// 检查域名
		if host == allowed {
			return true
		}
	}

	return false
}
