package middleware

import (
	"strings"
	"time"
	"tmsc/pkg/config"
	"tmsc/pkg/logger"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// LoggerMiddleware 是一个使用 zap 的日志中间件
func LoggerMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取请求的 Host
		host := c.Request.Host
		// 获取请求的 Scheme
		scheme := "http"
		if c.Request.TLS != nil {
			scheme = "https"
		}
		config.Config.System.Host = host
		config.Config.System.Scheme = scheme
		url := c.Request.URL.Path
		// 检查 URL 是否以 /static/ 开头，如果是则跳过记录
		if strings.HasPrefix(url, "/static/") || strings.HasPrefix(url, "/upload/") {
			c.Next() // 继续后续处理
			return
		}
		startTime := time.Now()
		c.Next() // 执行后续的处理函数

		// 记录请求的信息
		logger.Logger.Info("Request completed",
			zap.String("method", c.Request.Method),
			zap.String("path", url),
			zap.Int("status", c.Writer.Status()),
			zap.Duration("duration", time.Since(startTime)),
		)
	}
}
