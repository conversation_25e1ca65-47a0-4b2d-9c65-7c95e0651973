package middleware

// 服务端的签名校验

// import (
// 	"bytes"
// 	"crypto/hmac"
// 	"crypto/sha256"
// 	"encoding/base64"
// 	"fmt"
// 	"io"
// 	"net/http"
// 	"sort"
// 	"strconv"
// 	"strings"
// 	"sync"
// 	"time"

// 	"github.com/gin-gonic/gin"
// )

// // --- Nonce Store (In-Memory Implementation) ---
// // NOTE: For production, especially in a distributed environment,
// // replace this with a shared cache like Redis.
// type nonceStore struct {
// 	sync.RWMutex
// 	store map[string]time.Time
// }

// var nonces = &nonceStore{store: make(map[string]time.Time)}

// // Add stores a nonce with its timestamp.
// func (ns *nonceStore) Add(nonce string) {
// 	ns.Lock()
// 	defer ns.Unlock()
// 	ns.store[nonce] = time.Now()
// }

// // Exists checks if a nonce exists.
// func (ns *nonceStore) Exists(nonce string) bool {
// 	ns.RLock()
// 	defer ns.RUnlock()
// 	_, exists := ns.store[nonce]
// 	return exists
// }

// // Cleanup removes expired nonces.
// func (ns *nonceStore) Cleanup(expiration time.Duration) {
// 	ns.Lock()
// 	defer ns.Unlock()
// 	if len(ns.store) == 0 {
// 		return
// 	}

// 	for nonce, timestamp := range ns.store {
// 		if time.Since(timestamp) > expiration {
// 			delete(ns.store, nonce)
// 		}
// 	}
// }

// // --- Middleware ---

// const (
// 	// DefaultTimeWindow is the acceptable time difference between client and server.
// 	DefaultTimeWindow = 5 * time.Minute
// )

// // SecretProvider defines a function type that retrieves an AppSecret for a given AppKey.
// // It should return the secret and a boolean indicating if the key was found.
// type SecretProvider func(appKey string) (string, bool)

// // VerifySignature creates a Gin middleware for verifying HMAC request signatures.
// func VerifySignature(provider SecretProvider) gin.HandlerFunc {
// 	// Start a background goroutine to clean up expired nonces periodically.
// 	go func() {
// 		for {
// 			time.Sleep(10 * time.Minute)
// 			nonces.Cleanup(DefaultTimeWindow)
// 		}
// 	}()

// 	return func(c *gin.Context) {
// 		// 1. Extract headers
// 		appKey := c.GetHeader("X-App-Key")
// 		timestampStr := c.GetHeader("X-Timestamp")
// 		nonce := c.GetHeader("X-Nonce")
// 		clientSignature := c.GetHeader("X-Signature")

// 		if appKey == "" || timestampStr == "" || nonce == "" || clientSignature == "" {
// 			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "Missing signature headers"})
// 			return
// 		}

// 		// 2. Validate Timestamp
// 		timestamp, err := strconv.ParseInt(timestampStr, 10, 64)
// 		if err != nil {
// 			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "Invalid timestamp format"})
// 			return
// 		}
// 		requestTime := time.Unix(timestamp, 0)
// 		if time.Since(requestTime) > DefaultTimeWindow {
// 			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "Timestamp expired"})
// 			return
// 		}

// 		// 3. Validate Nonce
// 		if nonces.Exists(nonce) {
// 			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "Replay attack detected (nonce already used)"})
// 			return
// 		}
// 		nonces.Add(nonce)

// 		// 4. Get AppSecret
// 		appSecret, ok := provider(appKey)
// 		if !ok {
// 			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "Invalid App Key"})
// 			return
// 		}

// 		// 5. Reconstruct the string to sign
// 		// Read the body and then replace it for subsequent handlers.
// 		bodyBytes, _ := io.ReadAll(c.Request.Body)
// 		c.Request.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))

// 		sortedQuery := sortQuery(c.Request.URL.Query())

// 		stringToSign := fmt.Sprintf("%s\n%s\n%s\n%s\n%s\n%s",
// 			c.Request.Method,
// 			c.Request.URL.Path,
// 			sortedQuery,
// 			timestampStr,
// 			nonce,
// 			string(bodyBytes),
// 		)

// 		// 6. Generate server-side signature
// 		serverSignature := generateSignature(stringToSign, appSecret)

// 		// 7. Compare signatures
// 		if clientSignature != serverSignature {
// 			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "Invalid signature"})
// 			return
// 		}

// 		c.Next()
// 	}
// }

// // sortQuery sorts the query parameters by key and returns a URL-encoded string.
// func sortQuery(query map[string][]string) string {
// 	if len(query) == 0 {
// 		return ""
// 	}

// 	keys := make([]string, 0, len(query))
// 	for k := range query {
// 		keys = append(keys, k)
// 	}
// 	sort.Strings(keys)

// 	var sortedParts []string
// 	for _, k := range keys {
// 		for _, v := range query[k] {
// 			sortedParts = append(sortedParts, k+"="+v)
// 		}
// 	}

// 	return strings.Join(sortedParts, "&")
// }

// // generateSignature creates an HMAC-SHA256 signature and returns it as a Base64 encoded string.
// func generateSignature(stringToSign, secret string) string {
// 	h := hmac.New(sha256.New, []byte(secret))
// 	h.Write([]byte(stringToSign))
// 	return base64.StdEncoding.EncodeToString(h.Sum(nil))
// }
