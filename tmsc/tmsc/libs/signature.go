package libs

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"net/url"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/google/uuid"
)

// Signer holds the credentials required to sign an API request.
type Signer struct {
	AppKey    string
	AppSecret string
}

// NewSigner creates a new Signer with the given AppKey and AppSecret.
func NewSigner(appKey, appSecret string) *Signer {
	return &Signer{
		AppKey:    appKey,
		AppSecret: appSecret,
	}
}

// Sign generates the necessary headers for a signed API request.
// It takes the HTTP method, the full request URL, and the request body as input.
func (s *Signer) Sign(method, requestURL string, body interface{}) (map[string]string, error) {
	// 1. Parse the URL to separate the path and query parameters.
	u, err := url.Parse(requestURL)
	if err != nil {
		return nil, fmt.Errorf("failed to parse request URL: %w", err)
	}

	// 2. Prepare the components for the signature.
	method = strings.ToUpper(method)
	path := u.Path
	sortedQuery := s.sortQuery(u.Query())
	timestamp := strconv.FormatInt(time.Now().Unix(), 10)
	nonce := uuid.New().String()

	// 3. Handle the request body.
	bodyString := ""
	if body != nil {
		// Marshal the body to a JSON string.
		bodyBytes, err := json.Marshal(body)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal body: %w", err)
		}
		bodyString = string(bodyBytes)
	}

	// 4. Create the string to sign.
	stringToSign := fmt.Sprintf("%s\n%s\n%s\n%s\n%s\n%s",
		method,
		path,
		sortedQuery,
		timestamp,
		nonce,
		bodyString,
	)

	// 5. Generate the HMAC-SHA256 signature.
	signature := s.generateSignature(stringToSign)

	// 6. Return the headers.
	headers := map[string]string{
		"X-App-Key":   s.AppKey,
		"X-Timestamp": timestamp,
		"X-Nonce":     nonce,
		"X-Signature": signature,
	}

	return headers, nil
}

// sortQuery sorts the query parameters by key and returns a URL-encoded string.
func (s *Signer) sortQuery(query url.Values) string {
	if len(query) == 0 {
		return ""
	}

	keys := make([]string, 0, len(query))
	for k := range query {
		keys = append(keys, k)
	}
	sort.Strings(keys)

	var sortedParts []string
	for _, k := range keys {
		// URL-encode both key and value to be safe.
		// Note: GCM's query string is typically already encoded, but re-encoding is safe.
		encodedKey := url.QueryEscape(k)
		for _, v := range query[k] {
			sortedParts = append(sortedParts, encodedKey+"="+url.QueryEscape(v))
		}
	}

	return strings.Join(sortedParts, "&")
}

// generateSignature creates an HMAC-SHA256 signature and returns it as a Base64 encoded string.
func (s *Signer) generateSignature(stringToSign string) string {
	h := hmac.New(sha256.New, []byte(s.AppSecret))
	h.Write([]byte(stringToSign))
	return base64.StdEncoding.EncodeToString(h.Sum(nil))
}
