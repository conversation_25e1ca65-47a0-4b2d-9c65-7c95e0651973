package libs

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
)

// RemoteResponse is the generic structure for API responses from the remote server.
// It uses json.RawMessage to delay the parsing of the 'data' field.
type RemoteResponse struct {
	Code    int             `json:"code"`
	Success bool            `json:"success"`
	Message string          `json:"message"`
	Data    json.RawMessage `json:"data"`
}

// doRequest handles the common logic for making HTTP requests and parsing the standard API response.
func doRequest(method, url string, headers map[string]string, body interface{}, result interface{}, client *http.Client) error {
	var reqBody io.Reader

	// If a body is provided, marshal it to JSON.
	if body != nil {
		jsonBody, err := json.Marshal(body)
		if err != nil {
			return fmt.Errorf("failed to marshal request body: %w", err)
		}
		reqBody = bytes.NewBuffer(jsonBody)
	}

	// Create a new HTTP request.
	req, err := http.NewRequest(method, url, reqBody)
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}
	// Set default and custom headers.
	req.Header.Set("Content-Type", "application/json")
	for key, value := range headers {
		req.Header.Set(key, value)
	}

	// Send the request using the provided or default HTTP client.
	if client == nil {
		client = http.DefaultClient
	}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	// Read the response body.
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("failed to read response body: %w", err)
	}

	// Unmarshal the response into our generic response structure.
	var apiResp RemoteResponse
	if err := json.Unmarshal(respBody, &apiResp); err != nil {
		return fmt.Errorf("failed to unmarshal response: %w", err)
	}

	// Check if the API call was successful.
	if !apiResp.Success || apiResp.Code != 0 {
		return fmt.Errorf("api error: %s", apiResp.Message)
	}

	// If the API call was successful, unmarshal the 'data' field into the provided result pointer.
	if result != nil && len(apiResp.Data) > 0 && string(apiResp.Data) != "null" {
		if err := json.Unmarshal(apiResp.Data, result); err != nil {
			return fmt.Errorf("failed to unmarshal response data: %w", err)
		}
	}

	return nil
}

// Get sends a GET request to the specified URL and unmarshals the 'data' field
// from the successful response into the result pointer.
func Get(url string, headers map[string]string, result interface{}, client ...*http.Client) error {
	var c *http.Client
	if len(client) > 0 {
		c = client[0]
	}
	return doRequest(http.MethodGet, url, headers, nil, result, c)
}

// Post sends a POST request to the specified URL with the given body,
// and unmarshals the 'data' field from the successful response into the result pointer.
func Post(url string, headers map[string]string, body interface{}, result interface{}, client ...*http.Client) error {
	var c *http.Client
	if len(client) > 0 {
		c = client[0]
	}
	return doRequest(http.MethodPost, url, headers, body, result, c)
}

// Put sends a PUT request to the specified URL with the given body,
// and unmarshals the 'data' field from the successful response into the result pointer.
func Put(url string, headers map[string]string, body interface{}, result interface{}) error {
	return doRequest(http.MethodPut, url, headers, body, result, nil)
}

// Delete sends a DELETE request to the specified URL. It can optionally include a body.
// The 'data' field from the response is unmarshalled into the result pointer.
func Delete(url string, headers map[string]string, body interface{}, result interface{}) error {
	return doRequest(http.MethodDelete, url, headers, body, result, nil)
}
