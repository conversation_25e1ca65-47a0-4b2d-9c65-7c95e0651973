package cmd

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"strconv"
	"syscall"
	"time"
	"tms/model"
	_ "tmsc/app"

	"tmsc/middleware"
	"tmsc/pkg/cache"
	"tmsc/pkg/config"
	"tmsc/pkg/db"
	"tmsc/pkg/logger"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

var Server *http.Server

func Start() {
	err := config.LoadSystemConfig()
	if err != nil {
		fmt.Println("Failed to load system config:", err)
		os.Exit(1)
		return
	}

	err = db.InitDatabase()
	if err != nil {
		fmt.Println("Failed to initialize database:", err)
		os.Exit(1)
		return
	}

	db.DB.AutoMigrate(
		&model.Users{},
		&model.Roles{},
		&model.Menus{},
		&model.Permissions{},
		&model.RolePermissions{},
		&model.RoleMenu{},
		&model.UserRoles{},
		&model.UserCodes{},
		&model.SyncRecords{},

		&model.Class{},
		&model.Majors{},
		&model.StudentMap{},
		&model.TeachingPlan{},
		&model.TeachingPlanExt{},
		&model.Courses{},
		&model.Chapter{},
		&model.Courseware{},
		&model.CoursewareExt{},
		&model.QuestionsExt{},
		&model.Questions{},

		&model.Exams{},
		&model.ExamsExt{},
		&model.ExamProgress{},
		&model.Papers{},
		&model.PaperQuestions{},
		&model.ExamAnswer{},

		&model.ScormSessions{},

		&model.ResourceCategory{},
		&model.Resources{},
		&model.ResourceDownloadLog{},

		&model.VirtualCoursewareSubmitHistory{},
	)

	cache.New()

	err = logger.InitLogger()
	if err != nil {
		fmt.Println("Failed to initialize logger:", err)
		os.Exit(1)
		return
	}

	if config.Config.System.Debug {
		gin.SetMode(gin.DebugMode)
	} else {
		gin.SetMode(gin.ReleaseMode)
	}
	r := gin.Default()

	// 处理异常
	// r.NoRoute(middleware.HandleNotFound)
	// r.NoMethod(middleware.HandleNotFound)
	r.Use(middleware.Recover)

	// 使用中间件
	r.Use(middleware.Cors())
	r.Use(middleware.LoggerMiddleware())
	// store := middleware.GetSessionStore()
	// r.Use(sessions.Sessions("godoSession", store))
	r.Use(middleware.JwtVerify())
	middleware.BindRouter(r)

	// 将端口号转换为字符串
	portStr := strconv.Itoa(config.Config.System.Port)

	// 创建并返回 http.Server 实例
	Server = &http.Server{
		Addr:    ":" + portStr,
		Handler: r,
	}
	go func() {
		if err := Server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logger.Logger.Error("Failed to start server:", zap.Error(err))
			os.Exit(1)
		}
	}()

	logger.Logger.Info("Server started on port", zap.String("port", portStr))
	// 监听信号来重启服务
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, os.Interrupt, syscall.SIGTERM, syscall.SIGHUP)

	go func() {
		for sig := range sigChan {
			switch sig {
			case os.Interrupt, syscall.SIGTERM:
				logger.Logger.Info("Received SIGTERM or SIGINT, shutting down...")
				Shutdown()
				os.Exit(0)
			case syscall.SIGHUP:
				logger.Logger.Info("Received SIGHUP, restarting...")
				Restart()
			default:
				logger.Logger.Info("Received unknown signal:", zap.Any("signal", sig))
			}
		}
	}()

	// 主循环
	select {}
}
func Restart() {
	if Server == nil {
		logger.Logger.Error("Server is not running.")
		return
	}
	// 关闭当前服务
	Shutdown()

	// 重新启动服务
	Start()
}

// 关闭 HTTP 服务器
func Shutdown() {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// 关闭服务器
	if err := Server.Shutdown(ctx); err != nil {
		logger.Logger.Error("Failed to shutdown server:", zap.Error(err))
	} else {
		logger.Logger.Info("Server gracefully shutdown")
	}
}
