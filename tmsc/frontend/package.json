{"name": "<PERSON><PERSON><PERSON>", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "axios": "^1.10.0", "echarts": "^5.6.0", "element-plus": "^2.10.2", "pinia": "^3.0.3", "pinia-plugin-persistedstate": "^4.4.1", "vue": "^3.5.13", "vue-router": "^4.5.1"}, "devDependencies": {"@tailwindcss/vite": "^4.1.10", "@types/node": "^24.0.4", "@vitejs/plugin-vue": "^5.2.3", "@vue/tsconfig": "^0.7.0", "tailwindcss": "^4.1.10", "typescript": "~5.8.3", "unplugin-auto-import": "^19.3.0", "unplugin-vue-components": "^28.7.0", "vite": "^6.3.5", "vue-tsc": "^2.2.8"}}