<template>
    <div class="flex-1 flex flex-col overflow-y-auto">
        <div class="text-gray-700 mb-2">
            {{ content.title }}
            <span>({{ question.score }}分)</span>
        </div>
        <el-radio-group class="flex flex-col space-y-3" v-model="question.answer">
            <el-radio class="!mr-0 w-full" value="false" label="错" />
            <el-radio class="!mr-0 w-full" value="true" label="对" />
        </el-radio-group>
    </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

const props = defineProps<{
    question: any
}>()
const content = computed(() => JSON.parse(props.question.content))

</script>

<style scoped></style>