<template>
    <div class="fixed inset-0 z-50 flex items-center justify-center bg-black">
        <div class="relative w-full max-w-3xl h-[80vh] bg-gray-900 rounded-lg shadow-xl overflow-hidden flex flex-col">
            <!-- 顶部标题区 -->
            <div class="py-6 px-6 border-b border-gray-700">
                <h2 class="text-2xl font-bold text-center text-gray-100 tracking-wide">考试须知</h2>
            </div>
            <!-- 中部内容区 -->
            <div class="flex-1 overflow-y-auto px-6 py-6">
                <div class="space-y-6 text-gray-300">
                    <div class="flex items-start">
                        <div
                            class="flex-shrink-0 w-7 h-7 rounded-full bg-blue-900 text-blue-400 flex items-center justify-center mr-4 mt-0.5 font-semibold">
                            1</div>
                        <p class="text-lg leading-relaxed tracking-wide">考试时间为{{ paperStore.paper.total_minutes }}
                            分钟，请合理安排答题时间。</p>
                    </div>
                    <div class="flex items-start">
                        <div
                            class="flex-shrink-0 w-7 h-7 rounded-full bg-blue-900 text-blue-400 flex items-center justify-center mr-4 mt-0.5 font-semibold">
                            2</div>
                        <p class="text-lg leading-relaxed tracking-wide">考试过程中 <span
                                class="font-bold text-red-400">禁止切换浏览器标签页</span>，系统将自动监控并记录违规行为。</p>
                    </div>
                    <div class="flex items-start">
                        <div
                            class="flex-shrink-0 w-6 h-6 rounded-full bg-blue-100 text-blue-600 flex items-center justify-center mr-3 mt-0.5">
                            3</div>
                        <p>考试期间 <span class="font-bold text-red-500">禁止使用任何通讯工具</span>，包括但不限于手机、即时通讯软件等。</p>
                    </div>
                    <div class="flex items-start">
                        <div
                            class="flex-shrink-0 w-6 h-6 rounded-full bg-blue-100 text-blue-600 flex items-center justify-center mr-3 mt-0.5">
                            4</div>
                        <p>考试结束后，系统将自动提交试卷，请勿提前关闭浏览器。</p>
                    </div>
                    <div class="flex items-start">
                        <div
                            class="flex-shrink-0 w-6 h-6 rounded-full bg-blue-100 text-blue-600 flex items-center justify-center mr-3 mt-0.5">
                            5</div>
                        <p>如遇技术问题，请立即联系监考老师</p>
                    </div>
                </div>
            </div>
            <!-- 底部确认区 -->
            <div class="py-6 px-6 border-t border-gray-700 bg-gray-800">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <input id="agree-checkbox" v-model="isAgreed" type="checkbox"
                            class="w-6 h-6 text-blue-500 rounded border-gray-600 bg-gray-700 focus:ring-blue-500">
                        <label for="agree-checkbox" class="ml-3 text-gray-300 text-lg tracking-wide">我已阅读并同意考试须知</label>
                    </div>
                    <button @click="startExam" :disabled="!isAgreed"
                        :class="{ 'bg-blue-500 hover:bg-blue-600': isAgreed, 'bg-gray-600 cursor-not-allowed': !isAgreed }"
                        class="!rounded-button whitespace-nowrap px-8 py-3 text-white font-medium text-lg tracking-wide transition-colors">
                        开始考试
                    </button>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import { usePaperStore } from "@/store/paper";

const emit = defineEmits(["confirm"]);

//paper store
const paperStore = usePaperStore();
//是否同意
const isAgreed = ref(false);

/** 阅读手册 确认开始考试 */
const startExam = () => {
    emit("confirm")
};
</script>

<style scoped>
/* 自定义滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #1f2937;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #4b5563;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #6b7280;
}
</style>