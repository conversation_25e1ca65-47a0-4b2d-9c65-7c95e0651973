<template>
    <div class="flex-1 flex flex-col overflow-y-auto">
        <div class="text-gray-700 mb-4">
            {{ content.content }}
            <span>({{ question.score }}分)</span>
        </div>
        <el-input v-model="value" type="textarea" placeholder="请输入答案" :rows="8" resize="none" @blur="changeAnswer" />
    </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue';

const props = defineProps<{
    question: any
}>()
const content = computed(() => JSON.parse(props.question.content))

const value = ref('')
const changeAnswer = () => {
    props.question.answer = value.value
}
watch(() => props.question.answer, () => {
    value.value = props.question.answer
}, {
    immediate: true
})
</script>

<style scoped></style>