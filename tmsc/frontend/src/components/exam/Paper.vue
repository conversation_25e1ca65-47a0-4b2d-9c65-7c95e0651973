<template>
    <el-skeleton :rows="10" animated v-if="loading" />
    <div class="flex-1 flex overflow-hidden" v-else>
        <ExamTips v-if="!paperStore.is_confirm" @confirm="paperStore.is_confirm = true" />
        <div class="flex-1 bg-gray-50 flex flex-col overflow-hidden" v-else>
            <!-- 顶部考试信息区 -->
            <div class="bg-white shadow-sm py-4 px-6 border-b border-gray-200">
                <div class=" flex justify-between items-center">
                    <h1 class="text-xl font-semibold text-gray-800">{{ paperStore.paper.title }}</h1>
                    <div class="flex items-center mt-1 space-x-4 text-sm">
                        <div class="flex items-center">
                            <el-icon class="mr-1">
                                <Clock />
                            </el-icon>
                            <div class="text-red-500 font-medium">剩余时间: </div>
                            <el-countdown @finish="handleFinish" class="ml-1" format="HH:mm:ss" :value="time"
                                :value-style="{ 'color': 'red' }" />
                        </div>
                        <div class="text-gray-600">总分: {{ paperStore.paper.total_score }}分</div>
                        <div class="text-gray-600">题量: {{ paperStore.paper.question_count }}题</div>
                        <div class="text-green-600 font-medium">状态: 进行中</div>
                    </div>

                </div>
            </div>

            <div class="flex flex-1 overflow-hidden p-6 ">
                <!-- 左侧题目导航栏 -->
                <div
                    :class="['w-64 bg-white rounded-lg shadow-sm flex flex-col mr-2 transition-all duration-300 ease-in-out', { '!w-0': !showSidebar }]">
                    <div class="flex-1 overflow-y-auto">
                        <div class="px-2 py-4">
                            <el-scrollbar height="100%">
                                <div class="grid grid-cols-4 gap-3">

                                    <div v-for="(item, index) in paperStore.questions" :key="index" @click="i = index"
                                        class="h-12 w-12 flex items-center justify-center rounded border cursor-pointer transition-colors"
                                        :class="{
                                            'border-blue-300 border-2': i === index,
                                            'bg-blue-100  border-blue-300': item.answer,
                                            ' border-gray-300': !item.answer && i !== index,
                                            'bg-orange-100 border-orange-300': item.marked
                                        }">
                                        {{ index + 1 }}
                                    </div>
                                </div>
                            </el-scrollbar>
                        </div>
                    </div>
                    <div class="p-3 border-t border-gray-200">
                        <el-button class="w-full !rounded-button whitespace-nowrap" @click="toggleSidebar">
                            <el-icon size="24" v-if="!showSidebar" class="mr-1">
                                <Expand />
                            </el-icon>
                            <el-icon v-else class="mr-1">
                                <Fold />
                            </el-icon>
                            收起导航
                        </el-button>
                    </div>
                </div>

                <!-- 主答题区域 -->
                <div class="flex-1 overflow-hidden  flex flex-col">

                    <div class="flex-1 bg-white rounded-lg shadow-sm pt-6 pb-2 px-6 flex flex-col overflow-hidden">
                        <!-- 顶部操作区 -->
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex-1 mr-4">
                                <div class="flex justify-between text-xs text-gray-500 mb-1">
                                    <span>已完成: {{ answeredCount }} / {{ paperStore.questions.length }}</span>
                                </div>
                                <el-progress :percentage="answeredPercentage"
                                    :color="answeredPercentage === 100 ? '#67C23A' : '#409EFF'" />
                            </div>
                            <div class="space-x-3">
                                <el-button @click="handleSubmit('submit')" type="success"
                                    class="!rounded-button whitespace-nowrap">
                                    <el-icon class="mr-1">
                                        <Finished />
                                    </el-icon>
                                    提交试卷
                                </el-button>
                            </div>
                        </div>
                        <!-- 题目内容 -->
                        <div class="mb-6 flex-1 flex flex-col overflow-hidden">
                            <div class="flex gap-4 items-center mb-4">
                                <h2 class="text-lg font-medium text-gray-800">
                                    第 {{ i + 1 }} 题
                                    <span class="text-sm text-gray-500 ml-2">({{
                                        getType(paperStore.questions[i]) }})</span>
                                </h2>
                                <el-button size="small" :type="paperStore.questions[i].marked ? 'warning' : 'default'"
                                    @click="toggleMark" class="!rounded-button whitespace-nowrap">
                                    <el-icon class="mr-1">
                                        <Star />
                                    </el-icon>
                                    {{ paperStore.questions[i].marked ? '取消标记' : '标记此题' }}
                                </el-button>
                            </div>

                            <div class="flex-1 mb-4 flex overflow-hidden">
                                <single :question="paperStore.questions[i]" v-if="currentType == 'single'" />
                                <multiple :question="paperStore.questions[i]" v-if="currentType == 'multiple'" />
                                <judge :question="paperStore.questions[i]" v-if="currentType == 'judge'" />
                                <fill :question="paperStore.questions[i]" v-if="currentType == 'fill'" />
                                <short :question="paperStore.questions[i]"
                                    v-if="currentType == 'short' || currentType == 'discuss' || currentType == 'analyze' || currentType == 'comprehensive' || currentType == 'self'" />
                            </div>

                        </div>

                        <!-- 答题控制按钮 -->
                        <div class="flex justify-between pt-2 border-t border-gray-200">
                            <div>
                                <el-button :disabled="i === 0" @click="prevQuestion"
                                    class="!rounded-button whitespace-nowrap">
                                    <el-icon class="mr-1">
                                        <ArrowLeft />
                                    </el-icon>
                                    上一题
                                </el-button>
                                <el-button :disabled="i === paperStore.questions.length - 1" @click="nextQuestion"
                                    class="!rounded-button whitespace-nowrap">
                                    下一题
                                    <el-icon class="ml-1">
                                        <ArrowRight />
                                    </el-icon>
                                </el-button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onBeforeUnmount } from "vue";
import { useUserStore } from "@/store/user";
import { usePaperStore } from "@/store/paper";
import { msg, confirmMsg as confirm, warningMsg, confirmMsg } from "@/utils/msg";
import { typeMap, examPaper, examSubmit } from "@/api/examine";
import { ElMessageBox } from "element-plus";


const emit = defineEmits(["submit"]);

const userStore = useUserStore();
const paperStore = usePaperStore();
//加载中
const loading = ref<boolean>(false);

//当前题型
const currentType = computed(() => {
    const content = JSON.parse(paperStore.questions[i.value].content)
    return content.question_type || content.courseware_type
})
//当前题目索引
const i = ref<number>(0);
//提交提示文本
const submitText = ref<string>("您确定要提交试卷吗？提交后将无法继续作答。");
//显示题目导航
const showSidebar = ref<boolean>(true);
// 倒计时
const time = computed(() => {
    return paperStore.end_at * 1000
})
/** 答题数量 */
const answeredCount = computed(() => {
    return paperStore.questions.filter((q: any) => q.answer).length;
});
/** 答题进度 */
const answeredPercentage = computed(() => {
    return Math.round((answeredCount.value / paperStore.questions.length) * 100);
});


const getType = (item: any) => {
    const content = JSON.parse(item.content || '');
    return typeMap[content.question_type || content.courseware_type];
}

// 提交答卷
const submit = async (status: 'commit' | 'terminate') => {
    const answers = paperStore.questions.map((q: any) => {
        return {
            question_id: q.question_id,
            question_type: JSON.parse(q.content).question_type,
            question_category: q.question_category,
            user_answer: JSON.stringify(q.answer),
            answered_at: Math.floor(new Date().getTime() / 1000) //秒级时间戳
        }
    })
    const data = {
        id: paperStore.progress_id,
        answers,
        submit_status: status
    }
    // console.log(data, 'data-----')
    const res = await examSubmit(data)
    console.log(res, 'res-----')
}
/** 上一题 */
const prevQuestion = () => {
    if (i.value > 0) {
        i.value--;
    }
    submit('commit');
};

/** 下一题 */
const nextQuestion = () => {
    if (i.value < paperStore.questions.length - 1) {
        i.value++;
    }
    submit('commit');
};

/** 显示题目导航 */
const toggleSidebar = () => {
    showSidebar.value = !showSidebar.value;
};

/** 切换题目标记 */
const toggleMark = () => {
    paperStore.questions[i.value].marked = !paperStore.questions[i.value].marked;
};

/** 考试计时完成 提交考试 */
const handleFinish = () => {
    handleSubmit('finish');
}

/** 提交试卷 */
const handleSubmit = async (str: string) => {
    const count = paperStore.questions.filter((q: any) => !q.answer).length;
    if (count > 0) {
        submitText.value = `您还有${count}道题未完成,` + submitText.value
    }
    if (str == 'submit' || str == 'close') {
        // 手动点击提交或关闭考试页面
        confirmMsg(submitText.value).then(async (action) => {
            if (action) {
                await submit('terminate');
                paperStore.initPaper()
                emit("submit");
            } else {
                submitText.value = "您确定要提交试卷吗？提交后将无法继续作答。";
            }
        })

    } else if (str == 'finish') {
        ElMessageBox.close();
        // 时间结束，自动提交
        ElMessageBox.confirm("答题时间结束，自动提交试卷", "通知", {
            confirmButtonText: "确定",
            type: "warning",
            center: true,
            showCancelButton: false,
            closeOnClickModal: false,
            showClose: false,
            closeOnPressEscape: false,
        })
        await submit('terminate');
        paperStore.initPaper()
        emit("submit");
    }
};

const loadPaper = async () => {
    // 加载试卷数据
    loading.value = true;
    const res: any = await examPaper(paperStore.paper_id);
    const data = res.data || {}
    loading.value = false;
    paperStore.paper = data.papers || {};
    paperStore.questions = data.paper_questions || {};
}

// 添加页面可见性检测
const handleVisibilityChange = () => {
    if (document.hidden) {
        // 页面被隐藏，记录离开时间
        console.log('用户离开了考试页面')
        // 可以在这里添加记录用户离开行为的逻辑
        // 例如显示警告或记录日志
    } else {
        // 页面重新显示
        console.log('用户回到考试页面')
        // 可以在这里检查是否超时等
    }
}

// 添加窗口焦点检测
const handleWindowBlur = () => {
    console.log('考试窗口失去焦点')
}

const handleWindowFocus = () => {
    console.log('考试窗口获得焦点')
}



onMounted(() => {
    console.log("mounted")
    if (!paperStore.questions.length) {
        console.log("loadPaper")
        loadPaper()
    }
    // 添加事件监听器
    document.addEventListener('visibilitychange', handleVisibilityChange)
    window.addEventListener('blur', handleWindowBlur)
    window.addEventListener('focus', handleWindowFocus)
})

onBeforeUnmount(() => {
    document.removeEventListener('visibilitychange', handleVisibilityChange)
    window.removeEventListener('blur', handleWindowBlur)
    window.removeEventListener('focus', handleWindowFocus)
})
defineExpose({
    handleSubmit
});
</script>

<style scoped></style>