<template>
    <div class="question py-3 hover:bg-gray-50">
        <div class="flex items-start gap-3">
            <el-icon size="24" class="!text-blue-500 mt-1">
                <Tickets />
            </el-icon>
            <div class="flex-1">
                <div class="flex items-center gap-2">
                    <div class="flex items-center cursor-pointer" @click="emit('open')">
                        <span class="text-gray-700">{{ material.name }}</span>
                        <span class="px-2 py-1 ml-4 text-xs bg-blue-100 text-blue-600 rounded whitespace-nowrap">
                            {{ material.ext.slice(1) }}
                        </span>
                    </div>
                </div>
            </div>
            <div>
                <button class="text-gray-400 hover:text-gray-600" @click.stop="emit('download')">
                    <el-icon size="20">
                        <Download />
                    </el-icon>
                </button>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
defineProps<{
    material: any
}>();
const emit = defineEmits<{
    open: [];
    download: []
}>();
</script>

<style lang="scss" scoped></style>