<template>
    <div class="question py-3 hover:bg-gray-50">
        <div class="flex items-start gap-3">
            <el-icon size="24" class="!text-green-500 mt-1">
                <EditPen />
            </el-icon>
            <div class="flex-1">
                <div class="flex items-center gap-2">
                    <div class="flex items-center cursor-pointer" @click="emit('open')">
                        <span class="text-gray-700">{{ question.title }}</span>
                        <span class="px-2 py-0.5 ml-4 font-bold text-[14px] bg-gray-100 rounded whitespace-nowrap"
                            :style="{ color: typeStatusMap[question.question_type] }">
                            {{ typeMap[question.question_type] }}
                        </span>
                    </div>
                </div>
                <div class="flex items-center text-[14px] text-gray-600 mt-1">
                    <span class="mr-4">分值：{{ question.score }}</span>
                    <span class="mr-4">时长：{{ question.minutes }}分钟</span>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { typeMap, typeStatusMap } from "@/api/examine";

defineProps<{
    question: any
}>();
const emit = defineEmits<{
    open: []
}>();
</script>

<style lang="scss" scoped></style>