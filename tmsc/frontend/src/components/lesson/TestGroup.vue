<template>
    <div class="test-group">
        <div class="test-group-wrapper flex flex-col">
            <div class="test-group-item py-4" v-for="(question, index) in questions" :key="question.id">
                <TestSingle v-model="question.answer" :question="question" :index="index"
                    v-if="question.question_type == 'single'" />
                <TestMultiple v-model="question.answer" :question="question" :index="index"
                    v-else-if="question.question_type == 'multiple'" />
                <TestJudge v-model="question.answer" :question="question" :index="index"
                    v-else-if="question.question_type == 'judge'" />
                <TestFill v-model="question.answer" :question="question" :index="index"
                    v-else-if="question.question_type == 'fill'" />
                <TestShort v-model="question.answer" :question="question" :index="index" v-else />
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import type { Question } from "@/api/examine";
import TestShort from "./TestShort.vue";

const props = defineProps<{
    questions: Question[];
}>();
</script>

<style scoped></style>