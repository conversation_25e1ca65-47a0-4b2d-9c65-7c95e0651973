<template>
    <div class="test-single flex flex-col">
        <div class="test-single-title mb-6 flex flex-wrap items-center">
            <span class="num">{{ index + 1 }}.</span>
            <div class="ml-2 py-2" v-for="(item, index) in parsedContent" :key="index">
                <span class="whitespace-nowrap" v-if="!item.isInput">{{ item.text }}</span>
                <el-input class="px-2 min-w-[90px]" v-model.trim="answerList[index]" v-else placeholder="请输入答案"
                    @blur="handleBlur(answerList[index])" />
            </div>
            <span class="score ml-2 whitespace-nowrap">({{ question.score }}分)</span>
            <span class="ml-4 font-bold text-[14px] whitespace-nowrap"
                :style="{ color: typeStatusMap[question.question_type] }">
                {{ typeMap[question.question_type] }}
            </span>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { typeMap, typeStatusMap, type Question } from "@/api/examine";

const props = defineProps<{
    modelValue: string;
    question: Question;
    index: number;
}>();
const emit = defineEmits(["update:modelValue"]);

const answerList = ref<string[]>([]);
const parsedContent = computed(() => {
    const fill_content = props.question.content;
    const arr = [];
    const reg = /{content}/g;
    let i = 0;
    let match;

    while ((match = reg.exec(fill_content)) !== null) {
        if (match.index > i) {
            arr.push({ text: fill_content.slice(i, match.index), isInput: false });
        }
        arr.push({ text: '', isInput: true });
        i = match.index + match[0].length;
    }

    if (i < fill_content.length) {
        arr.push({ text: fill_content.slice(i), isInput: false });
    }

    return arr;
})
console.log(parsedContent.value, '---')
//答案model
const answer = computed({
    get() {
        return props.question.answer;
    },
    set(val) {
        emit("update:modelValue", val);
    }
});

const handleBlur = (value: any) => {
    // 如果 每个输入框都为空，则清空答案
    if (answerList.value.every((item: any) => !item)) props.question.answer = "";
    if (!value) return;
    props.question.answer = answerList.value.join(",");
}
</script>