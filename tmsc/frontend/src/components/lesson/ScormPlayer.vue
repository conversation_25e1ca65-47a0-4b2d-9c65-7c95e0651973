<template>
    <div class="scorm-player scorm-player-theory" v-loading="loading" element-loading-text="加载中...">
        <iframe v-if="scoHref" id="scoFrame" :src="scoHref" width="100%" height="100%"></iframe>
    </div>
</template>

<script setup lang="ts">
import { ref, watch } from "vue";
import { useUserStore } from "@/store/user";
import { initialize, navigate, getLastError, virtualNavigate } from "@/api/scormService";
import { injectSCORMAPI } from "@/scorm/scormApi";

const props = defineProps<{
    //课件类型
    type: string;
    //用户id
    userId: number;
    //章节id
    chapterId: number;
    //课件id
    scoId: number;
}>();

const userStore = useUserStore();
//会话id
const sessionId = ref<number | null>(null);
//课件地址
const scoHref = ref<string>("");
const loading = ref(true);
const base = "/courseware-assets";

watch(() => userStore.scormTerminated, newVal => {
    console.log("terminated", newVal);
    if (newVal) {
        navigatePage("continue");
    }
});

/** 虚拟课件导航 */
const virtualNavigatePage = async () => {
    try {
        const res = await virtualNavigate({
            courseware_id: props.scoId
        });
        if (res.code == 0) {
            scoHref.value = `${base}/${props.chapterId}/${props.scoId}/${res.data || ""}?userid=${props.userId}&courseid=${props.scoId}`;
        }
    } catch { }
    finally {
        loading.value = false;
    }
}

/** 理论课件页面导航 */
const navigatePage = async (request: string = "start") => {
    try {
        const res = await navigate({
            session_id: sessionId.value!,
            request,
        });
        if (res.code == 0) {
            scoHref.value = `${base}/${props.chapterId}/${props.scoId}/${res.data?.href || ""}`;
            // scoHref.value = `/scorm/Quiz_test/res/index.html`;
        }
    } catch (e) {
        const lastError = await getLastError({
            session_id: sessionId.value!
        });
        console.log(lastError);
    }
};

/** 初始化课件页面 */
const initialPlayer = async () => {
    try {
        const res: any = await initialize({ sco_id: props.scoId });
        if (res.code == 0) {
            const data = res.data || {}
            sessionId.value = data.session_id;
            userStore.scormSessionId = data.session_id;
            userStore.scormTerminated = false;
            injectSCORMAPI(data.session_id); // 注入 API_1484_11
            navigatePage();
            console.log(data);
        } else {
            console.log("initialize : ", res);
        }
    } catch (err) {
        console.error("初始化失败", err);
    } finally {
        loading.value = false;
    }
}

if (props.type == "theory_courseware") {
    initialPlayer();
} else {
    virtualNavigatePage();
}
</script>