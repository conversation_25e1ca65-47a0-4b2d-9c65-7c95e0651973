<template>
    <div class="test-single flex flex-col">
        <div class="test-single-title mb-6 flex items-center">
            <span class="num">{{ index + 1 }}.</span>
            <span class="content ml-2 font-medium">{{ question.content }}</span>
            <span class="score ml-2">({{ question.score }}分)</span>
            <span class="ml-4 font-bold text-[14px] whitespace-nowrap"
                :style="{ color: typeStatusMap[question.question_type] }">
                {{ typeMap[question.question_type] }}
            </span>
        </div>
        <el-radio-group class="flex-col !items-start" v-model="answer">
            <div class="mb-2">
                <el-radio class="!mr-0 w-full" value="false" label="错" />
                <el-radio class="!mr-0 w-full" value="true" label="对" />
            </div>
        </el-radio-group>
    </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { typeMap, typeStatusMap, type Question } from "@/api/examine";

const props = defineProps<{
    modelValue: string | boolean;
    question: Question;
    index: number;
}>();
const emit = defineEmits(["update:modelValue"]);

//答案model
const answer = computed({
    get() {
        return props.question.answer;
    },
    set(val) {
        emit("update:modelValue", val);
    }
});
</script>