<template>
    <div class="test-single flex flex-col">
        <div class="test-single-title mb-6 flex items-center">
            <span class="num whitespace-nowrap">{{ index + 1 }}.</span>
            <span class="content ml-2 font-medium">{{ question.content }}</span>
            <span class="score ml-2 whitespace-nowrap">({{ question.score }}分)</span>
            <span class="ml-4 font-bold text-[14px] whitespace-nowrap"
                :style="{ color: typeStatusMap[question.question_type] }">
                {{ typeMap[question.question_type] }}
            </span>
        </div>
        <el-input v-model="answer" type="textarea" placeholder="请输入答案" :maxlength="10000" :rows="8" resize="none"/>
    </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { typeMap, typeStatusMap, type Question } from "@/api/examine";

const props = defineProps<{
    modelValue: string;
    question: Question;
    index: number;
}>();
const emit = defineEmits(["update:modelValue"]);

//答案model
const answer = computed({
    get() {
        return props.question.answer;
    },
    set(val) {
        emit("update:modelValue", val);
    }
});
</script>