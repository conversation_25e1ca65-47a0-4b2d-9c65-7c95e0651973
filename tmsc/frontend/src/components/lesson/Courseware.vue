<template>
    <!-- 章节课件 -->
    <div class="courseware py-3 hover:bg-gray-50">
        <div class="flex items-start gap-3">
            <el-icon size="24" class="!text-blue-500 mt-1">
                <Document />
            </el-icon>
            <div class="flex-1">
                <div class="flex items-center gap-2">
                    <div class="flex items-center cursor-pointer" @click="emit('open')">
                        <span class="text-gray-700">{{ resource.title }}</span>
                        <span class="px-2 py-1 ml-4 text-xs bg-green-100 text-green-600 rounded whitespace-nowrap"
                            :class="{
                                'bg-blue-100 text-blue-600': resource.courseware_type === 'theory_courseware',
                                'bg-green-100 text-green-600': resource.courseware_type === 'virtual_courseware'
                            }">
                            {{ resource.courseware_type == "theory_courseware" ? "理论课件" : "虚拟课件" }}
                        </span>
                    </div>
                </div>
                <p class="text-sm text-gray-500 mt-1">{{ resource.description }}</p>
            </div>
            <div>
                <button v-if="resource.downloaded" class="text-gray-400 hover:text-gray-600"
                    @click.stop="emit('remove')">
                    <el-icon size="20">
                        <Close />
                    </el-icon>
                </button>
                <button v-else class="text-gray-400 hover:text-gray-600" @click.stop="emit('download')">
                    <el-icon size="20">
                        <Download />
                    </el-icon>
                </button>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
defineProps<{
    resource: any
}>();
const emit = defineEmits<{
    open: [];
    download: [];
    remove: []
}>();
</script>