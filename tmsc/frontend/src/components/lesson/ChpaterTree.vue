<template>
    <div class="chapter-tree">
        <template v-if="data && data.length">
            <ChapterTreeItem v-for="(node, index) in data" :key="index" :node="node" :default-props="defaultProps" @nodeClick="nodeClick">
                <!-- 节点标题插槽 -->
                <template #default="{ node }">
                    <slot name="title" :node="node"></slot>
                </template>
                <!-- 节点内容插槽 -->
                <template #content="{ node }">
                    <slot name="content" :node="node"></slot>
                </template>
            </ChapterTreeItem>
        </template>

        <slot name="empty" v-else>
            <div class="chapter-tree-empty flex justify-center items-center py-2">
                <span class="text-gray-700">No Data</span>
            </div>
        </slot>
    </div>
</template>

<script lang="ts" setup>
import { ref } from "vue";

interface DefaultProps {
    label: string,
    children: string
}

const props = defineProps<{
    data: any[],
    defaultProps: DefaultProps
}>();
const emit = defineEmits<{
    //节点点击
    nodeClick: [node: any];
    //节点展开
    nodeExpand: [expand: boolean];
}>();

/**
 * 点击节点
 */
const nodeClick = (node: any) => {
    node.isExpanded = !node.isExpanded;
    emit("nodeExpand", node.isExpanded);
    emit("nodeClick", node);
};
</script>

<style scoped>
.chapter-tree-item {
    transition: all 0.3s ease;
}
</style>
