<template>
    <div class="chapter-tree-item">
        <!-- 节点标题 -->
        <div class="chapter-tree-item-title flex items-center cursor-pointer py-2" @click.stop="nodeClick">
            <el-icon class="mr-2 transition-transform" :class="{ 'rotate-90': node.isExpanded }">
                <CaretRight />
            </el-icon>
            <slot :node="node">
                <span class="text-gray-900 font-medium">{{ node[defaultProps?.label || "title"] }}</span>
            </slot>
        </div>

        <!-- 节点内容 -->
        <div class="chapter-tree-item-content">
            <div v-show="node.isExpanded" class="pl-6 border-l-2 border-gray-100 ml-2">
                <!-- 节点内容 -->
                <slot name="content" :node="node"></slot>
                <!-- 子节点 -->
                <template
                    v-if="node[defaultProps?.children || 'children'] && node[defaultProps?.children || 'children'].length">
                    <ChapterTreeItem class="mt-2"
                        v-for="(child, sIndex) in node[defaultProps?.children || 'children']" :key="sIndex"
                        :node="child" :default-props="defaultProps" @nodeClick="childNodeClick">
                        <template #default="{ node }">
                            <slot :node="node" />
                        </template>
                        <template #content="{ node }">
                            <slot name="content" :node="node" />
                        </template>
                    </ChapterTreeItem>
                </template>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
interface DefaultProps {
    label: string,
    children: string
}

// 定义插槽类型
defineSlots<{
    default?: (props: { node: any }) => any
    content?: (props: { node: any }) => any
}>()
const props = defineProps<{
    //节点数据
    node: any,
    //默认节点属性
    defaultProps: DefaultProps
}>();
const emit = defineEmits<{
    nodeClick: [node: any];
}>();

/** 点击当前节点 */
const nodeClick = () => {
    emit("nodeClick", props.node);
}

/** 点击节点下的子节点 */
const childNodeClick = (node: any) => {
    //node是当前节点下的子节点数据 不能共用nodeClick方法 否则传递的会变成父节点
    emit("nodeClick", node);
}
</script>

<style scoped></style>