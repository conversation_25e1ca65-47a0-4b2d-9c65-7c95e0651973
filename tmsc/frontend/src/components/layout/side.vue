<template>
  <div class="w-64 bg-[#1F2937] text-white flex flex-col">
    <!-- <div class="flex justify-center mt-5 mb-5">
      <img src="@/assets/logo.png" alt="Logo" class="h-25 w-auto bg-white object-contain">
    </div> -->
    <el-menu ref="menuRef" :default-active="userStore.path" unique-opened class="flex-1 overflow-auto custom-scrollbar !border-none"
      background-color="#1F2937" text-color="#fff">
      <el-menu-item index="/home" @click="handleMenuClick({ path: '/home' })">
				<el-icon>
					<House />
				</el-icon>
				<span>首页</span>
			</el-menu-item>
      <div v-for="item in userStore.menuData" :key="item.id">
        <!-- 无子菜单 -->
        <el-menu-item v-if="!item.children || !item.children.length" :index="item.path" @click="handleMenuClick(item)">
          <el-icon>
            <component :is="item.icon" />
          </el-icon>
          <span>{{ item.menu_name }}</span>
        </el-menu-item>

        <!-- 有子菜单 -->
        <el-sub-menu v-if="item.children?.length" :index="item.path">
          <template #title>
            <el-icon>
              <component :is="item.icon" />
            </el-icon>
            <span>{{ item.menu_name }}</span>
          </template>
          <template v-for="child in item.children" :key="child.id">
            <el-menu-item :index="child.path" @click="handleMenuClick(child)">
              {{ child.menu_name }}
            </el-menu-item>
          </template>
        </el-sub-menu>
      </div>
    </el-menu>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from "vue";
import { useRouter, useRoute } from "vue-router";
import { useUserStore } from "@/store/user";
import { ElMenu } from "element-plus";

const router = useRouter();
const route = useRoute();
const userStore = useUserStore();
const menuRef = ref<typeof ElMenu>();

// 菜单点击跳转
const handleMenuClick = (menuItem: any) => {
  if (menuItem.path) {
    router.push(menuItem.path)
    userStore.path = menuItem.path;
  }
}

onMounted(() => {
  router.push(userStore.path);
});
</script>

<style scoped>
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #4B5563;
  /* 深灰色 */
  border-radius: 3px;
}
</style>