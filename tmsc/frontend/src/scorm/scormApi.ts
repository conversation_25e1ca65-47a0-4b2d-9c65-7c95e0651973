import { setValue, getValue, commit, terminate, getLastError, getErrorString } from "@/api/scormService";
import { useUserStore } from "@/store/user";
import { watch } from "vue";

const userStore = useUserStore();

let isInitialized = false;
let isTerminated = false;
let title = "begin";

watch(() => userStore.coursewareVisible, newVal => {
    title = newVal ? "begin" : "end";
});

// 统一错误处理：请求 getLastError 和 getErrorString
async function handleScormError(session_id: number) {
    try {
        const errRes = await getLastError({ session_id });
        const errorCode = errRes.data ?? "0";
        const errStrRes = await getErrorString({ errorCode });
        const errorString = errStrRes.data ?? "Unknown error";
        // 可选：记录或上报 errorCode/errorString
        // 课件端可调用 API_1484_11.GetLastError()/GetErrorString() 获取最新错误
        return { errorCode, errorString };
    } catch {
        return { errorCode: "0", errorString: "Unknown error" };
    }
}

export function injectSCORMAPI(session_id: number) {
    isInitialized = false;
    isTerminated = false;

    (window as any).API_1484_11 = {
        Initialize: (param: string) => {
            if (param !== "") return "false";
            if (isInitialized) return "false";
            isInitialized = true;
            return "true";
        },
        Terminate: async (param: string) => {
            if (param !== "") return "false";
            if (!isInitialized || isTerminated) return "false";
            try {
                const res = await terminate({ session_id });
                isTerminated = true;
                userStore.scormTerminated = true;
                console.log(`${title} Terminate: key ${param}, res ${res.data}`);
                return "true";
            } catch {
                await handleScormError(session_id);
                return "false";
            }
        },
        GetValue: async (key: string) => {
            if (!isInitialized || isTerminated) return "";
            try {
                const res = await getValue({ session_id, key });
                console.log(`${title} GetValue: key ${key}, res ${res.data}`);
                return res.data ?? "";
            } catch {
                await handleScormError(session_id);
                return "";
            }
        },
        SetValue: async (key: string, value: string) => {
            if (!isInitialized || isTerminated) return "false";
            try {
                const res = await setValue({ session_id, key, value });
                console.log(`${title} SetValue: key ${key}, value ${value}, res ${res.data}`);
                return "true";
            } catch {
                await handleScormError(session_id);
                return "false";
            }
        },
        Commit: async (param: string) => {
            if (param !== "") return "false";
            if (!isInitialized || isTerminated) return "false";
            try {
                const res = await commit({ session_id });
                console.log(`${title} Commit: key ${param}, res ${res.data}`);
                return "true";
            } catch {
                await handleScormError(session_id);
                return "false";
            }
        },
        // 课件可主动获取最近错误码
        GetLastError: async () => {
            try {
                const res = await getLastError({ session_id });
                return res.data ?? "0";
            } catch {
                return "0";
            }
        },
        // 获取错误码对应文本
        GetErrorString: async (errorCode: string) => {
            try {
                const res = await getErrorString({ errorCode });
                return res.data ?? "Unknown error";
            } catch {
                return "Unknown error";
            }
        }
    };
}