import { createRouter, createWebHistory } from 'vue-router'

const routes = [
  {
    path: '/login',
    name: 'login',
    component: () => import('@/views/login.vue'),
  },
  {
    path: '/',
    name: 'index',
    component: () => import('@/views/layout.vue'),
    redirect: '/home',
    children: [
      {
        path: '/home',
        name: 'Home',
        component: () => import('@/views/home/<USER>'),
        meta: {
          title: '首页'
        }
      },
      {
        path: '/materials',
        name: 'Materials',
        component: () => import('@/views/materials/index.vue'),
        meta: {
          title: '资料查看',
        }
      },
      {
        path: '/lesson',
        name: 'Lesson',
        component: () => import('@/views/lesson/index.vue'),
        meta: {
          title: '课程学习'
        }
      },
      {
        path: '/personal-progress',
        component: () => import('@/views/progress/personal-progress.vue'),
        meta: {
          title: '查看学习进度'
        }
      },
      {
        path: '/self-test',
        name: 'SelfTest',
        component: () => import('@/views/self-test/index.vue'),
        meta: {
          title: '自测评估'
        }
      },
      {
        path: '/examine-evaluate',
        name: 'ExamineEvaluate',
        component: () => import('@/views/examine-evaluate/index.vue'),
        meta: {
          title: '学员考核评估'
        }
      },
      {
        path: '/evaluate-analysis',
        name: 'EvaluateAnalysis',
        component: () => import('@/views/evaluate-analysis/index.vue'),
        meta: {
          title: '评估分析查看'
        }
      },
      {
        path: '/subject-examine',
        name: 'SubjectExamine',
        component: () => import('@/views/subject-examine/index.vue'),
        meta: {
          title: '科目考核'
        }
      },
      {
        path: '/user',
        name: 'User',
        component: () => import('@/views/user/index.vue'),
        meta: {
          title: '用户管理'
        }
      },
      {
        path: '/study-history',
        name: 'StudyHistory',
        component: () => import('@/views/study-history/index.vue'),
        meta: {
          title: '学习历史'
        }
      },
      {
        path: '/certificate',
        name: 'Certificate',
        component: () => import('@/views/certificate/index.vue'),
        meta: {
          title: '证书管理'
        }
      },
      {
        path: '/exam',
        name: 'Exam',
        component: () => import('@/views/examine-evaluate/exam/index.vue'),
        meta: {
          title: '学员考核'
        }
      }
    ],
  },
]

const router = createRouter({
  history: createWebHistory(),
  routes,
})

export default router
