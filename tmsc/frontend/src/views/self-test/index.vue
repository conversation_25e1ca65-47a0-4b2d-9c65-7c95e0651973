<template>
    <div class="self-test">
        <!-- 课程选择区域 -->
        <div class="mb-8">
            <div class="mb-6">
                <label class="block text-sm font-medium text-gray-700 mb-2">选择课程</label>
                <el-select v-model="selectedCourse" placeholder="请选择课程" class="w-full" @change="handleCourseChange">
                    <el-option v-for="course in courses" :key="course.id" :label="course.name" :value="course.id" />
                </el-select>
            </div>

            <!-- 课件选择 -->
            <div v-if="selectedCourse" class="mb-6">
                <label class="block text-sm font-medium text-gray-700 mb-2">选择课件</label>
                <el-select v-model="selectedMaterial" placeholder="请选择课件" class="w-full" @change="handleMaterialChange">
                    <el-option-group v-for="group in groupedMaterials" :key="group.type"
                        :label="getMaterialTypeLabel(group.type)">
                        <el-option v-for="material in group.materials" :key="material.id" :label="material.name"
                            :value="material.id" />
                    </el-option-group>
                </el-select>
            </div>
        </div>
        <!-- 内容主区域 -->
        <el-card class="w-full !border-none">
            <!-- 自测题库同步控制区 -->
            <div class="flex items-center justify-between mb-6">
                <div class="flex space-x-4">
                    <el-button type="primary" class="!rounded-button whitespace-nowrap" @click="syncQuestionBank">
                        <el-icon class="mr-2">
                            <Refresh />
                        </el-icon>
                        同步题库
                    </el-button>
                    <el-button class="!rounded-button whitespace-nowrap" @click="clearCache">
                        <el-icon class="mr-2">
                            <Delete />
                        </el-icon>
                        清除缓存
                    </el-button>
                </div>
                <div class="text-sm text-gray-500">
                    {{ syncStatus }}
                </div>
            </div>
            <!-- 试题列表区域 -->
            <div v-if="questions.length > 0">
                <div v-for="(question, index) in questions" :key="question.id"
                    class="mb-8 p-4 border border-gray-200 rounded-lg">
                    <div class="flex items-center mb-4">
                        <span class="px-3 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded-full">
                            {{ getQuestionType(question.type) }}
                        </span>
                        <span class="ml-2 text-gray-500 text-sm">第 {{ index + 1 }} 题</span>
                    </div>
                    <div class="mb-4 font-medium">{{ question.content }}</div>
                    <!-- 答题区域 -->
                    <div v-if="question.type === 'single_choice' || question.type === 'multiple_choice'">
                        <el-checkbox-group v-model="answers[question.id]" v-if="question.type === 'multiple_choice'">
                            <div v-for="option in question.options" :key="option.id" class="mb-2">
                                <el-checkbox :value="option.id">
                                    {{ option.content }}
                                </el-checkbox>
                            </div>
                        </el-checkbox-group>
                        <el-radio-group class="flex-col !items-start" v-model="answers[question.id]" v-else>
                            <div v-for="option in question.options" :key="option.id" class="mb-2">
                                <el-radio :value="option.id">
                                    {{ option.content }}
                                </el-radio>
                            </div>
                        </el-radio-group>
                    </div>
                    <div v-else-if="question.type === 'short_answer'" class="relative">
                        <el-input v-model="answers[question.id]" type="textarea" resize="none" :maxlength="500" :rows="5" placeholder="请输入您的答案" />
                        <span class="count absolute right-3 bottom-2 text-blue-600">{{ answers[question.id]?.length || 0 }}/500</span>
                    </div>
                </div>
                <!-- 提交按钮 -->
                <div class="text-center mt-8">
                    <el-button type="primary" class="!rounded-button whitespace-nowrap px-8" @click="submitAnswers">
                        提交答案
                    </el-button>
                </div>
            </div>
            <div v-else class="text-center py-12 text-gray-500">
                暂无试题，请先同步题库
            </div>
            <!-- 评分反馈区 -->
            <div v-if="showResult" class="mt-8 p-6 bg-gray-50 rounded-lg">
                <div class="text-center mb-6">
                    <h3 class="text-lg font-medium mb-2">测试结果</h3>
                    <div class="text-2xl font-bold text-blue-600">
                        得分: {{ score }} / {{ totalScore }}
                    </div>
                </div>
                <div v-for="(question, index) in questions" :key="'result-' + question.id" class="mb-6">
                    <div class="font-medium mb-2">
                        第 {{ index + 1 }} 题: {{ question.content }}
                    </div>
                    <div class="mb-2">
                        <span class="text-gray-600">您的答案: </span>
                        <span :class="getAnswerClass(question.id)">
                            {{ formatAnswer(question.id, question.type) }}
                        </span>
                    </div>
                    <div class="mb-2" v-if="question.type !== 'short_answer'">
                        <span class="text-gray-600">正确答案: </span>
                        <span class="text-green-600">
                            {{ getCorrectAnswer(question) }}
                        </span>
                    </div>
                    <div class="text-sm text-gray-500" v-if="question.explanation">
                        {{ question.explanation }}
                    </div>
                </div>
            </div>
        </el-card>
    </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from "vue";

// 当前选中的课程ID
const selectedCourse = ref<string>("");
// 当前选中的课件ID
const selectedMaterial = ref<string>("");
// 当前激活的标签页
const activeTab = ref<string>("theory");
// 同步状态提示
const syncStatus = ref<string>("未同步");
// 答案记录
const answers = ref<Record<string, any>>({});
// 课件列表
const courseMaterials = ref<Array<{
    id: string;
    courseId: string;
    name: string;
    type: string;
}>>([]);
// 是否显示结果
const showResult = ref<boolean>(false);
// 得分
const score = ref<number>(0);
// 总分
const totalScore = ref<number>(0);
// 课程列表数据
const courses = ref<Array<{ id: string; name: string }>>([
    { id: "1", name: "Web前端开发基础" },
    { id: "2", name: "JavaScript高级编程" },
    { id: "3", name: "Vue.js框架实战" },
    { id: "4", name: "Node.js后端开发" },
    { id: "5", name: "数据库设计与优化" }
]);
// 试题列表数据
const questions = ref<Array<{
    id: string;
    type: string;
    content: string;
    options?: Array<{ id: string; content: string; isCorrect?: boolean }>;
    answer?: string | string[];
    explanation?: string;
    score: number;
}>>([]);
//课件分组计算
const groupedMaterials = computed(() => {
    const groups: Record<string, { type: string; materials: any[] }> = {
        theory: { type: "theory", materials: [] },
        virtual: { type: "virtual", materials: [] },
        test: { type: "test", materials: [] }
    };

    courseMaterials.value.forEach(material => {
        if (groups[material.type]) {
            groups[material.type].materials.push(material);
        }
    });

    return Object.values(groups).filter(group => group.materials.length > 0);
});

/**
* 课程变更处理
*/
const handleCourseChange = () => {
    questions.value = [];
    answers.value = {};
    showResult.value = false;
    syncStatus.value = "未同步";
    selectedMaterial.value = "";

    // 根据课程ID加载对应的课件
    const materials = [
        {
            id: "m1",
            courseId: "1",
            name: "HTML基础课件",
            type: "theory"
        },
        {
            id: "m2",
            courseId: "1",
            name: "CSS实践课件",
            type: "theory"
        },
        {
            id: "m3",
            courseId: "1",
            name: "Web交互练习",
            type: "virtual"
        },
        {
            id: "m4",
            courseId: "2",
            name: "JavaScript基础知识",
            type: "theory"
        },
        {
            id: "m5",
            courseId: "2",
            name: "DOM编程实践",
            type: "virtual"
        },
        {
            id: "m6",
            courseId: "2",
            name: "JavaScript测试题",
            type: "test"
        }
    ];

    courseMaterials.value = materials.filter(m => m.courseId === selectedCourse.value);
};

/**
* 同步题库
*/
const syncQuestionBank = () => {
    syncStatus.value = "同步中...";
    // 模拟API请求延迟
    setTimeout(() => {
        // 根据当前标签页加载不同的试题
        if (activeTab.value === "theory") {
            questions.value = [
                {
                    id: "1",
                    type: "single_choice",
                    content: "下列哪个不是HTML5的新特性？",
                    options: [
                        { id: "1", content: "Canvas", isCorrect: false },
                        { id: "2", content: "WebSocket", isCorrect: false },
                        { id: "3", content: "Flash", isCorrect: true },
                        { id: "4", content: "Geolocation", isCorrect: false }
                    ],
                    explanation: "Flash不是HTML5的新特性，HTML5的新特性包括Canvas、WebSocket、Geolocation等。",
                    score: 5
                },
                {
                    id: "2",
                    type: "multiple_choice",
                    content: "以下哪些是JavaScript的基本数据类型？",
                    options: [
                        { id: "1", content: "String", isCorrect: true },
                        { id: "2", content: "Object", isCorrect: false },
                        { id: "3", content: "Number", isCorrect: true },
                        { id: "4", content: "Boolean", isCorrect: true }
                    ],
                    explanation: "JavaScript的基本数据类型包括String、Number、Boolean、Null、Undefined和Symbol(ES6新增)。Object是引用类型。",
                    score: 10
                },
                {
                    id: "3",
                    type: "short_answer",
                    content: "简述Vue.js的核心特性有哪些？",
                    answer: "响应式数据绑定、组件系统、虚拟DOM、指令系统等",
                    explanation: "Vue.js的核心特性包括响应式数据绑定、组件系统、虚拟DOM、指令系统、模板语法等。",
                    score: 15
                }
            ];
        } else if (activeTab.value === "virtual") {
            questions.value = [
                {
                    id: "4",
                    type: "single_choice",
                    content: "在虚拟DOM中，下列哪个操作效率最高？",
                    options: [
                        { id: "1", content: "直接操作DOM", isCorrect: false },
                        { id: "2", content: "批量更新虚拟DOM", isCorrect: true },
                        { id: "3", content: "频繁操作DOM", isCorrect: false },
                        { id: "4", content: "直接修改innerHTML", isCorrect: false }
                    ],
                    explanation: "虚拟DOM的优势在于可以批量更新，减少直接操作DOM的次数，提高性能。",
                    score: 5
                }
            ];
        } else {
            questions.value = [
                {
                    id: "5",
                    type: "single_choice",
                    content: "下列哪个不是常见的HTTP状态码？",
                    options: [
                        { id: "1", content: "200", isCorrect: false },
                        { id: "2", content: "404", isCorrect: false },
                        { id: "3", content: "500", isCorrect: false },
                        { id: "4", content: "700", isCorrect: true }
                    ],
                    explanation: "常见的HTTP状态码有200(成功)、404(未找到)、500(服务器内部错误)等，700不是标准状态码。",
                    score: 5
                },
                {
                    id: "6",
                    type: "short_answer",
                    content: "简述RESTful API的设计原则",
                    answer: "无状态、统一接口、资源导向、使用HTTP方法表示操作等",
                    explanation: "RESTful API的设计原则包括无状态、统一接口、资源导向、使用HTTP方法表示操作(GET获取、POST创建、PUT更新、DELETE删除)等。",
                    score: 15
                }
            ];
        }
        syncStatus.value = "同步完成";
        totalScore.value = questions.value.reduce((sum, q) => sum + q.score, 0);
    }, 1000);
};

/**
* 清除缓存
*/
const clearCache = () => {
    questions.value = [];
    answers.value = {};
    showResult.value = false;
    syncStatus.value = "缓存已清除";
};

/**
* 提交答案
*/
const submitAnswers = () => {
    // 计算得分
    score.value = 0;
    questions.value.forEach(question => {
        if (question.type === "single_choice" || question.type === "multiple_choice") {
            const correctAnswers = question.options
                ?.filter(opt => opt.isCorrect)
                ?.map(opt => opt.id) || [];
            if (question.type === "single_choice") {
                if (answers.value[question.id] === correctAnswers[0]) {
                    score.value += question.score;
                }
            } else {
                const userAnswers = Array.isArray(answers.value[question.id])
                    ? answers.value[question.id]
                    : [];
                if (
                    userAnswers.length === correctAnswers.length &&
                    userAnswers.every((ans: any) => correctAnswers.includes(ans))
                ) {
                    score.value += question.score;
                }
            }
        }
    });
    showResult.value = true;
};

/**
* 获取题目类型显示文本
*/
const getQuestionType = (type: string) => {
    const types: Record<string, string> = {
        "single_choice": "单选题",
        "multiple_choice": "多选题",
        "short_answer": "简答题"
    };
    return types[type] || "未知题型";
};

/**
* 格式化答案显示
*/
const formatAnswer = (questionId: string, type: string) => {
    const answer = answers.value[questionId];
    if (!answer) return "未作答";
    if (type === "single_choice" || type === "multiple_choice") {
        const selectedOptions = Array.isArray(answer) ? answer : [answer];
        return questions.value
            .find(q => q.id === questionId)
            ?.options
            ?.filter(opt => selectedOptions.includes(opt.id))
            ?.map(opt => opt.content)
            ?.join("，") || "无";
    }
    return answer;
};

/**
* 获取正确答案
*/
const getCorrectAnswer = (question: any) => {
    if (question.type === "single_choice" || question.type === "multiple_choice") {
        return question.options
            ?.filter((opt: any) => opt.isCorrect)
            ?.map((opt: any) => opt.content)
            ?.join("，") || "无";
    }
    return question.answer || "无";
};

/**
* 获取课件类型显示文本
*/
const getMaterialTypeLabel = (type: string) => {
    const types: Record<string, string> = {
        "theory": "理论课件",
        "virtual": "虚拟课件",
        "test": "理论试题"
    };
    return types[type] || "其他";
};

/**
* 课件变更处理
*/
const handleMaterialChange = () => {
    questions.value = [];
    answers.value = {};
    showResult.value = false;
    syncStatus.value = "未同步";
    const material = courseMaterials.value.find(m => m.id === selectedMaterial.value);
    if (material) {
        activeTab.value = material.type;
    }
};

/**
* 获取答案显示样式类
*/
const getAnswerClass = (questionId: string) => {
    const question = questions.value.find(q => q.id === questionId);
    if (!question) return "";
    if (question.type === "short_answer") return "text-gray-800";
    const isCorrect = (() => {
        if (question.type === "single_choice") {
            const correctAnswer = question.options?.find(opt => opt.isCorrect)?.id;
            return answers.value[questionId] === correctAnswer;
        } else if (question.type === "multiple_choice") {
            const correctAnswers = question.options
                ?.filter(opt => opt.isCorrect)
                ?.map(opt => opt.id) || [];
            const userAnswers = Array.isArray(answers.value[questionId])
                ? answers.value[questionId]
                : [];
            return (
                userAnswers.length === correctAnswers.length &&
                userAnswers.every(ans => correctAnswers.includes(ans))
            );
        }
        return false;
    })();
    return isCorrect ? "text-green-600" : "text-red-600";
};

// 初始化选中第一个课程
onMounted(() => {
    if (courses.value.length > 0) {
        selectedCourse.value = courses.value[0].id;
    }
});
</script>

<style scoped>
</style>