<template>
    <div class="user min-h-screen">
        <!-- 本机登录用户管理 -->
        <el-card class="mb-8 border-none shadow-lg">
            <template #header>
                <div class="flex justify-between items-center py-2">
                    <div class="flex items-center gap-3">
                        <div class="w-1 h-6 bg-blue-500 rounded-full"></div>
                        <span class="font-bold text-xl text-gray-800">用户管理</span>
                    </div>
                    <div class="flex gap-3">
                        <el-button type="primary" class="!rounded-button" @click="handleAddAccount">
                            <el-icon class="mr-1">
                                <Plus />
                            </el-icon>添加新账户
                        </el-button>
                    </div>
                </div>
            </template>
            <div class="grid gap-5">
                <div v-for="user in localUsers" :key="user.id"
                    class="flex items-center justify-between p-5 bg-white rounded-xl transition-all duration-300 hover:shadow-md border border-gray-100"
                    :class="{ 'ring-2 ring-blue-500 bg-blue-50': user.id === currentUser?.id }"
                    :style="{ order: user.order }">
                    <div class="flex items-center gap-5">
                        <!-- <el-avatar :src="user.avatar" :size="56" /> -->
                        <div>
                            <div class="font-medium text-lg mb-1">{{ user.username }}</div>
                            <div class="text-gray-500 text-sm flex items-center gap-2">
                                <el-icon>
                                    <User />
                                </el-icon>
                                ID: {{ user.id }}
                            </div>
                        </div>
                    </div>
                    <div class="flex gap-3">
                        <el-button type="primary" class="!rounded-button" :disabled="user.id === currentUser?.id"
                            @click="handleSwitchUser(user)">
                            <el-icon class="mr-1">
                                <Switch />
                            </el-icon>切换
                        </el-button>
                        <el-button type="primary" class="!rounded-button" @click="handleEditUser(user)">
                            <el-icon class="mr-1">
                                <Edit />
                            </el-icon>编辑
                        </el-button>
                        <el-button type="danger" class="!rounded-button" :disabled="user.id === currentUser?.id"
                            @click="handleDeleteUser(user)">
                            <el-icon class="mr-1">
                                <Delete />
                            </el-icon>删除
                        </el-button>
                    </div>
                </div>
            </div>
        </el-card>

        <!-- 编辑用户 -->
        <el-dialog v-model="showDialog" title="编辑用户" label-position="left" :modal="false" :destroy-on-close="true"
            width="400px">
            <el-form ref="formRef" :model="form" :rules="rules" class="p-2">
                <el-form-item label-width="60" label="账户名" prop="account">
                    <el-input v-model="form.account" placeholder="请输入账户名"></el-input>
                </el-form-item>
                <el-form-item label-width="60" label="用户名" prop="username">
                    <el-input v-model="form.username" placeholder="请输入用户名"></el-input>
                </el-form-item>
                <el-form-item label-width="60" label="手机号" prop="phone">
                    <el-input v-model="form.phone" placeholder="请输入手机号"></el-input>
                </el-form-item>
                <el-form-item label-width="60" label="密码" prop="password">
                    <el-input v-model="form.password" type="password" placeholder="请输入密码"></el-input>
                </el-form-item>
                <el-form-item label-width="60" label="邮箱" prop="email">
                    <el-input v-model="form.email" placeholder="请输入邮箱"></el-input>
                </el-form-item>
            </el-form>
            <div class="flex justify-center">
                <el-button type="primary" class="w-24" @click="submitEdit">提交</el-button>
            </div>
        </el-dialog>
    </div>
</template>
<script lang="ts" setup>
import { ref, nextTick } from "vue";
import { useRouter } from "vue-router";
import { useUserStore } from "@/store/user";
import { msg, confirmMsg as confirm } from "@/utils/msg";
import { userList, updateUser, deleteUser } from "@/api/user";
import type { FormInstance } from "element-plus";

interface UserInfo {
    id: number;
    account: string;
    username: string;
    avatar?: string;
    email: string;
    phone: string;
    password: string;
    roles: any[];
    salt: string;
    status: string;
    created_at: number;
    updated_at: number;
    order?: number;
}

const router = useRouter();
const store = useUserStore();
const formRef = ref<FormInstance>();
// 当前登录用户信息
const currentUser = ref<UserInfo>();
// 本机已登录用户列表
const localUsers = ref<UserInfo[]>([]);
const selectedUser = ref<UserInfo | null>(null);
const showDialog = ref<boolean>(false);
const form = ref<any>({
    account: "",
    username: "",
    phone: "",
    password: "",
    email: ""
});
const rules = ref<any>({
    account: [{ message: "账户名长度在3~18位", min: 3, max: 18, trigger: "blur" }],
    username: [{ message: "用户名长度在3~18位", min: 3, max: 18, trigger: "blur" }],
    phone: [{ message: "请输入正确的手机号", pattern: /^(?:(?:\+|00)86)?1[3-9]\d{9}$/, trigger: "blur" }],
    password: [{ message: "密码长度在6~18位", min: 6, max: 18, trigger: "blur" }],
    email: [{ message: "请输入正确的邮箱", pattern: /^[A-Za-z0-9\u4e00-\u9fa5]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/, trigger: "blur" }]
});
const flag = ref<boolean>(false);
//用户id
let userId = 0;

/**
* 添加新账户
*/
const handleAddAccount = () => {
    router.push("/login");
};

/**
* 切换用户
* @param user 目标用户
*/
const handleSwitchUser = async (user: UserInfo) => {
    // 为所有用户设置初始顺序
    localUsers.value.forEach((u, index) => {
        u.order = index;
    });
    // 设置目标用户的顺序为-1，使其移动到最前
    user.order = -1;
    // 触发动画
    await nextTick();
    currentUser.value = user;
    msg("success", `已切换到用户: ${user.username}`);
};

/** 编辑用户 */
const handleEditUser = (user: UserInfo) => {
    userId = user.id;
    form.value.account = user.account;
    form.value.username = user.username;
    form.value.phone = user.phone;
    form.value.password = user.password;
    form.value.email = user.email;
    showDialog.value = true;
}

/** 提交编辑 */
const submitEdit = () => {
    if (flag.value) {
        msg("warning", "正在提交...");
        return;
    }
    flag.value = true;
    formRef.value?.validate(async valid => {
        if (valid) {
            try {
                const data = {
                    user_info: {
                        id: userId,
                        ...form.value
                    },
                    role_codes: []
                }
                const res = await updateUser(data);
                if (res.code == 0) {
                    msg("success", "编辑成功");
                    getUserList();
                    formRef.value?.resetFields();
                    showDialog.value = false;
                } else {
                    msg("error", res.message);
                }
            } catch (e) { }
        }
        flag.value = false;
    });
}

/**
* 删除用户
* @param user 要删除的用户
*/
const handleDeleteUser = (user: UserInfo) => {
    confirm("确定要删除该用户吗？", "", async action => {
        if (action) {
            const res = await deleteUser(user.id);
            if (res.code == 0) {
                msg("success", "删除成功");
                getUserList();
            }
        }
    });
};

/** 获取用户列表 */
const getUserList = async () => {
    try {
        const res = await userList({});
        if (res.code === 0) {
            localUsers.value = res.data.list;
            // 为所有用户设置初始顺序
            localUsers.value.forEach((u, index) => {
                if (u.id === store.userInfo.id) {
                    currentUser.value = u;
                    u.order = -1;
                    return;
                }
                u.order = index;
            });
        }
    } catch (e) { }
}

getUserList();
</script>
<style scoped>
.el-card {
    border-radius: 16px;
}

.el-button {
    transition: all 0.3s;
}

.el-avatar {
    border: 2px solid #fff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.grid>div {
    transition: transform .8s ease-in-out,
        order .8s ease-in-out;
}

@keyframes scaleUpDown {
    0% {
        transform: scale(1);
    }

    50% {
        transform: scale(1.016);
    }

    100% {
        transform: scale(1);
    }
}

.grid>div[style*="order: -1"] {
    animation: scaleUpDown .8s ease-in-out;
}

:deep(.role-switch-dialog .el-dialog__body) {
    padding-top: 0;
}
</style>