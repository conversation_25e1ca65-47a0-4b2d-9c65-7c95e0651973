<template>
    <div class="subject-examine min-h-screen">
        <!-- 考核信息区 -->
        <el-card class="mb-6 shadow-md">
            <template #header>
                <div class="flex justify-between items-center">
                    <span class="text-xl font-semibold text-gray-800">{{ examInfo.subjectName }}</span>
                    <el-tag :type="examInfo.status === '进行中' ? 'success' : 'info'">
                        {{ examInfo.status }}
                    </el-tag>
                </div>
            </template>
            <div class="info-content grid grid-cols-1 md:grid-cols-2 gap-4 text-gray-700">
                <div class="flex items-center">
                    <el-icon class="mr-2">
                        <Clock />
                    </el-icon>
                    <span>考试时间：{{ examInfo.duration }}分钟</span>
                </div>
                <div class="flex items-center">
                    <el-icon class="mr-2">
                        <Document />
                    </el-icon>
                    <span>题型分布：{{ examInfo.questionTypes }}</span>
                </div>
                <div class="flex items-center">
                    <el-icon class="mr-2">
                        <List />
                    </el-icon>
                    <span>考核大纲：{{ examInfo.outline }}</span>
                </div>
                <div class="flex items-center">
                    <el-icon class="mr-2">
                        <Warning />
                    </el-icon>
                    <span>考核要求：{{ examInfo.requirements }}</span>
                </div>
            </div>
        </el-card>

        <!-- 考核状态区 -->
        <el-card class="mb-6 shadow-md">
            <div class="guide-steps px-4 py-2">
                <el-steps :active="currentStep" finish-status="success" align-center>
                    <el-step title="登录验证" description="已完成身份验证" />
                    <el-step title="设备检查" description="请确保摄像头和麦克风正常" />
                    <el-step title="考试规则" description="阅读并确认考试规则" />
                    <el-step title="正式考试" description="开始答题" />
                </el-steps>
            </div>
            <div class="flex justify-center mt-4">
                <el-button v-if="currentStep < 3" type="primary" class="!rounded-button whitespace-nowrap"
                    @click="nextStep">
                    下一步
                </el-button>
                <el-button v-else type="success" class="!rounded-button whitespace-nowrap" @click="startExam">
                    开始考试
                </el-button>
            </div>
        </el-card>

        <!-- 评分列表区 -->
        <el-card class="shadow-md">
            <template #header>
                <div class="flex justify-between items-center">
                    <span class="text-lg font-semibold text-gray-800">考核评分记录</span>
                    <el-radio-group v-model="examMode" @change="changeMode">
                        <el-radio-button value="theory" class="!rounded-button whitespace-nowrap">理论考试</el-radio-button>
                        <el-radio-button value="practice"
                            class="!rounded-button whitespace-nowrap">实操考试</el-radio-button>
                    </el-radio-group>
                </div>
            </template>
            <el-table :data="scoreList" stripe style="width: 100%" class="mb-4">
                <el-table-column prop="date" label="考试日期" width="180" />
                <el-table-column prop="type" label="考试类型">
                    <template #default="{ row }">
                        <el-tag :type="row.type === '理论' ? 'primary' : 'success'">
                            {{ row.type }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="score" label="得分">
                    <template #default="{ row }">
                        <span :class="row.score >= 60 ? 'text-green-600' : 'text-red-600'">
                            {{ row.score }}
                        </span>
                    </template>
                </el-table-column>
                <el-table-column prop="status" label="状态">
                    <template #default="{ row }">
                        <el-tag :type="getStatusTagType(row.status)">
                            {{ row.status }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="操作" width="120">
                    <template #default="{ row }">
                        <el-button link type="primary" @click="viewDetail(row)" class="whitespace-nowrap">
                            查看详情
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
            <div class="flex justify-end">
                <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[5, 10, 20]"
                    :total="total" layout="total, sizes, prev, pager, next, jumper" />
            </div>
        </el-card>

        <!-- 考试详情对话框 -->
        <el-dialog v-model="detailVisible" title="考试详情" width="60%">
            <div v-if="currentDetail" class="detail-content">
                <div class="grid grid-cols-2 gap-4 mb-4">
                    <div><span class="font-medium">考试科目：</span>{{ currentDetail.subject }}</div>
                    <div><span class="font-medium">考试类型：</span>{{ currentDetail.type }}</div>
                    <div><span class="font-medium">考试日期：</span>{{ currentDetail.date }}</div>
                    <div><span class="font-medium">考试时长：</span>{{ currentDetail.duration }}分钟</div>
                </div>
                <el-divider />
                <h3 class="text-lg font-medium mb-2">各题型得分情况</h3>
                <el-table :data="currentDetail.questionScores" border>
                    <el-table-column prop="type" label="题型"/>
                    <el-table-column prop="count" label="题数" />
                    <el-table-column prop="correct" label="正确数" />
                    <el-table-column prop="score" label="得分" />
                    <el-table-column prop="rate" label="正确率">
                        <template #default="{ row }">
                            <el-progress :percentage="row.rate" :status="row.rate >= 60 ? 'success' : 'exception'" />
                        </template>
                    </el-table-column>
                </el-table>
                <el-divider />
                <h3 class="text-lg font-medium mb-2">主要扣分点</h3>
                <el-timeline>
                    <el-timeline-item v-for="(item, index) in currentDetail.deductionPoints" :key="index"
                        :timestamp="item.time">
                        {{ item.content }}
                    </el-timeline-item>
                </el-timeline>
            </div>
        </el-dialog>
    </div>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import { useRouter } from "vue-router";

const router = useRouter();
// 考试信息
const examInfo = ref({
    subjectName: "汽车维修技术中级认证",
    status: "进行中",
    duration: 120,
    questionTypes: "选择题(60%)、判断题(20%)、实操题(20%)",
    outline: "发动机系统、传动系统、制动系统、电气系统",
    requirements: "80分及格，实操部分需达到基本操作标准"
});
// 当前步骤
const currentStep = ref(1);
// 考试模式
const examMode = ref("theory");
// 成绩列表
const scoreList = ref([
    {
        id: 1,
        date: "2023-05-15",
        type: "理论",
        score: 85,
        status: "已通过",
        subject: "汽车维修技术初级认证",
        duration: 90,
        questionScores: [
            { type: "选择题", count: 50, correct: 42, score: 42, rate: 84 },
            { type: "判断题", count: 20, correct: 16, score: 16, rate: 80 },
            { type: "简答题", count: 5, correct: 3, score: 27, rate: 60 }
        ],
        deductionPoints: [
            { time: "00:25:30", content: "简答题第3题：未完整描述故障排除步骤" },
            { time: "00:42:15", content: "选择题第28题：错误识别了点火系统部件" }
        ]
    },
    {
        id: 2,
        date: "2023-04-10",
        type: "实操",
        score: 72,
        status: "已通过",
        subject: "汽车维修技术初级认证",
        duration: 120,
        questionScores: [
            { type: "发动机拆装", count: 1, correct: 1, score: 30, rate: 100 },
            { type: "故障诊断", count: 1, correct: 0.8, score: 24, rate: 80 },
            { type: "部件更换", count: 1, correct: 0.6, score: 18, rate: 60 }
        ],
        deductionPoints: [
            { time: "00:15:20", content: "故障诊断：未正确使用诊断仪器" },
            { time: "00:45:10", content: "部件更换：操作顺序不规范" }
        ]
    },
    {
        id: 3,
        date: "2023-03-05",
        type: "理论",
        score: 58,
        status: "未通过",
        subject: "汽车维修技术初级认证",
        duration: 90,
        questionScores: [
            { type: "选择题", count: 50, correct: 30, score: 30, rate: 60 },
            { type: "判断题", count: 20, correct: 10, score: 10, rate: 50 },
            { type: "简答题", count: 5, correct: 1, score: 9, rate: 20 }
        ],
        deductionPoints: [
            { time: "00:10:45", content: "简答题第1题：未回答完整" },
            { time: "00:25:30", content: "选择题第15-20题：连续错误" },
            { time: "00:50:15", content: "判断题：基础知识掌握不牢" }
        ]
    }
]);
// 分页相关
const currentPage = ref(1);
const pageSize = ref(5);
const total = ref(scoreList.value.length);
// 考试详情
const detailVisible = ref(false);
const currentDetail = ref<any>(null);

/* 下一步操作 */
const nextStep = () => {
    if (currentStep.value < 3) {
        currentStep.value++;
    }
};

/* 开始考试 */
const startExam = () => {
    console.log("开始考试");
    router.push("/exam");
};

/* 切换考试模式 */
const changeMode = (mode: string) => {
    examMode.value = mode;
    // 这里可以添加加载不同模式成绩列表的逻辑
};

/* 查看详情 */
const viewDetail = (row: any) => {
    currentDetail.value = row;
    detailVisible.value = true;
};

/* 获取状态标签类型 */
const getStatusTagType = (status: string) => {
    switch (status) {
        case "已通过":
            return "success";
        case "未通过":
            return "danger";
        default:
            return "info";
    }
};
</script>

<style scoped>
.exam-container {
    max-width: 1200px;
    margin: 0 auto;
}

.info-content {
    line-height: 1.8;
}

.detail-content {
    line-height: 1.6;
}

:deep(.el-step__title) {
    font-size: 14px;
}

:deep(.el-table .cell) {
    padding: 8px 12px;
}
</style>