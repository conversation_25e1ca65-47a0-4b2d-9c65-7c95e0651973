<template>
    <div class="lesson flex w-full min-h-screen bg-gray-50 shadow rounded-xl overflow-hidden">
        <!-- 左侧课程列表区域 -->
        <div class="flex flex-col w-80 p-6 bg-white shadow-md">
            <!-- 搜索框 -->
            <div class="mb-6">
                <div class="relative">
                    <input v-model="searchKey" type="text"
                        class="!w-full pl-10 pr-4 py-2.5 border border-gray-300 rounded-lg" placeholder="搜索课程..." />
                    <el-icon class="!absolute left-3 top-1/2 -translate-y-1/2 text-gray-400">
                        <Search />
                    </el-icon>
                </div>
            </div>
            <!-- 课程分类标签 -->
            <div class="flex flex-wrap gap-2 mb-6">
                <button v-for="tab in tabs" :key="tab.value" @click="activeTab = tab.value" :class="{
                    'bg-blue-500 text-white': activeTab === tab.value,
                    'bg-gray-100 text-gray-700': activeTab !== tab.value
                }" class="px-4 py-1.5 rounded-full text-sm font-medium transition-colors whitespace-nowrap">
                    {{ tab.label }}
                </button>
            </div>
            <!-- 课程列表 -->
            <div class="space-y-4 flex-1">
                <div v-for="course in filteredCourses" :key="course.id" @click="selectCourse(course)"
                    class="p-4 border border-gray-200 rounded-lg cursor-pointer transition-all hover:shadow-md"
                    :class="{ 'border-blue-500 bg-blue-50': currentCourse?.id === course.id }">
                    <h3 class="font-medium text-gray-900 line-clamp-1">{{ course.name }}</h3>
                    <div class="mt-2">
                        <div class="w-full bg-gray-200 rounded-full h-1.5">
                            <div class="bg-blue-600 h-1.5 rounded-full" :style="{ width: `${course.progress}%` }"></div>
                        </div>
                        <div class="flex text-sm text-gray-500 mt-1">
                            <span>{{ course.total_hours }}课时</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 右侧课程内容区域 -->
        <div class="flex-1 p-6">
            <!-- 课程详情头部 -->
            <div v-if="currentCourse" class="mb-6 p-6 bg-white rounded-xl shadow-sm">
                <div class="flex items-start">
                    <div class="ml-6 flex-1">
                        <div class="flex justify-between items-start">
                            <div>
                                <h2 class="text-2xl font-bold text-gray-900">{{ currentCourse.name }}</h2>
                                <p class="text-gray-500 mt-1">{{ currentCourse.teacher }}</p>
                            </div>
                            <div class="flex space-x-3">
                                <button @click="syncProgress"
                                    class="!rounded-button whitespace-nowrap px-4 py-2 bg-blue-600 text-white hover:bg-blue-700 flex items-center">
                                    <el-icon class="mr-1">
                                        <Refresh />
                                    </el-icon>
                                    同步进度
                                </button>
                                <button @click="clearCourseCache"
                                    class="!rounded-button whitespace-nowrap px-4 py-2 bg-gray-200 text-gray-700 hover:bg-gray-300 flex items-center">
                                    <el-icon class="mr-1">
                                        <Delete />
                                    </el-icon>
                                    清除缓存
                                </button>
                            </div>
                        </div>
                        <div class="mt-4">
                            <div class="flex items-center space-x-4">
                                <div class="flex items-center text-sm text-gray-500">
                                    <span>课程编号</span>
                                    <span class="ml-2">{{ currentCourse.course_code }}</span>
                                </div>
                                <div class="flex items-center text-sm text-gray-500">
                                    <el-icon class="mr-1">
                                        <Clock />
                                    </el-icon>
                                    <span>更新于</span>
                                    <span class="ml-1">
                                        {{ new Date(currentCourse.updated_at * 1000).toLocaleString() }}
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="mt-4">
                            <p class="text-gray-700">{{ currentCourse.description }}</p>
                        </div>
                    </div>
                </div>
            </div>
            <!-- 课程内容区 -->
            <div v-if="currentCourse" class="bg-white rounded-xl shadow-sm">
                <div class="p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">课程章节</h3>
                    <div class="space-y-6">
                        <ChpaterTree :data="chapterData" :default-props="defaultProps" @nodeClick="handleSelectNode">
                            <template #content="{ node }">
                                <el-collapse class="!border-none" v-model="node.activeNames" accordion>
                                    <el-collapse-item title="课件" name="courseware" v-if="node.hasCourseware">
                                        <!-- 章节课件 -->
                                        <Courseware v-for="(resource, index) in node.resources" :key="index"
                                            :resource="resource" @open="openResource(resource)"
                                            @download="downloadResource(resource)" @remove="removeDownload(resource)" />
                                    </el-collapse-item>
                                    <el-collapse-item title="试题" name="question" v-if="node.hasQuestion">
                                        <!-- 章节试题 -->
                                        <Question v-for="(question, index) in node.questions" :key="index"
                                            :question="question" @open="openQuestion(node.questions, question)" />
                                    </el-collapse-item>
                                    <el-collapse-item title="资料" name="material" v-if="node.hasMaterial">
                                        <!-- 章节资料 -->
                                        <Material v-for="(material, index) in node.materials" :key="index"
                                            :material="material" @open="openMaterial(material)"
                                            @download="downloadMaterial(material)" />
                                    </el-collapse-item>
                                </el-collapse>
                            </template>
                        </ChpaterTree>
                    </div>
                </div>
            </div>
            <!-- 空状态 -->
            <div v-if="!currentCourse" class="flex flex-col items-center justify-center h-full">
                <div class="w-40 h-40 mb-6">
                    <img src="@/assets/images/lesson-default.jpg" class="w-full h-full object-contain" alt="空状态" />
                </div>
                <h3 class="text-xl font-medium text-gray-900 mb-2">请选择课程</h3>
                <p class="text-gray-500 mb-6">从左侧列表中选择一门课程开始学习</p>
            </div>
        </div>
    </div>
    <el-drawer v-model="previewVisible" :size="userStore.expand ? getContentWidth() : '100%'" :modal="false"
        :close-on-click-modal="false" :destroy-on-close="true" :with-header="false">
        <div class="flex flex-col h-full overflow-auto">
            <div class="flex items-center mb-6 cursor-pointer" @click="previewVisible = false">
                <el-icon class="mr-2" color="#666" :size="24">
                    <ArrowLeft />
                </el-icon>
                <span class="text-lg font-semibold text-gray-900">{{ previewTitle }}</span>
            </div>
            <!-- 课件 -->
            <div v-if="previewType == 'courseware'" class="flex-1 min-h-[500px] bg-gray-50">
                <ScormPlayer :type="currentResource.courseware_type" :userId="currentResource.user_id"
                    :chapterId="currentResource.chapter_id" :scoId="currentResource.id" class="w-full h-full">
                </ScormPlayer>
            </div>
            <!-- 试题 -->
            <div v-else-if="previewType == 'question'" class="flex-1 min-h-[500px] bg-white p-6 overflow-y-auto">
                <div class="max-w-6xl mx-auto space-y-6">
                    <TestGroup :questions="nodeQuestions"/>
                </div>
            </div>
            <!-- 资料 -->
            <div class="flex-1 min-h-[500px] bg-gray-50" v-else>
                <!-- 视频预览 -->
                <div v-if="videoFormat.includes(currentMaterial?.ext)"
                    class="h-full bg-black flex items-center justify-center">
                    <video v-if="fileUrl" class="w-full h-full" controls>
                        <source :src="fileUrl" type="video/mp4">
                        您的浏览器不支持视频播放
                    </video>
                    <div v-else class="text-white">
                        暂不支持预览该视频格式
                    </div>
                </div>
                <!-- 文档预览 -->
                <div v-else class="h-full bg-gray-50">
                    <iframe v-if="fileUrl" :src="fileUrl" class="w-full h-full" frameborder="0"></iframe>
                    <div v-else class="flex items-center justify-center h-full text-gray-500">
                        暂不支持预览该文档格式
                    </div>
                </div>
            </div>
        </div>
    </el-drawer>
</template>

<script lang="ts" setup>
import { ref, computed, watch } from "vue";
import { useUserStore } from "@/store/user";
import { msg } from "@/utils/msg";
import { syncPlan, clearCache, courseList, chapterList, coursewareList, questionList, materialList } from "@/api/lesson";
import { IPConfig } from "@/utils/request";
import type { Question } from "@/api/examine";

interface Chapter {
    attached_attribute_value: string;
    children: Chapter[];
    description: string;
    id: number;
    name: string;
    parent_id: number,
    resources?: any[]
}

const userStore = useUserStore();
// 搜索关键词
const searchKey = ref("");
// 当前激活的标签页
const activeTab = ref("all");
// 课程列表数据
const courseData = ref<any[]>([]);
//章节列表数据
const chapterData = ref<Chapter[]>([]);
// 当前选中的课程
const currentCourse = ref<any | null>(null);
// 是否正在同步
const isSyncing = ref(false);
// 标签页选项
const tabs = [
    { label: "全部课程", value: "all" },
    { label: "进行中", value: "progress" },
    { label: "已完成", value: "completed" },
    { label: "推荐", value: "recommend" }
];
//tree默认属性
const defaultProps = {
    children: "children",
    label: "name"
};
//视频格式
const videoFormat = ref<string[]>([".mp4", ".avi", ".mkv", ".mov", ".webm", ".flv", ".wmv"]);
// PDF预览地址
const fileUrl = ref("");
//过滤后的课程列表
const filteredCourses = computed(() => {
    let courses = courseData.value;
    // 根据标签页过滤
    if (activeTab.value === "progress") {
        courses = courses.filter(course => course.progress > 0 && course.progress < 100);
    } else if (activeTab.value === "completed") {
        courses = courses.filter(course => course.progress === 100);
    } else if (activeTab.value === "recommend") {
        courses = courses.filter(course => course.recommended);
    }
    return courses;
})
// 预览对话框
const previewVisible = ref(false);
//预览标题
const previewTitle = ref<string>("");
//预览类型
const previewType = ref<string>("courseware");
// 当前预览的课件
const currentResource = ref<any>(null);
//当前预览节点下的试题列表
const nodeQuestions = ref<Question[]>([]);
// 当前预览的试题
const currentQuestion = ref<any>(null);
//当前预览的资料
const currentMaterial = ref<any>(null);

watch(previewVisible, newVal => userStore.coursewareVisible = newVal);

watch(searchKey, newVal => {
    loadCourses({ name: newVal });
});

/** 选择章节节点 */
const handleSelectNode = (data: any) => {
    if (data.attached_attribute_value) {
        const attr = JSON.parse(data.attached_attribute_value);
        attr.forEach((item: any) => {
            if (!data.resources && item.key == "courseware_count" && +item.value > 0) {
                loadCourseware(data);
                data.hasCourseware = +item.value > 0
            }
            if (!data.questions && item.key == "question_count" && +item.value > 0) {
                loadQuestion(data);
                data.hasQuestion = +item.value > 0
            }
            if (!data.materials && item.key == "resource_count" && +item.value > 0) {
                loadMaterial(data);
                data.hasMaterial = +item.value > 0
            }
        });
        data.activeNames = "courseware";
    }
}

/** 获取容器宽度 */
const getContentWidth = () => {
    const screen = window.innerWidth;
    return (screen - 256) / screen * 100 + "%";
}

/**
* 选择课程
*/
const selectCourse = (course: any) => {
    if (currentCourse.value && currentCourse.value.id == course.id) return;
    currentCourse.value = course;
    getChapterList(course.id);
}

/**
* 打开课程资源
*/
const openResource = (resource: any) => {
    currentResource.value = resource;
    previewTitle.value = resource.title;
    previewType.value = "courseware";
    console.log(resource);
    previewVisible.value = true;
}

/** 打开试题 */
const openQuestion = (questions: Question[], question: Question) => {
    nodeQuestions.value = questions;
    currentQuestion.value = question;
    previewTitle.value = "理论试题";
    previewType.value = "question";
    previewVisible.value = true;
    console.log(question);
}

/** 打开资料 */
const openMaterial = (material: any) => {
    currentMaterial.value = material;
    previewTitle.value = material.name;
    previewType.value = "material";
    fileUrl.value = `${IPConfig.API_URL}/resource-assets/${material.download_url}`;
    previewVisible.value = true;
    console.log(material);
}

/**
* 下载资源
*/
const downloadResource = (resource: any) => {
    msg("success", `开始下载: ${resource.name}`);
    resource.downloaded = true;
}

/** 下载资料 */
const downloadMaterial = (material: any) => {

}

/**
* 移除下载
*/
const removeDownload = (resource: any) => {
    msg("success", `已移除: ${resource.name}`);
    resource.downloaded = false;
}

/**
* 同步学习进度
*/
const syncProgress = async () => {
    if (isSyncing.value) return;
    isSyncing.value = true;
    try {
        // 模拟API请求
        await mockSyncProgress();
        msg("success", "同步成功");
    } catch (error) {
        msg("error", "同步失败");
    } finally {
        isSyncing.value = false;
    }
}

/**
* 清除本地缓存
*/
const clearCourseCache = async () => {
    try {
        const res = await clearCache(currentCourse.value.id);
        if (res.code == 0) {
            msg("success", "清除成功");
        }
    } catch {
        console.log("清除课程缓存失败");
    }
}

const mockSyncProgress = () => {
    return new Promise(resolve => {
        setTimeout(() => {
            resolve({});
        }, 1000);
    })
}

/** 获取章节下的课件列表 */
const loadCourseware = async (chapter: any) => {
    try {
        const res = await coursewareList(chapter.id);
        if (res.code === 0) {
            chapter.resources = res.data || [];
        }
    } catch { }
}

/** 获取章节下的试题 */
const loadQuestion = async (chapter: any) => {
    try {
        const res = await questionList(chapter.id);
        if (res.code == 0) {
            chapter.questions = res.data || [];
        }
    } catch { }
}

/** 获取章节下的资料 */
const loadMaterial = async (chapter: any) => {
    try {
        const res = await materialList({ chapter_id: chapter.id });
        if (res.code == 0) {
            const data = res.data || {}
            chapter.materials = data.list || [];
        }
    } catch { }
}

/** 获取章节列表 */
const getChapterList = async (id: number) => {
    try {
        const res = await chapterList(id);
        if (res.code === 0) {
            chapterData.value = res.data || [];
        }
    } catch { }
}

/**
* 初始化加载课程列表
*/
const loadCourses = async (params?: { name?: string }) => {
    params = params || {};
    try {
        const res = await courseList(params);
        if (res.code == 0) {
            const data = res.data || {}
            courseData.value = data.list || [];
        }
    } catch { }
}

/** 远程同步 */
const sync = async () => {
    try {
        const res = await syncPlan();
        if (res.code == 0) {
            msg("success", "同步成功");
            loadCourses();
        }
    } catch {
        console.log("课程同步失败");
    }
}

sync();
loadCourses();
</script>

<style scoped>
:deep(.el-tree) {
    .el-tree-node__content {
        height: auto;
    }
}

:deep(.el-collapse) {
    .el-collapse-item__title {
        font-size: 16px;
    }

    .el-collapse-item__content {
        padding-bottom: 0;
        font-size: inherit;
    }
}
</style>