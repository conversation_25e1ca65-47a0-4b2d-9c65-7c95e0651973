<template>
    <div class="evaluate-analysis w-4/5 mx-auto">
        <!-- 薄弱知识点分析卡片 -->
        <el-card class="mb-6">
            <template #header>
                <div class="font-medium text-lg">知识点薄弱环节分析</div>
            </template>
            <div v-for="item in weakPoints" :key="item.id" class="mb-6 last:mb-0">
                <div class="flex items-center justify-between mb-2">
                    <span class="text-gray-700 font-medium">{{ item.name }}</span>
                    <el-tag :type="item.level" size="small">{{ item.levelText }}</el-tag>
                </div>
                <div class="bg-gray-50 p-4 rounded-lg">
                    <div class="text-sm text-gray-600 mb-3">{{ item.analysis }}</div>
                    <div class="flex flex-wrap gap-2">
                        <el-tag v-for="tag in item.suggestions" :key="tag" class="!rounded-button" effect="plain">
                            {{ tag }}
                        </el-tag>
                    </div>
                </div>
            </div>
        </el-card>

        <!-- 知识点分析卡片 -->
        <el-card class="mb-6">
            <template #header>
                <div class="font-medium text-lg">知识点掌握分析</div>
            </template>
            <el-progress v-for="item in knowledgePoints" :key="item.id" :percentage="item.score"
                :color="getColorByScore(item.score)" :format="format" class="mb-4">
                <template #default="{ percentage }">
                    <span class="text-gray-700">{{ item.name }}</span>
                    <span class="ml-2">{{ percentage }}%</span>
                </template>
            </el-progress>
        </el-card>
        <!-- 排名展示卡片 -->
        <el-card class="mb-6">
            <template #header>
                <div class="font-medium text-lg">成绩排名</div>
            </template>
            <div class="flex justify-between mb-4">
                <div class="text-center">
                    <div class="text-3xl text-blue-500 font-bold">{{ classRank }}</div>
                    <div class="text-gray-500 mt-2">班级排名</div>
                </div>
                <div class="text-center">
                    <div class="text-3xl text-green-500 font-bold">{{ courseRank }}</div>
                    <div class="text-gray-500 mt-2">课程排名</div>
                </div>
            </div>
        </el-card>
        <!-- 推荐资源卡片 -->
        <el-card>
            <template #header>
                <div class="font-medium text-lg">推荐学习资源</div>
            </template>
            <el-table :data="recommendResources" style="width: 100%">
                <el-table-column prop="name" label="资源名称" />
                <el-table-column prop="type" label="类型" width="120" />
                <el-table-column fixed="right" label="操作" width="120">
                    <template #default="scope">
                        <el-button link type="primary" @click="handleViewResource(scope.row)">
                            查看
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
        </el-card>
    </div>
</template>
<script lang="ts" setup>
import { ref } from "vue";
import { msg } from "@/utils/msg";

// 薄弱知识点分析数据
const weakPoints = ref([
    {
        id: 1,
        name: "状态管理",
        level: "danger",
        levelText: "待加强",
        analysis: "在 Vuex/Pinia 的状态管理方面存在明显不足，特别是在复杂状态树的设计和模块化管理方面需要提升。数据流转不够清晰，且缺乏合理的状态划分。",
        suggestions: ["复习 Vuex 核心概念", "练习 Pinia 实战案例", "状态设计原则"]
    },
    {
        id: 2,
        name: "TypeScript 应用",
        level: "warning",
        levelText: "需提升",
        analysis: "TypeScript 的类型定义和泛型应用掌握不够深入，在处理复杂类型时容易出现问题。接口设计和类型推导能力需要加强。",
        suggestions: ["泛型进阶", "高级类型运用", "类型体操训练"]
    },
    {
        id: 3,
        name: "前端工程化",
        level: "warning",
        levelText: "需提升",
        analysis: "在项目构建优化和自动化部署方面经验较少，对前端工程化体系理解不够系统，需要加强实践。",
        suggestions: ["Webpack 优化", "CI/CD 实践", "构建工具选型"]
    }
]);
// 知识点掌握数据
const knowledgePoints = ref([
    { id: 1, name: "JavaScript 基础", score: 85 },
    { id: 2, name: "Vue 组件开发", score: 92 },
    { id: 3, name: "状态管理", score: 78 },
    { id: 4, name: "TypeScript 应用", score: 65 },
    { id: 5, name: "前端工程化", score: 72 }
]);
// 排名数据
const classRank = ref(5);
const courseRank = ref(28);
// 推荐资源列表
const recommendResources = ref([
    { id: 1, name: "JavaScript 进阶教程", type: "视频课程" },
    { id: 2, name: "Vue3 最佳实践指南", type: "电子文档" },
    { id: 3, name: "状态管理专题练习", type: "实战练习" },
    { id: 4, name: "前端性能优化手册", type: "电子文档" },
    { id: 5, name: "TypeScript 类型体操", type: "实战练习" }
]);

/**
* 根据分数获取进度条颜色
*/
const getColorByScore = (score: number) => {
    if (score >= 90) return "#67C23A";
    if (score >= 75) return "#409EFF";
    if (score >= 60) return "#E6A23C";
    return "#F56C6C";
};

/**
* 进度条格式化
*/
const format = (percentage: number) => {
    return percentage === 100 ? "满分" : `${percentage}分`;
};

/**
* 查看资源处理函数
*/
const handleViewResource = (resource: any) => {
    console.log("查看资源:", resource);
    // 这里可以添加跳转资源详情页的逻辑
};
</script>

<style scoped>
.el-progress {
    --el-progress-text-color: #333;
}

.el-card {
    --el-card-padding: 20px;
}

.el-table {
    --el-table-border-color: #f0f0f0;
    --el-table-header-bg-color: #f8f8f8;
}
</style>