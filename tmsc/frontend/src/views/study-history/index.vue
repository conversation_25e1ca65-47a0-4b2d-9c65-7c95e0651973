<template>
    <div class="study-history min-h-screen">
        <!-- 学习历史列表 -->
        <el-card>
            <template #header>
                <div class="flex justify-between">
                    <h2 class="text-xl font-semibold">学习历史</h2>
                    <div class="flex items-center gap-4">
                        <el-select class="!w-54 shrink-0" v-model="filterParams.courseType" placeholder="课程类型"
                            clearable>
                            <el-option v-for="item in courseTypes" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                        <el-date-picker class="!w-70 shrink-0" v-model="filterParams.dateRange" type="daterange"
                            range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" />
                        <el-button type="primary" @click="handleSearch">查询</el-button>
                        <el-button class="!ml-0" @click="handleReset">重置</el-button>
                    </div>
                </div>
            </template>
            <el-table :data="learningHistory" style="width: 100%">
                <el-table-column prop="courseName" label="课件名称" width="300" />
                <el-table-column prop="courseType" label="课件类型">
                    <template #default="scope">
                        <el-tag :type="scope.row.courseType === '3D模型' ? 'success' : 'primary'">
                            {{ scope.row.courseType }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="学习进度">
                    <template #default="scope">
                        <el-progress :percentage="scope.row.progress" :color="getProgressColor(scope.row.progress)" />
                    </template>
                </el-table-column>
                <el-table-column prop="lastStudyTime" label="最后学习时间" />
                <el-table-column prop="duration" label="学习时长" />
            </el-table>

            <!-- 分页 -->
            <div class="mt-4 flex justify-end">
                <el-pagination v-model:current-page="pagination.currentPage" v-model:page-size="pagination.pageSize"
                    :total="pagination.total" :page-sizes="[10, 20, 50, 100]"
                    layout="total, sizes, prev, pager, next, jumper" @size-change="handleSizeChange"
                    @current-change="handleCurrentChange" />
            </div>
        </el-card>
    </div>
</template>

<script lang="ts" setup>
import { ref, reactive } from "vue";

// 筛选参数
const filterParams = reactive({
    courseType: "",
    dateRange: []
});
// 课程类型选项
const courseTypes = ref([
    { value: "3D模型", label: "3D模型" },
    { value: "视频", label: "视频" },
    { value: "文档", label: "文档" },
    { value: "测验", label: "测验" }
]);
// 学习历史数据
const learningHistory = ref([
    {
        id: "1",
        courseName: "心脏解剖3D模型",
        courseType: "3D模型",
        progress: 85,
        lastStudyTime: "2023-06-15 14:30",
        duration: "2小时15分"
    },
    {
        id: "2",
        courseName: "发动机工作原理3D演示",
        courseType: "3D模型",
        progress: 20,
        lastStudyTime: "2023-06-10 09:15",
        duration: "1小时30分"
    },
    {
        id: "3",
        courseName: "Vue3基础教程",
        courseType: "视频",
        progress: 100,
        lastStudyTime: "2023-05-28 16:45",
        duration: "4小时20分"
    },
    {
        id: "4",
        courseName: "TypeScript入门指南",
        courseType: "文档",
        progress: 45,
        lastStudyTime: "2023-05-20 11:10",
        duration: "3小时05分"
    }
]);
// 分页参数
const pagination = reactive({
    currentPage: 1,
    pageSize: 10,
    total: 40
});

/**
 * 处理查询
 */
const handleSearch = () => {
    // 模拟查询
    console.log("查询参数:", filterParams);
    pagination.currentPage = 1;
    fetchLearningHistory();
};

/**
 * 重置查询条件
 */
const handleReset = () => {
    filterParams.courseType = "";
    filterParams.dateRange = [];
    handleSearch();
};

/**
 * 获取学习历史数据
 */
const fetchLearningHistory = () => {
    // 实际项目中这里应该是API调用
    console.log("获取学习历史数据");
};

/**
 * 查看详情
 */
const handleViewDetail = (item: any) => {
    console.log("查看详情:", item);
};

/**
 * 继续学习
 */
const handleContinueStudy = (item: any) => {
    console.log("继续学习:", item);
};

/**
 * 获取进度条颜色
 */
const getProgressColor = (progress: number) => {
    if (progress >= 100) return "#67C23A";
    if (progress >= 70) return "#1989FA";
    if (progress >= 30) return "#e6a23c";
    return "#f56c6c";
};

/**
 * 处理分页大小变化
 */
const handleSizeChange = (val: number) => {
    pagination.pageSize = val;
    fetchLearningHistory();
};

/**
 * 处理当前页变化
 */
const handleCurrentChange = (val: number) => {
    pagination.currentPage = val;
    fetchLearningHistory();
};
</script>

<style scoped></style>