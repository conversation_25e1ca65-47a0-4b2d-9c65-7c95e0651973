<template>
    <div class="certificate">
        <!-- 证书列表 -->
        <el-card class="mb-6 shadow-md">
            <template #header>
                <div class="flex">
                    <h2 class="text-xl font-semibold">我的证书</h2>
                </div>
            </template>
            <el-table :data="certificateList" style="width: 100%">
                <el-table-column prop="name" label="证书名称" />
                <el-table-column prop="courseName" label="课程名称" />
                <el-table-column prop="issueDate" label="颁发日期" />
                <el-table-column prop="score" label="成绩" />
                <el-table-column label="操作" width="220">
                    <template #default="scope">
                        <el-button size="small" @click="handleView(scope.row)">查看</el-button>
                        <el-button size="small" @click="handleDownload(scope.row)">下载</el-button>
                        <el-button size="small" @click="handlePrint(scope.row)">打印</el-button>
                    </template>
                </el-table-column>
            </el-table>
        </el-card>

        <!-- 证书预览对话框 -->
        <el-dialog v-model="showPreviewDialog" title="证书预览" width="70%">
            <div class="certificate-preview">
                <!-- 这里应该是证书的预览内容 -->
                <div class="text-center py-8 border border-gray-200">
                    <h3 class="text-2xl font-bold mb-4">{{ previewCertificate.name }}</h3>
                    <p class="text-lg mb-2">课程名称: {{ previewCertificate.courseName }}</p>
                    <p class="text-lg mb-2">颁发日期: {{ previewCertificate.issueDate }}</p>
                    <p class="text-lg mb-2">成绩: {{ previewCertificate.score }}</p>
                </div>
            </div>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="showPreviewDialog = false">关闭</el-button>
                    <el-button type="primary" @click="handlePrint(previewCertificate)">打印</el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>

<script lang="ts" setup>
import { ref, reactive } from "vue";
import { msg, confirmMsg as confirm } from "@/utils/msg";

// 证书列表数据
const certificateList = ref([
    {
        id: "1",
        name: "课程结业证书",
        courseName: "Vue3高级开发",
        issueDate: "2023-05-15",
        score: "优秀",
        pdfUrl: "/certificates/1.pdf"
    },
    {
        id: "2",
        name: "课程结业证书",
        courseName: "TypeScript入门",
        issueDate: "2023-06-20",
        score: "良好",
        pdfUrl: "/certificates/2.pdf"
    }
]);
const showPreviewDialog = ref(false);
// 当前预览的证书
const previewCertificate = reactive({
    id: "",
    name: "",
    courseName: "",
    issueDate: "",
    score: ""
});

/**
 * 查看证书
 */
const handleView = (certificate: any) => {
    Object.assign(previewCertificate, certificate);
    showPreviewDialog.value = true;
};

/**
 * 下载证书
 */
const handleDownload = (certificate: any) => {
    // 模拟下载
    msg("success", `开始下载证书: ${certificate.name}`);
    const link = document.createElement("a");
    link.href = certificate.pdfUrl;
    link.download = `${certificate.courseName}_证书.pdf`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
};

/**
 * 打印证书
 */
const handlePrint = (certificate: any) => {
    confirm(`确定要打印 ${certificate.name} 吗?`, "提示", action => {
        if (action) {
            // 实际项目中这里应该调用打印API
            msg("success", "已发送打印请求");
        }
    })
};
</script>

<style scoped>
.certificate-preview {
    background-color: #f9f9f9;
    min-height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
}
</style>
```