<template>
    <div class="examine-evaluate min-h-screen">
        <!-- 考核列表卡片 -->
        <el-card class="mb-6 shadow-md">
            <template #header>
                <div class="flex justify-between items-center">
                    <span class="font-bold text-xl text-gray-800">考核列表</span>
                    <el-tag :type="getStatusType(activeTab)" size="large">{{ activeTabLabel }}</el-tag>
                </div>
            </template>
            <!-- 考核类型切换 -->
            <el-tabs v-model="activeTab" class="mb-4">
                <el-tab-pane v-for="(label, name) in map" :label="label" :name="name"></el-tab-pane>
            </el-tabs>
            <!-- 考核列表表格 -->
            <el-table :data="examData" border stripe class="!w-full border-gray-200 rounded-lg">
                <el-table-column show-overflow-tooltip prop="name" label="考核名称" width="280" />
                <el-table-column show-overflow-tooltip prop="location" label="考核地点" width="280" />
                <el-table-column label="开始时间">
                    <template #default="{ row }">{{ new Date(row.start_at * 1000).toLocaleString() }}</template>
                </el-table-column>
                <el-table-column label="结束时间">
                    <template #default="{ row }">{{ new Date(row.end_at * 1000).toLocaleString() }}</template>
                </el-table-column>
                <el-table-column prop="total_minutes" label="考试时长">
                    <template #default="{ row }">
                        {{ row.total_minutes }}分钟
                    </template>
                </el-table-column>
                <el-table-column prop="status" label="状态">
                    <template #default="{ row }">
                        <el-tag :type="getStatusType(row.exam_type)" class="!rounded-[6px]">
                            {{ map[row.exam_type as MapKeys] }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="操作" width="180">
                    <template #default="{ row }">
                        <el-button type="primary" :disabled="row.progress_status == 'finished'"
                            class="!rounded-[6px] whitespace-nowrap" @click="enterExam(row)">
                            进入考试
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
            <!-- 分页 -->
            <div class="mt-6 flex justify-center">
                <el-pagination v-model:current-page="pages.page" :page-size="pages.limit" :total="pages.total"
                    layout="prev, pager, next" background @current-change="handlePageChange" />
            </div>
        </el-card>
        <!-- 考试记录卡片 -->
        <el-card class="shadow-md">
            <template #header>
                <div class="flex justify-between items-center">
                    <span class="font-bold text-xl text-gray-800">考试记录</span>
                    <el-button type="primary" class="!rounded-[6px] whitespace-nowrap" @click="refreshRecords">
                        <el-icon class="mr-1">
                            <Refresh />
                        </el-icon>
                        刷新记录
                    </el-button>
                </div>
            </template>
            <el-table :data="examRecords" border stripe class="!w-full border-gray-200 rounded-lg">
                <el-table-column prop="exam_name" label="考核名称" width="280" />
                <el-table-column prop="submit_at" label="提交时间">
                    <template #default="{ row }">{{ new Date(row.submit_at * 1000).toLocaleString() }}</template>
                </el-table-column>
                <el-table-column prop="duration" label="答题用时">
                    <template #default="{ row }">
                        {{ row.cost_time }}分钟
                    </template>
                </el-table-column>
                <el-table-column prop="score" label="得分">
                    <template #default="{ row }">
                        <span :class="getScoreClass(row.score)">
                            {{ row.score }}
                        </span>
                    </template>
                </el-table-column>
                <el-table-column prop="rank" label="排名">
                    <template #default="{ row }">
                        <span class="font-medium">
                            第{{ row.rank }}名
                        </span>
                    </template>
                </el-table-column>
                <el-table-column prop="progress_status" label="考试进度">
                    <template #default="{ row }">{{ row.progress_status }}</template>
                </el-table-column>
                <el-table-column label="操作" width="120">
                    <template #default="scope">
                        <el-button type="info" class="!rounded-[6px] whitespace-nowrap" @click="viewDetail(scope.row)">
                            查看详情
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
        </el-card>
        <!-- 开始考试 -->
        <el-drawer v-model="store.preview_drawer" title="开始考试" size="100%" :before-close="handleViewClose">
            <Paper ref="view_paper" v-if="store.preview_drawer" @submit="store.preview_drawer = false" />
        </el-drawer>
    </div>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, watch } from "vue";
import { useRouter } from "vue-router";
import { msg } from "@/utils/msg";
import { syncExam, examList, examStart, examRecord } from "@/api/examine";
import { usePaperStore } from "@/store/paper";
const store = usePaperStore()
type MapKeys = keyof typeof map;
const progress_map = {
    'not_started': '未开始',
    'ongoing': '进行中',
    'finished': '已完成'
}


//router
const router = useRouter();
// 当前选中的考试类型
const activeTab = ref("regular");
// 考试列表数据
const examData = ref<any[]>([]);
// 考试记录数据
const examRecords = ref<any[]>([]);
const pages = reactive({
    page: 1,
    limit: 10,
    total: 0
});
const map = {
    regular: "平时",
    final: "结业",
    retake: "补考"
}
// 当前选中的考试类型标签文本
const activeTabLabel = computed(() => {
    return map[activeTab.value as MapKeys];
});
// preview drawer
const view_paper = ref();


watch(activeTab, newVal => {
    pages.page = 1;
    getExamList({ page: pages.page, exam_type: newVal });
});

/**
* 获取标签类型
* @param type 考试类型
* @returns 标签类型
*/
const getStatusType = (type: string): string => {
    const statusMap = {
        regular: "primary",
        final: "success",
        retake: "warning"
    };
    return statusMap[type as keyof typeof statusMap] || "primary";
};

/**
* 根据分数获取样式类
* @param score 考试分数
* @returns 样式类名
*/
const getScoreClass = (score: number): string => {
    if (score >= 90) return "text-green-600 font-bold";
    if (score >= 80) return "text-blue-600 font-medium";
    if (score >= 60) return "text-yellow-600";
    return "text-red-600";
};

/**
* 进入考试
* @param exam 考试信息
*/
const enterExam = async (exam: any) => {
    console.log(exam, '---')
    store.end_at = exam.end_at
    const res = await examStart({ id: exam.id });
    console.log(res)
    store.progress_id = res.id
    store.exam_id = exam.id;
    store.preview_drawer = true;
    store.paper_id = exam.paper_id;
};

/** 关闭考试 */
const handleViewClose = async () => {
    await view_paper.value?.handleSubmit('close');
}

/**
* 查看考试详情
* @param record 考试记录
*/
const viewDetail = (record: any): void => {
    console.log("查看详情", record);
    // 实际项目中这里会打开详情弹窗或跳转到详情页
    msg("info", `查看考试详情: ${record.name}`);
};

/**
* 刷新考试记录
*/
const refreshRecords = (): void => {
    console.log("刷新考试记录");
    // 实际项目中这里会重新请求API获取最新记录
    msg("success", "考试记录已刷新");
};

/** 获取考试列表 */
const getExamList = async (params?: { page?: number; page_size?: number, exam_type?: string }) => {
    params = params || {
        exam_type: activeTab.value
    };
    try {
        const res = await examList(params);
        if (res.code == 0) {
            const data = res.data || {}
            examData.value = data.list || [];
            pages.total = data.total || 0;
        }
    } catch { }
}

/** 获取考试记录列表 */
const getExamRecords = async () => {
    try{
        const res = await examRecord();
        examRecords.value = res.data || [];
    }catch{
        console.log("获取考试记录失败");
    }
}

/** 考试同步数据 */
const sync = async () => {
    try {
        const res = await syncExam();
        if (res.code == 0) {
            msg("success", "同步成功");
            getExamList();
        }
    } catch {
        console.log("考核同步失败");
    }
}

/**
 * 分页变化
 */
const handlePageChange = (page: number) => {
    pages.page = page;
    getExamList({
        page,
        exam_type: activeTab.value
    });
    getExamRecords();
}

sync();
getExamList();
getExamRecords();
</script>

<style scoped>
.assessment-container {
    background-color: #f8fafc;
}

:deep(.el-card__header) {
    border-bottom: 1px solid #e2e8f0;
    padding: 16px 20px;
}

:deep(.el-table) {
    font-size: 14px;
}

:deep(.el-table th) {
    background-color: #f1f5f9;
    color: #334155;
    font-weight: 600;
}

:deep(.el-drawer__header) {
    margin-bottom: 0 !important;
}

:deep(.el-drawer__body) {
    display: flex;
    overflow: hidden;
}
</style>