<!-- 代码已包含 CSS：使用 TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 -->

<template>
  <div class="flex flex-col h-screen overflow-hidden">
    <!-- 顶部导航 -->
    <header class="bg-cyan-600 text-white h-16">
      <div class="mx-auto  h-full flex items-center justify-between">
        <div :class="['text-xl h-full font-medium flex items-center justify-center w-64 bg-cyan-700', overmodalStyle]">
          教学管理系统
          <el-icon class="ml-18 cursor-pointer" :size="24" @click="handleExpand">
            <Fold v-if="expand" />
            <Expand v-else />
          </el-icon>
        </div>
        <div class="flex items-center space-x-4 px-4">
          <span>欢迎学员 {{ store.userInfo.username }}</span>
          <el-dropdown v-if="store.roles.length > 1" trigger="click" @command="switchRole">
            <el-button type="primary">切换角色</el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item v-for="role in filterRoles" :key="role.id"
                  :disabled="role.role_code == store.userCode" :command="role.role_code">
                  {{ role.role_name }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
          <el-button @click="logout" type="primary" class="!rounded-button whitespace-nowrap">
            退出
          </el-button>
        </div>
      </div>
    </header>

    <div class="flex flex-1 bg-white overflow-hidden">
      <!-- 左侧菜单 -->
      <side :class="['transition-all duration-300 ease-in-out', { '!w-0': !expand }, overmodalStyle]" />
      <!-- 右侧内容区 -->
      <main class="flex-1 px-6 py-4 overflow-auto">
        <tabs />
        <div class="px-4 py-4">
          <RouterView />
        </div>
      </main>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from "vue";
import { useRouter } from "vue-router";
import { msg } from "@/utils/msg";
import { useUserStore } from "@/store/user";
import { useTabsStore } from "@/store/tabs";
import { confirmMsg as confirm } from "@/utils/msg";
import { logout as logoutApi, changeRole } from "@/api/login";

const store = useUserStore();
const tabsStore = useTabsStore();
const router = useRouter();
const logoUrl = "https://ai-public.mastergo.com/ai/img_res/541285f7b8272f43415eb165626d720b.jpg";
//菜单是否展开
const expand = ref<boolean>(store.expand);
//展开的菜单高于遮罩层样式
const overmodalStyle = computed(() => {
  return expand.value && store.coursewareVisible
    ? {
      relative: true,
      'z-[3000]': true
    } : ""
});
//筛选角色列表
const filterRoles = computed(() => {
  return store.roles.filter(f => f.role_code != "admin" && f.role_code != "teacher");
});

/** 切换角色 */
const switchRole = async (role: string) => {
  tabsStore.closeAllTabs();
  try {
    const res = await changeRole({
      user_code: role
    });
    if (res.code == 0) {
      store.token = res.data.token;
      store.menuData = res.data.menus;
      store.userCode = role;
      store.sysCode = res.data.sys_code;
      store.setUserToken(res.data.token);
    } else {
      msg("error", res.message);
    }
  } catch { }
}

/** 菜单展开 */
const handleExpand = () => {
  expand.value = !expand.value;
  store.expand = expand.value;
}

const logout = () => {
  confirm("确定要退出登录吗？", "退出登录", action => {
    if (action) {
      logoutApi();
      store.logout();
      tabsStore.closeAllTabs();
      router.push("/login");
    }
  });
}

onMounted(() => {
  if (!store.token) {
    router.push("/login");
  }
})
</script>

<style scoped>
.el-menu {
  --el-menu-hover-bg-color: #374151;
}
</style>
