<template>
    <div class="home">
        <!-- 学习进度概览 -->
        <div class="bg-white rounded-lg shadow-[0_0_12px_2px_rgba(0,0,0,0.12)] p-6 mb-6">
            <h2 class="text-xl font-bold mb-4">学习进度概览</h2>
            <div class="grid grid-cols-3 gap-4">
                <div class="text-center p-4 bg-blue-50 rounded-lg">
                    <h3 class="text-gray-600 mb-2">总体完成率</h3>
                    <div class="text-3xl font-bold text-blue-600">{{ totalProgress }}%</div>
                </div>
                <div class="text-center p-4 bg-green-50 rounded-lg">
                    <h3 class="text-gray-600 mb-2">累计学习时长</h3>
                    <div class="text-3xl font-bold text-green-600">{{ totalHours }} 小时</div>
                </div>
                <div class="text-center p-4 bg-purple-50 rounded-lg">
                    <h3 class="text-gray-600 mb-2">已完成章节</h3>
                    <div class="text-3xl font-bold text-purple-600">{{ completedChapters }} 章</div>
                </div>
            </div>
        </div>

        <!-- 核心功能区 -->
        <div class="grid grid-cols-2 gap-6 mb-6">
            <!-- 我的课程 -->
            <div class="bg-white rounded-lg shadow-[0_0_12px_2px_rgba(0,0,0,0.12)] p-6">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-xl font-bold">我的课程</h2>
                    <el-button type="primary" size="small" class="!rounded-button whitespace-nowrap" @click="router.push('/lesson')">查看全部</el-button>
                </div>
                <div class="space-y-4">
                    <div v-for="course in myCourses" :key="course.id"
                        class="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer"
                        @click="goToCourseDetail(course.id)">
                        <div class="flex items-center">
                            <img :src="course.cover" class="w-12 h-12 rounded-md object-cover mr-3" alt="课程封面">
                            <div>
                                <div class="font-medium">{{ course.name }}</div>
                                <div class="text-sm text-gray-500">{{ course.progress }}% 已完成</div>
                            </div>
                        </div>
                        <el-button type="primary" size="small"
                            class="!rounded-button whitespace-nowrap" @click="router.push('/lesson')">继续学习</el-button>
                    </div>
                </div>
            </div>

            <!-- 学习资料 -->
            <div class="bg-white rounded-lg shadow-[0_0_12px_2px_rgba(0,0,0,0.12)] p-6">
                <h2 class="text-xl font-bold mb-4">学习资料</h2>
                <div class="grid grid-cols-3 gap-4">
                    <div v-for="type in resourceTypes" :key="type.id"
                        class="flex flex-col items-center p-4 rounded-lg bg-gray-50 hover:bg-gray-100 cursor-pointer"
                        @click="goToResource(type.id)">
                        <el-icon :size="32" class="mb-2" :color="type.color">
                            <component :is="type.icon" />
                        </el-icon>
                        <div class="font-medium">{{ type.name }}</div>
                        <div class="text-sm text-gray-500">{{ type.count }} 个资源</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 考试与测评 -->
        <div class="bg-white rounded-lg shadow-[0_0_12px_2px_rgba(0,0,0,0.12)] p-6 mb-6">
            <div class="flex justify-between items-center mb-4">
                <h2 class="text-xl font-bold">考试与测评</h2>
                <el-button type="primary" size="small" class="!rounded-button whitespace-nowrap">全部考试</el-button>
            </div>
            <div class="grid grid-cols-4 gap-4">
                <div v-for="exam in examList" :key="exam.id"
                    class="flex flex-col items-center p-4 rounded-lg bg-gray-50 hover:bg-gray-100 cursor-pointer"
                    @click="goToExam(exam.id)">
                    <div class="w-12 h-12 rounded-full flex items-center justify-center mb-2" :class="exam.bgColor">
                        <el-icon :size="24" color="white">
                            <component :is="exam.icon" />
                        </el-icon>
                    </div>
                    <div class="font-bold mb-1">{{ exam.name }}</div>
                    <div class="text-sm text-gray-500">{{ exam.status }}</div>
                </div>
            </div>
        </div>

        <!-- 最近学习 -->
        <div class="bg-white rounded-lg shadow-[0_0_12px_2px_rgba(0,0,0,0.12)] p-6">
            <h2 class="text-xl font-bold mb-4">最近学习</h2>
            <div class="space-y-4">
                <div v-for="record in learningHistory" :key="record.id"
                    class="flex p-4 border-b border-b-gray-200 hover:bg-gray-50" @click="router.push('/study-history')">
                    <div class="flex items-center">
                        <img :src="record.courseCover" class="w-10 h-10 rounded-md object-cover mr-3" alt="课程封面">
                        <div>
                            <div class="font-medium">{{ record.courseName }}</div>
                            <div class="text-sm text-gray-500">{{ record.chapter }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, markRaw } from "vue";
import { useRouter } from "vue-router";
import {
    VideoPlay,
    Document,
    Notebook,
    Clock,
    EditPen,
    Reading,
    CollectionTag
} from "@element-plus/icons-vue";

const router = useRouter();
// 总体完成进度
const totalProgress = ref(85);
// 总学习时长
const totalHours = ref(36);
// 已完成章节数
const completedChapters = ref(18);
// 我的课程列表
const myCourses = ref([
    {
        id: "1",
        name: "高等数学",
        cover: "https://mastergo.com/ai/api/search-image?query=advanced mathematics textbook on white background with clean layout&width=300&height=200&orientation=landscape&flag=9dfe11907ce0c9b2793a189e653afa4a",
        progress: 75
    },
    {
        id: "2",
        name: "大学英语",
        cover: "https://mastergo.com/ai/api/search-image?query=english learning book on white background with clean layout&width=300&height=200&orientation=landscape&flag=41072e4c47e39cd3bdbe9d1344f56583",
        progress: 45
    },
    {
        id: "3",
        name: "数据结构",
        cover: "https://mastergo.com/ai/api/search-image?query=data structure textbook on white background with clean layout&width=300&height=200&orientation=landscape&flag=5fcf1ff10b98b690991a3db530ec1989",
        progress: 90
    }
]);
// 资源类型列表
const resourceTypes = ref([
    {
        id: "video",
        name: "视频课程",
        icon: markRaw(VideoPlay),
        color: "#3b82f6",
        count: 28
    },
    {
        id: "document",
        name: "学习文档",
        icon: markRaw(Document),
        color: "#10b981",
        count: 42
    },
    {
        id: "exercise",
        name: "习题练习",
        icon: markRaw(EditPen),
        color: "#8b5cf6",
        count: 65
    }
]);
// 考试列表
const examList = ref([
    {
        id: "1",
        name: "期中考试",
        icon: markRaw(Reading),
        status: "未开始",
        bgColor: "bg-blue-300",
        statusColor: "text-blue-500"
    },
    {
        id: "2",
        name: "单元测试",
        icon: markRaw(CollectionTag),
        status: "已完成",
        bgColor: "bg-green-300",
        statusColor: "text-green-500"
    },
    {
        id: "3",
        name: "模拟考试",
        icon: markRaw(Notebook),
        status: "进行中",
        bgColor: "bg-yellow-300",
        statusColor: "text-yellow-500"
    },
    {
        id: "4",
        name: "期末考试",
        icon: markRaw(Clock),
        status: "未开始",
        bgColor: "bg-gray-300",
        statusColor: "text-gray-500"
    }
]);
// 学习历史记录
const learningHistory = ref([
    {
        id: "1",
        courseName: "高等数学",
        chapter: "第三章 微分方程",
        time: "2小时前",
        courseCover: "https://mastergo.com/ai/api/search-image?query=advanced mathematics textbook on white background with clean layout&width=300&height=200&orientation=landscape&flag=ac540e7851ea8261910cdbbca6bae05f"
    },
    {
        id: "2",
        courseName: "大学英语",
        chapter: "Unit 5 Reading",
        time: "昨天",
        courseCover: "https://mastergo.com/ai/api/search-image?query=english learning book on white background with clean layout&width=300&height=200&orientation=landscape&flag=40c4155c7071c5eb818cd93f21474430"
    },
    {
        id: "3",
        courseName: "数据结构",
        chapter: "树与二叉树",
        time: "3天前",
        courseCover: "https://mastergo.com/ai/api/search-image?query=data structure textbook on white background with clean layout&width=300&height=200&orientation=landscape&flag=a531d7b05beee7086ff9f80bbc293d4b"
    }
]);

/**
 * 初始化页面数据
 */
const initPageData = async () => {
    // 这里可以添加从API获取数据的逻辑
    console.log("初始化数据");
};

/**
 * 跳转到课程详情
 * @param courseId 课程ID
 */
const goToCourseDetail = (courseId: string) => {
    console.log("跳转到课程详情:", courseId);
};

/**
 * 跳转到资料中心
 * @param type 资料类型
 */
const goToResource = (type: string) => {
    console.log("跳转到资料中心:", type);
    router.push("/materials");
};

/**
 * 跳转到考试页面
 * @param examId 考试ID
 */
const goToExam = (examId: string) => {
    console.log("跳转到考试页面:", examId);
    router.push("/examine-evaluate");
};

// 页面加载时初始化数据
onMounted(() => {
    initPageData();
});
</script>

<style scoped>
.container {
    max-width: 1440px;
}
</style>