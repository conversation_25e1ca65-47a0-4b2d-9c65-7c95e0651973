<template>
    <div class="materials min-h-screen bg-gray-50 rounded-xl flex overflow-hidden" v-if="!previewVisible">
        <!-- 左侧筛选区域 -->
        <div class="w-64 bg-gray-100 border-r p-4 border-gray-200">
            <div class="space-y-6">
                <el-tree :data="categories" node-key="id" :props="defaultProps" :expand-on-click-node="false"
                    default-expand-all @node-click="handleSelectNode" class="border-none"></el-tree>
            </div>
        </div>

        <!-- 右侧内容区域 -->
        <div class="flex flex-1 flex-col p-6">
            <!-- 功能按钮区 -->
            <div class="flex justify-between items-center mb-6">
                <h2 class="min-w-[120px] text-2xl font-bold text-gray-800 whitespace-nowrap mr-6">
                    <div class="flex items-center cursor-pointer" v-if="viewRecord" @click="viewDownloadBack">
                        <el-icon size="24">
                            <ArrowLeft />
                        </el-icon>
                        <span class="ml-2">下载记录</span>
                    </div>
                    <span v-else>学习资料库</span>
                </h2>
                <div class="flex space-x-3">
                    <el-button class="!rounded-button whitespace-nowrap" @click="viewDownloadHistory">
                        <el-icon class="mr-1">
                            <Document />
                        </el-icon>
                        下载记录
                    </el-button>
                    <el-button class="!rounded-button whitespace-nowrap" @click="clearCache">
                        <el-icon class="mr-1">
                            <Delete />
                        </el-icon>
                        清除缓存
                    </el-button>
                </div>
            </div>

            <!-- 资料列表 -->
            <div v-if="loading" class="flex flex-1 justify-center items-center h-64">
                <el-icon class="animate-spin !text-4xl">
                    <Loading color="#2b7fff" />
                </el-icon>
            </div>

            <div v-else class="flex-1 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 items-start">
                <template v-if="!viewRecord">
                    <div class="flex h-full justify-center items-center col-span-1 md:col-span-2 lg:col-span-3"
                        v-if="!materials.length">
                        <el-icon size="24" color="#2b7fff">
                            <FolderOpened />
                        </el-icon>
                        <span class="text-gray-500 text-xl ml-2">暂无资料</span>
                    </div>
                    <div v-for="material in materials" :key="material.id"
                        class="min-w-[312px] bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300 border border-gray-100 overflow-hidden">
                        <div class="p-4">
                            <h3 class="text-lg font-medium text-gray-800 mb-1 truncate">
                                {{ material.name }}
                            </h3>
                            <div class="text-gray-600 text-sm mb-1">{{ material.description }}</div>
                            <div class="flex justify-between items-center text-sm text-gray-700 mb-3">
                                <span>{{ formatFileSize(material.size) }}</span>
                                <span class="mr-3">{{ material.ext.slice(1) }}</span>
                            </div>

                            <div class="flex justify-between items-center">
                                <div class="flex items-center">
                                    <el-icon v-if="material.download_url" class="mr-1">
                                        <Check color="#00c951" />
                                    </el-icon>
                                    <span class="text-sm">{{ material.download_url ? '已预览' : '未预览' }}</span>
                                </div>
                                <div class="flex space-x-2">
                                    <el-button size="small" class="!rounded-button whitespace-nowrap"
                                        @click="previewMaterial(material)">
                                        <el-icon class="mr-1">
                                            <View />
                                        </el-icon>
                                        预览
                                    </el-button>
                                    <el-button size="small" type="primary" class="!rounded-button whitespace-nowrap"
                                        @click="downloadMaterial(material.id)">
                                        <el-icon class="mr-1">
                                            <Download />
                                        </el-icon>
                                        下载
                                    </el-button>
                                </div>
                            </div>
                        </div>
                    </div>
                </template>
                <!-- 下载记录列表 -->
                <el-table :data="downloadList" style="width: 100%"
                    class="rounded-lg col-span-1 md:col-span-2 lg:col-span-3" v-else>
                    <el-table-column show-overflow-tooltip prop="ResourceName" label="资料名称" width="320">
                        <template #default="{ row }">
                            <div class="flex items-center">
                                <el-icon class="mr-2">
                                    <Document />
                                </el-icon>
                                <span class="truncate">{{ row.ResourceName }}</span>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column prop="resource_ext" label="类型">
                        <template #default="{ row }">{{ row.resource_ext.slice(1) }}</template>
                    </el-table-column>
                    <el-table-column prop="resource_size" label="大小">
                        <template #default="{ row }">
                            {{ formatFileSize(row.resource_size) }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="download_time" label="下载时间" width="180">
                        <template #default="{ row }">
                            {{ new Date(row.download_time * 1000).toLocaleString() }}
                        </template>
                    </el-table-column>
                </el-table>
            </div>

            <!-- 分页 -->
            <div class="mt-6 flex justify-center">
                <el-pagination v-model:current-page="pages.page" :page-size="pages.limit" :total="pages.total"
                    layout="prev, pager, next" background @current-change="handlePageChange" />
            </div>
        </div>
    </div>

    <div class="min-h-[80vh] bg-gray-50 rounded-xl" v-else>
        <el-button class="mb-4" @click="previewVisible = false">返回</el-button>
        <!-- 视频预览 -->
        <div v-if="videoFormat.includes(currentPreviewMaterial?.ext)"
            class="h-[80vh] bg-black flex items-center justify-center">
            <video v-if="videoUrl" class="w-full h-full" controls>
                <source :src="videoUrl" type="video/mp4">
                您的浏览器不支持视频播放
            </video>
            <div v-else class="text-white">
                暂不支持预览该视频格式
            </div>
        </div>
        <!-- 文档预览 -->
        <div v-else class="h-[80vh] bg-gray-50">
            <iframe v-if="fileUrl" :src="fileUrl" class="w-full h-full" frameborder="0"></iframe>
            <div v-else class="flex items-center justify-center h-full text-gray-500">
                暂不支持预览该文档格式
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, reactive } from "vue";
import { msg, confirmMsg as confirm } from "@/utils/msg";
import { syncMaterial, materialCategory, materialList, materialContent, downloadRecord, download } from "@/api/materials";
import { IPConfig } from "@/utils/request";

// 资料列表数据
const materials = ref<any[]>([]);
// 分类列表
const categories = ref<any[]>([]);
//当前选中的分类id
const cateId = ref<number>(0);
//树形选择默认属性
const defaultProps = {
    children: "children",
    label: "name",
};
// 加载状态
const loading = ref(false);
// 当前页码
const pages = reactive({
    page: 1,
    limit: 10,
    total: 0
});
//记录原始分页
let lastPages = { ...pages }
//查看下载记录
const viewRecord = ref<boolean>(false);
//下载记录列表
const downloadList = ref<any[]>([]);
// 预览对话框
const previewVisible = ref(false);
// 当前预览的资料
const currentPreviewMaterial = ref<any>(null);
//视频格式
const videoFormat = ref<string[]>([".mp4", ".avi", ".mkv", ".mov", ".webm", ".flv", ".wmv"]);
// PDF预览地址
const fileUrl = ref("");
// 视频预览地址
const videoUrl = ref("");

/** 选择分类节点 */
const handleSelectNode = (data: any) => {
    cateId.value = data.id;
    getMaterialList({ category_id: data.id });
}

/**
* 预览资料
*/
const previewMaterial = (material: any) => {
    if (!material.download_url) {
        msg("warning", "请先下载资料");
        return;
    }
    currentPreviewMaterial.value = material;
    if (videoFormat.value.includes(material.ext)) {
        videoUrl.value = `${IPConfig.API_URL}/resource-assets/${material.download_url}`;
        console.log(videoUrl.value);
    } else {
        fileUrl.value = `${IPConfig.API_URL}/resource-assets/${material.download_url}`;
        console.log(fileUrl.value);
    }
    previewVisible.value = true;
}

/**
 * 下载资料
 */
const downloadMaterial = async (id: number) => {
    try {
        const res = await download({
            resources_ids: [id]
        });
        if (res.code == 0) {
            await getMaterialList({ page: pages.page, category_id: cateId.value });
            const material = materials.value.find(f => f.id == id);
            if (material) {
                const a = document.createElement("a");
                const url = `${IPConfig.API_URL}/resource-assets/${material.download_url}`;
                a.href = url;
                a.setAttribute("download", material.name);
                a.setAttribute("target", "_blank");
                document.body.appendChild(a);
                a.click();
                a.remove();
                msg("success", "下载成功");
            }
        }
    } catch { }
}

/**
 * 清除缓存
 */
const clearCache = () => {
    confirm("确定要清除所有本地缓存资料吗?", "提示", action => {
        if (action) {
            msg("success", "缓存已清除");
        }
    })
}

/**
 * 查看下载记录
 */
const viewDownloadHistory = () => {
    if (viewRecord.value) return;
    getDownloadList();
    viewRecord.value = true;
    lastPages = { ...pages }
}

/** 下载记录返回 */
const viewDownloadBack = () => {
    viewRecord.value = false;
    pages.page = lastPages.page;
}

/** 格式化文件大小 */
const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
};

/** 获取下载记录 */
const getDownloadList = async (params?: { page?: number, page_size?: number }) => {
    params = params || {};
    try {
        const res = await downloadRecord(params);
        if (res.code == 0) {
            const data = res.data || {}
            downloadList.value = data.list || [];
            pages.total = data.total || 0;
        }
    } catch { }
}

/** 同步资料数据 */
const sync = async () => {
    try {
        const res = await syncMaterial();
        if (res.code == 0) {
            msg("success", "资料同步成功");
            getMaterialCategory();
        }
    } catch {
        console.log("资料同步失败");
    }
}

/** 获取资料分类 */
const getMaterialCategory = async () => {
    try {
        const res = await materialCategory();
        if (res.code == 0) {
            categories.value = res.data || [];
        }
    } catch { }
}

/** 获取资料列表 */
const getMaterialList = async (params?: { page?: number, page_size?: number, category_id?: number }) => {
    params = params || {};
    loading.value = true;
    return new Promise(async (resolve, reject) => {
        try {
            const res = await materialList(params);
            if (res.code == 0) {
                const data = res.data || {}
                materials.value = data.list || [];
                pages.total = data.total || 0;
            }
            resolve(res);
        } catch (e) {
            reject(e);
        }
        finally {
            loading.value = false;
        }
    });
}

/**
 * 分页变化
 */
const handlePageChange = (page: number) => {
    pages.page = page;
    if (viewRecord.value) {
        getDownloadList({ page });
    } else {
        getMaterialList({ page, category_id: cateId.value });
    }
}

sync();
getMaterialCategory();
</script>

<style scoped>
:deep(.el-tree) {
    .el-tree-node__content {
        height: auto;
        padding: 10px 0;
    }
}
</style>