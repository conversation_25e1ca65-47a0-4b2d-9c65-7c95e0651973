<template>
    <div class="min-h-screen w-full flex flex-col items-center justify-center bg-gray-800">
        <div class="w-[420px] bg-gray-700 rounded-lg shadow-xl p-8">
            <!-- Logo和标题区域 -->
            <div class="flex flex-col items-center mb-8">
                <!-- <div class="w-auto h-20 mb-4 overflow-hidden rounded-lg">
                    <img :src="logo" alt="TMSC Logo" class="w-full h-full object-cover">
                </div> -->
                <h1 class="text-2xl font-bold text-white mb-2">教学管理系统</h1>
                <p class="text-gray-400">Teaching Management System Center</p>
            </div>

            <!-- 登录表单 -->
            <el-form ref="formRef" :model="form" :rules="rules" @submit.prevent="handleLogin" class="space-y-6">
                <el-form-item prop="account" required>
                    <el-input v-model="form.account" :class="{ '!rounded-[6px]': true }" placeholder="请输入账号"
                        size="large">
                        <template #prefix>
                            <el-icon class="text-gray-400">
                                <User />
                            </el-icon>
                        </template>
                    </el-input>
                </el-form-item>

                <el-form-item prop="password" required>
                    <el-input v-model="form.password" :class="{ '!rounded-[6px]': true }" type="password"
                        placeholder="请输入密码" size="large" show-password>
                        <template #prefix>
                            <el-icon class="text-gray-400">
                                <Lock />
                            </el-icon>
                        </template>
                    </el-input>
                </el-form-item>

                <div class="flex items-center justify-between">
                    <el-checkbox v-model="store.remember" class="text-sm text-gray-600">记住密码</el-checkbox>
                    <!-- <a href="#" class="text-sm text-blue-600 hover:text-blue-800">忘记密码？</a> -->
                </div>

                <el-button type="primary" :class="{ '!rounded-button': true }"
                    class="w-full !bg-[#4A90E2] hover:!bg-blue-600 whitespace-nowrap" size="large" @click="handleLogin">
                    <template v-if="inLogin">
                        <el-icon class="animate-spin">
                            <Loading />
                        </el-icon>
                        <span class="ml-2">登录中...</span>
                    </template>
                    <span v-else>登录</span>
                </el-button>

                <div class="text-center text-sm text-gray-600">
                    <span>首次使用？</span>
                    <a href="#" class="text-blue-600 hover:text-blue-800 ml-1" @click="showConfig = true">配置</a>
                </div>
            </el-form>
        </div>

        <!-- 页脚 -->
        <footer class="mt-8 text-center text-gray-400 text-sm">
            <p class="mb-2">© 2024 TMSC教学管理系统 版权所有</p>
        </footer>

        <!-- 服务器ip配置 -->
        <el-dialog v-model="showConfig" :modal="false" :close-on-click-modal="false" :destroy-on-close="true"
            width="500px">
            <el-form ref="configFormRef" :model="configForm" :rules="configFormRules" label-width="50" class="mt-4">
                <el-form-item label="IP" prop="ip">
                    <el-input v-model="configForm.ip"></el-input>
                </el-form-item>
                <el-form-item label="Port" prop="port">
                    <el-input v-model="configForm.port"></el-input>
                </el-form-item>
            </el-form>
            <div class="flex justify-end">
                <div class="flex items-center">
                    <el-button @click="cancelConfig">取消</el-button>
                    <el-button type="primary" @click="saveConfig">保存</el-button>
                </div>
            </div>
        </el-dialog>
    </div>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import { useUserStore } from "@/store/user";
import { useRouter } from "vue-router";
import { msg } from "@/utils/msg";
import { login } from "@/api/login";
import { IPConfig } from "@/utils/request";
import type { FormInstance } from "element-plus";

import logo from "@/assets/logo.png";

interface LoginForm {
    account: string;
    password: string;
    user_code?: string;
}

interface IPConfig {
    ip: string;
    port: string;
}

const logoUrl = "https://ai-public.mastergo.com/ai/img_res/badf11400ab55bbe090c06c162eac419.jpg";
const formRef = ref<FormInstance>();
const form = ref<LoginForm>({
    account: "user",
    password: "123456"
});
const store = useUserStore()
const router = useRouter()
const rules = {
    account: [
        { required: true, message: "请输入用户名", trigger: "blur" },
        { min: 3, max: 20, message: "长度在 3 到 20 个字符", trigger: "blur", }
    ],
    password: [
        { required: true, message: "请输入密码", trigger: "blur" },
        { min: 3, max: 20, message: "长度在 3 到 18 个字符", trigger: "blur", }
    ]
};
//登录中
const inLogin = ref<boolean>(false);
//显示ip配置
const showConfig = ref<boolean>(false);
//配置表单实例
const configFormRef = ref<FormInstance>();
//配置ip表单数据
const configForm = ref<IPConfig>({
    ip: "*************",
    port: "8866"
});
const configFormRules = {
    ip: [
        {
            message: "请输入正确的IP地址",
            pattern: /^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,
            trigger: "blur"
        }
    ],
    port: [{ message: "请输入正确的端口号", pattern: /^(0|[1-9]\d{0,3}|[1-5]\d{4}|6[0-4]\d{3}|65[0-4]\d{2}|655[0-2]\d|6553[0-5])$/, trigger: "blur" }]
}

/** 登录 */
const handleLogin = () => {
    formRef.value?.validate(async valid => {
        if (valid) {
            if (inLogin.value) return;
            inLogin.value = true;
            try {
                if (store.userCode) {
                    form.value.user_code = store.userCode;
                }
                const res = await login(form.value);
                if (res.code == 0) {
                    const data = res.data || {}
                    msg("success", "登录成功");
                    store.setUserToken(data.token);
                    store.token = data.token;
                    store.menuData = data.menus;
                    store.userInfo = data.user;
                    store.roles = data.roles;
                    store.userCode = data.user_code;
                    store.sysCode = data.sys_code;
                    store.path = "/home";
                    router.push("/home");
                }
            } finally {
                inLogin.value = false;
            }
        }
    });
};

/** 取消配置 */
const cancelConfig = () => {
    showConfig.value = false;
}

/** 保存配置 */
const saveConfig = () => {
    configFormRef.value?.validate(valid => {
        if (valid) {
            if (!configForm.value.ip) {
                msg("warning", "请输入IP地址");
                return;
            }
            if (!configForm.value.port) {
                msg("warning", "请输入端口号");
                return;
            }
            IPConfig.API_URL = `http://${configForm.value.ip}:${configForm.value.port}`;
            showConfig.value = false;
            msg("success", "配置成功", 500);
        }
    });
}
</script>

<style scoped>
.el-input :deep(.el-input__wrapper) {
    box-shadow: 0 0 0 1px #dcdfe6 inset;
}

.el-input :deep(.el-input__wrapper.is-focus) {
    box-shadow: 0 0 0 1px #4A90E2 inset;
}

.el-input :deep(.el-input__inner) {
    height: 40px;
}

.el-checkbox :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
    background-color: #4A90E2;
    border-color: #4A90E2;
}

.el-checkbox :deep(.el-checkbox__input.is-checked + .el-checkbox__label) {
    color: #4A90E2;
}

.el-button.el-button--primary {
    background-color: #4A90E2;
    border-color: #4A90E2;
}

.el-button.el-button--primary:hover {
    background-color: #357abd;
    border-color: #357abd;
}
</style>
