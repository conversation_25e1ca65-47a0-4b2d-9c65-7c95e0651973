<!-- 代码已包含 CSS：使用 TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 -->
<template>
    <div class="min-h-screen bg-gray-50 py-8 overflow-auto">
        <div class="mx-auto max-w-7xl px-4">
            <div class="flex justify-between items-center mb-8">
                <div class="">
                    <h1 class="text-3xl font-bold text-gray-900 mb-2">学习数据概览</h1>
                    <p class="text-gray-600">实时跟踪您的学习进度和表现</p>
                </div>
                <div class="flex items-center space-x-8">
                    <!-- 同步进度 -->
                    <button @click=""
                        class="rounded-md whitespace-nowrap px-4 py-2 bg-blue-600 text-white hover:bg-blue-700 flex items-center self-end">
                        <el-icon class="mr-1">
                            <Refresh />
                        </el-icon>
                        同步进度
                    </button>
                    <!-- 选择时间 -->
                    <div>
                        <span class="block text-sm font-medium text-gray-700 mb-1">选择时间</span>
                        <el-date-picker v-model="date" type="datetime" placeholder="选择时间"
                            value-format="YYYY-MM-DD HH:mm:ss" format="YYYY-MM-DD HH:mm:ss" />
                    </div>
                    <!-- 课程选择 -->
                    <div class="w-55">
                        <span class="block text-sm font-medium text-gray-700 mb-1">选择课程</span>
                        <el-select v-model="selectedCourse" placeholder="全部课程" class="w-full" clearable>
                            <el-option v-for="course in courses" :key="course.id" :label="course.name"
                                :value="course.id" />
                        </el-select>
                    </div>
                </div>
            </div>
            <div class="grid grid-cols-3 gap-6 mb-8">
                <!-- 总体完成率 -->
                <div class="bg-white rounded-lg p-6 shadow-sm">
                    <div class="flex items-center">
                        <!-- 左侧饼图 -->
                        <div class="relative w-32 h-32 mr-6" ref="progressChart"></div>

                        <!-- 右侧文字 -->
                        <div class="flex flex-col">
                            <h4 class="text-lg font-medium text-gray-700 mb-1">总体完成率</h4>
                            <p class="text-4xl font-bold text-gray-900">80%</p>
                        </div>
                    </div>
                </div>
                <!-- 本周学习时长 -->
                <div class="bg-white rounded-lg p-6 shadow-sm">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-medium">本周学习时长</h3>
                        <span class="text-2xl font-bold text-indigo-600">12.5h</span>
                    </div>
                    <div class="h-2 bg-gray-200 rounded-full">
                        <div class="h-2 bg-indigo-600 rounded-full" style="width: 75%"></div>
                    </div>
                    <p class="mt-2 text-sm text-gray-600">较上周提升 15%</p>
                </div>
                <!-- 最后学习记录 -->
                <div class="bg-white rounded-lg p-6 shadow-sm">
                    <h3 class="text-lg font-medium mb-4">最后学习记录</h3>
                    <div class="space-y-2">
                        <div class="flex items-center text-gray-600">
                            <el-icon class="mr-2">
                                <Timer />
                            </el-icon>
                            <span>最后学习时间：2024-01-15 20:30</span>
                        </div>
                        <div class="flex items-center text-gray-600">
                            <el-icon class="mr-2">
                                <Document />
                            </el-icon>
                            <span>学习章节：数据结构基础 - 第三章</span>
                        </div>
                        <div class="flex items-center text-gray-600">
                            <el-icon class="mr-2">
                                <Clock />
                            </el-icon>
                            <span>本次学习时长：1.5 小时</span>
                        </div>
                    </div>
                </div>
            </div>
            <!-- 章节完成度 -->
            <div class="bg-white rounded-lg p-8 shadow-sm mb-8">
                <div class="flex items-center justify-between mb-8">
                    <h3 class="text-xl font-semibold text-gray-800">章节完成度</h3>
                    <div class="flex items-center gap-2">
                        <span class="w-3 h-3 rounded-full bg-indigo-600"></span>
                        <span class="text-sm text-gray-600">当前进度</span>
                    </div>
                </div>
                <div class="space-y-8">
                    <div v-for="(chapter, index) in chapters" :key="index">
                        <div class="flex items-center justify-between mb-3">
                            <div class="flex items-center gap-3">
                                <span
                                    class="flex items-center justify-center w-6 h-6 rounded-full bg-indigo-100 text-indigo-600 text-sm font-medium">{{
                                        index
                                        + 1 }}</span>
                                <span class="text-gray-700 font-medium">{{ chapter.name }}</span>
                            </div>
                            <span class="text-sm font-bold" :class="[
                                chapter.progress === 100 ? 'text-green-600' : 'text-indigo-600'
                            ]">{{ chapter.progress }}%</span>
                        </div>
                        <div class="h-2.5 bg-gray-100 rounded-full overflow-hidden">
                            <div class="h-full rounded-full transition-all duration-500 ease-in-out" :class="[
                                chapter.progress === 100 ? 'bg-green-500' : 'bg-indigo-600'
                            ]" :style="{ width: `${chapter.progress}%` }"></div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- 每日学习时长统计 -->
            <div class="bg-white rounded-lg p-6 shadow-sm">
                <h3 class="text-lg font-medium mb-6">每日学习时长统计</h3>
                <div ref="timeChart" style="height: 400px"></div>
            </div>
        </div>
    </div>
</template>
<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import * as echarts from 'echarts';

const progressChart = ref<HTMLElement | null>(null);
const timeChart = ref<HTMLElement | null>(null);
const chapters = ref([
    { name: '编程基础入门', progress: 100 },
    { name: '变量与数据类型', progress: 100 },
    { name: '控制流程', progress: 85 },
    { name: '函数与模块化', progress: 60 },
    { name: '面向对象编程', progress: 40 },
    { name: '数据结构基础', progress: 20 },
]);
//搜索时间
const date = ref<string>("");
// 选中的课程
const selectedCourse = ref("");
// 课程列表
const courses = ref([
    { id: "1", name: "计算机科学导论" },
    { id: "2", name: "数据结构与算法" },
    { id: "3", name: "数据库系统原理" },
    { id: "4", name: "计算机网络" },
    { id: "5", name: "操作系统原理" }
]);

onMounted(() => {
    if (progressChart.value) {
        const chart = echarts.init(progressChart.value)

        chart.setOption({
            series: [
                {
                    type: 'pie',
                    radius: ['80%', '95%'], // 外圈和内圈比例，形成环形图
                    avoidLabelOverlap: false,
                    itemStyle: {
                        borderRadius: 5,
                        borderColor: '#fff',
                        borderWidth: 2
                    },
                    label: {
                        show: false // 不显示百分比标签
                    },
                    emphasis: {
                        label: {
                            show: false
                        }
                    },
                    data: [
                        { value: 80, name: '已完成', itemStyle: { color: '#6366f1' } }, // 蓝色
                        { value: 20, name: '未完成', itemStyle: { color: '#e5e7eb' } } // 灰色
                    ],
                    animation: false
                }
            ]
        })
    }
    if (timeChart.value) {
        const chart = echarts.init(timeChart.value);
        chart.setOption({
            tooltip: {
                trigger: 'axis'
            },
            xAxis: {
                type: 'category',
                data: ['1月9日', '1月10日', '1月11日', '1月12日', '1月13日', '1月14日', '1月15日'],
                axisLine: {
                    lineStyle: {
                        color: '#ddd'
                    }
                }
            },
            yAxis: {
                type: 'value',
                name: '学习时长(小时)',
                axisLine: {
                    lineStyle: {
                        color: '#ddd'
                    }
                },
                splitLine: {
                    lineStyle: {
                        color: '#eee'
                    }
                }
            },
            series: [{
                data: [2.5, 1.8, 3.2, 2.1, 2.8, 3.5, 1.5],
                type: 'bar',
                itemStyle: {
                    color: '#6366f1'
                },
                barWidth: '40%',
                label: {
                    show: true,
                    position: 'top',
                    formatter: '{c}h'
                },
                animation: false
            }]
        });
    }
});
</script>
<style scoped>
.el-icon {
    font-size: 1.25rem;
}
</style>
