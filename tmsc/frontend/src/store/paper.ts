import { defineStore } from 'pinia'
import { computed, ref } from 'vue'

export const usePaperStore = defineStore(
  'paper',
  () => {
    //试卷数据
    const paper: any = ref({})
    //试卷题目
    const questions: any = ref([])

    const preview_drawer = ref(false)
    const paper_id = ref(0)
    const exam_id = ref(0)
    const progress_id = ref(0)
    //是否阅读并确认考试
    const is_confirm = ref<boolean>(false)

    // 倒计时时间
    const end_at = ref(0)
    const initPaper = () => {
      paper.value = {}
      questions.value = []
      paper_id.value = 0
      exam_id.value = 0
      is_confirm.value = false
      progress_id.value = 0
      // end_at.value = 0
    }
    return {
      paper,
      questions,
      preview_drawer,
      paper_id,
      initPaper,
      is_confirm,
      exam_id,
      progress_id,
      end_at,
    }
  },
  {
    persist: true, // 开启持久化（默认使用 localStorage）
  }
)
