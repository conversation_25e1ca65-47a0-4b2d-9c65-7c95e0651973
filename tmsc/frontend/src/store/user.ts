import { defineStore } from 'pinia'
import { ref } from 'vue'
import { setToken, clearToken } from '@/utils/request'

export const useUserStore = defineStore('user', () => {
  const token = ref('');
  const path = ref('/home');
  const remember = ref<boolean>(false);
  const menuData = ref<any[]>([
    {
      index: '1',
      icon: 'House',
      title: '首页',
      path: '/home'
    },
    {
      index: '2',
      icon: 'Reading',
      title: '自主学习',
      children: [
        {
          index: '2-1',
          title: '课程管理',
          path: '/lesson'
        },
        {
          index: '2-2',
          title: '查看学习进度',
          path: '/personal-progress'
        },
        {
          index: '2-3',
          title: '自测评估',
          path: '/self-test'
        }
      ]
    },
    {
      index: '3',
      icon: 'SuitcaseLine',
      title: '考核评估',
      children: [
        {
          index: '3-1',
          title: '学员考核评估',
          path: '/examine-evaluate'
        },
        {
          index: '3-2',
          title: '评估分析查看',
          path: '/evaluate-analysis'
        }
      ]
    },
    {
      index: '4',
      icon: 'Suitcase',
      title: '科目考核',
      path: '/subject-examine'
    },
    {
      index: '5',
      icon: 'Document',
      title: '资料查看',
      path: '/materials'
    },
    {
      index: '6',
      icon: 'User',
      title: '用户管理',
      path: '/user'
    },
    {
      index: '7',
      icon: 'Clock',
      title: '学习历史',
      path: '/study-history'
    },
    {
      index: '8',
      icon: 'Medal',
      title: '证书管理',
      path: '/certificate'
    }
  ])
  const userInfo = ref<any>({});
  //角色列表
  const roles = ref<any[]>([]);
  //用户角色
  const userCode = ref<string>("");
  //内置角色
  const sysCode = ref<string>("");
  //菜单展开
  const expand = ref<boolean>(true);
  //课件是否显示
  const coursewareVisible = ref<boolean>(false);
  //考试完成
  const examCompleted = ref<boolean>(false);
  //scorm会话id
  const scormSessionId = ref<number>(0);
  //课件终止状态
  const scormTerminated = ref<boolean>(false);

  const setUserToken = (token: string) => setToken(token);

  const logout = () => {
    clearToken();
    token.value = "";
    path.value = "/home";
    menuData.value = [];
    userInfo.value = {}
    roles.value = [];
    userCode.value = "";
    sysCode.value = "";
    expand.value = true;
    coursewareVisible.value = false;
  }

  return {
    token,
    menuData,
    userInfo,
    roles,
    userCode,
    sysCode,
    path,
    remember,
    setUserToken,
    logout,
    expand,
    coursewareVisible,
    examCompleted,
    scormSessionId,
    scormTerminated
  }
}, {
  persist: {
    key: 'userStore',
    pick: ['menuData', 'token', 'path', 'remember', 'userInfo', 'roles', 'userCode', 'sysCode', 'coursewareVisible'],
  },
})
