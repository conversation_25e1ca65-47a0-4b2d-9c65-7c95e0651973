// src/store/tabsStore.ts
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { useUserStore } from './user'

const userStore = useUserStore()
export const useTabsStore = defineStore(
    'tabs',
    () => {
        // 响应式状态
        const tabs = ref([
            {
                path: '/home',
                title: '首页',
                affix: true,
            },
        ])

        // 获取非固定标签页
        const dynamicTabs = computed(() => tabs.value.filter((tab) => !tab.affix))

        // 初始化tab
        function initTabs() {
            tabs.value = [
                {
                    path: '/home',
                    title: '首页',
                    affix: true,
                },
            ]
        }
        // 添加 tab
        function addTab(newTab: { path: string; title: any; affix?: boolean }) {
            if (tabs.value.some((tab) => tab.path === newTab.path) || newTab.path == "/login") return
            tabs.value.push({
                ...newTab,
                affix: newTab.affix || false,
            })
        }

        // 移除 tab
        function removeTab(path: string) {
            if(path == "/exam" && !userStore.examCompleted) return;
            const index = tabs.value.findIndex((tab) => tab.path === path)
            if (index > -1 && !tabs.value[index].affix) {
                tabs.value.splice(index, 1)
            }
        }

        // 关闭其他 tab
        function closeOtherTabs(keepPath: string) {
            const homeTab = tabs.value.find((tab) => tab.path === '/home')
            tabs.value = [
                ...(homeTab ? [homeTab] : []),
                ...tabs.value.filter((tab) => tab.path === keepPath && !tab.affix),
            ]
        }

        // 关闭所有 tab
        function closeAllTabs() {
            tabs.value = tabs.value.filter((tab) => tab.affix || tab.path == '/exam' && !userStore.examCompleted)
        }

        return {
            tabs,
            dynamicTabs,
            initTabs,
            addTab,
            removeTab,
            closeOtherTabs,
            closeAllTabs
        }
    },
    {
        persist: true, // 开启持久化（默认使用 localStorage）
    }
)
