import { get, post } from "@/utils/request";

/** 获取用户列表 */
export const userList = (params: { page?: number, page_size?: number }) => {
    params = { page: 1, page_size: 10, ...params }
    return get("user/users/list", params);
}

/** 编辑用户 */
export const updateUser = (data: any) => {
    return post("user/users/update", data);
}

/** 删除用户 */
export const deleteUser = (id: number) => post(`user/users/delete/${id}`);