import { get, post } from "@/utils/request";

/** 远程同步计划 */
export const syncPlan = () => post("user/teach_plan/grab");

/** 清除课程缓存 */
export const clearCache = (course_id: number) => post(`user/courses/clear_cache/${course_id}`);

/** 获取课程列表 */
export const courseList = (params: { name?: string }) => get("user/courses/list", params);

/** 获取章节列表 */
export const chapterList = (id: number) => get(`user/courses/chapter_list/${id}`);

/** 获取课件列表 */
export const coursewareList = (id: number) => get(`user/courses/courseware_list/${id}`);

/** 获取试题列表 */
export const questionList = (id: number) => get(`user/courses/question_list/${id}`);

/** 获取资料列表 */
export const materialList = (params: { chapter_id: number }) => get("user/resources/list", params)