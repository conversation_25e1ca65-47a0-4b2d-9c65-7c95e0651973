import { get, post } from "@/utils/request";

/** 同步资料 */
export const syncMaterial = () => post("user/resources/grab");

/** 获取资料分类 */
export const materialCategory = () => get("user/resources/category");

/** 获取资料列表 */
export const materialList = (params: { page?: number, page_size?: number, category_id?: number }) => {
    params = { page: 1, page_size: 10, ...params };
    return get("user/resources/list", params);
}

/** 获取资料内容 */
export const materialContent = (id: number) => get(`user/resources/content/${id}`);

/** 获取下载记录 */
export const downloadRecord = (params: { page?: number, page_size?: number }) => {
    params = { page: 1, page_size: 10, ...params };
    return get("user/resources/download/history", params);
}
/** 下载资料 */
export const download = (data: { resources_ids: number[] }) => post("user/resources/download", data);