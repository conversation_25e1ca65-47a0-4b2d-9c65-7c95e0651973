import { get, post } from '@/utils/request'

/** 理论试题 */
export interface Question {
  id: number;
  user_id: number;
  title: string;
  question_type: QuestionType;
  content: string;
  answer: string;
  score_points: string;
  score: number;
  minutes: number;
  options: string;
  status: string;
  created_at: number;
  updated_at: number;
  submit_at: number;
  published_at: number;
  version: string;
}

export type QuestionType = 'single' | 'multiple' | 'judge' | 'fill' | 'short' | 'discuss' | 'analyze' | 'comprehensive' | 'self';

/** 试题类型映射 */
export const typeMap: Record<string, string> = {
  single: '单选题',
  multiple: '多选题',
  judge: '判断题',
  fill: '填空题',
  short: '简答题',
  discuss: '论述题',
  analyze: '分析题',
  comprehensive: '综合题',
  self: '自拟题',
}

/** 试题标签映射 */
export const tagMap: Record<string, string> = {
  single: 'primary',
  multiple: 'success',
  judge: 'warning',
  fill: 'info',
  short: 'danger',
  discuss: 'primary',
  analyze: 'success',
  comprehensive: 'warning',
  self: 'info',
}

/** 试题状态映射 */
export const typeStatusMap: Record<string, string> = {
  single: '#409EFF',
  multiple: '#2dc575',
  judge: '#2dafc5',
  fill: '#2da3c5',
  short: '#E6A23C',
  discuss: '#bdd0d1',
  analyze: '#4858b9',
  comprehensive: '#a6a6ff',
  self: '#86b568',
}

/** 考试同步 */
export const syncExam = () => post('user/exam/grab')

/** 考试列表 */
export const examList = (params: {
  page?: number
  page_size?: number
  exam_type?: string
}) => {
  params = { page: 1, page_size: 10, ...params }
  return get('user/exam/list', params)
}

/** 开始考试 */
export const examStart = (data: { id: number }) =>
  post('user/exam/start', data).then((res) => res.data)

/** 获取试卷 */
export const examPaper = (paper_id: number) =>
  get(`user/exam/paper/${paper_id}`)

/** 提交答案 */
export const examSubmit = (data: any) => post('user/exam/submit', data)

/** 考试记录 */
export const examRecord = () => get("user/exam/hisrory");
