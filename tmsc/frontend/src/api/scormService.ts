import { get, post } from "@/utils/request";

interface getValueParams {
    session_id: number;
    key: string;
}

type setValueData = getValueParams & {
    value: string;
}

/** 初始化 */
export const initialize = (data: { sco_id: number }) => post("user/lms/scorm/initialize", data);

/** 获取值 */
export const getValue = (params: getValueParams) =>
    get("user/lms/scorm/get-value", params);

/** 设置值 */
export const setValue = (data: setValueData) => post("user/lms/scorm/set-value", data);

/** 导航 */
export const navigate = (data: { session_id: number; request: string }) => post("user/lms/scorm/navigate", data);

/** 提交 */
export const commit = (data: { session_id: number }) => post("user/lms/scorm/commit", data);

/** 终止 */
export const terminate = (data: { session_id: number }) => post("user/lms/scorm/terminate", data);

/** 最近错误 */
export const getLastError = (params: {  session_id: number }) => get("user/lms/scorm/get-last-error", params);

/** 获取错误信息 */
export const getErrorString = (params: { errorCode: string }) => get("user/lms/scorm/get-error-string", params);

/** 虚拟课件导航 */
export const virtualNavigate = (data: { courseware_id: number }) => post("user/virtual_courseware/navigate", data);
