
| 表名称 | 表注释 | 详细用途 |
| --- | --- | --- |
| `adjustment_records` | 调整记录表 | 记录进度状态调整的历史详情，包括调整原因、调整人和时间等信息 |
| `alert_rules` | 预警规则配置表 | 存储预警规则的详细信息，如规则条件、预警等级及推荐干预措施 |
| `assessment_adjustments` | 考核计划调整记录表 | 跟踪考核计划的调整历史，包括原值、新值及调整原因等 |
| `assessment_alert_rules` | 考核预警规则配置表 | 定义考核相关的预警规则，包括触发条件和动作 |
| `assessment_plans` | 考核计划表 | 管理课程考核计划的信息，如考核类型、方法、权重等 |
| `audit_logs` | 审计日志表 | 记录用户的操作日志，用于审计和追踪用户行为 |
| `base_data` | 基础数据项表 | 维护系统基础数据项，如科目类别、课程类型等 |
| `base_data_ext` | 基础数据扩展表 | 扩展基础数据项的附加信息，通过键值对形式存储 |
| `certificates` | 证书表 | 管理学生获得的电子或纸质证书信息，包括签名、二维码等内容 |
| `classroom_usage` | 教室资源占用表 | 记录教室的使用情况，包括使用时间和用途说明 |
| `course_ext` | 课程扩展字段表 | 扩展课程相关信息，通过键值对形式存储 |
| `course_knowledge` | 课程知识点表 | 管理课程的知识点及其难度等级 |
| `course_review` | 课程审核记录表 | 记录课程审核的过程和结果，包括审核意见 |
| `course_revision` | 课程修订记录表 | 跟踪课程修订的历史，包括修订内容和说明 |
| `course_teacher` | 课程-教师关联表 | 关联课程与授课教师，记录教师角色（主讲/助教） |
| `course_textbook` | 课程-教材关联表 | 关联课程与推荐教材 |
| `courses` | 课程表 | 存储课程的基本信息，如课程代码、标题、描述等 |
| `courseware` | 课件表 | 存储课件的相关信息，包括文件类型、路径和适用对象 |
| `courseware_chapter` | 课件-章节关联表 | 关联课件与其所属的章节 |
| `courseware_ext` | 课件扩展字段表 | 扩展课件相关信息，通过键值对形式存储 |
| `courseware_parse_status` | 课件解析状态表 | 记录课件解析的状态，包括失败原因和解析结果 |
| `courseware_review` | 课件审核记录表 | 记录课件审核的过程和结果，包括审核意见 |
| `courseware_revision` | 课件修订记录表 | 跟踪课件修订的历史，包括修订内容和说明 |
| `courseware_statistics` | 课件使用统计表 | 统计课件的下载和访问情况 |
| `evaluation_criteria` | 评估标准表 | 定义不同类型的评估标准和权重 |
| `exam_records` | 考试记录表 | 记录学生的考试答题情况和得分 |
| `exams` | 考试表 | 管理考试的信息，包括考试时间、考生范围和规则 |
| `flow_check` | 审批任务处理记录表 | 记录审批任务的处理过程和结果 |
| `form_data` | 审批表单数据表 | 存储审批表单的数据和状态 |
| `grade_leader` | 年级组长关联表 | 关联年级组长与年级和学年 |
| `knowledge_intervention` | 薄弱点知识干预建议表 | 提供薄弱知识点的干预建议和推荐资源 |
| `learning_actions` | 学习行为扩展表 | 扩展学习行为的相关信息 |
| `learning_progress` | 学习进度记录表 | 记录学生的学习进度和完成情况 |
| `learning_progress_summary` | 学习进度汇总表 | 汇总学生的学习进度和成绩 |
| `learning_summary` | 学习进度汇总表 | 汇总学生的学习时长和章节完成率 |
| `manual_record_logs` | 手动记录日志表 | 记录手动录入学习进度的日志 |
| `manual_score_logs` | 手动成绩记录与调整日志表 | 记录手动录入和调整成绩的日志 |
| `material_tags` | 资料-标签关联表 | 关联资料与标签 |
| `materials` | 资料表 | 存储教学资料的相关信息，包括文件类型、路径和加密算法 |
| `menu_role` | 菜单角色关联表 | 关联菜单与角色，管理角色权限 |
| `menus` | 菜单表 | 存储系统的菜单信息，包括路由路径和权限标识 |
| `minio_files` | MinIO文件存储元数据表 | 存储MinIO文件的元数据信息 |
| `papers` | 试卷表 | 管理试卷的信息，包括组卷规则和试题列表 |
| `permission_cache` | 用户权限缓存表 | 缓存用户的权限信息 |
| `permissions` | 权限表 | 存储系统权限的信息，包括权限标识和模块 |
| `plugins` | 播放器插件表 | 存储播放器插件的相关信息，包括版本和兼容性 |
| `prerequisite_courses` | 先修课程关联表 | 关联当前课程与先修课程 |
| `progress_alerts` | 学习进度预警表 | 记录学习进度的预警信息和推荐干预措施 |
| `questions` | 试题库表 | 存储试题的相关信息，包括题型、知识点和难度 |
| `resource_chapter` | 教学资源-章节关联表 | 关联教学资源与章节 |
| `review_tasks` | 审核任务表 | 管理审核任务的信息，包括审核人和状态 |
| `role_permissions` | 角色权限关联表 | 关联角色与权限，管理角色权限 |
| `roles` | 角色表 | 存储系统角色的信息，包括角色名称和描述 |
| `schedule_sync` | 排课系统同步表 | 记录排课系统的同步状态和失败原因 |
| `scorm_packages` | SCORM包表 | 存储SCORM包的相关信息，包括入口文件路径和密钥标识 |
| `scorm_sco` | SCORM学习对象表 | 存储SCORM学习对象的相关信息，包括标题和入口URL |
| `scorm_tracking` | SCORM学习进度跟踪表 | 跟踪用户在SCORM学习对象中的学习进度 |
| `security_event` | 安全事件告警表 | 记录安全事件的详细信息和严重级别 |
| `security_policy` | 安全策略配置表 | 存储安全策略的规则和生效时间 |
| `syllabus` | 教学大纲表 | 存储教学大纲的相关信息，包括章节结构和参考资料 |
| `syllabus_ext` | 教学大纲扩展表 | 扩展教学大纲的相关信息，通过键值对形式存储 |
| `syllabus_revision` | 教学大纲修订记录表 | 跟踪教学大纲修订的历史，包括修订内容和说明 |
| `system_config` | 系统配置表 | 存储系统的配置信息，包括数据库密码和参数名 |
| `teacher_schedule` | 教师课表安排表 | 记录教师的课表安排，包括时间、地点和状态 |
| `teaching_plan` | 教学计划表 | 存储教学计划的相关信息，包括课程列表和教学进度 |
| `teaching_plan_ext` | 教学计划扩展字段表 | 扩展教学计划的相关信息，通过键值对形式存储 |
| `teaching_plan_review` | 教学计划审核记录表 | 记录教学计划审核的过程和结果 |
| `textbook` | 教材表 | 存储教材的相关信息，包括作者、出版社和库存数量 |
| `user_roles` | 用户角色关联表 | 关联用户与角色，管理用户权限 |
| `users` | 用户表 | 存储用户的基本信息，包括用户名、密码和邮箱 |
| `workflow` | 审批流程模板表 | 存储审批流程模板的相关信息，包括节点配置 |
| `workflow_template` | 审核流程模板表 | 存储审核流程模板的相关信息，包括节点配置 |
| `grade` | 年级表 | 存储年级信息 |
| `class` | 班级表 | 存储班级信息 |
| `grade_class` | 年级与班级关系表 | 存储年级与班级的管理关系信息 |
| `class_user` | 班级老师学员表 | 存储班级老师学员的关联关系 |
| `class_teaching_plan` | 班级与教学计划关联关系表 | 存储班级与教学计划关联关系 |
| `class_course` | 班级与课程关系表 | 存储班级与课程的关联关系 |
| `class_questions` | 班级与题库关系表 | 存储班级与试题库的关联关系 |
| `class_papers` | 班级与考试试卷关系表 | 存储班级与考试试卷的关联关系 |
