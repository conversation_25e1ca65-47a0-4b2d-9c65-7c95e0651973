/*
 Navicat Premium Dump SQL

 Source Server         : temp
 Source Server Type    : MySQL
 Source Server Version : 90100 (9.1.0)
 Source Host           : localhost:3306
 Source Schema         : tms

 Target Server Type    : MySQL
 Target Server Version : 90100 (9.1.0)
 File Encoding         : 65001

 Date: 25/06/2025 16:58:09
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for adjustment_records
-- ----------------------------
DROP TABLE IF EXISTS `adjustment_records`;
CREATE TABLE `adjustment_records` (
  `adjustment_id` bigint NOT NULL AUTO_INCREMENT,
  `progress_id` bigint NOT NULL COMMENT '原进度ID',
  `new_status` enum('not_started','in_progress','completed') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '新状态',
  `reason` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '调整原因',
  `adjusted_by` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '调整人ID',
  `adjusted_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '调整时间',
  PRIMARY KEY (`adjustment_id`) USING BTREE,
  KEY `idx_adjustment_progress` (`progress_id`) USING BTREE,
  KEY `idx_adjustment_user` (`adjusted_by`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='调整记录表';

-- ----------------------------
-- Table structure for alert_rules
-- ----------------------------
DROP TABLE IF EXISTS `alert_rules`;
CREATE TABLE `alert_rules` (
  `rule_id` bigint NOT NULL AUTO_INCREMENT,
  `rule_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '规则名称',
  `rule_condition` json NOT NULL COMMENT '规则条件（JSON格式，如章节完成率<50%）',
  `alert_level` enum('low','medium','high') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '预警等级',
  `recommendation` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '推荐干预措施',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`rule_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='预警规则配置表';

-- ----------------------------
-- Table structure for assessment_adjustments
-- ----------------------------
DROP TABLE IF EXISTS `assessment_adjustments`;
CREATE TABLE `assessment_adjustments` (
  `adjustment_id` bigint NOT NULL AUTO_INCREMENT,
  `assessment_plan_id` bigint NOT NULL COMMENT '关联考核计划ID',
  `adjusted_by` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '调整人ID',
  `original_value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '原值（JSON格式）',
  `new_value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '新值（JSON格式）',
  `reason` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '调整原因',
  `adjusted_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '调整时间',
  PRIMARY KEY (`adjustment_id`) USING BTREE,
  KEY `idx_adjustment_plan` (`assessment_plan_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='考核计划调整记录表';

-- ----------------------------
-- Table structure for assessment_alert_rules
-- ----------------------------
DROP TABLE IF EXISTS `assessment_alert_rules`;
CREATE TABLE `assessment_alert_rules` (
  `rule_id` bigint NOT NULL AUTO_INCREMENT,
  `rule_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '规则名称',
  `rule_description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '规则描述',
  `condition` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '触发条件（JSON格式）',
  `action` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '触发动作（JSON格式）',
  `status` enum('active','inactive') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'active' COMMENT '状态',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`rule_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='考核预警规则配置表';

-- ----------------------------
-- Table structure for assessment_plans
-- ----------------------------
DROP TABLE IF EXISTS `assessment_plans`;
CREATE TABLE `assessment_plans` (
  `assessment_plan_id` bigint NOT NULL AUTO_INCREMENT,
  `course_id` bigint NOT NULL,
  `assessment_type` enum('daily','midterm','final','makeup') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `assessment_time` datetime NOT NULL,
  `method` enum('written','practical','project') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `weight` decimal(5,2) NOT NULL COMMENT '成绩占比',
  `location` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '考核地点',
  `invigilator_ids` json DEFAULT NULL COMMENT '监考教师ID列表',
  `syllabus_id` bigint DEFAULT NULL COMMENT '关联教学大纲ID',
  `original_assessment_id` bigint DEFAULT NULL COMMENT '原考核计划ID（补考关联）',
  `makeup_students` json DEFAULT NULL COMMENT '补考学生列表（JSON数组）',
  `makeup_location` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '补考地点',
  `status` enum('draft','reviewing','approved','published') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'draft',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `published_at` datetime DEFAULT NULL COMMENT '发布时间',
  `effective_date` date DEFAULT NULL COMMENT '生效时间',
  PRIMARY KEY (`assessment_plan_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='考核计划表';

-- ----------------------------
-- Table structure for audit_logs
-- ----------------------------
DROP TABLE IF EXISTS `audit_logs`;
CREATE TABLE `audit_logs` (
  `log_id` bigint NOT NULL AUTO_INCREMENT,
  `operation_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `ip_address` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'IP地址',
  `operation_detail` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '操作详情',
  PRIMARY KEY (`log_id`) USING BTREE,
  KEY `idx_auditlogs_userid` (`user_id`) USING BTREE,
  KEY `idx_auditlogs_operationtime` (`operation_time`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='审计日志表';

-- ----------------------------
-- Table structure for base_data
-- ----------------------------
DROP TABLE IF EXISTS `base_data`;
CREATE TABLE `base_data` (
  `data_id` bigint NOT NULL AUTO_INCREMENT,
  `type_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '数据类型（subject_category/course_type等）',
  `data_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '数据编码（字母/数字组合）',
  `data_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '数据名称',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '描述',
  `status` enum('active','disabled') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'active',
  `sort_order` int DEFAULT '0',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`data_id`) USING BTREE,
  UNIQUE KEY `uniq_type_code` (`type_code`,`data_code`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='基础数据项表';

-- ----------------------------
-- Table structure for base_data_ext
-- ----------------------------
DROP TABLE IF EXISTS `base_data_ext`;
CREATE TABLE `base_data_ext` (
  `ext_id` bigint NOT NULL AUTO_INCREMENT,
  `data_id` bigint NOT NULL,
  `ext_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '扩展字段键',
  `ext_value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '扩展字段值',
  PRIMARY KEY (`ext_id`) USING BTREE,
  KEY `data_id` (`data_id`) USING BTREE,
  CONSTRAINT `base_data_ext_ibfk_1` FOREIGN KEY (`data_id`) REFERENCES `base_data` (`data_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='基础数据扩展表';

-- ----------------------------
-- Table structure for certificates
-- ----------------------------
DROP TABLE IF EXISTS `certificates`;
CREATE TABLE `certificates` (
  `cert_id` bigint NOT NULL AUTO_INCREMENT,
  `student_id` bigint NOT NULL,
  `course_project_id` bigint NOT NULL,
  `cert_type` enum('electronic','paper') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `cert_number` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '证书编号',
  `signature` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '数字签名（SM2算法）',
  `qr_code` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '二维码内容',
  `template_id` bigint DEFAULT NULL COMMENT '印刷模板ID',
  `address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '邮寄地址',
  `tracking_number` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '物流单号',
  `status` enum('issued','revoked','archived') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'issued',
  `issued_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `revoked_at` datetime DEFAULT NULL COMMENT '撤销时间',
  `revoke_reason` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '撤销原因',
  PRIMARY KEY (`cert_id`) USING BTREE,
  UNIQUE KEY `cert_number` (`cert_number`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='证书表';

-- ----------------------------
-- Table structure for classes
-- ----------------------------
DROP TABLE IF EXISTS `classes`;
CREATE TABLE `classes` (
  `class_id` bigint NOT NULL AUTO_INCREMENT COMMENT '班级ID',
  `class_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '班级编码',
  `class_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '班级名称',
  `major_id` bigint NOT NULL COMMENT '所属专业ID',
  `grade` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '年级',
  `academic_year` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '学年（YYYY-YYYY格式）',
  `student_count` int DEFAULT '0' COMMENT '学生人数',
  `class_teacher_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '班主任ID',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '班级描述',
  `status` enum('active','inactive') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'active' COMMENT '状态',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`class_id`) USING BTREE,
  UNIQUE KEY `uniq_class_code` (`class_code`) USING BTREE,
  KEY `idx_classes_major_grade` (`major_id`,`grade`) USING BTREE,
  CONSTRAINT `classes_ibfk_1` FOREIGN KEY (`major_id`) REFERENCES `majors` (`major_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='班级表';

-- ----------------------------
-- Table structure for course_ext
-- ----------------------------
DROP TABLE IF EXISTS `course_ext`;
CREATE TABLE `course_ext` (
  `ext_id` bigint NOT NULL AUTO_INCREMENT,
  `course_id` bigint NOT NULL COMMENT '课程ID',
  `ext_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '扩展字段键',
  `ext_value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '扩展字段值',
  PRIMARY KEY (`ext_id`) USING BTREE,
  KEY `idx_course_ext_courseid` (`course_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='课程扩展字段表';

-- ----------------------------
-- Table structure for course_knowledge
-- ----------------------------
DROP TABLE IF EXISTS `course_knowledge`;
CREATE TABLE `course_knowledge` (
  `knowledge_id` bigint NOT NULL AUTO_INCREMENT,
  `course_id` bigint NOT NULL,
  `knowledge_point` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `difficulty` enum('easy','medium','hard') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`knowledge_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='课程知识点表';

-- ----------------------------
-- Table structure for course_review
-- ----------------------------
DROP TABLE IF EXISTS `course_review`;
CREATE TABLE `course_review` (
  `review_id` bigint NOT NULL AUTO_INCREMENT,
  `course_id` bigint NOT NULL COMMENT '课程ID',
  `review_level` tinyint(1) NOT NULL COMMENT '审核级别（1：学科组，2：教务处）',
  `reviewer_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '审核人ID',
  `result` enum('approved','rejected') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '审核结果',
  `comments` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '审核意见',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`review_id`) USING BTREE,
  KEY `idx_course_review_course` (`course_id`,`review_level`) USING BTREE,
  KEY `idx_course_review_reviewer` (`reviewer_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='课程审核记录表';

-- ----------------------------
-- Table structure for course_revision
-- ----------------------------
DROP TABLE IF EXISTS `course_revision`;
CREATE TABLE `course_revision` (
  `revision_id` bigint NOT NULL AUTO_INCREMENT,
  `original_course_id` bigint NOT NULL COMMENT '原课程ID',
  `new_course_id` bigint NOT NULL COMMENT '新课程ID',
  `revision_content` json NOT NULL COMMENT '修订内容（JSON格式）',
  `revision_notes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '修订说明',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`revision_id`) USING BTREE,
  UNIQUE KEY `uniq_original_new` (`original_course_id`,`new_course_id`) USING BTREE,
  KEY `idx_course_revision_original` (`original_course_id`) USING BTREE,
  KEY `idx_course_revision_new` (`new_course_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='课程修订记录表';

-- ----------------------------
-- Table structure for course_teacher
-- ----------------------------
DROP TABLE IF EXISTS `course_teacher`;
CREATE TABLE `course_teacher` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `course_id` bigint NOT NULL COMMENT '课程ID',
  `teacher_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '教师ID',
  `role` enum('primary','assistant') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'primary' COMMENT '教师角色（主讲/助教）',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uniq_course_teacher` (`course_id`,`teacher_id`) USING BTREE,
  KEY `idx_course_teacher_course` (`course_id`) USING BTREE,
  KEY `idx_course_teacher_teacher` (`teacher_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='课程-教师关联表';

-- ----------------------------
-- Table structure for course_textbook
-- ----------------------------
DROP TABLE IF EXISTS `course_textbook`;
CREATE TABLE `course_textbook` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `course_id` bigint NOT NULL COMMENT '课程ID',
  `textbook_id` bigint NOT NULL COMMENT '教材ID',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uniq_course_textbook` (`course_id`,`textbook_id`) USING BTREE,
  KEY `idx_course_textbook_course` (`course_id`) USING BTREE,
  KEY `idx_course_textbook_textbook` (`textbook_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='课程-教材关联表';

-- ----------------------------
-- Table structure for courses
-- ----------------------------
DROP TABLE IF EXISTS `courses`;
CREATE TABLE `courses` (
  `course_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `course_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
  `total_hours` smallint unsigned NOT NULL,
  `scorm_package_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `status` enum('draft','reviewing','approved','published','disabled','archived') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'draft',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `type_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '课程类型编码（关联base_data）',
  `theory_hours` smallint unsigned DEFAULT '0' COMMENT '理论课时',
  `lab_hours` smallint unsigned DEFAULT '0' COMMENT '虚拟课时',
  `prerequisite` json DEFAULT NULL COMMENT '先修课程列表（JSON数组）',
  `textbook_ids` json DEFAULT NULL COMMENT '推荐教材ID列表（JSON数组）',
  `teacher_ids` json DEFAULT NULL COMMENT '关联教师ID列表（JSON数组）',
  `version` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'v1.0' COMMENT '版本号',
  `effective_date` date DEFAULT NULL COMMENT '生效时间',
  `syllabus_id` bigint DEFAULT NULL COMMENT '关联教学大纲ID',
  `class_ids` json DEFAULT NULL COMMENT '适用班级ID列表（JSON数组）',
  `major_ids` json DEFAULT NULL COMMENT '适用专业ID列表（JSON数组）',
  `id` bigint NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`id` DESC) USING BTREE,
  UNIQUE KEY `course_code` (`course_code`) USING BTREE,
  KEY `scorm_package_id` (`scorm_package_id`) USING BTREE,
  KEY `courses_ibfk_syllabus` (`syllabus_id`),
  CONSTRAINT `courses_ibfk_1` FOREIGN KEY (`scorm_package_id`) REFERENCES `scorm_packages` (`package_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `courses_ibfk_syllabus` FOREIGN KEY (`syllabus_id`) REFERENCES `syllabus` (`syllabus_id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='课程表';

-- ----------------------------
-- Table structure for courseware
-- ----------------------------
DROP TABLE IF EXISTS `courseware`;
CREATE TABLE `courseware` (
  `courseware_id` bigint NOT NULL AUTO_INCREMENT,
  `course_id` bigint NOT NULL,
  `chapter_id` bigint NOT NULL,
  `file_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `file_type` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '文件类型（pptx/pdf/mp4等）',
  `file_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '文件存储路径',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '课件描述',
  `target` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '适用对象',
  `knowledge_points` json DEFAULT NULL COMMENT '关联知识点（JSON数组）',
  `status` enum('draft','reviewing','approved','published','archived') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'draft',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `published_at` datetime DEFAULT NULL COMMENT '发布时间',
  `version` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'v1.0' COMMENT '版本号',
  `syllabus_id` bigint DEFAULT NULL COMMENT '关联教学大纲ID',
  `class_ids` json DEFAULT NULL COMMENT '适用班级ID列表（JSON数组）',
  `major_ids` json DEFAULT NULL COMMENT '适用专业ID列表（JSON数组）',
  `order` int unsigned DEFAULT '0' COMMENT '显示顺序',
  `scorm_compliance` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否符合SCORM标准',
  `question_ids` json DEFAULT NULL COMMENT '关联试题ID列表（JSON数组，仅适用于理论试题）',
  `package_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '关联SCORM包ID',
  PRIMARY KEY (`courseware_id`) USING BTREE,
  KEY `idx_courseware_course_chapter` (`course_id`,`chapter_id`,`status`) USING BTREE,
  KEY `idx_courseware_filetype` (`file_type`) USING BTREE,
  KEY `courseware_ibfk_syllabus` (`syllabus_id`),
  KEY `courseware_ibfk_scorm_package` (`package_id`),
  CONSTRAINT `courseware_ibfk_scorm_package` FOREIGN KEY (`package_id`) REFERENCES `scorm_packages` (`package_id`) ON DELETE RESTRICT ON UPDATE CASCADE,
  CONSTRAINT `courseware_ibfk_syllabus` FOREIGN KEY (`syllabus_id`) REFERENCES `syllabus` (`syllabus_id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='理论课件表';

-- ----------------------------
-- Table structure for courseware_chapter
-- ----------------------------
DROP TABLE IF EXISTS `courseware_chapter`;
CREATE TABLE `courseware_chapter` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `courseware_id` bigint NOT NULL,
  `chapter_id` bigint NOT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `courseware_id` (`courseware_id`) USING BTREE,
  CONSTRAINT `courseware_chapter_ibfk_1` FOREIGN KEY (`courseware_id`) REFERENCES `courseware` (`courseware_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='课件-章节关联表';

-- ----------------------------
-- Table structure for courseware_ext
-- ----------------------------
DROP TABLE IF EXISTS `courseware_ext`;
CREATE TABLE `courseware_ext` (
  `ext_id` bigint NOT NULL AUTO_INCREMENT,
  `courseware_id` bigint NOT NULL COMMENT '课件ID',
  `ext_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '扩展字段键',
  `ext_value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '扩展字段值',
  PRIMARY KEY (`ext_id`) USING BTREE,
  KEY `idx_courseware_ext_coursewareid` (`courseware_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='课件扩展字段表';

-- ----------------------------
-- Table structure for courseware_parse_status
-- ----------------------------
DROP TABLE IF EXISTS `courseware_parse_status`;
CREATE TABLE `courseware_parse_status` (
  `parse_id` bigint NOT NULL AUTO_INCREMENT,
  `courseware_id` bigint NOT NULL COMMENT '课件ID',
  `parse_status` enum('pending','success','failed') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'pending' COMMENT '解析状态',
  `error_message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '失败原因',
  `parsed_data` json DEFAULT NULL COMMENT '解析结果（JSON格式，如章节结构）',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '解析时间',
  PRIMARY KEY (`parse_id`) USING BTREE,
  UNIQUE KEY `uniq_courseware_parse` (`courseware_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='课件解析状态表';

-- ----------------------------
-- Table structure for courseware_review
-- ----------------------------
DROP TABLE IF EXISTS `courseware_review`;
CREATE TABLE `courseware_review` (
  `review_id` bigint NOT NULL AUTO_INCREMENT,
  `courseware_id` bigint NOT NULL COMMENT '课件ID',
  `review_level` tinyint(1) NOT NULL COMMENT '审核级别（1：学科组，2：教务处）',
  `reviewer_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '审核人ID',
  `result` enum('approved','rejected') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '审核结果',
  `comments` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '审核意见',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '审核时间',
  PRIMARY KEY (`review_id`) USING BTREE,
  KEY `idx_courseware_review_courseware` (`courseware_id`,`review_level`) USING BTREE,
  KEY `idx_courseware_review_reviewer` (`reviewer_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='课件审核记录表';

-- ----------------------------
-- Table structure for courseware_revision
-- ----------------------------
DROP TABLE IF EXISTS `courseware_revision`;
CREATE TABLE `courseware_revision` (
  `revision_id` bigint NOT NULL AUTO_INCREMENT,
  `original_courseware_id` bigint NOT NULL COMMENT '原课件ID',
  `new_courseware_id` bigint NOT NULL COMMENT '新课件ID',
  `revision_content` json NOT NULL COMMENT '修订内容（JSON格式，如文件大小、修订说明）',
  `revision_notes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '修订说明',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修订时间',
  PRIMARY KEY (`revision_id`) USING BTREE,
  UNIQUE KEY `uniq_original_new` (`original_courseware_id`,`new_courseware_id`) USING BTREE,
  KEY `idx_courseware_revision_original` (`original_courseware_id`) USING BTREE,
  KEY `idx_courseware_revision_new` (`new_courseware_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='课件修订记录表';

-- ----------------------------
-- Table structure for courseware_statistics
-- ----------------------------
DROP TABLE IF EXISTS `courseware_statistics`;
CREATE TABLE `courseware_statistics` (
  `stat_id` bigint NOT NULL AUTO_INCREMENT,
  `courseware_id` bigint NOT NULL COMMENT '课件ID',
  `user_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户ID',
  `action_type` enum('download','view') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '操作类型（下载/访问）',
  `action_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
  `ip_address` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'IP地址',
  PRIMARY KEY (`stat_id`) USING BTREE,
  KEY `idx_courseware_statistics_courseware` (`courseware_id`) USING BTREE,
  KEY `idx_courseware_statistics_user_action` (`user_id`,`action_type`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='课件使用统计表';

-- ----------------------------
-- Table structure for evaluation_criteria
-- ----------------------------
DROP TABLE IF EXISTS `evaluation_criteria`;
CREATE TABLE `evaluation_criteria` (
  `criteria_id` bigint NOT NULL AUTO_INCREMENT,
  `evaluation_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '评估类型（operation_exam/theory_exam）',
  `rule_description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '规则描述',
  `weight` decimal(5,2) NOT NULL COMMENT '权重',
  `effective_date` date NOT NULL COMMENT '生效时间',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`criteria_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='评估标准表';

-- ----------------------------
-- Table structure for exam_records
-- ----------------------------
DROP TABLE IF EXISTS `exam_records`;
CREATE TABLE `exam_records` (
  `record_id` bigint NOT NULL AUTO_INCREMENT,
  `exam_id` bigint NOT NULL COMMENT '考试ID',
  `student_id` bigint NOT NULL COMMENT '学生ID',
  `question_id` bigint NOT NULL COMMENT '试题ID',
  `student_answer` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '学生答案（JSON格式）',
  `score` decimal(5,2) DEFAULT NULL COMMENT '得分',
  `status` enum('pending','auto_scored','manual_scored') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'pending' COMMENT '评分状态',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `courseware_id` bigint DEFAULT '0' COMMENT '虚拟课件id',
  PRIMARY KEY (`record_id`) USING BTREE,
  KEY `idx_exam_student_question` (`exam_id`,`student_id`,`question_id`) USING BTREE,
  KEY `exam_records_ibfk_question` (`question_id`),
  KEY `exam_records_ibfk_virtual` (`courseware_id`),
  CONSTRAINT `exam_records_ibfk_exam` FOREIGN KEY (`exam_id`) REFERENCES `exams` (`exam_id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `exam_records_ibfk_question` FOREIGN KEY (`question_id`) REFERENCES `questions` (`question_id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `exam_records_ibfk_virtual` FOREIGN KEY (`courseware_id`) REFERENCES `virtual_courseware` (`courseware_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='考试记录表';

-- ----------------------------
-- Table structure for exams
-- ----------------------------
DROP TABLE IF EXISTS `exams`;
CREATE TABLE `exams` (
  `exam_id` bigint NOT NULL AUTO_INCREMENT,
  `paper_id` bigint NOT NULL COMMENT '试卷ID',
  `assessment_plan_id` bigint NOT NULL COMMENT '考核计划ID',
  `start_time` datetime NOT NULL COMMENT '考试开始时间',
  `end_time` datetime NOT NULL COMMENT '考试结束时间',
  `student_ids` json NOT NULL COMMENT '考生范围（JSON数组）',
  `rules` json DEFAULT NULL COMMENT '考试规则（JSON格式）',
  `status` enum('draft','published','completed') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'draft' COMMENT '状态',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `published_at` datetime DEFAULT NULL COMMENT '发布时间',
  PRIMARY KEY (`exam_id`) USING BTREE,
  KEY `idx_exam_paper_assessment` (`paper_id`,`assessment_plan_id`) USING BTREE,
  KEY `exams_ibfk_assessment_plan` (`assessment_plan_id`),
  CONSTRAINT `exams_ibfk_assessment_plan` FOREIGN KEY (`assessment_plan_id`) REFERENCES `assessment_plans` (`assessment_plan_id`) ON DELETE RESTRICT ON UPDATE CASCADE,
  CONSTRAINT `exams_ibfk_paper` FOREIGN KEY (`paper_id`) REFERENCES `papers` (`paper_id`) ON DELETE RESTRICT ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='考试表';

-- ----------------------------
-- Table structure for flow_check
-- ----------------------------
DROP TABLE IF EXISTS `flow_check`;
CREATE TABLE `flow_check` (
  `check_id` bigint NOT NULL AUTO_INCREMENT,
  `flow_id` bigint NOT NULL,
  `checker_id` bigint NOT NULL COMMENT '审批人ID',
  `status` enum('pending','approved','rejected') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'pending',
  `approval_result` enum('pass','reject') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '审批结果',
  `approval_comment` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '审批意见',
  `signature` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '电子签名（Base64编码）',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`check_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='审批任务处理记录表';

-- ----------------------------
-- Table structure for form_data
-- ----------------------------
DROP TABLE IF EXISTS `form_data`;
CREATE TABLE `form_data` (
  `form_id` bigint NOT NULL AUTO_INCREMENT,
  `flow_id` bigint NOT NULL,
  `form_data` json NOT NULL COMMENT '表单数据（JSON格式）',
  `status` enum('draft','submitted','approved','rejected') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'draft',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`form_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='审批表单数据表';

-- ----------------------------
-- Table structure for grade_leader
-- ----------------------------
DROP TABLE IF EXISTS `grade_leader`;
CREATE TABLE `grade_leader` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `grade` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '年级名称',
  `leader_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '年级组长用户ID',
  `academic_year` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '学年（YYYY-YYYY格式）',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uniq_grade_leader` (`grade`,`academic_year`) USING BTREE,
  KEY `idx_grade_leader_user` (`leader_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='年级组长关联表';

-- ----------------------------
-- Table structure for knowledge_intervention
-- ----------------------------
DROP TABLE IF EXISTS `knowledge_intervention`;
CREATE TABLE `knowledge_intervention` (
  `intervention_id` bigint NOT NULL AUTO_INCREMENT,
  `exam_id` bigint NOT NULL COMMENT '考试ID',
  `knowledge_point` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '知识点',
  `score_rate` decimal(5,2) NOT NULL COMMENT '得分率',
  `recommendations` json NOT NULL COMMENT '推荐资源（JSON格式）',
  `suggestions` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '干预建议',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`intervention_id`) USING BTREE,
  KEY `idx_knowledge_intervention_exam` (`exam_id`,`knowledge_point`) USING BTREE,
  CONSTRAINT `knowledge_intervention_ibfk_exam` FOREIGN KEY (`exam_id`) REFERENCES `exams` (`exam_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='薄弱知识点干预建议表';

-- ----------------------------
-- Table structure for learning_actions
-- ----------------------------
DROP TABLE IF EXISTS `learning_actions`;
CREATE TABLE `learning_actions` (
  `action_id` bigint NOT NULL AUTO_INCREMENT,
  `action_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '行为编码',
  `action_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '行为名称',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '描述',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`action_id`) USING BTREE,
  UNIQUE KEY `uniq_action_code` (`action_code`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='学习行为扩展表';

-- ----------------------------
-- Table structure for learning_progress
-- ----------------------------
DROP TABLE IF EXISTS `learning_progress`;
CREATE TABLE `learning_progress` (
  `progress_id` bigint NOT NULL AUTO_INCREMENT,
  `student_id` bigint NOT NULL COMMENT '学生ID',
  `course_id` bigint NOT NULL,
  `chapter_id` bigint NOT NULL,
  `action_type` enum('video_watched','assignment_submitted','test_completed') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `status` enum('not_started','in_progress','completed') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'not_started',
  `complete_time` datetime DEFAULT NULL COMMENT '完成时间',
  `duration` int DEFAULT NULL COMMENT '学习时长（秒）',
  `score` decimal(5,2) DEFAULT NULL COMMENT '得分',
  `recorded_by` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'system' COMMENT '记录来源（system/manual）',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `class_id` bigint DEFAULT NULL COMMENT '班级ID',
  PRIMARY KEY (`progress_id`) USING BTREE,
  KEY `idx_learningprogress_student_course` (`student_id`,`course_id`) USING BTREE,
  KEY `idx_learningprogress_chapter` (`chapter_id`) USING BTREE,
  KEY `learning_progress_ibfk_class` (`class_id`),
  CONSTRAINT `learning_progress_ibfk_class` FOREIGN KEY (`class_id`) REFERENCES `classes` (`class_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='学习进度记录表';

-- ----------------------------
-- Table structure for learning_progress_summary
-- ----------------------------
DROP TABLE IF EXISTS `learning_progress_summary`;
CREATE TABLE `learning_progress_summary` (
  `summary_id` bigint NOT NULL AUTO_INCREMENT,
  `student_id` bigint NOT NULL COMMENT '学生ID',
  `course_id` bigint NOT NULL COMMENT '课程ID',
  `progress_rate` decimal(5,2) NOT NULL COMMENT '学习进度百分比',
  `last_assessment_score` decimal(5,2) DEFAULT NULL COMMENT '最近考核成绩',
  `weak_knowledge_points` json DEFAULT NULL COMMENT '薄弱知识点列表（JSON数组）',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`summary_id`) USING BTREE,
  UNIQUE KEY `uniq_student_course` (`student_id`,`course_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='学习进度汇总总表';

-- ----------------------------
-- Table structure for learning_summary
-- ----------------------------
DROP TABLE IF EXISTS `learning_summary`;
CREATE TABLE `learning_summary` (
  `summary_id` bigint NOT NULL AUTO_INCREMENT,
  `student_id` bigint NOT NULL COMMENT '学生ID',
  `course_id` bigint NOT NULL COMMENT '课程ID',
  `chapter_completion_rate` decimal(5,2) NOT NULL DEFAULT '0.00' COMMENT '章节完成率（%）',
  `total_study_time` int NOT NULL DEFAULT '0' COMMENT '总学习时长（秒）',
  `last_updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  PRIMARY KEY (`summary_id`) USING BTREE,
  UNIQUE KEY `uniq_student_course` (`student_id`,`course_id`) USING BTREE,
  KEY `idx_learning_summary_course` (`course_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='学习进度汇总单表';

-- ----------------------------
-- Table structure for majors
-- ----------------------------
DROP TABLE IF EXISTS `majors`;
CREATE TABLE `majors` (
  `major_id` bigint NOT NULL AUTO_INCREMENT COMMENT '专业ID',
  `major_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '专业编码',
  `major_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '专业名称',
  `department` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '所属院系',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '专业描述',
  `status` enum('active','inactive') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'active' COMMENT '状态',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`major_id`) USING BTREE,
  UNIQUE KEY `uniq_major_code` (`major_code`) USING BTREE,
  KEY `idx_majors_department` (`department`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='专业表';

-- ----------------------------
-- Table structure for manual_record_logs
-- ----------------------------
DROP TABLE IF EXISTS `manual_record_logs`;
CREATE TABLE `manual_record_logs` (
  `log_id` bigint NOT NULL AUTO_INCREMENT,
  `progress_id` bigint NOT NULL COMMENT '关联进度ID',
  `teacher_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '教师ID',
  `action_type` enum('create','update') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '操作类型',
  `details` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '操作详情',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
  PRIMARY KEY (`log_id`) USING BTREE,
  KEY `idx_manual_record_progress` (`progress_id`) USING BTREE,
  KEY `idx_manual_record_teacher` (`teacher_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='手动记录日志表';

-- ----------------------------
-- Table structure for manual_score_logs
-- ----------------------------
DROP TABLE IF EXISTS `manual_score_logs`;
CREATE TABLE `manual_score_logs` (
  `log_id` bigint NOT NULL AUTO_INCREMENT,
  `score_record_id` bigint NOT NULL COMMENT '关联成绩记录ID',
  `operation` enum('create','update','delete') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '操作类型',
  `original_value` decimal(5,2) DEFAULT NULL COMMENT '原成绩',
  `new_value` decimal(5,2) DEFAULT NULL COMMENT '新成绩',
  `reason` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '调整原因',
  `operated_by` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '操作人ID',
  `operation_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
  PRIMARY KEY (`log_id`) USING BTREE,
  KEY `idx_manualscorelogs_record` (`score_record_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='手动成绩记录与调整日志表';

-- ----------------------------
-- Table structure for material_tags
-- ----------------------------
DROP TABLE IF EXISTS `material_tags`;
CREATE TABLE `material_tags` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `material_id` bigint NOT NULL,
  `tag` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_material_tag` (`material_id`,`tag`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='资料-标签关联表';

-- ----------------------------
-- Table structure for materials
-- ----------------------------
DROP TABLE IF EXISTS `materials`;
CREATE TABLE `materials` (
  `material_id` bigint NOT NULL AUTO_INCREMENT,
  `file_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `file_type` enum('manual','guide','reference') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `course_id` bigint NOT NULL,
  `target` enum('teacher','student','both') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `tags` json NOT NULL COMMENT '分类标签（JSON数组）',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
  `status` enum('draft','reviewing','approved','published','deleted') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'draft',
  `storage_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '存储路径',
  `encryption` enum('sm4','aes256') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'sm4' COMMENT '加密算法',
  `key_version` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '密钥版本',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`material_id`) USING BTREE,
  FULLTEXT KEY `idx_materials_filename_desc` (`file_name`,`description`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='资料表';

-- ----------------------------
-- Table structure for menu_role
-- ----------------------------
DROP TABLE IF EXISTS `menu_role`;
CREATE TABLE `menu_role` (
  `menu_id` bigint NOT NULL,
  `role_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  PRIMARY KEY (`menu_id`,`role_id`) USING BTREE,
  KEY `role_id` (`role_id`) USING BTREE,
  CONSTRAINT `menu_role_ibfk_1` FOREIGN KEY (`menu_id`) REFERENCES `menus` (`menu_id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `menu_role_ibfk_2` FOREIGN KEY (`role_id`) REFERENCES `roles` (`role_id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='菜单角色关联表';

-- ----------------------------
-- Table structure for menus
-- ----------------------------
DROP TABLE IF EXISTS `menus`;
CREATE TABLE `menus` (
  `menu_id` bigint NOT NULL AUTO_INCREMENT,
  `parent_id` bigint NOT NULL DEFAULT '0' COMMENT '父菜单ID',
  `menu_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '菜单名称',
  `path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '路由路径',
  `component` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '组件路径',
  `perms` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '权限标识',
  `icon` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '图标',
  `sort_order` int NOT NULL DEFAULT '0' COMMENT '排序',
  `status` enum('active','inactive') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'active' COMMENT '状态',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`menu_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='菜单表';

-- ----------------------------
-- Table structure for papers
-- ----------------------------
DROP TABLE IF EXISTS `papers`;
CREATE TABLE `papers` (
  `paper_id` bigint NOT NULL AUTO_INCREMENT,
  `course_id` bigint NOT NULL COMMENT '课程ID',
  `assessment_type` enum('daily','midterm','final','makeup') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '考核类型',
  `rules` json NOT NULL COMMENT '组卷规则（JSON格式）',
  `questions` json NOT NULL COMMENT '试题列表（JSON格式）',
  `total_score` decimal(5,2) NOT NULL COMMENT '总分',
  `status` enum('draft','published','archived') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'draft' COMMENT '状态',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `published_at` datetime DEFAULT NULL COMMENT '发布时间',
  PRIMARY KEY (`paper_id`) USING BTREE,
  KEY `idx_paper_course_assessment` (`course_id`,`assessment_type`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='试卷表';

-- ----------------------------
-- Table structure for permission_cache
-- ----------------------------
DROP TABLE IF EXISTS `permission_cache`;
CREATE TABLE `permission_cache` (
  `user_id` bigint NOT NULL,
  `permissions` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT 'JSON格式的权限列表',
  `last_updated` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`user_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='用户权限缓存表';

-- ----------------------------
-- Table structure for permissions
-- ----------------------------
DROP TABLE IF EXISTS `permissions`;
CREATE TABLE `permissions` (
  `permission_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '格式: resource:action',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `module` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  PRIMARY KEY (`permission_id`) USING BTREE,
  UNIQUE KEY `code` (`code`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='权限表';

-- ----------------------------
-- Table structure for plugins
-- ----------------------------
DROP TABLE IF EXISTS `plugins`;
CREATE TABLE `plugins` (
  `plugin_id` bigint NOT NULL AUTO_INCREMENT,
  `plugin_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '插件名称',
  `file_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '存储路径',
  `version` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '版本号',
  `compatibility` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '兼容信息',
  `status` enum('enabled','disabled') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'enabled',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`plugin_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='播放器插件表';

-- ----------------------------
-- Table structure for prerequisite_courses
-- ----------------------------
DROP TABLE IF EXISTS `prerequisite_courses`;
CREATE TABLE `prerequisite_courses` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `course_id` bigint NOT NULL COMMENT '当前课程ID',
  `prerequisite_id` bigint NOT NULL COMMENT '先修课程ID',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uniq_prerequisite` (`course_id`,`prerequisite_id`) USING BTREE,
  KEY `idx_prerequisite_course` (`course_id`) USING BTREE,
  KEY `idx_prerequisite_prereq` (`prerequisite_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='先修课程关联表';

-- ----------------------------
-- Table structure for progress_alerts
-- ----------------------------
DROP TABLE IF EXISTS `progress_alerts`;
CREATE TABLE `progress_alerts` (
  `alert_id` bigint NOT NULL AUTO_INCREMENT,
  `student_id` bigint NOT NULL,
  `course_id` bigint NOT NULL,
  `rule` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '预警规则',
  `level` enum('low','medium','high') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `recommendations` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '推荐干预措施',
  `triggered_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `handled_at` datetime DEFAULT NULL COMMENT '处理时间',
  PRIMARY KEY (`alert_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='学习进度预警表';

-- ----------------------------
-- Table structure for questions
-- ----------------------------
DROP TABLE IF EXISTS `questions`;
CREATE TABLE `questions` (
  `question_id` bigint NOT NULL AUTO_INCREMENT,
  `course_id` bigint NOT NULL,
  `question_type` enum('single_choice','multiple_choice','short_answer','programming') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '题干',
  `knowledge_point` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '知识点',
  `correct_answer` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `difficulty` enum('easy','medium','hard') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `difficulty_score` decimal(3,2) DEFAULT NULL COMMENT '难度系数（0.00-1.00）',
  `score` decimal(5,2) NOT NULL,
  `options` json DEFAULT NULL COMMENT '选项（仅选择题必填）',
  `explanation` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '解析',
  `tags` json DEFAULT NULL COMMENT '自定义标签（JSON数组）',
  `classification` json DEFAULT NULL COMMENT '分类维度及值（JSON格式）',
  `chapter_id` bigint DEFAULT NULL COMMENT '关联章节ID',
  `status` enum('active','archived') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'active',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `type` enum('theory_courseware','theory_test','virtual_courseware') COLLATE utf8mb4_general_ci NOT NULL COMMENT '题库类型（理论题库/考试题库/虚拟题库）',
  PRIMARY KEY (`question_id`) USING BTREE,
  KEY `idx_questions_course_knowledge` (`course_id`,`knowledge_point`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='理论试题库表';

-- ----------------------------
-- Table structure for resource_chapter
-- ----------------------------
DROP TABLE IF EXISTS `resource_chapter`;
CREATE TABLE `resource_chapter` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `syllabus_id` bigint NOT NULL COMMENT '教学大纲ID',
  `chapter_number` int NOT NULL COMMENT '章节编号',
  `resource_id` bigint NOT NULL COMMENT '资源ID',
  `resource_type` enum('courseware','experiment_guide','reference') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '资源类型',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_resource_chapter_syllabus` (`syllabus_id`,`chapter_number`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='教学资源-章节关联表';

-- ----------------------------
-- Table structure for review_tasks
-- ----------------------------
DROP TABLE IF EXISTS `review_tasks`;
CREATE TABLE `review_tasks` (
  `task_id` bigint NOT NULL AUTO_INCREMENT,
  `syllabus_id` bigint NOT NULL COMMENT '教学大纲ID',
  `assignee_id` bigint NOT NULL COMMENT '审核人ID',
  `status` enum('pending','approved','rejected') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'pending',
  `comments` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '审核意见',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`task_id`) USING BTREE,
  KEY `idx_reviewtasks_syllabus_assignee` (`syllabus_id`,`assignee_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='审核任务表';

-- ----------------------------
-- Table structure for role_permissions
-- ----------------------------
DROP TABLE IF EXISTS `role_permissions`;
CREATE TABLE `role_permissions` (
  `role_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `permission_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  PRIMARY KEY (`role_id`,`permission_id`) USING BTREE,
  KEY `permission_id` (`permission_id`) USING BTREE,
  CONSTRAINT `role_permissions_ibfk_1` FOREIGN KEY (`role_id`) REFERENCES `roles` (`role_id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `role_permissions_ibfk_2` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`permission_id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='角色权限关联表';

-- ----------------------------
-- Table structure for roles
-- ----------------------------
DROP TABLE IF EXISTS `roles`;
CREATE TABLE `roles` (
  `role_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `role_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
  PRIMARY KEY (`role_id`) USING BTREE,
  UNIQUE KEY `role_name` (`role_name`) USING BTREE,
  KEY `idx_roles_rolename` (`role_name`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='角色表';

-- ----------------------------
-- Table structure for schedule_sync
-- ----------------------------
DROP TABLE IF EXISTS `schedule_sync`;
CREATE TABLE `schedule_sync` (
  `sync_id` bigint NOT NULL AUTO_INCREMENT,
  `syllabus_id` bigint NOT NULL COMMENT '教学大纲ID',
  `sync_status` enum('pending','success','failed') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'pending',
  `error_message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '失败原因',
  `synced_at` datetime DEFAULT NULL COMMENT '同步时间',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`sync_id`) USING BTREE,
  KEY `idx_schedulesync_syllabus` (`syllabus_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='排课系统同步表';

-- ----------------------------
-- Table structure for scorm_packages
-- ----------------------------
DROP TABLE IF EXISTS `scorm_packages`;
CREATE TABLE `scorm_packages` (
  `package_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `identifier` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'SCORM标识符',
  `version` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '1.2',
  `launch_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '入口文件路径',
  `encryption_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '国密SM4密钥标识',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`package_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='SCORM包表';

-- ----------------------------
-- Table structure for scorm_sco
-- ----------------------------
DROP TABLE IF EXISTS `scorm_sco`;
CREATE TABLE `scorm_sco` (
  `sco_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `package_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `identifier` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `entry_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `parent_sco` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  PRIMARY KEY (`sco_id`) USING BTREE,
  KEY `package_id` (`package_id`) USING BTREE,
  CONSTRAINT `scorm_sco_ibfk_1` FOREIGN KEY (`package_id`) REFERENCES `scorm_packages` (`package_id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='SCORM学习对象表';

-- ----------------------------
-- Table structure for scorm_tracking
-- ----------------------------
DROP TABLE IF EXISTS `scorm_tracking`;
CREATE TABLE `scorm_tracking` (
  `track_id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `sco_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `completion` enum('completed','incomplete','unknown') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'unknown',
  `success` enum('passed','failed','unknown') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'unknown',
  `score` decimal(5,2) DEFAULT NULL COMMENT '原始分数',
  `max_score` decimal(5,2) DEFAULT NULL COMMENT '最大分数',
  `progress` tinyint unsigned DEFAULT '0' COMMENT '0-100百分比',
  `total_time` int DEFAULT '0' COMMENT '总学习秒数',
  `session_time` int DEFAULT '0' COMMENT '本次学习秒数',
  `suspend_data` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT 'SCORM suspend_data',
  `last_modified` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`track_id`) USING BTREE,
  UNIQUE KEY `user_sco` (`user_id`,`sco_id`) USING BTREE,
  KEY `sco_id` (`sco_id`) USING BTREE,
  KEY `idx_user_progress` (`user_id`,`completion`) USING BTREE,
  CONSTRAINT `scorm_tracking_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `scorm_tracking_ibfk_2` FOREIGN KEY (`sco_id`) REFERENCES `scorm_sco` (`sco_id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='SCORM学习进度跟踪表';

-- ----------------------------
-- Table structure for security_event
-- ----------------------------
DROP TABLE IF EXISTS `security_event`;
CREATE TABLE `security_event` (
  `event_id` bigint NOT NULL AUTO_INCREMENT,
  `event_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '事件类型（abnormal_login/permeission_violation等）',
  `detail` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '事件详情',
  `level` enum('high','medium','low') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '严重级别',
  `status` enum('pending','resolved') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'pending',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `resolved_at` datetime DEFAULT NULL COMMENT '解决时间',
  PRIMARY KEY (`event_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='安全事件告警表';

-- ----------------------------
-- Table structure for security_policy
-- ----------------------------
DROP TABLE IF EXISTS `security_policy`;
CREATE TABLE `security_policy` (
  `policy_id` bigint NOT NULL AUTO_INCREMENT,
  `policy_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '策略类型（password_policy/login_policy等）',
  `policy_rules` json NOT NULL COMMENT '策略规则（JSON格式）',
  `effective_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '生效时间',
  PRIMARY KEY (`policy_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='安全策略配置表';

-- ----------------------------
-- Table structure for syllabus
-- ----------------------------
DROP TABLE IF EXISTS `syllabus`;
CREATE TABLE `syllabus` (
  `syllabus_id` bigint NOT NULL AUTO_INCREMENT,
  `course_id` bigint NOT NULL,
  `course_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `major_grade` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '适用专业/年级',
  `total_hours` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '总课时描述',
  `goals` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '教学目标',
  `chapters` json NOT NULL COMMENT '章节结构（JSON格式）',
  `textbook_id` bigint DEFAULT NULL COMMENT '推荐教材ID',
  `references` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '参考资料',
  `status` enum('draft','reviewing','approved','published') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'draft',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `published_at` datetime DEFAULT NULL COMMENT '发布时间',
  `effective_date` date DEFAULT NULL COMMENT '生效时间',
  `class_ids` json DEFAULT NULL COMMENT '适用班级ID列表（JSON数组）',
  `major_ids` json DEFAULT NULL COMMENT '适用专业ID列表（JSON数组）',
  PRIMARY KEY (`syllabus_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='教学大纲表';

-- ----------------------------
-- Table structure for syllabus_ext
-- ----------------------------
DROP TABLE IF EXISTS `syllabus_ext`;
CREATE TABLE `syllabus_ext` (
  `ext_id` bigint NOT NULL AUTO_INCREMENT,
  `syllabus_id` bigint NOT NULL COMMENT '关联教学大纲ID',
  `ext_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '扩展字段键',
  `ext_value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '扩展字段值',
  PRIMARY KEY (`ext_id`) USING BTREE,
  KEY `idx_syllabus_ext_syllabusid` (`syllabus_id`) USING BTREE,
  CONSTRAINT `syllabus_ext_ibfk_1` FOREIGN KEY (`syllabus_id`) REFERENCES `syllabus` (`syllabus_id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='教学大纲扩展表';

-- ----------------------------
-- Table structure for syllabus_revision
-- ----------------------------
DROP TABLE IF EXISTS `syllabus_revision`;
CREATE TABLE `syllabus_revision` (
  `revision_id` bigint NOT NULL AUTO_INCREMENT,
  `original_syllabus_id` bigint NOT NULL COMMENT '原大纲ID',
  `new_syllabus_id` bigint NOT NULL COMMENT '新大纲ID',
  `revision_content` json NOT NULL COMMENT '修订内容（JSON格式）',
  `revision_notes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '修订说明',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`revision_id`) USING BTREE,
  KEY `idx_syllabus_revision_original_new` (`original_syllabus_id`,`new_syllabus_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='教学大纲修订记录表';

-- ----------------------------
-- Table structure for system_config
-- ----------------------------
DROP TABLE IF EXISTS `system_config`;
CREATE TABLE `system_config` (
  `config_id` bigint NOT NULL AUTO_INCREMENT,
  `config_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '配置类型（database/storage等）',
  `config_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '具体参数名',
  `config_value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '参数值（数据库密码使用AES-256加密存储）',
  `is_dynamic` tinyint(1) DEFAULT '1' COMMENT '是否立即生效',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`config_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='系统配置表';

-- ----------------------------
-- Table structure for teacher_schedule
-- ----------------------------
DROP TABLE IF EXISTS `teacher_schedule`;
CREATE TABLE `teacher_schedule` (
  `schedule_id` bigint NOT NULL AUTO_INCREMENT,
  `teacher_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '教师ID',
  `plan_id` bigint NOT NULL COMMENT '关联教学计划ID',
  `course_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '课程ID',
  `weekday` tinyint(1) NOT NULL COMMENT '星期几（1-7）',
  `time_slot` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '时间段（如1-2节、3-4节）',
  `classroom_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '教室ID',
  `term` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '学期（YYYY-YYYY-N格式）',
  `status` enum('planned','confirmed','cancelled') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'planned',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `class_id` bigint DEFAULT NULL COMMENT '班级ID',
  PRIMARY KEY (`schedule_id`) USING BTREE,
  UNIQUE KEY `uniq_teacher_time` (`teacher_id`,`term`,`weekday`,`time_slot`) USING BTREE,
  KEY `idx_teacher_schedule_plan` (`plan_id`) USING BTREE,
  KEY `idx_teacher_schedule_course` (`course_id`) USING BTREE,
  KEY `teacher_schedule_ibfk_class` (`class_id`),
  CONSTRAINT `teacher_schedule_ibfk_class` FOREIGN KEY (`class_id`) REFERENCES `classes` (`class_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='教师课表安排表';

-- ----------------------------
-- Table structure for teaching_plan
-- ----------------------------
DROP TABLE IF EXISTS `teaching_plan`;
CREATE TABLE `teaching_plan` (
  `plan_id` bigint NOT NULL AUTO_INCREMENT,
  `plan_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `term` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '学期（YYYY-YYYY-N格式）',
  `grade` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '适用年级',
  `courses` json NOT NULL COMMENT '课程列表（JSON数组）',
  `schedule` json DEFAULT NULL COMMENT '教学进度（JSON格式）',
  `teachers` json DEFAULT NULL COMMENT '任课教师分配（JSON数组）',
  `classroom_requirements` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '教室需求描述',
  `status` enum('draft','reviewing','approved','published') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'draft',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `published_at` datetime DEFAULT NULL COMMENT '发布时间',
  `effective_date` date DEFAULT NULL COMMENT '生效时间',
  `version` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'v1.0' COMMENT '版本号',
  `previous_plan_id` bigint DEFAULT NULL COMMENT '前序计划ID（用于调整关联）',
  `major_id` bigint DEFAULT NULL COMMENT '适用专业ID',
  `class_ids` json DEFAULT NULL COMMENT '适用班级ID列表（JSON数组）',
  `major_ids` json DEFAULT NULL COMMENT '适用专业ID列表（JSON数组）',
  PRIMARY KEY (`plan_id`) USING BTREE,
  KEY `idx_teachingplan_term_grade` (`term`,`grade`,`status`) USING BTREE,
  KEY `teaching_plan_ibfk_major` (`major_id`),
  CONSTRAINT `teaching_plan_ibfk_major` FOREIGN KEY (`major_id`) REFERENCES `majors` (`major_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='教学计划表';

-- ----------------------------
-- Table structure for teaching_plan_ext
-- ----------------------------
DROP TABLE IF EXISTS `teaching_plan_ext`;
CREATE TABLE `teaching_plan_ext` (
  `ext_id` bigint NOT NULL AUTO_INCREMENT,
  `plan_id` bigint NOT NULL COMMENT '关联教学计划ID',
  `ext_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '扩展字段键',
  `ext_value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '扩展字段值',
  PRIMARY KEY (`ext_id`) USING BTREE,
  KEY `idx_teachingplan_ext_planid` (`plan_id`) USING BTREE,
  CONSTRAINT `teaching_plan_ext_ibfk_1` FOREIGN KEY (`plan_id`) REFERENCES `teaching_plan` (`plan_id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='教学计划扩展字段表';

-- ----------------------------
-- Table structure for teaching_plan_review
-- ----------------------------
DROP TABLE IF EXISTS `teaching_plan_review`;
CREATE TABLE `teaching_plan_review` (
  `review_id` bigint NOT NULL AUTO_INCREMENT,
  `plan_id` bigint NOT NULL COMMENT '教学计划ID',
  `review_level` tinyint(1) NOT NULL COMMENT '审核级别（1：教研室，2：教务处）',
  `reviewer_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '审核人ID',
  `result` enum('approved','rejected') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '审核结果',
  `comments` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '审核意见',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`review_id`) USING BTREE,
  KEY `idx_teachingplan_review_plan` (`plan_id`,`review_level`) USING BTREE,
  KEY `idx_teachingplan_reviewer` (`reviewer_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='教学计划审核记录表';

-- ----------------------------
-- Table structure for textbook
-- ----------------------------
DROP TABLE IF EXISTS `textbook`;
CREATE TABLE `textbook` (
  `textbook_id` bigint NOT NULL AUTO_INCREMENT,
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '教材名称',
  `author` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '作者',
  `isbn` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'ISBN编号',
  `publisher` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '出版社',
  `edition` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '版本号',
  `stock_quantity` int NOT NULL DEFAULT '0' COMMENT '库存数量',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`textbook_id`) USING BTREE,
  UNIQUE KEY `uniq_isbn` (`isbn`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='教材表';

-- ----------------------------
-- Table structure for user_roles
-- ----------------------------
DROP TABLE IF EXISTS `user_roles`;
CREATE TABLE `user_roles` (
  `user_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `role_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  PRIMARY KEY (`user_id`,`role_id`) USING BTREE,
  KEY `role_id` (`role_id`) USING BTREE,
  KEY `idx_user_roles_userid` (`user_id`) USING BTREE,
  CONSTRAINT `user_roles_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `user_roles_ibfk_2` FOREIGN KEY (`role_id`) REFERENCES `roles` (`role_id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='用户角色关联表';

-- ----------------------------
-- Table structure for users
-- ----------------------------
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users` (
  `user_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `password` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'BCrypt加密',
  `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `status` enum('active','locked','frozen') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'active',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `title` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '职称/职务',
  `department` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '所属部门',
  PRIMARY KEY (`user_id`) USING BTREE,
  UNIQUE KEY `username` (`username`) USING BTREE,
  UNIQUE KEY `email` (`email`) USING BTREE,
  KEY `idx_users_username` (`username`) USING BTREE,
  KEY `idx_users_email` (`email`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='用户表';

-- ----------------------------
-- Table structure for virtual_courseware
-- ----------------------------
DROP TABLE IF EXISTS `virtual_courseware`;
CREATE TABLE `virtual_courseware` (
  `courseware_id` bigint NOT NULL AUTO_INCREMENT COMMENT '虚拟课件ID',
  `course_id` bigint NOT NULL COMMENT '关联课程ID',
  `package_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'SCORM包ID',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '课件标题',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '课件描述',
  `total_duration` int NOT NULL COMMENT '总时长（秒）',
  `max_score` decimal(5,2) NOT NULL COMMENT '总分',
  `file_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '文件存储路径',
  `status` enum('draft','reviewing','approved','published','archived') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'draft' COMMENT '状态',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `published_at` datetime DEFAULT NULL COMMENT '发布时间',
  `type` enum('theory_courseware','theory_test','virtual_courseware') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'virtual_courseware' COMMENT '课件类型（固定为虚拟课件）',
  `knowledge_points` json DEFAULT NULL COMMENT '关联知识点（JSON数组）',
  `question_ids` json DEFAULT NULL COMMENT '关联试题ID列表（JSON数组，仅适用于理论试题）',
  `syllabus_id` bigint DEFAULT NULL COMMENT '关联教学大纲ID',
  `class_ids` json DEFAULT NULL COMMENT '适用班级ID列表（JSON数组）',
  `major_ids` json DEFAULT NULL COMMENT '适用专业ID列表（JSON数组）',
  `order` int unsigned DEFAULT '0' COMMENT '显示顺序',
  PRIMARY KEY (`courseware_id`) USING BTREE,
  KEY `idx_virtual_courseware_course` (`course_id`) USING BTREE,
  KEY `idx_virtual_courseware_scorm` (`package_id`) USING BTREE,
  CONSTRAINT `virtual_courseware_ibfk_scorm` FOREIGN KEY (`package_id`) REFERENCES `scorm_packages` (`package_id`) ON DELETE RESTRICT ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='虚拟课件表';

-- ----------------------------
-- Table structure for workflow
-- ----------------------------
DROP TABLE IF EXISTS `workflow`;
CREATE TABLE `workflow` (
  `flow_id` bigint NOT NULL AUTO_INCREMENT,
  `flow_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '流程名称（唯一）',
  `flow_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '流程类型（课件审核/资源发布）',
  `node_config` json NOT NULL COMMENT '节点配置（JSON格式）',
  `form_template_id` bigint NOT NULL COMMENT '表单模板ID',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`flow_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='审批流程表';

-- ----------------------------
-- Table structure for workflow_template
-- ----------------------------
DROP TABLE IF EXISTS `workflow_template`;
CREATE TABLE `workflow_template` (
  `template_id` bigint NOT NULL AUTO_INCREMENT,
  `template_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '模板名称',
  `flow_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '流程类型（syllabus_review等）',
  `node_config` json NOT NULL COMMENT '节点配置（JSON格式）',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`template_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='审核流程模板表';

SET FOREIGN_KEY_CHECKS = 1;
