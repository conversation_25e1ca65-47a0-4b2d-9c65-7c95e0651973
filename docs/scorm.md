Scorm
SCORM（可共享内容对象参考模型）是由美国高级分布式学习计划（ADL）制定的一套网络教育技术标准，旨在实现学习资源的跨平台共享与复用。该模型通过结构化封装教材、标准化元数据描述及运行环境规范，解决教育资源重复开发、管理系统互操作性差等问题。
SCORM包含内容聚合模型（CAM）、运行时间环境（RTE）和排序导航（SN）三大核心组件，定义了学习资源打包、传输及交互的规则。其特性涵盖可获取性、可沟通性、耐久性和可再使用性，支持学习者在不同平台调用标准化课程。当前主流版本为2006年发布的SCORM 2004 3rd Edition。
该标准起源于1997年美国国防部主导的ADL计划，整合了IMS、AICC、IEEE等组织的现有规范，强调"不重复发明轮子"原则。通过融合航空、计算机等领域的技术标准，形成统一模型以促进军事、教育和企业间的协作学习系统构建。

SCORM——可共享对象参考模型的研究
网络教育是当今国际国内教育的主流发展方向，其开发性、协同学习、共享资源、无时空限制为学习者构建了一个随时随地自主学习的终身学习环境。目前网络教育资源的共享基本上停留在简单的HTML网页和其他常用文件共享的基础上，缺少统一的结构，课件开发重复严重，资源管理共享难度大，学习资源在教学平台间难以交互。SCORM提供了强有力的支持，提供了基于现有标准的可共享、互操作的模式。SCORM的核心即在可共享与重复使用的学习物件教材﹔它仍保有课程结构，更厉害的是它能够让电脑懂得这些物件到底是什么内容，这要归功Meta-data来描述它，而实际的档案架构亦有特别规划，以方便教材再次使用。其目的是实现在确保学习者无论在何时何地，希望能透过一套可重复存取、可再用、有耐久性、以及可相互沟通的建立，能及时获取所需的高品质学习资源。目前越来越多的e-Learning 服务公司采用SCORM标准，如上海汇旌。
SCROM概述
SCORM(Sharable Content Object Reference Model) 是由ADL（高级分布式学习）计划开发的，该计划最初由美国国防部于1997 年启动，是关于共享课件的创建、管理和使用的标准。ADL从兼容性、促进自主远程学习和商业上考虑，定义了一系列高级要求，如内容重用、可访问性、持久性和协同自主学习等，宗旨是在美国联邦政府各个部门、企业、教育和培训机构之间实现协作，为模块化在线教育内容和有关的工具创建业务/市场。目前SCORM已经发展到2.0 版本。
SCORM在已有的网络教育技术标准（IMS, AICC, IEEE LTSC, ARIADNE）的基础上建立的具有可访问性、协作性、持久性和可重用性的特定模型，提供了网络教育的实现和应用指南，其目的是为了解决如何使课程从一个平台转移到另一个平台，如何创建可供不同课程共享的可重用构件，以及快速准确地查找课程素材。
简单说，它就是为了满足对网络化学习内容的高水平要求而设计的参考了一系列相关技术规范的模型。许多组织对SCORM的开发做出了重要的贡献，例如ARIADNE（欧洲远程教学和分布式网络联盟）、 AICC (航空工业计算机培训委员会)、IEEE LTSC（电气和电子工程师协会学习技术标准委员会）、IMS（教学管理系统全球化学习联盟）等。SCORM的开发参考利用了它们已有的一些规范和标准，并进行了适当的改编、综合，最后形成了这个更为完整，更容易执行的模型。
起源
1. 由美国ADL (Advanced Distribution Learning) 整合教材开发厂商及使用者与IMS、AICC、IEEE等标准推动单位，共同汇整各界在教材标准上的努力成果，研订出来的一套相互关连的技术指引。
2. ADL订定SCORM 时，特别强调不再重新研发(Don't reinvent the wheel)。意思是不会提出新的规格，而是把重点摆在提出整合现有E-learning规范的架构模型。
功能
1. 可获取性(Accessibility)：学习者可在任何时间或地点，透过网络获取所需的教材。
2. 可沟通性(Interoperability)：教材可以在任何开发系统及教学平台上使用。
3. 耐久性(Durability)：科技提升或改变时，不须重新修改应用程序或教材。
4. 可再使用性(Reusability):在不同应用环境下，教材可以重复使用。
版本
2006年5月，SCORM 2004 3rd ed.：目前最新的版本。
SCORM 主要标准和规范
可共享对象参考模型(SCORM) 2004主要包括以下三个方面的标准和规范：SCORM内容聚合模型 (CAM)、SCORM运行时间环境（RTE）、SCORM 排序和导航（SN）。
SCORM内容聚合模型 (CAM)一书描述了组成一次学习过程的材料，如何包装才能使这些材料能够在不同系统之间交换，如何描述这些材料才能实现查询，以及如何定义这些材料呈现的先后次序的规则。 SCORM内容聚合模型（CAM）也确定了构建内容集合体（如课程、课、模块等）的任务和要求。这本书包括创建内容包的信息、将元数据应用到内容包的成分中和将排序和导航细目应用于内容包中的前后关系。SCORM CAM有很多地方依赖SCORM RTE。 SCORM元数据描述了SCORM内容聚合模型（内容集合体、活动、SCOs和微单元）的不同成分。元数据作为标识的一种形式促进了这些组成部分的查询和开发。到现在SCORM元数据和SCORM RET还没有确定的相关性，SCORM元数据还没有影响到运行时间的动作或事件。由于这些原因，元数据在SCORM RTE中没有详细说明。随着SCORM的演变，这种关联也会得到改变。
SCORM运行时间环境（RTE）描述了学习管理系统(LMS)对运行时间环境的要求（如，内容启动过程、内容和不同管理系统之间的交流、以及用于呈现学习者过去信息的标准的数据模型元素）。RTE包括SCO（可共享内容对象）的要求及其在API（应用编程接口）中的应用、SCORM运行时间环境数据模型。SCORM RTE的目的是提供SCO 和LMS互操作的方法。SCORM为学习内容在多种LMSs之间提供互操作方法，无论是用什么工具开发的内容。为了使这成为可能，必须有一个共同的方法来启动内容，有一个内容和LMS沟通的共同方法，并且预先确定运行过程中LMS和内容交换的数据元素。SCORM RTE的三个组成部分定义为启动、应用编程接口（API）和数据模型。这些元素的技术说明在SCORM RTE中有描述，但是RTE遵循这些数据元素的简单概述。
SCORM 排序和导航（SN）描述了符合SCORM 的内容是如何通过一些学习者发起的或系统发起的导航时间排序的。内容的分支和流程可以用预先确定的一些活动来描述，尤其是在设计时定义。SCORM SN也描述了符合SCORM的LMS是如何根据一些学习者发起的或系统发起的导航事件和它们对运行事件环境的影响来解释排序规则。
主要架构
1. CAM(Content Aggregation Model)内容整合模式：规定单独的学习内容如何描述、内容如何组成可共享和互操作的课程，依照一门课程应涵盖的范围，将特定的学习资源(Learning Resource)包裹在一起，以便于LMS可以启动此课程。
2. RTE(Run-time Environment)课程执行环境：一套标准的方法，让LMS 启动学习资源以及让学习资源与平台之间可以互相沟通信息，让学习资源能够在不同的学习管理平台内也可以重复使用。
CAM (Content Aggregation Model)的三大规范
1. Content Model共享教材：定义教材中有那些教材组件，及组件间应如何被编排、统整成一套可重复使用的课程。
2. Metadata诠释资料：透过XML来描述教材(Html档，图文件或多媒体文件等) 的信息；透过Metadata对教材及其组件的描述，可以进一步管理课程的资源。
3. Content Package内容包裹：使用档名一致的档案(imsmanifest.xml) 来包裹教材和课程，透过XML来描述教材组件和课程编排架构，只要将该课程输出成SCORM的Content Package，支持SCORM的LMS就能够解析SCORM的Manifest档案，将该课程转入，达成教材共享之目的。
RTE( Run-time Environment)的三大规范
1. 启动(Launch)：启动的机制是要让LMS可以依照特定条件启动SCO或是Assets。LMS可以根据课程包裹中所定义的顺序、或是依照学习者的指示、或是依照学习者学习的状况而启动课程，例如，当使用者通过某个课程的前测(pretest)时就启动课程A，否则启动课程B。
2. 应用程序编程接口(API)：API是由SCO资源发送状态信息(初始化、完成、错误)和交换数据(获取和设置)的标准函数所构成，使得教材内容与LMS能互相沟通，简单说，API是LMS与SCO间的沟通方式。
3. 数据模式(Data model)：数据模式是用以定义SCO对象之相关属性、行为、关连、组合、及继承等，是LMS与SCO彼此都知道的数据格式。
Content Model的内容类型
1. Asset素材：是构成学习对象的最基本单位，例如文字、图像、声音、影像、以及其它可在网络上传输的数据等。
2. SCO(Sharable Content Object)学习单元：是由一或多个素材所组成，至少具有一个学习目标。
3. CA(Content Aggregation)教材/课程：是指教学者透过教学设计理念，将一些素材或学习单元予以结构化组织编排，使其成为具有逻辑顺序及呈现架构的一个课程，可达到建立特定的学习经验之目的。
Metadata的内容
1. General：包括课程标题、描述、建立日期、版本等一般性的课程描述等。
2. Lifecycle：描述此对象之版本、目前完工状态(如草稿或已完工)，以及修改此对象之日期、修改者姓名等信息。
3. Meta-metadata：描述metadata本身的相关信息，例如由谁输入这些metadata、何时输入、用来输入metadata的语言等信息。
4. Technical：描述技术需求与此资源的特性，例如需要哪些附加软件(plugin)才能正常读取课程、这个课程的大小与储存位置、需要哪种版本的浏览器才能读取此课程等。
5. Educational：描述此资源教学或教育上的特性，例如本课程的讲师、助教、是否属于互动性课程或是一般自我阅读性课程、适合的学习者龄或是学历、课程难易程度、预估学习时间等。
6. Rights：描述使用此资源的权限或其它限制，例如是否需付费、此课程是否有任何版权限制等。
7. Relation：描述此资源和其它标的资源之间的关系，例如这个课程是否是其它课程的一部份等。
8. Annotation：提供在教育环境上使用此资源的建议，以及此资源由谁、何时所建立等信息。
9. Classification：描述此资源属于哪一个系统领域类别。　
Content Package
课程内容包裹的目的是提供一套标准的包裹作业方式，使得课程制作工具可依此包裹出一套标准的课程储存在课程宝库(repository)，并提供给不同的LMS读取。
经过包裹后的课程将形成几个重要的档案叙述，coursename.html(读取课程的主要档案)、coursename.xml(记录课程一般 性的metadata)、imsmanifest.xml(记录一个课程的组成结构、各种学习 资源的存放位置，以及其它相关的metadata，这个档案也可记录SCO或Assets的读取顺序)。
经过包裹后的课程会变成单一的PIF(Package Interchange File)档， 这个档案就好比常见的ZIP压缩档，解开后就是一门课程所有相关的 内容。因此任何符合SCORM的LMS平台都可以汇入∕汇出这些PIF档。
LMS与SCO的通讯与沟通
LMS 负责SCO之间的流程顺序，而SCO 则是负责SCO 之内的流程顺序，因此SCO 必须透过LMS 管理与协助，来和其它SCO 沟通，相关沟通记录则储存于imsmanifest.xml档案内。为了达成处处可学习、随时可学习的目标，LMS必须提供应用程序接口适性套性(API Adaptor)，让所有的浏览器皆可执行e-Learning化的学习内容，ADL目前RTE中是以Java Applet的方式，提供一套建置API Adaptor的机制。
SCO是课件编辑最基本的课件单位，也是平台组织和跟踪的最小单位。一小粒度的SCO可以仅仅是一张图片，也可以是整个一门课程。而每个SCO根SCORM规范的要求都必需要有完整的元数据描述。一般来讲，SCO粒度大小主要受以下两个方面的影响:一是课件编辑者需获得的课件内容的可复用的程度。比如，某一图片或者视频，由于有可能在课反复出现，课件编辑者可将其直接定义为一个SCO，以便在以后直接复用;是基于网络浏览该课件的信息传输量，当SCO过大时，将会直接影响网络传输质量。
通常的做法是将SCO的最小粒度为一个页面，且具有相对较为完整的实际学含义(比如某个单元的一个小节)，而对于在课件中出现的图片，动画等作单独的Asset。另外，当课件规模较大或SCO的粒度较小时，SCO元数据描述的工作量将有可能变得很大。
当完成课件规划以后，就是具体课件的制作了，该制作过程是和传统意义上网络教育课件的制作也没有本质的区别，比如可以采用FrontPage、PowerPoint等完成课件的制作。
由于SCO本身必须要独立于具体的课件，而且在基于SCORM课件的设计中，SCO之间也不需要内容关联，因此在SCO的具体制作中，不应含有课件结构的目录信息。
SCORM标准教材制作步骤
一、整个课程结构的设计
根据内容和学习的需要，进行章节内容的分配。
1、通过一个IMSmanifest.xml文件来呈现
2、根据教学内容需要设计课程清单、课程树状结构
二、课程中SCO的分割
在SCORM中，所谓的SCO可以是教材中的：章、节、主题、单元，也可以是任何大小，端看需求而定。要强调的是SCO至少要包含一个学习目标，而且它是可被记录的单位。
SCO大小的规定
1、共享内容对象（SCO）代表了一个或多个基本素材的集合，这个集合包含了一个特殊的可发布基本素材，它利用SCORM运行时环境与LMS进行通讯。
2、为了可以重用，SCO 并不依赖于学习内容，而是其本身。比如，一个SCO可以用在不同的学习体验中来执行不同的课件。另外，一个或者多个SCO可以整合成更加高端的教学和培训单元，来执行更高端的学习对象。3、SCO被认为是小型的单元，所以跨课件的重用是有可能的。SCORM对SCO的大小不会有特别的限制。尽管在设计编写期间，决定SCO大小的时候还是定一个其内容的最小的逻辑大小，而这些内容在运行是可以由LMS跟踪。内容开发者根据学习内容的信息数量和其可重用的水平来决定SCO的大小。
三、SCO的制作
在具体页面中加入跟踪代码
1、SCO是课程与LMS能够进行跟踪的最小单元，Asset是无法让LMS来进行跟踪的，这个也是我们制作SCORM教材的目的。
2、制作SCO的步骤：
入门操作à“加头加尾”
（1）制作HTML 页面；
（2）嵌入JavaScript文件(APIWrapper.js 和SCOFunctions.js)；
（3）Initialize 和Terminate “SCOFunctions.js”中的函数
高级层次à“应用元数据（data model）”
四、课程结构的实现
五、课程内容的scorm打包
六、标准课程的测试
网络化教育是当今国际国内教育发展新的生长点，是现代教育技术的主流发展方向。研制可共享、可重用的教育资源和互操作的管理系统是网络教育发展的一个重要方面。SCORM正是针对于此，提供了基于现有网络教育标准的可共享、互操作的网络教育模式。并且，SCORM的研究为网络教育的研究发展提供了强有力的支持，起到了很好的促进作用。