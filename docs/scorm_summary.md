# SCORM标准概述

## 什么是SCORM？

SCORM（可共享内容对象参考模型）是由美国高级分布式学习计划（ADL）制定的一套网络教育技术标准，旨在实现学习资源的跨平台共享与复用。该模型通过结构化封装教材、标准化元数据描述及运行环境规范，解决教育资源重复开发、管理系统互操作性差等问题。

SCORM包含内容聚合模型（CAM）、运行时间环境（RTE）和排序导航（SN）三大核心组件，定义了学习资源打包、传输及交互的规则。其特性涵盖可获取性、可沟通性、耐久性和可再使用性，支持学习者在不同平台调用标准化课程。当前主流版本为2006年发布的SCORM 2004 3rd Edition。

## SCORM的发展背景

该标准起源于1997年美国国防部主导的ADL计划，整合了IMS、AICC、IEEE等组织的现有规范，强调"不重复发明轮子"原则。通过融合航空、计算机等领域的技术标准，形成统一模型以促进军事、教育和企业间的协作学习系统构建。

## SCORM的核心价值

网络教育是当今国际国内教育的主流发展方向，其开发性、协同学习、共享资源、无时空限制为学习者构建了一个随时随地自主学习的终身学习环境。目前网络教育资源的共享基本上停留在简单的HTML网页和其他常用文件共享的基础上，缺少统一的结构，课件开发重复严重，资源管理共享难度大，学习资源在教学平台间难以交互。SCORM提供了强有力的支持，提供了基于现有标准的可共享、互操作的模式。

## SCORM的主要功能

1. **可获取性(Accessibility)**：学习者可在任何时间或地点，透过网络获取所需的教材。
2. **可沟通性(Interoperability)**：教材可以在任何开发系统及教学平台上使用。
3. **耐久性(Durability)**：科技提升或改变时，不须重新修改应用程序或教材。
4. **可再使用性(Reusability)**：在不同应用环境下，教材可以重复使用。

## SCORM的技术架构

### 主要标准和规范

SCORM 2004主要包括以下三个方面的标准和规范：

1. **SCORM内容聚合模型 (CAM)**
2. **SCORM运行时间环境（RTE）**
3. **SCORM 排序和导航（SN）**

### CAM(Content Aggregation Model)内容整合模式

规定单独的学习内容如何描述、内容如何组成可共享和互操作的课程，依照一门课程应涵盖的范围，将特定的学习资源(Learning Resource)包裹在一起，以便于LMS可以启动此课程。

### RTE(Run-time Environment)课程执行环境

一套标准的方法，让LMS 启动学习资源以及让学习资源与平台之间可以互相沟通信息，让学习资源能够在不同的学习管理平台内也可以重复使用。

### Content Package内容包裹

课程内容包裹的目的是提供一套标准的包裹作业方式，使得课程制作工具可依此包裹出一套标准的课程储存在课程宝库(repository)，并提供给不同的LMS读取。

经过包裹后的课程将形成几个重要的档案叙述，coursename.html(读取课程的主要档案)、coursename.xml(记录课程一般性的metadata)、imsmanifest.xml(记录一个课程的组成结构、各种学习资源的存放位置，以及其它相关的metadata，这个档案也可记录SCO或Assets的读取顺序)。

## SCORM的应用场景

SCORM标准广泛应用于e-Learning领域，越来越多的在线教育服务公司采用SCORM标准，如上海汇旌。它解决了教育资源重复开发、管理系统互操作性差等问题，支持学习者在不同平台调用标准化课程。

## SCORM标准教材制作步骤

1. **整个课程结构的设计**：根据内容和学习的需要，进行章节内容的分配。
2. **课程中SCO的分割**：在SCORM中，所谓的SCO可以是教材中的：章、节、主题、单元，也可以是任何大小，端看需求而定。
3. **SCO的制作**：在具体页面中加入跟踪代码。
4. **课程结构的实现**
5. **课程内容的scorm打包**
6. **标准课程的测试"